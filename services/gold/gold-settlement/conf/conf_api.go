package conf

import(
	config "golang.52tt.com/pkg/config"
	commission "golang.52tt.com/clients/commission"
)

type IServiceConfigT interface {
	GetCommissionConfig() *commission.Config
	GetMysqlAmuseConfig() *config.MysqlConfig
	GetMysqlAmuseConnectionString() string
	GetMysqlGameConnectionString() string
	GetMysqlYuyinConfig() *config.MysqlConfig
	GetMysqlYuyinConnectionString() string
	GetRedisAmuseConfig() *RedisConfig
	GetRedisGameConfig() *RedisConfig
	GetRedisYuyinConfig() *RedisConfig
	IsTest() bool
	Parse(configFile string) (err error)
}

