// Code generated by protoc-gen-go. DO NOT EDIT.
// source: glory-lottery/glory-lottery.proto

package glory_lottery // import "golang.52tt.com/protocol/services/glory-lottery"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LotteryItemType int32

const (
	LotteryItemType_LOTTERY_ITEM_UNSPECIFIED      LotteryItemType = 0
	LotteryItemType_LOTTERY_ITEM_PACKAGE          LotteryItemType = 1
	LotteryItemType_LOTTERY_ITEM_HORSE            LotteryItemType = 2
	LotteryItemType_LOTTERY_ITEM_MIC_STYLE        LotteryItemType = 3
	LotteryItemType_Lottery_ITEM_WEALTH_CARD      LotteryItemType = 4
	LotteryItemType_LOTTERY_ITEM_INFORMATION_CARD LotteryItemType = 5
	LotteryItemType_LOTTERY_ITEM_DYNAMIC_ICON     LotteryItemType = 6
	LotteryItemType_LOTTERY_ITEM_MIC_EMOJI        LotteryItemType = 7
	LotteryItemType_LOTTERY_ITEM_DECORATION       LotteryItemType = 8
	LotteryItemType_LOTTERY_ITEM_DRESSUP_DEBRIS   LotteryItemType = 9
	LotteryItemType_LOTTERY_ITEM_NONE             LotteryItemType = 10
)

var LotteryItemType_name = map[int32]string{
	0:  "LOTTERY_ITEM_UNSPECIFIED",
	1:  "LOTTERY_ITEM_PACKAGE",
	2:  "LOTTERY_ITEM_HORSE",
	3:  "LOTTERY_ITEM_MIC_STYLE",
	4:  "Lottery_ITEM_WEALTH_CARD",
	5:  "LOTTERY_ITEM_INFORMATION_CARD",
	6:  "LOTTERY_ITEM_DYNAMIC_ICON",
	7:  "LOTTERY_ITEM_MIC_EMOJI",
	8:  "LOTTERY_ITEM_DECORATION",
	9:  "LOTTERY_ITEM_DRESSUP_DEBRIS",
	10: "LOTTERY_ITEM_NONE",
}
var LotteryItemType_value = map[string]int32{
	"LOTTERY_ITEM_UNSPECIFIED":      0,
	"LOTTERY_ITEM_PACKAGE":          1,
	"LOTTERY_ITEM_HORSE":            2,
	"LOTTERY_ITEM_MIC_STYLE":        3,
	"Lottery_ITEM_WEALTH_CARD":      4,
	"LOTTERY_ITEM_INFORMATION_CARD": 5,
	"LOTTERY_ITEM_DYNAMIC_ICON":     6,
	"LOTTERY_ITEM_MIC_EMOJI":        7,
	"LOTTERY_ITEM_DECORATION":       8,
	"LOTTERY_ITEM_DRESSUP_DEBRIS":   9,
	"LOTTERY_ITEM_NONE":             10,
}

func (x LotteryItemType) String() string {
	return proto.EnumName(LotteryItemType_name, int32(x))
}
func (LotteryItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{0}
}

// 库存类型
type StockType int32

const (
	StockType_STOCK_TYPE_UNSPECIFIED StockType = 0
	StockType_STOCK_TYPE_LIMITED     StockType = 1
	StockType_STOCK_TYPE_INFINITE    StockType = 2
)

var StockType_name = map[int32]string{
	0: "STOCK_TYPE_UNSPECIFIED",
	1: "STOCK_TYPE_LIMITED",
	2: "STOCK_TYPE_INFINITE",
}
var StockType_value = map[string]int32{
	"STOCK_TYPE_UNSPECIFIED": 0,
	"STOCK_TYPE_LIMITED":     1,
	"STOCK_TYPE_INFINITE":    2,
}

func (x StockType) String() string {
	return proto.EnumName(StockType_name, int32(x))
}
func (StockType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{1}
}

// 刷新类型
type RefreshType int32

const (
	RefreshType_REFRESH_TYPE_UNSPECIFIED RefreshType = 0
	RefreshType_REFRESH_TYPE_DAILY       RefreshType = 1
	RefreshType_REFRESH_TYPE_HOURLY      RefreshType = 2
)

var RefreshType_name = map[int32]string{
	0: "REFRESH_TYPE_UNSPECIFIED",
	1: "REFRESH_TYPE_DAILY",
	2: "REFRESH_TYPE_HOURLY",
}
var RefreshType_value = map[string]int32{
	"REFRESH_TYPE_UNSPECIFIED": 0,
	"REFRESH_TYPE_DAILY":       1,
	"REFRESH_TYPE_HOURLY":      2,
}

func (x RefreshType) String() string {
	return proto.EnumName(RefreshType_name, int32(x))
}
func (RefreshType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{2}
}

type ResetType int32

const (
	ResetType_RESET_TYPE_UNSPECIFIED ResetType = 0
	ResetType_RESET_TYPE_DAY         ResetType = 1
	ResetType_RESET_TYPE_MONTH       ResetType = 2
)

var ResetType_name = map[int32]string{
	0: "RESET_TYPE_UNSPECIFIED",
	1: "RESET_TYPE_DAY",
	2: "RESET_TYPE_MONTH",
}
var ResetType_value = map[string]int32{
	"RESET_TYPE_UNSPECIFIED": 0,
	"RESET_TYPE_DAY":         1,
	"RESET_TYPE_MONTH":       2,
}

func (x ResetType) String() string {
	return proto.EnumName(ResetType_name, int32(x))
}
func (ResetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{3}
}

type LotteryResp_WINNING_TYPE int32

const (
	LotteryResp_WINNING_TYPE_UNKNOWN LotteryResp_WINNING_TYPE = 0
	LotteryResp_WINNING_TYPE_BIG     LotteryResp_WINNING_TYPE = 1
	LotteryResp_WINNING_TYPE_FLASH   LotteryResp_WINNING_TYPE = 2
	LotteryResp_WINNING_TYPE_NORMAL  LotteryResp_WINNING_TYPE = 3
	LotteryResp_WINNING_TYPE_NONE    LotteryResp_WINNING_TYPE = 4
)

var LotteryResp_WINNING_TYPE_name = map[int32]string{
	0: "WINNING_TYPE_UNKNOWN",
	1: "WINNING_TYPE_BIG",
	2: "WINNING_TYPE_FLASH",
	3: "WINNING_TYPE_NORMAL",
	4: "WINNING_TYPE_NONE",
}
var LotteryResp_WINNING_TYPE_value = map[string]int32{
	"WINNING_TYPE_UNKNOWN": 0,
	"WINNING_TYPE_BIG":     1,
	"WINNING_TYPE_FLASH":   2,
	"WINNING_TYPE_NORMAL":  3,
	"WINNING_TYPE_NONE":    4,
}

func (x LotteryResp_WINNING_TYPE) String() string {
	return proto.EnumName(LotteryResp_WINNING_TYPE_name, int32(x))
}
func (LotteryResp_WINNING_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{30, 0}
}

// 奖池配置
type StockConf struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ItemType             uint32   `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemName             string   `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemId               string   `protobuf:"bytes,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	StockPrice           uint32   `protobuf:"varint,5,opt,name=stock_price,json=stockPrice,proto3" json:"stock_price,omitempty"`
	ItemCount            uint32   `protobuf:"varint,6,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	StockType            uint32   `protobuf:"varint,7,opt,name=stock_type,json=stockType,proto3" json:"stock_type,omitempty"`
	RefreshType          uint32   `protobuf:"varint,8,opt,name=refresh_type,json=refreshType,proto3" json:"refresh_type,omitempty"`
	ResetType            uint32   `protobuf:"varint,9,opt,name=reset_type,json=resetType,proto3" json:"reset_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StockConf) Reset()         { *m = StockConf{} }
func (m *StockConf) String() string { return proto.CompactTextString(m) }
func (*StockConf) ProtoMessage()    {}
func (*StockConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{0}
}
func (m *StockConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StockConf.Unmarshal(m, b)
}
func (m *StockConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StockConf.Marshal(b, m, deterministic)
}
func (dst *StockConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StockConf.Merge(dst, src)
}
func (m *StockConf) XXX_Size() int {
	return xxx_messageInfo_StockConf.Size(m)
}
func (m *StockConf) XXX_DiscardUnknown() {
	xxx_messageInfo_StockConf.DiscardUnknown(m)
}

var xxx_messageInfo_StockConf proto.InternalMessageInfo

func (m *StockConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StockConf) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *StockConf) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *StockConf) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *StockConf) GetStockPrice() uint32 {
	if m != nil {
		return m.StockPrice
	}
	return 0
}

func (m *StockConf) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *StockConf) GetStockType() uint32 {
	if m != nil {
		return m.StockType
	}
	return 0
}

func (m *StockConf) GetRefreshType() uint32 {
	if m != nil {
		return m.RefreshType
	}
	return 0
}

func (m *StockConf) GetResetType() uint32 {
	if m != nil {
		return m.ResetType
	}
	return 0
}

type UpdateBaseStockReq struct {
	List                 []*StockConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateBaseStockReq) Reset()         { *m = UpdateBaseStockReq{} }
func (m *UpdateBaseStockReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBaseStockReq) ProtoMessage()    {}
func (*UpdateBaseStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{1}
}
func (m *UpdateBaseStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBaseStockReq.Unmarshal(m, b)
}
func (m *UpdateBaseStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBaseStockReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBaseStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBaseStockReq.Merge(dst, src)
}
func (m *UpdateBaseStockReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBaseStockReq.Size(m)
}
func (m *UpdateBaseStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBaseStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBaseStockReq proto.InternalMessageInfo

func (m *UpdateBaseStockReq) GetList() []*StockConf {
	if m != nil {
		return m.List
	}
	return nil
}

type UpdateBaseStockResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBaseStockResp) Reset()         { *m = UpdateBaseStockResp{} }
func (m *UpdateBaseStockResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBaseStockResp) ProtoMessage()    {}
func (*UpdateBaseStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{2}
}
func (m *UpdateBaseStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBaseStockResp.Unmarshal(m, b)
}
func (m *UpdateBaseStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBaseStockResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBaseStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBaseStockResp.Merge(dst, src)
}
func (m *UpdateBaseStockResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBaseStockResp.Size(m)
}
func (m *UpdateBaseStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBaseStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBaseStockResp proto.InternalMessageInfo

type GetBaseStockReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBaseStockReq) Reset()         { *m = GetBaseStockReq{} }
func (m *GetBaseStockReq) String() string { return proto.CompactTextString(m) }
func (*GetBaseStockReq) ProtoMessage()    {}
func (*GetBaseStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{3}
}
func (m *GetBaseStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBaseStockReq.Unmarshal(m, b)
}
func (m *GetBaseStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBaseStockReq.Marshal(b, m, deterministic)
}
func (dst *GetBaseStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBaseStockReq.Merge(dst, src)
}
func (m *GetBaseStockReq) XXX_Size() int {
	return xxx_messageInfo_GetBaseStockReq.Size(m)
}
func (m *GetBaseStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBaseStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBaseStockReq proto.InternalMessageInfo

type GetBaseStockResp struct {
	List                 []*StockConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBaseStockResp) Reset()         { *m = GetBaseStockResp{} }
func (m *GetBaseStockResp) String() string { return proto.CompactTextString(m) }
func (*GetBaseStockResp) ProtoMessage()    {}
func (*GetBaseStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{4}
}
func (m *GetBaseStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBaseStockResp.Unmarshal(m, b)
}
func (m *GetBaseStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBaseStockResp.Marshal(b, m, deterministic)
}
func (dst *GetBaseStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBaseStockResp.Merge(dst, src)
}
func (m *GetBaseStockResp) XXX_Size() int {
	return xxx_messageInfo_GetBaseStockResp.Size(m)
}
func (m *GetBaseStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBaseStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBaseStockResp proto.InternalMessageInfo

func (m *GetBaseStockResp) GetList() []*StockConf {
	if m != nil {
		return m.List
	}
	return nil
}

type GetCurrentStockReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurrentStockReq) Reset()         { *m = GetCurrentStockReq{} }
func (m *GetCurrentStockReq) String() string { return proto.CompactTextString(m) }
func (*GetCurrentStockReq) ProtoMessage()    {}
func (*GetCurrentStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{5}
}
func (m *GetCurrentStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrentStockReq.Unmarshal(m, b)
}
func (m *GetCurrentStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrentStockReq.Marshal(b, m, deterministic)
}
func (dst *GetCurrentStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrentStockReq.Merge(dst, src)
}
func (m *GetCurrentStockReq) XXX_Size() int {
	return xxx_messageInfo_GetCurrentStockReq.Size(m)
}
func (m *GetCurrentStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrentStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrentStockReq proto.InternalMessageInfo

type GetCurrentStockResp struct {
	List                 []*StockConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	LastUpdateTime       uint32       `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCurrentStockResp) Reset()         { *m = GetCurrentStockResp{} }
func (m *GetCurrentStockResp) String() string { return proto.CompactTextString(m) }
func (*GetCurrentStockResp) ProtoMessage()    {}
func (*GetCurrentStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{6}
}
func (m *GetCurrentStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrentStockResp.Unmarshal(m, b)
}
func (m *GetCurrentStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrentStockResp.Marshal(b, m, deterministic)
}
func (dst *GetCurrentStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrentStockResp.Merge(dst, src)
}
func (m *GetCurrentStockResp) XXX_Size() int {
	return xxx_messageInfo_GetCurrentStockResp.Size(m)
}
func (m *GetCurrentStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrentStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrentStockResp proto.InternalMessageInfo

func (m *GetCurrentStockResp) GetList() []*StockConf {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetCurrentStockResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type UpdateManualStockReq struct {
	List                 []*StockConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	EffectStart          uint32       `protobuf:"varint,2,opt,name=effect_start,json=effectStart,proto3" json:"effect_start,omitempty"`
	EffectEnd            uint32       `protobuf:"varint,3,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateManualStockReq) Reset()         { *m = UpdateManualStockReq{} }
func (m *UpdateManualStockReq) String() string { return proto.CompactTextString(m) }
func (*UpdateManualStockReq) ProtoMessage()    {}
func (*UpdateManualStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{7}
}
func (m *UpdateManualStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateManualStockReq.Unmarshal(m, b)
}
func (m *UpdateManualStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateManualStockReq.Marshal(b, m, deterministic)
}
func (dst *UpdateManualStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateManualStockReq.Merge(dst, src)
}
func (m *UpdateManualStockReq) XXX_Size() int {
	return xxx_messageInfo_UpdateManualStockReq.Size(m)
}
func (m *UpdateManualStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateManualStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateManualStockReq proto.InternalMessageInfo

func (m *UpdateManualStockReq) GetList() []*StockConf {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *UpdateManualStockReq) GetEffectStart() uint32 {
	if m != nil {
		return m.EffectStart
	}
	return 0
}

func (m *UpdateManualStockReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type UpdateManualStockResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateManualStockResp) Reset()         { *m = UpdateManualStockResp{} }
func (m *UpdateManualStockResp) String() string { return proto.CompactTextString(m) }
func (*UpdateManualStockResp) ProtoMessage()    {}
func (*UpdateManualStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{8}
}
func (m *UpdateManualStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateManualStockResp.Unmarshal(m, b)
}
func (m *UpdateManualStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateManualStockResp.Marshal(b, m, deterministic)
}
func (dst *UpdateManualStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateManualStockResp.Merge(dst, src)
}
func (m *UpdateManualStockResp) XXX_Size() int {
	return xxx_messageInfo_UpdateManualStockResp.Size(m)
}
func (m *UpdateManualStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateManualStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateManualStockResp proto.InternalMessageInfo

type GetManualStockReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetManualStockReq) Reset()         { *m = GetManualStockReq{} }
func (m *GetManualStockReq) String() string { return proto.CompactTextString(m) }
func (*GetManualStockReq) ProtoMessage()    {}
func (*GetManualStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{9}
}
func (m *GetManualStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetManualStockReq.Unmarshal(m, b)
}
func (m *GetManualStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetManualStockReq.Marshal(b, m, deterministic)
}
func (dst *GetManualStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetManualStockReq.Merge(dst, src)
}
func (m *GetManualStockReq) XXX_Size() int {
	return xxx_messageInfo_GetManualStockReq.Size(m)
}
func (m *GetManualStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetManualStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetManualStockReq proto.InternalMessageInfo

type ManualStockConf struct {
	List                 []*StockConf `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	EffectStart          uint32       `protobuf:"varint,2,opt,name=effect_start,json=effectStart,proto3" json:"effect_start,omitempty"`
	EffectEnd            uint32       `protobuf:"varint,3,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ManualStockConf) Reset()         { *m = ManualStockConf{} }
func (m *ManualStockConf) String() string { return proto.CompactTextString(m) }
func (*ManualStockConf) ProtoMessage()    {}
func (*ManualStockConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{10}
}
func (m *ManualStockConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualStockConf.Unmarshal(m, b)
}
func (m *ManualStockConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualStockConf.Marshal(b, m, deterministic)
}
func (dst *ManualStockConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualStockConf.Merge(dst, src)
}
func (m *ManualStockConf) XXX_Size() int {
	return xxx_messageInfo_ManualStockConf.Size(m)
}
func (m *ManualStockConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualStockConf.DiscardUnknown(m)
}

var xxx_messageInfo_ManualStockConf proto.InternalMessageInfo

func (m *ManualStockConf) GetList() []*StockConf {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ManualStockConf) GetEffectStart() uint32 {
	if m != nil {
		return m.EffectStart
	}
	return 0
}

func (m *ManualStockConf) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type GetManualStockResp struct {
	CurrentStock         *ManualStockConf `protobuf:"bytes,1,opt,name=current_stock,json=currentStock,proto3" json:"current_stock,omitempty"`
	FutureSotck          *ManualStockConf `protobuf:"bytes,2,opt,name=future_sotck,json=futureSotck,proto3" json:"future_sotck,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetManualStockResp) Reset()         { *m = GetManualStockResp{} }
func (m *GetManualStockResp) String() string { return proto.CompactTextString(m) }
func (*GetManualStockResp) ProtoMessage()    {}
func (*GetManualStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{11}
}
func (m *GetManualStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetManualStockResp.Unmarshal(m, b)
}
func (m *GetManualStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetManualStockResp.Marshal(b, m, deterministic)
}
func (dst *GetManualStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetManualStockResp.Merge(dst, src)
}
func (m *GetManualStockResp) XXX_Size() int {
	return xxx_messageInfo_GetManualStockResp.Size(m)
}
func (m *GetManualStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetManualStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetManualStockResp proto.InternalMessageInfo

func (m *GetManualStockResp) GetCurrentStock() *ManualStockConf {
	if m != nil {
		return m.CurrentStock
	}
	return nil
}

func (m *GetManualStockResp) GetFutureSotck() *ManualStockConf {
	if m != nil {
		return m.FutureSotck
	}
	return nil
}

// 概率配置
type ChanceConf struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ItemType             uint32   `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemName             string   `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemId               string   `protobuf:"bytes,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	StockPrice           uint32   `protobuf:"varint,5,opt,name=stock_price,json=stockPrice,proto3" json:"stock_price,omitempty"`
	DayCount             uint32   `protobuf:"varint,6,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	Chance               uint32   `protobuf:"varint,7,opt,name=chance,proto3" json:"chance,omitempty"`
	StockId              uint32   `protobuf:"varint,8,opt,name=stock_id,json=stockId,proto3" json:"stock_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChanceConf) Reset()         { *m = ChanceConf{} }
func (m *ChanceConf) String() string { return proto.CompactTextString(m) }
func (*ChanceConf) ProtoMessage()    {}
func (*ChanceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{12}
}
func (m *ChanceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChanceConf.Unmarshal(m, b)
}
func (m *ChanceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChanceConf.Marshal(b, m, deterministic)
}
func (dst *ChanceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChanceConf.Merge(dst, src)
}
func (m *ChanceConf) XXX_Size() int {
	return xxx_messageInfo_ChanceConf.Size(m)
}
func (m *ChanceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ChanceConf.DiscardUnknown(m)
}

var xxx_messageInfo_ChanceConf proto.InternalMessageInfo

func (m *ChanceConf) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChanceConf) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ChanceConf) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *ChanceConf) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *ChanceConf) GetStockPrice() uint32 {
	if m != nil {
		return m.StockPrice
	}
	return 0
}

func (m *ChanceConf) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *ChanceConf) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

func (m *ChanceConf) GetStockId() uint32 {
	if m != nil {
		return m.StockId
	}
	return 0
}

type GetChanceStockReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChanceStockReq) Reset()         { *m = GetChanceStockReq{} }
func (m *GetChanceStockReq) String() string { return proto.CompactTextString(m) }
func (*GetChanceStockReq) ProtoMessage()    {}
func (*GetChanceStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{13}
}
func (m *GetChanceStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanceStockReq.Unmarshal(m, b)
}
func (m *GetChanceStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanceStockReq.Marshal(b, m, deterministic)
}
func (dst *GetChanceStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanceStockReq.Merge(dst, src)
}
func (m *GetChanceStockReq) XXX_Size() int {
	return xxx_messageInfo_GetChanceStockReq.Size(m)
}
func (m *GetChanceStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanceStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanceStockReq proto.InternalMessageInfo

type AllChanceStock struct {
	TopConf              *ChanceConf   `protobuf:"bytes,1,opt,name=top_conf,json=topConf,proto3" json:"top_conf,omitempty"`
	GuaranteeConf        *ChanceConf   `protobuf:"bytes,2,opt,name=guarantee_conf,json=guaranteeConf,proto3" json:"guarantee_conf,omitempty"`
	UnstableList         []*ChanceConf `protobuf:"bytes,3,rep,name=unstable_list,json=unstableList,proto3" json:"unstable_list,omitempty"`
	EffectTime           uint32        `protobuf:"varint,4,opt,name=effect_time,json=effectTime,proto3" json:"effect_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AllChanceStock) Reset()         { *m = AllChanceStock{} }
func (m *AllChanceStock) String() string { return proto.CompactTextString(m) }
func (*AllChanceStock) ProtoMessage()    {}
func (*AllChanceStock) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{14}
}
func (m *AllChanceStock) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllChanceStock.Unmarshal(m, b)
}
func (m *AllChanceStock) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllChanceStock.Marshal(b, m, deterministic)
}
func (dst *AllChanceStock) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllChanceStock.Merge(dst, src)
}
func (m *AllChanceStock) XXX_Size() int {
	return xxx_messageInfo_AllChanceStock.Size(m)
}
func (m *AllChanceStock) XXX_DiscardUnknown() {
	xxx_messageInfo_AllChanceStock.DiscardUnknown(m)
}

var xxx_messageInfo_AllChanceStock proto.InternalMessageInfo

func (m *AllChanceStock) GetTopConf() *ChanceConf {
	if m != nil {
		return m.TopConf
	}
	return nil
}

func (m *AllChanceStock) GetGuaranteeConf() *ChanceConf {
	if m != nil {
		return m.GuaranteeConf
	}
	return nil
}

func (m *AllChanceStock) GetUnstableList() []*ChanceConf {
	if m != nil {
		return m.UnstableList
	}
	return nil
}

func (m *AllChanceStock) GetEffectTime() uint32 {
	if m != nil {
		return m.EffectTime
	}
	return 0
}

type GetChanceStockResp struct {
	CurrentStock         *AllChanceStock `protobuf:"bytes,1,opt,name=current_stock,json=currentStock,proto3" json:"current_stock,omitempty"`
	FutureSotck          *AllChanceStock `protobuf:"bytes,2,opt,name=future_sotck,json=futureSotck,proto3" json:"future_sotck,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetChanceStockResp) Reset()         { *m = GetChanceStockResp{} }
func (m *GetChanceStockResp) String() string { return proto.CompactTextString(m) }
func (*GetChanceStockResp) ProtoMessage()    {}
func (*GetChanceStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{15}
}
func (m *GetChanceStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanceStockResp.Unmarshal(m, b)
}
func (m *GetChanceStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanceStockResp.Marshal(b, m, deterministic)
}
func (dst *GetChanceStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanceStockResp.Merge(dst, src)
}
func (m *GetChanceStockResp) XXX_Size() int {
	return xxx_messageInfo_GetChanceStockResp.Size(m)
}
func (m *GetChanceStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanceStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanceStockResp proto.InternalMessageInfo

func (m *GetChanceStockResp) GetCurrentStock() *AllChanceStock {
	if m != nil {
		return m.CurrentStock
	}
	return nil
}

func (m *GetChanceStockResp) GetFutureSotck() *AllChanceStock {
	if m != nil {
		return m.FutureSotck
	}
	return nil
}

type UpdateChanceStockReq struct {
	Stock                *AllChanceStock `protobuf:"bytes,1,opt,name=stock,proto3" json:"stock,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateChanceStockReq) Reset()         { *m = UpdateChanceStockReq{} }
func (m *UpdateChanceStockReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChanceStockReq) ProtoMessage()    {}
func (*UpdateChanceStockReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{16}
}
func (m *UpdateChanceStockReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChanceStockReq.Unmarshal(m, b)
}
func (m *UpdateChanceStockReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChanceStockReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChanceStockReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChanceStockReq.Merge(dst, src)
}
func (m *UpdateChanceStockReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChanceStockReq.Size(m)
}
func (m *UpdateChanceStockReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChanceStockReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChanceStockReq proto.InternalMessageInfo

func (m *UpdateChanceStockReq) GetStock() *AllChanceStock {
	if m != nil {
		return m.Stock
	}
	return nil
}

type UpdateChanceStockResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChanceStockResp) Reset()         { *m = UpdateChanceStockResp{} }
func (m *UpdateChanceStockResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChanceStockResp) ProtoMessage()    {}
func (*UpdateChanceStockResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{17}
}
func (m *UpdateChanceStockResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChanceStockResp.Unmarshal(m, b)
}
func (m *UpdateChanceStockResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChanceStockResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChanceStockResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChanceStockResp.Merge(dst, src)
}
func (m *UpdateChanceStockResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChanceStockResp.Size(m)
}
func (m *UpdateChanceStockResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChanceStockResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChanceStockResp proto.InternalMessageInfo

type AwardInfo struct {
	AwardName            string   `protobuf:"bytes,1,opt,name=award_name,json=awardName,proto3" json:"award_name,omitempty"`
	AwardIcon            string   `protobuf:"bytes,2,opt,name=award_icon,json=awardIcon,proto3" json:"award_icon,omitempty"`
	AwardWorth           uint32   `protobuf:"varint,3,opt,name=award_worth,json=awardWorth,proto3" json:"award_worth,omitempty"`
	AwardNum             uint32   `protobuf:"varint,4,opt,name=award_num,json=awardNum,proto3" json:"award_num,omitempty"`
	AwardDays            uint32   `protobuf:"varint,5,opt,name=award_days,json=awardDays,proto3" json:"award_days,omitempty"`
	AwardType            uint32   `protobuf:"varint,6,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardInfo) Reset()         { *m = AwardInfo{} }
func (m *AwardInfo) String() string { return proto.CompactTextString(m) }
func (*AwardInfo) ProtoMessage()    {}
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{18}
}
func (m *AwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardInfo.Unmarshal(m, b)
}
func (m *AwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardInfo.Marshal(b, m, deterministic)
}
func (dst *AwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardInfo.Merge(dst, src)
}
func (m *AwardInfo) XXX_Size() int {
	return xxx_messageInfo_AwardInfo.Size(m)
}
func (m *AwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AwardInfo proto.InternalMessageInfo

func (m *AwardInfo) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *AwardInfo) GetAwardIcon() string {
	if m != nil {
		return m.AwardIcon
	}
	return ""
}

func (m *AwardInfo) GetAwardWorth() uint32 {
	if m != nil {
		return m.AwardWorth
	}
	return 0
}

func (m *AwardInfo) GetAwardNum() uint32 {
	if m != nil {
		return m.AwardNum
	}
	return 0
}

func (m *AwardInfo) GetAwardDays() uint32 {
	if m != nil {
		return m.AwardDays
	}
	return 0
}

func (m *AwardInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

// 获取中奖轮播
type GetWinningCarouselReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWinningCarouselReq) Reset()         { *m = GetWinningCarouselReq{} }
func (m *GetWinningCarouselReq) String() string { return proto.CompactTextString(m) }
func (*GetWinningCarouselReq) ProtoMessage()    {}
func (*GetWinningCarouselReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{19}
}
func (m *GetWinningCarouselReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningCarouselReq.Unmarshal(m, b)
}
func (m *GetWinningCarouselReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningCarouselReq.Marshal(b, m, deterministic)
}
func (dst *GetWinningCarouselReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningCarouselReq.Merge(dst, src)
}
func (m *GetWinningCarouselReq) XXX_Size() int {
	return xxx_messageInfo_GetWinningCarouselReq.Size(m)
}
func (m *GetWinningCarouselReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningCarouselReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningCarouselReq proto.InternalMessageInfo

func (m *GetWinningCarouselReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type WinningInfo struct {
	Nickname             string     `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AwardInfo            *AwardInfo `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	NicknameColor        string     `protobuf:"bytes,3,opt,name=nickname_color,json=nicknameColor,proto3" json:"nickname_color,omitempty"`
	AwardNameColor       string     `protobuf:"bytes,4,opt,name=award_name_color,json=awardNameColor,proto3" json:"award_name_color,omitempty"`
	Timestamp            uint32     `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Uid                  uint32     `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *WinningInfo) Reset()         { *m = WinningInfo{} }
func (m *WinningInfo) String() string { return proto.CompactTextString(m) }
func (*WinningInfo) ProtoMessage()    {}
func (*WinningInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{20}
}
func (m *WinningInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WinningInfo.Unmarshal(m, b)
}
func (m *WinningInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WinningInfo.Marshal(b, m, deterministic)
}
func (dst *WinningInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WinningInfo.Merge(dst, src)
}
func (m *WinningInfo) XXX_Size() int {
	return xxx_messageInfo_WinningInfo.Size(m)
}
func (m *WinningInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WinningInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WinningInfo proto.InternalMessageInfo

func (m *WinningInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *WinningInfo) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *WinningInfo) GetNicknameColor() string {
	if m != nil {
		return m.NicknameColor
	}
	return ""
}

func (m *WinningInfo) GetAwardNameColor() string {
	if m != nil {
		return m.AwardNameColor
	}
	return ""
}

func (m *WinningInfo) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *WinningInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWinningCarouselResp struct {
	WinningInfoList      []*WinningInfo `protobuf:"bytes,1,rep,name=winning_info_list,json=winningInfoList,proto3" json:"winning_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetWinningCarouselResp) Reset()         { *m = GetWinningCarouselResp{} }
func (m *GetWinningCarouselResp) String() string { return proto.CompactTextString(m) }
func (*GetWinningCarouselResp) ProtoMessage()    {}
func (*GetWinningCarouselResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{21}
}
func (m *GetWinningCarouselResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningCarouselResp.Unmarshal(m, b)
}
func (m *GetWinningCarouselResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningCarouselResp.Marshal(b, m, deterministic)
}
func (dst *GetWinningCarouselResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningCarouselResp.Merge(dst, src)
}
func (m *GetWinningCarouselResp) XXX_Size() int {
	return xxx_messageInfo_GetWinningCarouselResp.Size(m)
}
func (m *GetWinningCarouselResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningCarouselResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningCarouselResp proto.InternalMessageInfo

func (m *GetWinningCarouselResp) GetWinningInfoList() []*WinningInfo {
	if m != nil {
		return m.WinningInfoList
	}
	return nil
}

// 获取福利墙
type GetWelfareWallReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWelfareWallReq) Reset()         { *m = GetWelfareWallReq{} }
func (m *GetWelfareWallReq) String() string { return proto.CompactTextString(m) }
func (*GetWelfareWallReq) ProtoMessage()    {}
func (*GetWelfareWallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{22}
}
func (m *GetWelfareWallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWelfareWallReq.Unmarshal(m, b)
}
func (m *GetWelfareWallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWelfareWallReq.Marshal(b, m, deterministic)
}
func (dst *GetWelfareWallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWelfareWallReq.Merge(dst, src)
}
func (m *GetWelfareWallReq) XXX_Size() int {
	return xxx_messageInfo_GetWelfareWallReq.Size(m)
}
func (m *GetWelfareWallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWelfareWallReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWelfareWallReq proto.InternalMessageInfo

func (m *GetWelfareWallReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWelfareWallResp struct {
	IsOpen               uint32       `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	WelfareWall          []*AwardInfo `protobuf:"bytes,2,rep,name=welfare_wall,json=welfareWall,proto3" json:"welfare_wall,omitempty"`
	RemainLotteryTimes   uint32       `protobuf:"varint,3,opt,name=remain_lottery_times,json=remainLotteryTimes,proto3" json:"remain_lottery_times,omitempty"`
	OnceLotteryCost      uint32       `protobuf:"varint,4,opt,name=once_lottery_cost,json=onceLotteryCost,proto3" json:"once_lottery_cost,omitempty"`
	IncrLotteryTbean     uint32       `protobuf:"varint,5,opt,name=incr_lottery_tbean,json=incrLotteryTbean,proto3" json:"incr_lottery_tbean,omitempty"`
	IncrLotteryTimes     uint32       `protobuf:"varint,6,opt,name=incr_lottery_times,json=incrLotteryTimes,proto3" json:"incr_lottery_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWelfareWallResp) Reset()         { *m = GetWelfareWallResp{} }
func (m *GetWelfareWallResp) String() string { return proto.CompactTextString(m) }
func (*GetWelfareWallResp) ProtoMessage()    {}
func (*GetWelfareWallResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{23}
}
func (m *GetWelfareWallResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWelfareWallResp.Unmarshal(m, b)
}
func (m *GetWelfareWallResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWelfareWallResp.Marshal(b, m, deterministic)
}
func (dst *GetWelfareWallResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWelfareWallResp.Merge(dst, src)
}
func (m *GetWelfareWallResp) XXX_Size() int {
	return xxx_messageInfo_GetWelfareWallResp.Size(m)
}
func (m *GetWelfareWallResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWelfareWallResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWelfareWallResp proto.InternalMessageInfo

func (m *GetWelfareWallResp) GetIsOpen() uint32 {
	if m != nil {
		return m.IsOpen
	}
	return 0
}

func (m *GetWelfareWallResp) GetWelfareWall() []*AwardInfo {
	if m != nil {
		return m.WelfareWall
	}
	return nil
}

func (m *GetWelfareWallResp) GetRemainLotteryTimes() uint32 {
	if m != nil {
		return m.RemainLotteryTimes
	}
	return 0
}

func (m *GetWelfareWallResp) GetOnceLotteryCost() uint32 {
	if m != nil {
		return m.OnceLotteryCost
	}
	return 0
}

func (m *GetWelfareWallResp) GetIncrLotteryTbean() uint32 {
	if m != nil {
		return m.IncrLotteryTbean
	}
	return 0
}

func (m *GetWelfareWallResp) GetIncrLotteryTimes() uint32 {
	if m != nil {
		return m.IncrLotteryTimes
	}
	return 0
}

// 获取我得中奖奖励类型
type GetMyLotteryRecordAwardTypeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyLotteryRecordAwardTypeReq) Reset()         { *m = GetMyLotteryRecordAwardTypeReq{} }
func (m *GetMyLotteryRecordAwardTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordAwardTypeReq) ProtoMessage()    {}
func (*GetMyLotteryRecordAwardTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{24}
}
func (m *GetMyLotteryRecordAwardTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordAwardTypeReq.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordAwardTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordAwardTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordAwardTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordAwardTypeReq.Merge(dst, src)
}
func (m *GetMyLotteryRecordAwardTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordAwardTypeReq.Size(m)
}
func (m *GetMyLotteryRecordAwardTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordAwardTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordAwardTypeReq proto.InternalMessageInfo

func (m *GetMyLotteryRecordAwardTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMyLotteryRecordAwardTypeResp struct {
	AwardTypeList        []uint32 `protobuf:"varint,1,rep,packed,name=award_type_list,json=awardTypeList,proto3" json:"award_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyLotteryRecordAwardTypeResp) Reset()         { *m = GetMyLotteryRecordAwardTypeResp{} }
func (m *GetMyLotteryRecordAwardTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordAwardTypeResp) ProtoMessage()    {}
func (*GetMyLotteryRecordAwardTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{25}
}
func (m *GetMyLotteryRecordAwardTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordAwardTypeResp.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordAwardTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordAwardTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordAwardTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordAwardTypeResp.Merge(dst, src)
}
func (m *GetMyLotteryRecordAwardTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordAwardTypeResp.Size(m)
}
func (m *GetMyLotteryRecordAwardTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordAwardTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordAwardTypeResp proto.InternalMessageInfo

func (m *GetMyLotteryRecordAwardTypeResp) GetAwardTypeList() []uint32 {
	if m != nil {
		return m.AwardTypeList
	}
	return nil
}

// 获取我得记录
type GetMyLotteryRecordReq struct {
	AwardType            uint32   `protobuf:"varint,1,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMyLotteryRecordReq) Reset()         { *m = GetMyLotteryRecordReq{} }
func (m *GetMyLotteryRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordReq) ProtoMessage()    {}
func (*GetMyLotteryRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{26}
}
func (m *GetMyLotteryRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordReq.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordReq.Merge(dst, src)
}
func (m *GetMyLotteryRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordReq.Size(m)
}
func (m *GetMyLotteryRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordReq proto.InternalMessageInfo

func (m *GetMyLotteryRecordReq) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *GetMyLotteryRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMyLotteryRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMyLotteryRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type LotteryRecord struct {
	AwardInfo            *AwardInfo `protobuf:"bytes,1,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	Cost                 uint32     `protobuf:"varint,2,opt,name=cost,proto3" json:"cost,omitempty"`
	GotTime              uint32     `protobuf:"varint,3,opt,name=got_time,json=gotTime,proto3" json:"got_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *LotteryRecord) Reset()         { *m = LotteryRecord{} }
func (m *LotteryRecord) String() string { return proto.CompactTextString(m) }
func (*LotteryRecord) ProtoMessage()    {}
func (*LotteryRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{27}
}
func (m *LotteryRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryRecord.Unmarshal(m, b)
}
func (m *LotteryRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryRecord.Marshal(b, m, deterministic)
}
func (dst *LotteryRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryRecord.Merge(dst, src)
}
func (m *LotteryRecord) XXX_Size() int {
	return xxx_messageInfo_LotteryRecord.Size(m)
}
func (m *LotteryRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryRecord.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryRecord proto.InternalMessageInfo

func (m *LotteryRecord) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *LotteryRecord) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

func (m *LotteryRecord) GetGotTime() uint32 {
	if m != nil {
		return m.GotTime
	}
	return 0
}

type GetMyLotteryRecordResp struct {
	LotteryRecordList    []*LotteryRecord `protobuf:"bytes,1,rep,name=lottery_record_list,json=lotteryRecordList,proto3" json:"lottery_record_list,omitempty"`
	Offset               uint32           `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMyLotteryRecordResp) Reset()         { *m = GetMyLotteryRecordResp{} }
func (m *GetMyLotteryRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetMyLotteryRecordResp) ProtoMessage()    {}
func (*GetMyLotteryRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{28}
}
func (m *GetMyLotteryRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyLotteryRecordResp.Unmarshal(m, b)
}
func (m *GetMyLotteryRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyLotteryRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetMyLotteryRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyLotteryRecordResp.Merge(dst, src)
}
func (m *GetMyLotteryRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetMyLotteryRecordResp.Size(m)
}
func (m *GetMyLotteryRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyLotteryRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyLotteryRecordResp proto.InternalMessageInfo

func (m *GetMyLotteryRecordResp) GetLotteryRecordList() []*LotteryRecord {
	if m != nil {
		return m.LotteryRecordList
	}
	return nil
}

func (m *GetMyLotteryRecordResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

// 抽奖接口
type LotteryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Times                uint32   `protobuf:"varint,2,opt,name=times,proto3" json:"times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LotteryReq) Reset()         { *m = LotteryReq{} }
func (m *LotteryReq) String() string { return proto.CompactTextString(m) }
func (*LotteryReq) ProtoMessage()    {}
func (*LotteryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{29}
}
func (m *LotteryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryReq.Unmarshal(m, b)
}
func (m *LotteryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryReq.Marshal(b, m, deterministic)
}
func (dst *LotteryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryReq.Merge(dst, src)
}
func (m *LotteryReq) XXX_Size() int {
	return xxx_messageInfo_LotteryReq.Size(m)
}
func (m *LotteryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryReq.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryReq proto.InternalMessageInfo

func (m *LotteryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LotteryReq) GetTimes() uint32 {
	if m != nil {
		return m.Times
	}
	return 0
}

type LotteryResp struct {
	WinningType          uint32     `protobuf:"varint,1,opt,name=winning_type,json=winningType,proto3" json:"winning_type,omitempty"`
	Text                 string     `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	AwardInfo            *AwardInfo `protobuf:"bytes,3,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	ResourceUrl          string     `protobuf:"bytes,4,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *LotteryResp) Reset()         { *m = LotteryResp{} }
func (m *LotteryResp) String() string { return proto.CompactTextString(m) }
func (*LotteryResp) ProtoMessage()    {}
func (*LotteryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{30}
}
func (m *LotteryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryResp.Unmarshal(m, b)
}
func (m *LotteryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryResp.Marshal(b, m, deterministic)
}
func (dst *LotteryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryResp.Merge(dst, src)
}
func (m *LotteryResp) XXX_Size() int {
	return xxx_messageInfo_LotteryResp.Size(m)
}
func (m *LotteryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryResp.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryResp proto.InternalMessageInfo

func (m *LotteryResp) GetWinningType() uint32 {
	if m != nil {
		return m.WinningType
	}
	return 0
}

func (m *LotteryResp) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *LotteryResp) GetAwardInfo() *AwardInfo {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *LotteryResp) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

type AddUserConsumeValReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Val                  uint32   `protobuf:"varint,2,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserConsumeValReq) Reset()         { *m = AddUserConsumeValReq{} }
func (m *AddUserConsumeValReq) String() string { return proto.CompactTextString(m) }
func (*AddUserConsumeValReq) ProtoMessage()    {}
func (*AddUserConsumeValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{31}
}
func (m *AddUserConsumeValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserConsumeValReq.Unmarshal(m, b)
}
func (m *AddUserConsumeValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserConsumeValReq.Marshal(b, m, deterministic)
}
func (dst *AddUserConsumeValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserConsumeValReq.Merge(dst, src)
}
func (m *AddUserConsumeValReq) XXX_Size() int {
	return xxx_messageInfo_AddUserConsumeValReq.Size(m)
}
func (m *AddUserConsumeValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserConsumeValReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserConsumeValReq proto.InternalMessageInfo

func (m *AddUserConsumeValReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserConsumeValReq) GetVal() uint32 {
	if m != nil {
		return m.Val
	}
	return 0
}

type AddUserConsumeValResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddUserConsumeValResp) Reset()         { *m = AddUserConsumeValResp{} }
func (m *AddUserConsumeValResp) String() string { return proto.CompactTextString(m) }
func (*AddUserConsumeValResp) ProtoMessage()    {}
func (*AddUserConsumeValResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{32}
}
func (m *AddUserConsumeValResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddUserConsumeValResp.Unmarshal(m, b)
}
func (m *AddUserConsumeValResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddUserConsumeValResp.Marshal(b, m, deterministic)
}
func (dst *AddUserConsumeValResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddUserConsumeValResp.Merge(dst, src)
}
func (m *AddUserConsumeValResp) XXX_Size() int {
	return xxx_messageInfo_AddUserConsumeValResp.Size(m)
}
func (m *AddUserConsumeValResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddUserConsumeValResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddUserConsumeValResp proto.InternalMessageInfo

type HourReportReq struct {
	ReportTs             uint32   `protobuf:"varint,1,opt,name=report_ts,json=reportTs,proto3" json:"report_ts,omitempty"`
	ReportType           uint32   `protobuf:"varint,2,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HourReportReq) Reset()         { *m = HourReportReq{} }
func (m *HourReportReq) String() string { return proto.CompactTextString(m) }
func (*HourReportReq) ProtoMessage()    {}
func (*HourReportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{33}
}
func (m *HourReportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HourReportReq.Unmarshal(m, b)
}
func (m *HourReportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HourReportReq.Marshal(b, m, deterministic)
}
func (dst *HourReportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HourReportReq.Merge(dst, src)
}
func (m *HourReportReq) XXX_Size() int {
	return xxx_messageInfo_HourReportReq.Size(m)
}
func (m *HourReportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HourReportReq.DiscardUnknown(m)
}

var xxx_messageInfo_HourReportReq proto.InternalMessageInfo

func (m *HourReportReq) GetReportTs() uint32 {
	if m != nil {
		return m.ReportTs
	}
	return 0
}

func (m *HourReportReq) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

type HourReportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HourReportResp) Reset()         { *m = HourReportResp{} }
func (m *HourReportResp) String() string { return proto.CompactTextString(m) }
func (*HourReportResp) ProtoMessage()    {}
func (*HourReportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{34}
}
func (m *HourReportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HourReportResp.Unmarshal(m, b)
}
func (m *HourReportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HourReportResp.Marshal(b, m, deterministic)
}
func (dst *HourReportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HourReportResp.Merge(dst, src)
}
func (m *HourReportResp) XXX_Size() int {
	return xxx_messageInfo_HourReportResp.Size(m)
}
func (m *HourReportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HourReportResp.DiscardUnknown(m)
}

var xxx_messageInfo_HourReportResp proto.InternalMessageInfo

type ReconcileReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReconcileReq) Reset()         { *m = ReconcileReq{} }
func (m *ReconcileReq) String() string { return proto.CompactTextString(m) }
func (*ReconcileReq) ProtoMessage()    {}
func (*ReconcileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{35}
}
func (m *ReconcileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReconcileReq.Unmarshal(m, b)
}
func (m *ReconcileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReconcileReq.Marshal(b, m, deterministic)
}
func (dst *ReconcileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReconcileReq.Merge(dst, src)
}
func (m *ReconcileReq) XXX_Size() int {
	return xxx_messageInfo_ReconcileReq.Size(m)
}
func (m *ReconcileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReconcileReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReconcileReq proto.InternalMessageInfo

func (m *ReconcileReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ReconcileReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ReconcileDataItem struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	ItemId               string   `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemCnt              uint32   `protobuf:"varint,4,opt,name=item_cnt,json=itemCnt,proto3" json:"item_cnt,omitempty"`
	FameCost             uint32   `protobuf:"varint,5,opt,name=fame_cost,json=fameCost,proto3" json:"fame_cost,omitempty"`
	PeopleCnt            uint32   `protobuf:"varint,6,opt,name=people_cnt,json=peopleCnt,proto3" json:"people_cnt,omitempty"`
	SumWorth             uint32   `protobuf:"varint,7,opt,name=sum_worth,json=sumWorth,proto3" json:"sum_worth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReconcileDataItem) Reset()         { *m = ReconcileDataItem{} }
func (m *ReconcileDataItem) String() string { return proto.CompactTextString(m) }
func (*ReconcileDataItem) ProtoMessage()    {}
func (*ReconcileDataItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{36}
}
func (m *ReconcileDataItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReconcileDataItem.Unmarshal(m, b)
}
func (m *ReconcileDataItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReconcileDataItem.Marshal(b, m, deterministic)
}
func (dst *ReconcileDataItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReconcileDataItem.Merge(dst, src)
}
func (m *ReconcileDataItem) XXX_Size() int {
	return xxx_messageInfo_ReconcileDataItem.Size(m)
}
func (m *ReconcileDataItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ReconcileDataItem.DiscardUnknown(m)
}

var xxx_messageInfo_ReconcileDataItem proto.InternalMessageInfo

func (m *ReconcileDataItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *ReconcileDataItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *ReconcileDataItem) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *ReconcileDataItem) GetItemCnt() uint32 {
	if m != nil {
		return m.ItemCnt
	}
	return 0
}

func (m *ReconcileDataItem) GetFameCost() uint32 {
	if m != nil {
		return m.FameCost
	}
	return 0
}

func (m *ReconcileDataItem) GetPeopleCnt() uint32 {
	if m != nil {
		return m.PeopleCnt
	}
	return 0
}

func (m *ReconcileDataItem) GetSumWorth() uint32 {
	if m != nil {
		return m.SumWorth
	}
	return 0
}

type ReconcileResp struct {
	DataList             []*ReconcileDataItem `protobuf:"bytes,1,rep,name=data_list,json=dataList,proto3" json:"data_list,omitempty"`
	PeopleCnt            uint32               `protobuf:"varint,2,opt,name=people_cnt,json=peopleCnt,proto3" json:"people_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ReconcileResp) Reset()         { *m = ReconcileResp{} }
func (m *ReconcileResp) String() string { return proto.CompactTextString(m) }
func (*ReconcileResp) ProtoMessage()    {}
func (*ReconcileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_glory_lottery_ac75e166e358ac62, []int{37}
}
func (m *ReconcileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReconcileResp.Unmarshal(m, b)
}
func (m *ReconcileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReconcileResp.Marshal(b, m, deterministic)
}
func (dst *ReconcileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReconcileResp.Merge(dst, src)
}
func (m *ReconcileResp) XXX_Size() int {
	return xxx_messageInfo_ReconcileResp.Size(m)
}
func (m *ReconcileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReconcileResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReconcileResp proto.InternalMessageInfo

func (m *ReconcileResp) GetDataList() []*ReconcileDataItem {
	if m != nil {
		return m.DataList
	}
	return nil
}

func (m *ReconcileResp) GetPeopleCnt() uint32 {
	if m != nil {
		return m.PeopleCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*StockConf)(nil), "glory_lottery.StockConf")
	proto.RegisterType((*UpdateBaseStockReq)(nil), "glory_lottery.UpdateBaseStockReq")
	proto.RegisterType((*UpdateBaseStockResp)(nil), "glory_lottery.UpdateBaseStockResp")
	proto.RegisterType((*GetBaseStockReq)(nil), "glory_lottery.GetBaseStockReq")
	proto.RegisterType((*GetBaseStockResp)(nil), "glory_lottery.GetBaseStockResp")
	proto.RegisterType((*GetCurrentStockReq)(nil), "glory_lottery.GetCurrentStockReq")
	proto.RegisterType((*GetCurrentStockResp)(nil), "glory_lottery.GetCurrentStockResp")
	proto.RegisterType((*UpdateManualStockReq)(nil), "glory_lottery.UpdateManualStockReq")
	proto.RegisterType((*UpdateManualStockResp)(nil), "glory_lottery.UpdateManualStockResp")
	proto.RegisterType((*GetManualStockReq)(nil), "glory_lottery.GetManualStockReq")
	proto.RegisterType((*ManualStockConf)(nil), "glory_lottery.ManualStockConf")
	proto.RegisterType((*GetManualStockResp)(nil), "glory_lottery.GetManualStockResp")
	proto.RegisterType((*ChanceConf)(nil), "glory_lottery.ChanceConf")
	proto.RegisterType((*GetChanceStockReq)(nil), "glory_lottery.GetChanceStockReq")
	proto.RegisterType((*AllChanceStock)(nil), "glory_lottery.AllChanceStock")
	proto.RegisterType((*GetChanceStockResp)(nil), "glory_lottery.GetChanceStockResp")
	proto.RegisterType((*UpdateChanceStockReq)(nil), "glory_lottery.UpdateChanceStockReq")
	proto.RegisterType((*UpdateChanceStockResp)(nil), "glory_lottery.UpdateChanceStockResp")
	proto.RegisterType((*AwardInfo)(nil), "glory_lottery.AwardInfo")
	proto.RegisterType((*GetWinningCarouselReq)(nil), "glory_lottery.GetWinningCarouselReq")
	proto.RegisterType((*WinningInfo)(nil), "glory_lottery.WinningInfo")
	proto.RegisterType((*GetWinningCarouselResp)(nil), "glory_lottery.GetWinningCarouselResp")
	proto.RegisterType((*GetWelfareWallReq)(nil), "glory_lottery.GetWelfareWallReq")
	proto.RegisterType((*GetWelfareWallResp)(nil), "glory_lottery.GetWelfareWallResp")
	proto.RegisterType((*GetMyLotteryRecordAwardTypeReq)(nil), "glory_lottery.GetMyLotteryRecordAwardTypeReq")
	proto.RegisterType((*GetMyLotteryRecordAwardTypeResp)(nil), "glory_lottery.GetMyLotteryRecordAwardTypeResp")
	proto.RegisterType((*GetMyLotteryRecordReq)(nil), "glory_lottery.GetMyLotteryRecordReq")
	proto.RegisterType((*LotteryRecord)(nil), "glory_lottery.LotteryRecord")
	proto.RegisterType((*GetMyLotteryRecordResp)(nil), "glory_lottery.GetMyLotteryRecordResp")
	proto.RegisterType((*LotteryReq)(nil), "glory_lottery.LotteryReq")
	proto.RegisterType((*LotteryResp)(nil), "glory_lottery.LotteryResp")
	proto.RegisterType((*AddUserConsumeValReq)(nil), "glory_lottery.AddUserConsumeValReq")
	proto.RegisterType((*AddUserConsumeValResp)(nil), "glory_lottery.AddUserConsumeValResp")
	proto.RegisterType((*HourReportReq)(nil), "glory_lottery.HourReportReq")
	proto.RegisterType((*HourReportResp)(nil), "glory_lottery.HourReportResp")
	proto.RegisterType((*ReconcileReq)(nil), "glory_lottery.ReconcileReq")
	proto.RegisterType((*ReconcileDataItem)(nil), "glory_lottery.ReconcileDataItem")
	proto.RegisterType((*ReconcileResp)(nil), "glory_lottery.ReconcileResp")
	proto.RegisterEnum("glory_lottery.LotteryItemType", LotteryItemType_name, LotteryItemType_value)
	proto.RegisterEnum("glory_lottery.StockType", StockType_name, StockType_value)
	proto.RegisterEnum("glory_lottery.RefreshType", RefreshType_name, RefreshType_value)
	proto.RegisterEnum("glory_lottery.ResetType", ResetType_name, ResetType_value)
	proto.RegisterEnum("glory_lottery.LotteryResp_WINNING_TYPE", LotteryResp_WINNING_TYPE_name, LotteryResp_WINNING_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GloryLotteryClient is the client API for GloryLottery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GloryLotteryClient interface {
	// -------------------- 运营后台协议 begin ----------------------------------
	// 增加或删除库存配置
	UpdateBaseStock(ctx context.Context, in *UpdateBaseStockReq, opts ...grpc.CallOption) (*UpdateBaseStockResp, error)
	// 获取生效中的库存配置
	GetBaseStock(ctx context.Context, in *GetBaseStockReq, opts ...grpc.CallOption) (*GetBaseStockResp, error)
	// 获取当前小时的库存配置
	GetCurrentStock(ctx context.Context, in *GetCurrentStockReq, opts ...grpc.CallOption) (*GetCurrentStockResp, error)
	// 增加或删除手动库存配置
	UpdateManualStock(ctx context.Context, in *UpdateManualStockReq, opts ...grpc.CallOption) (*UpdateManualStockResp, error)
	// 获取手动库存配置
	GetManualStock(ctx context.Context, in *GetManualStockReq, opts ...grpc.CallOption) (*GetManualStockResp, error)
	// 更新概率配置
	UpdateChanceStock(ctx context.Context, in *UpdateChanceStockReq, opts ...grpc.CallOption) (*UpdateChanceStockResp, error)
	// 获取概率配置
	GetChanceStock(ctx context.Context, in *GetChanceStockReq, opts ...grpc.CallOption) (*GetChanceStockResp, error)
	// 获取福利墙
	GetWelfareWall(ctx context.Context, in *GetWelfareWallReq, opts ...grpc.CallOption) (*GetWelfareWallResp, error)
	// 获取中将轮播
	GetWinningCarousel(ctx context.Context, in *GetWinningCarouselReq, opts ...grpc.CallOption) (*GetWinningCarouselResp, error)
	// 获取中奖记录类型
	GetMyLotteryRecordAwardType(ctx context.Context, in *GetMyLotteryRecordAwardTypeReq, opts ...grpc.CallOption) (*GetMyLotteryRecordAwardTypeResp, error)
	// 获取中奖记录
	GetMyLotteryRecord(ctx context.Context, in *GetMyLotteryRecordReq, opts ...grpc.CallOption) (*GetMyLotteryRecordResp, error)
	// 抽奖接口
	Lottery(ctx context.Context, in *LotteryReq, opts ...grpc.CallOption) (*LotteryResp, error)
	AddUserConsumeVal(ctx context.Context, in *AddUserConsumeValReq, opts ...grpc.CallOption) (*AddUserConsumeValResp, error)
	HourReport(ctx context.Context, in *HourReportReq, opts ...grpc.CallOption) (*HourReportResp, error)
	Reconcile(ctx context.Context, in *ReconcileReq, opts ...grpc.CallOption) (*ReconcileResp, error)
	GetUseFameCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetSendPkgCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
}

type gloryLotteryClient struct {
	cc *grpc.ClientConn
}

func NewGloryLotteryClient(cc *grpc.ClientConn) GloryLotteryClient {
	return &gloryLotteryClient{cc}
}

func (c *gloryLotteryClient) UpdateBaseStock(ctx context.Context, in *UpdateBaseStockReq, opts ...grpc.CallOption) (*UpdateBaseStockResp, error) {
	out := new(UpdateBaseStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/UpdateBaseStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetBaseStock(ctx context.Context, in *GetBaseStockReq, opts ...grpc.CallOption) (*GetBaseStockResp, error) {
	out := new(GetBaseStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetBaseStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetCurrentStock(ctx context.Context, in *GetCurrentStockReq, opts ...grpc.CallOption) (*GetCurrentStockResp, error) {
	out := new(GetCurrentStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetCurrentStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) UpdateManualStock(ctx context.Context, in *UpdateManualStockReq, opts ...grpc.CallOption) (*UpdateManualStockResp, error) {
	out := new(UpdateManualStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/UpdateManualStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetManualStock(ctx context.Context, in *GetManualStockReq, opts ...grpc.CallOption) (*GetManualStockResp, error) {
	out := new(GetManualStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetManualStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) UpdateChanceStock(ctx context.Context, in *UpdateChanceStockReq, opts ...grpc.CallOption) (*UpdateChanceStockResp, error) {
	out := new(UpdateChanceStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/UpdateChanceStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetChanceStock(ctx context.Context, in *GetChanceStockReq, opts ...grpc.CallOption) (*GetChanceStockResp, error) {
	out := new(GetChanceStockResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetChanceStock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetWelfareWall(ctx context.Context, in *GetWelfareWallReq, opts ...grpc.CallOption) (*GetWelfareWallResp, error) {
	out := new(GetWelfareWallResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetWelfareWall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetWinningCarousel(ctx context.Context, in *GetWinningCarouselReq, opts ...grpc.CallOption) (*GetWinningCarouselResp, error) {
	out := new(GetWinningCarouselResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetWinningCarousel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetMyLotteryRecordAwardType(ctx context.Context, in *GetMyLotteryRecordAwardTypeReq, opts ...grpc.CallOption) (*GetMyLotteryRecordAwardTypeResp, error) {
	out := new(GetMyLotteryRecordAwardTypeResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetMyLotteryRecordAwardType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetMyLotteryRecord(ctx context.Context, in *GetMyLotteryRecordReq, opts ...grpc.CallOption) (*GetMyLotteryRecordResp, error) {
	out := new(GetMyLotteryRecordResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetMyLotteryRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) Lottery(ctx context.Context, in *LotteryReq, opts ...grpc.CallOption) (*LotteryResp, error) {
	out := new(LotteryResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/Lottery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) AddUserConsumeVal(ctx context.Context, in *AddUserConsumeValReq, opts ...grpc.CallOption) (*AddUserConsumeValResp, error) {
	out := new(AddUserConsumeValResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/AddUserConsumeVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) HourReport(ctx context.Context, in *HourReportReq, opts ...grpc.CallOption) (*HourReportResp, error) {
	out := new(HourReportResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/HourReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) Reconcile(ctx context.Context, in *ReconcileReq, opts ...grpc.CallOption) (*ReconcileResp, error) {
	out := new(ReconcileResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/Reconcile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetUseFameCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetUseFameCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryLotteryClient) GetSendPkgCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/glory_lottery.GloryLottery/GetSendPkgCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GloryLotteryServer is the server API for GloryLottery service.
type GloryLotteryServer interface {
	// -------------------- 运营后台协议 begin ----------------------------------
	// 增加或删除库存配置
	UpdateBaseStock(context.Context, *UpdateBaseStockReq) (*UpdateBaseStockResp, error)
	// 获取生效中的库存配置
	GetBaseStock(context.Context, *GetBaseStockReq) (*GetBaseStockResp, error)
	// 获取当前小时的库存配置
	GetCurrentStock(context.Context, *GetCurrentStockReq) (*GetCurrentStockResp, error)
	// 增加或删除手动库存配置
	UpdateManualStock(context.Context, *UpdateManualStockReq) (*UpdateManualStockResp, error)
	// 获取手动库存配置
	GetManualStock(context.Context, *GetManualStockReq) (*GetManualStockResp, error)
	// 更新概率配置
	UpdateChanceStock(context.Context, *UpdateChanceStockReq) (*UpdateChanceStockResp, error)
	// 获取概率配置
	GetChanceStock(context.Context, *GetChanceStockReq) (*GetChanceStockResp, error)
	// 获取福利墙
	GetWelfareWall(context.Context, *GetWelfareWallReq) (*GetWelfareWallResp, error)
	// 获取中将轮播
	GetWinningCarousel(context.Context, *GetWinningCarouselReq) (*GetWinningCarouselResp, error)
	// 获取中奖记录类型
	GetMyLotteryRecordAwardType(context.Context, *GetMyLotteryRecordAwardTypeReq) (*GetMyLotteryRecordAwardTypeResp, error)
	// 获取中奖记录
	GetMyLotteryRecord(context.Context, *GetMyLotteryRecordReq) (*GetMyLotteryRecordResp, error)
	// 抽奖接口
	Lottery(context.Context, *LotteryReq) (*LotteryResp, error)
	AddUserConsumeVal(context.Context, *AddUserConsumeValReq) (*AddUserConsumeValResp, error)
	HourReport(context.Context, *HourReportReq) (*HourReportResp, error)
	Reconcile(context.Context, *ReconcileReq) (*ReconcileResp, error)
	GetUseFameCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetSendPkgCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
}

func RegisterGloryLotteryServer(s *grpc.Server, srv GloryLotteryServer) {
	s.RegisterService(&_GloryLottery_serviceDesc, srv)
}

func _GloryLottery_UpdateBaseStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBaseStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).UpdateBaseStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/UpdateBaseStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).UpdateBaseStock(ctx, req.(*UpdateBaseStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetBaseStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBaseStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetBaseStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetBaseStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetBaseStock(ctx, req.(*GetBaseStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetCurrentStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrentStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetCurrentStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetCurrentStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetCurrentStock(ctx, req.(*GetCurrentStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_UpdateManualStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateManualStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).UpdateManualStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/UpdateManualStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).UpdateManualStock(ctx, req.(*UpdateManualStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetManualStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetManualStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetManualStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetManualStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetManualStock(ctx, req.(*GetManualStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_UpdateChanceStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChanceStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).UpdateChanceStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/UpdateChanceStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).UpdateChanceStock(ctx, req.(*UpdateChanceStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetChanceStock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChanceStockReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetChanceStock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetChanceStock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetChanceStock(ctx, req.(*GetChanceStockReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetWelfareWall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWelfareWallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetWelfareWall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetWelfareWall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetWelfareWall(ctx, req.(*GetWelfareWallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetWinningCarousel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWinningCarouselReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetWinningCarousel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetWinningCarousel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetWinningCarousel(ctx, req.(*GetWinningCarouselReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetMyLotteryRecordAwardType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyLotteryRecordAwardTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetMyLotteryRecordAwardType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetMyLotteryRecordAwardType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetMyLotteryRecordAwardType(ctx, req.(*GetMyLotteryRecordAwardTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetMyLotteryRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyLotteryRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetMyLotteryRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetMyLotteryRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetMyLotteryRecord(ctx, req.(*GetMyLotteryRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_Lottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).Lottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/Lottery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).Lottery(ctx, req.(*LotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_AddUserConsumeVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserConsumeValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).AddUserConsumeVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/AddUserConsumeVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).AddUserConsumeVal(ctx, req.(*AddUserConsumeValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_HourReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HourReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).HourReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/HourReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).HourReport(ctx, req.(*HourReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_Reconcile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReconcileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).Reconcile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/Reconcile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).Reconcile(ctx, req.(*ReconcileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetUseFameCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetUseFameCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetUseFameCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetUseFameCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryLottery_GetSendPkgCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryLotteryServer).GetSendPkgCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/glory_lottery.GloryLottery/GetSendPkgCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryLotteryServer).GetSendPkgCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GloryLottery_serviceDesc = grpc.ServiceDesc{
	ServiceName: "glory_lottery.GloryLottery",
	HandlerType: (*GloryLotteryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateBaseStock",
			Handler:    _GloryLottery_UpdateBaseStock_Handler,
		},
		{
			MethodName: "GetBaseStock",
			Handler:    _GloryLottery_GetBaseStock_Handler,
		},
		{
			MethodName: "GetCurrentStock",
			Handler:    _GloryLottery_GetCurrentStock_Handler,
		},
		{
			MethodName: "UpdateManualStock",
			Handler:    _GloryLottery_UpdateManualStock_Handler,
		},
		{
			MethodName: "GetManualStock",
			Handler:    _GloryLottery_GetManualStock_Handler,
		},
		{
			MethodName: "UpdateChanceStock",
			Handler:    _GloryLottery_UpdateChanceStock_Handler,
		},
		{
			MethodName: "GetChanceStock",
			Handler:    _GloryLottery_GetChanceStock_Handler,
		},
		{
			MethodName: "GetWelfareWall",
			Handler:    _GloryLottery_GetWelfareWall_Handler,
		},
		{
			MethodName: "GetWinningCarousel",
			Handler:    _GloryLottery_GetWinningCarousel_Handler,
		},
		{
			MethodName: "GetMyLotteryRecordAwardType",
			Handler:    _GloryLottery_GetMyLotteryRecordAwardType_Handler,
		},
		{
			MethodName: "GetMyLotteryRecord",
			Handler:    _GloryLottery_GetMyLotteryRecord_Handler,
		},
		{
			MethodName: "Lottery",
			Handler:    _GloryLottery_Lottery_Handler,
		},
		{
			MethodName: "AddUserConsumeVal",
			Handler:    _GloryLottery_AddUserConsumeVal_Handler,
		},
		{
			MethodName: "HourReport",
			Handler:    _GloryLottery_HourReport_Handler,
		},
		{
			MethodName: "Reconcile",
			Handler:    _GloryLottery_Reconcile_Handler,
		},
		{
			MethodName: "GetUseFameCount",
			Handler:    _GloryLottery_GetUseFameCount_Handler,
		},
		{
			MethodName: "GetSendPkgCount",
			Handler:    _GloryLottery_GetSendPkgCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "glory-lottery/glory-lottery.proto",
}

func init() {
	proto.RegisterFile("glory-lottery/glory-lottery.proto", fileDescriptor_glory_lottery_ac75e166e358ac62)
}

var fileDescriptor_glory_lottery_ac75e166e358ac62 = []byte{
	// 2162 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x59, 0x4f, 0x6f, 0xdb, 0xc8,
	0x15, 0xb7, 0x64, 0xc7, 0x96, 0x9e, 0x2c, 0x9b, 0x9e, 0x38, 0x8e, 0x2c, 0xc7, 0xeb, 0x98, 0xdd,
	0x2d, 0xd2, 0x60, 0xe3, 0xb4, 0xde, 0x2d, 0x0a, 0xb4, 0x68, 0x11, 0x59, 0xa6, 0x6d, 0xd6, 0xb2,
	0xe4, 0xa5, 0xe4, 0x75, 0x1d, 0x14, 0xe0, 0x32, 0xd4, 0x48, 0x21, 0x42, 0x91, 0x5c, 0xce, 0x28,
	0xae, 0x2f, 0x0b, 0xf4, 0xd2, 0x6b, 0xef, 0x45, 0x8f, 0xbd, 0xf4, 0x83, 0xf4, 0xda, 0x0f, 0xd0,
	0x53, 0x81, 0x9e, 0xfa, 0x29, 0x5a, 0xcc, 0x1f, 0x4a, 0x1c, 0x8a, 0xb6, 0x93, 0x3d, 0x14, 0xbd,
	0x69, 0x7e, 0xef, 0xcd, 0x8f, 0x6f, 0xde, 0xbc, 0xf7, 0xe6, 0xcd, 0x08, 0x76, 0x87, 0x7e, 0x18,
	0xdf, 0xbc, 0xf0, 0x43, 0x4a, 0x71, 0x7c, 0xf3, 0x52, 0x19, 0xed, 0x45, 0x71, 0x48, 0x43, 0x54,
	0xe5, 0xa0, 0x2d, 0xc1, 0xfa, 0x4e, 0x8c, 0xdd, 0x30, 0x70, 0x3d, 0x1f, 0xbf, 0x78, 0xbf, 0xff,
	0x32, 0x3d, 0x10, 0xfa, 0xfa, 0x1f, 0x8b, 0x50, 0xee, 0xd2, 0xd0, 0x7d, 0xd7, 0x0c, 0x83, 0x01,
	0x5a, 0x81, 0xa2, 0xd7, 0xaf, 0x15, 0x9e, 0x16, 0x9e, 0x55, 0xad, 0xa2, 0xd7, 0x47, 0x5b, 0x50,
	0xf6, 0x28, 0x1e, 0xd9, 0xf4, 0x26, 0xc2, 0xb5, 0x22, 0x87, 0x4b, 0x0c, 0xe8, 0xdd, 0x44, 0x78,
	0x22, 0x0c, 0x9c, 0x11, 0xae, 0xcd, 0x3f, 0x2d, 0x3c, 0x2b, 0x0b, 0x61, 0xdb, 0x19, 0x61, 0xf4,
	0x18, 0x96, 0xb8, 0xd0, 0xeb, 0xd7, 0x16, 0xb8, 0x68, 0x91, 0x0d, 0xcd, 0x3e, 0xda, 0x81, 0x0a,
	0x61, 0xdf, 0xb3, 0xa3, 0xd8, 0x73, 0x71, 0xed, 0x01, 0x27, 0x05, 0x0e, 0x9d, 0x33, 0x04, 0x6d,
	0x03, 0xf0, 0x99, 0x6e, 0x38, 0x0e, 0x68, 0x6d, 0x91, 0xcb, 0xf9, 0x87, 0x9a, 0x0c, 0x60, 0x62,
	0x31, 0x9f, 0xdb, 0xb4, 0x24, 0xc4, 0x1c, 0xe1, 0x46, 0xed, 0xc2, 0x72, 0x8c, 0x07, 0x31, 0x26,
	0x6f, 0x85, 0x42, 0x89, 0x2b, 0x54, 0x24, 0xc6, 0x55, 0xb6, 0x01, 0x62, 0x4c, 0x30, 0x15, 0x0a,
	0x65, 0xc1, 0xc0, 0x11, 0x26, 0xd6, 0x0f, 0x00, 0x5d, 0x44, 0x7d, 0x87, 0xe2, 0x03, 0x87, 0x60,
	0xee, 0x1a, 0x0b, 0x7f, 0x8b, 0x3e, 0x87, 0x05, 0xdf, 0x23, 0xb4, 0x56, 0x78, 0x3a, 0xff, 0xac,
	0xb2, 0x5f, 0xdb, 0x53, 0xdc, 0xbc, 0x37, 0xf1, 0xa0, 0xc5, 0xb5, 0xf4, 0x47, 0xf0, 0x70, 0x86,
	0x83, 0x44, 0xfa, 0x1a, 0xac, 0x1e, 0x63, 0x9a, 0xe6, 0xd5, 0x5f, 0x81, 0xa6, 0x42, 0x24, 0xfa,
	0xc8, 0x6f, 0xad, 0x03, 0x3a, 0xc6, 0xb4, 0x39, 0x8e, 0x63, 0x1c, 0xd0, 0x09, 0xef, 0x08, 0x1e,
	0xce, 0xa0, 0x1f, 0x4b, 0x8d, 0x9e, 0x81, 0xe6, 0x3b, 0x84, 0xda, 0x63, 0xbe, 0x16, 0x9b, 0x7a,
	0xa3, 0x24, 0x0a, 0x56, 0x18, 0x2e, 0x96, 0xd8, 0xf3, 0x46, 0x58, 0xff, 0x43, 0x01, 0xd6, 0xc5,
	0xf0, 0xcc, 0x09, 0xc6, 0x8e, 0xff, 0xfd, 0xfc, 0xc6, 0x76, 0x0f, 0x0f, 0x06, 0xd8, 0xa5, 0x36,
	0xa1, 0x4e, 0x4c, 0xe5, 0xc7, 0x2a, 0x02, 0xeb, 0x32, 0x88, 0xed, 0x9e, 0x54, 0xc1, 0x41, 0x9f,
	0x87, 0x5d, 0xd5, 0x2a, 0x0b, 0xc4, 0x08, 0xfa, 0xfa, 0x63, 0x78, 0x94, 0x63, 0x07, 0x89, 0xf4,
	0x87, 0xb0, 0x76, 0x8c, 0xa9, 0x6a, 0x9d, 0xfe, 0xfb, 0x02, 0xac, 0xa6, 0x20, 0x9e, 0x03, 0xff,
	0x6b, 0x8b, 0xff, 0x5c, 0xe0, 0x1b, 0x98, 0xb1, 0x17, 0x35, 0xa1, 0xea, 0x8a, 0xdd, 0xb3, 0x79,
	0x74, 0xf3, 0xac, 0xac, 0xec, 0x7f, 0x92, 0xb1, 0x27, 0x63, 0xbd, 0xb5, 0xec, 0xa6, 0xb6, 0x1c,
	0x35, 0x60, 0x79, 0x30, 0xa6, 0xe3, 0x18, 0xdb, 0x24, 0xa4, 0xee, 0x3b, 0x6e, 0xdd, 0xfd, 0x1c,
	0x15, 0x31, 0xa7, 0xcb, 0xa6, 0xe8, 0xff, 0x2c, 0x00, 0x34, 0xdf, 0x3a, 0x81, 0x8b, 0xff, 0x6f,
	0x2a, 0xc4, 0x16, 0x94, 0xfb, 0xce, 0x8d, 0x52, 0x20, 0x4a, 0x7d, 0xe7, 0x46, 0xd4, 0x87, 0x0d,
	0x58, 0x74, 0xb9, 0xb9, 0xb2, 0x36, 0xc8, 0x11, 0xda, 0x84, 0x92, 0x60, 0xf5, 0xfa, 0xb2, 0x28,
	0x2c, 0xf1, 0xb1, 0xd9, 0x97, 0xa1, 0x21, 0x16, 0x39, 0x09, 0x8d, 0x7f, 0x17, 0x60, 0xa5, 0xe1,
	0xfb, 0x29, 0x14, 0x7d, 0x09, 0x25, 0x1a, 0x46, 0xb6, 0x1b, 0x06, 0x03, 0xb9, 0x1b, 0x9b, 0x19,
	0x4f, 0x4e, 0x1d, 0x65, 0x2d, 0xd1, 0x30, 0xe2, 0x1e, 0x7b, 0x05, 0x2b, 0xc3, 0xb1, 0x13, 0x3b,
	0x01, 0xc5, 0x58, 0xcc, 0x2d, 0xde, 0x37, 0xb7, 0x3a, 0x99, 0xc0, 0x19, 0x7e, 0x05, 0xd5, 0x71,
	0x40, 0xa8, 0xf3, 0xc6, 0xc7, 0x36, 0x0f, 0xcd, 0x79, 0x1e, 0x9a, 0x77, 0x10, 0x2c, 0x27, 0xfa,
	0x2d, 0x16, 0xa3, 0x3b, 0x20, 0xe3, 0x51, 0x64, 0xf0, 0x82, 0x70, 0xa8, 0x80, 0x78, 0xf6, 0xfe,
	0x49, 0x84, 0xa0, 0xe2, 0x01, 0x12, 0xa1, 0x83, 0xfc, 0x10, 0xdc, 0xce, 0x7c, 0x57, 0xf5, 0x52,
	0x26, 0x02, 0x5f, 0xe5, 0x46, 0xe0, 0x3d, 0x14, 0x4a, 0x00, 0x9e, 0x26, 0x95, 0x45, 0xdd, 0x20,
	0xf4, 0x05, 0x3c, 0xf8, 0x08, 0xab, 0x84, 0xee, 0xb4, 0x3c, 0x64, 0xd6, 0xaa, 0xff, 0xad, 0x00,
	0xe5, 0xc6, 0xb5, 0x13, 0xf7, 0xcd, 0x60, 0x10, 0xb2, 0x94, 0x75, 0xd8, 0x40, 0x44, 0x6e, 0x81,
	0x87, 0x67, 0x99, 0x23, 0x3c, 0x74, 0x27, 0x62, 0xcf, 0x0d, 0x03, 0xbe, 0xa4, 0x44, 0x6c, 0xba,
	0x61, 0xc0, 0xfc, 0x2d, 0xc4, 0xd7, 0x61, 0x4c, 0xdf, 0xca, 0x8c, 0x17, 0x33, 0x2e, 0x19, 0xc2,
	0x02, 0x58, 0xd2, 0x8f, 0x47, 0x72, 0x3b, 0x4a, 0x82, 0x7d, 0x3c, 0x9a, 0x92, 0xf7, 0x9d, 0x1b,
	0x22, 0xa3, 0x5f, 0xa8, 0x1f, 0x3a, 0x37, 0x64, 0x2a, 0xe6, 0x19, 0xb7, 0x98, 0x12, 0xf3, 0xd3,
	0xeb, 0x47, 0xf0, 0xe8, 0x18, 0xd3, 0x4b, 0x2f, 0x08, 0xbc, 0x60, 0xd8, 0x74, 0xe2, 0x70, 0x4c,
	0xb0, 0xcf, 0xdc, 0xa5, 0xc1, 0xfc, 0x78, 0x92, 0xb9, 0xec, 0xa7, 0xfe, 0xaf, 0x02, 0x54, 0xa4,
	0x22, 0x5f, 0x74, 0x1d, 0x4a, 0x81, 0xe7, 0xbe, 0x4b, 0x2d, 0x79, 0x32, 0x46, 0x3f, 0x9b, 0xac,
	0x38, 0x18, 0x84, 0x72, 0x13, 0xb3, 0xa5, 0x71, 0xe2, 0xbe, 0xc4, 0x17, 0x8c, 0xf4, 0x33, 0x58,
	0x49, 0x48, 0x6c, 0x37, 0xf4, 0xc3, 0x58, 0xd6, 0x81, 0x6a, 0x82, 0x36, 0x19, 0xc8, 0x4e, 0x9a,
	0xa9, 0xc3, 0xa5, 0xa2, 0xa8, 0x0a, 0x2b, 0x13, 0xb7, 0x0b, 0xcd, 0x27, 0x50, 0x66, 0x51, 0x4c,
	0xa8, 0x33, 0x8a, 0x12, 0xef, 0x4c, 0x80, 0x64, 0x95, 0x8b, 0xd3, 0x55, 0x7e, 0x03, 0x1b, 0x79,
	0x0e, 0x21, 0x11, 0x3a, 0x82, 0xb5, 0x6b, 0x01, 0xf3, 0x55, 0xd9, 0xa9, 0xaa, 0x5f, 0xcf, 0x2c,
	0x2d, 0xe5, 0x26, 0x6b, 0xf5, 0x7a, 0x3a, 0x60, 0xe9, 0xa5, 0x7f, 0xc6, 0xcb, 0xc7, 0x25, 0xf6,
	0x07, 0x4e, 0x8c, 0x2f, 0x1d, 0xff, 0x16, 0x77, 0xff, 0xa5, 0xc8, 0x93, 0x4c, 0xd1, 0x23, 0x11,
	0x2f, 0x83, 0xc4, 0x0e, 0x23, 0x1c, 0x48, 0xe5, 0x45, 0x8f, 0x74, 0x22, 0x1c, 0xa0, 0x5f, 0xc0,
	0xf2, 0xb5, 0xd0, 0xb5, 0xaf, 0x1d, 0xdf, 0xaf, 0x15, 0x73, 0xcf, 0xa3, 0xa9, 0xd3, 0x2b, 0xd7,
	0x53, 0x66, 0xf4, 0x63, 0x58, 0x8f, 0xf1, 0xc8, 0xf1, 0x82, 0x44, 0x91, 0xa7, 0x3e, 0x91, 0xb1,
	0x88, 0x84, 0xac, 0x25, 0x44, 0xac, 0x04, 0x10, 0xf4, 0x1c, 0xd6, 0xc2, 0xc0, 0xc5, 0x13, 0x7d,
	0x37, 0x24, 0x54, 0xc6, 0xe6, 0x2a, 0x13, 0x48, 0xe5, 0x66, 0x48, 0x28, 0xfa, 0x1c, 0x90, 0x17,
	0xb8, 0xf1, 0x94, 0xfb, 0x0d, 0x76, 0x02, 0xb9, 0x19, 0x1a, 0x93, 0x24, 0xcc, 0x0c, 0x9f, 0xd5,
	0xe6, 0x96, 0x2c, 0xce, 0x6a, 0x33, 0x5c, 0xdf, 0x87, 0x4f, 0xd8, 0x69, 0x78, 0x23, 0x41, 0x0b,
	0xbb, 0x61, 0xdc, 0x6f, 0x24, 0xf1, 0x9d, 0xef, 0x5a, 0x13, 0x76, 0xee, 0x9c, 0x43, 0x22, 0xf4,
	0x43, 0x58, 0x9d, 0xa6, 0xcd, 0x74, 0xab, 0xab, 0x56, 0x75, 0x92, 0x3b, 0x7c, 0x33, 0xdf, 0xf3,
	0xfc, 0xc9, 0x50, 0xb1, 0xaf, 0xaa, 0x79, 0x57, 0xc8, 0xe4, 0x1d, 0x3b, 0x76, 0xc2, 0xc1, 0x80,
	0xe0, 0xa4, 0x03, 0x90, 0x23, 0xb4, 0x0e, 0x0f, 0x7c, 0x6f, 0xe4, 0x51, 0xe9, 0x79, 0x31, 0x48,
	0x96, 0xb0, 0x30, 0x5d, 0xc2, 0x35, 0x54, 0x95, 0x4f, 0x66, 0x32, 0xae, 0xf0, 0xe1, 0x19, 0x87,
	0x60, 0x81, 0xef, 0x9d, 0xb0, 0x83, 0xff, 0x66, 0x87, 0xdf, 0x30, 0x94, 0xe5, 0x5f, 0x18, 0xb2,
	0x34, 0x0c, 0x45, 0xed, 0xff, 0x8e, 0xe7, 0xc7, 0xcc, 0x82, 0x49, 0x84, 0x5a, 0xf0, 0x30, 0xd9,
	0xb2, 0x98, 0xa3, 0xe9, 0x0c, 0x79, 0x92, 0x31, 0x45, 0x9d, 0xbe, 0xe6, 0xa7, 0x87, 0xfc, 0x10,
	0xba, 0xc5, 0x41, 0xfa, 0x97, 0x00, 0x93, 0xb9, 0x39, 0x7b, 0xcb, 0x1c, 0x28, 0x02, 0x46, 0x4c,
	0x13, 0x03, 0xfd, 0xaf, 0x45, 0xa8, 0x4c, 0xa6, 0x91, 0x88, 0xb5, 0x61, 0x49, 0x2e, 0xa7, 0xf6,
	0xa7, 0x22, 0x31, 0xbe, 0x43, 0x08, 0x16, 0x28, 0xfe, 0x1d, 0x95, 0xe5, 0x9a, 0xff, 0xce, 0x38,
	0x79, 0xfe, 0xc3, 0x9d, 0xcc, 0xaf, 0x19, 0x24, 0x1c, 0xc7, 0x2e, 0xb6, 0xc7, 0xb1, 0x2f, 0x6b,
	0x55, 0x25, 0xc1, 0x2e, 0x62, 0x9f, 0xb5, 0xc4, 0xcb, 0x97, 0x66, 0xbb, 0x6d, 0xb6, 0x8f, 0xed,
	0xde, 0xd5, 0xb9, 0x81, 0x6a, 0xb0, 0x9e, 0x1e, 0xdb, 0x17, 0xed, 0xd3, 0x76, 0xe7, 0xb2, 0xad,
	0xcd, 0xa1, 0x75, 0xd0, 0x14, 0xc9, 0x81, 0x79, 0xac, 0x15, 0xd0, 0x06, 0x20, 0x05, 0x3d, 0x6a,
	0x35, 0xba, 0x27, 0x5a, 0x11, 0x3d, 0x86, 0x87, 0x0a, 0xde, 0xee, 0x58, 0x67, 0x8d, 0x96, 0x36,
	0x8f, 0x1e, 0xc1, 0x5a, 0x46, 0xd0, 0x36, 0xb4, 0x05, 0xfd, 0xe7, 0xb0, 0xde, 0xe8, 0xf7, 0x2f,
	0x08, 0x8e, 0x9b, 0x61, 0x40, 0xc6, 0x23, 0xfc, 0xb5, 0x93, 0x5f, 0xa2, 0x18, 0xf2, 0xde, 0xf1,
	0xa5, 0xa7, 0xd9, 0x4f, 0x76, 0x5e, 0xe6, 0xcc, 0x25, 0x91, 0x7e, 0x06, 0xd5, 0x93, 0x70, 0x1c,
	0x5b, 0x38, 0x0a, 0x63, 0xca, 0xd8, 0xb6, 0xa0, 0x1c, 0xf3, 0x81, 0x4d, 0x89, 0xe4, 0x2c, 0x09,
	0xa0, 0x47, 0xd8, 0x89, 0x98, 0x08, 0xa7, 0x7d, 0x22, 0x48, 0x31, 0x3b, 0xb6, 0x34, 0x58, 0x49,
	0xd3, 0x91, 0x48, 0x3f, 0x81, 0x65, 0x2b, 0xb9, 0xae, 0xca, 0xfc, 0x7b, 0x83, 0x87, 0x5e, 0x20,
	0x82, 0x58, 0xe6, 0x1f, 0x47, 0x58, 0x18, 0xb3, 0x08, 0xc7, 0x41, 0x3f, 0x7d, 0x45, 0x59, 0xc2,
	0x41, 0x9f, 0x47, 0xf8, 0x3f, 0x0a, 0xb0, 0x36, 0xa1, 0x3a, 0x74, 0xa8, 0x63, 0x52, 0x3c, 0x52,
	0x1b, 0xd7, 0x42, 0xa6, 0x71, 0x4d, 0xf5, 0xa6, 0x45, 0xa5, 0x37, 0xbd, 0xb3, 0xa3, 0xdd, 0x84,
	0x92, 0xb8, 0xb9, 0x06, 0x49, 0xe5, 0xe4, 0x2c, 0xcd, 0x80, 0xb2, 0x79, 0x03, 0x71, 0xb2, 0x11,
	0x2a, 0x0b, 0x65, 0x69, 0xc0, 0xcf, 0x34, 0xc2, 0x2f, 0x08, 0x11, 0x0e, 0x23, 0x1f, 0xf3, 0x99,
	0xf2, 0x48, 0x17, 0x88, 0x9c, 0x4b, 0xc6, 0x23, 0xd9, 0x4c, 0x88, 0xa6, 0xb6, 0x44, 0xc6, 0x23,
	0xde, 0x4a, 0xe8, 0x23, 0xa8, 0xa6, 0xdc, 0x44, 0x22, 0xf4, 0x4b, 0xd6, 0x1c, 0x53, 0x27, 0x9d,
	0xab, 0x4f, 0x33, 0x11, 0x3d, 0xe3, 0x0c, 0xd6, 0x3e, 0x53, 0x87, 0xa7, 0xa9, 0x6a, 0x4b, 0x31,
	0x63, 0xcb, 0xf3, 0xbf, 0x17, 0x61, 0x55, 0xe6, 0x9d, 0x99, 0x38, 0xeb, 0x09, 0xd4, 0x5a, 0x9d,
	0x5e, 0xcf, 0xb0, 0xae, 0x6c, 0xb3, 0x67, 0x9c, 0xd9, 0x17, 0xed, 0xee, 0xb9, 0xd1, 0x34, 0x8f,
	0x4c, 0xe3, 0x50, 0x9b, 0x63, 0x51, 0xaf, 0x48, 0xcf, 0x1b, 0xcd, 0xd3, 0xc6, 0xb1, 0x21, 0xe2,
	0x5b, 0x91, 0x9c, 0x74, 0xac, 0xae, 0xa1, 0x15, 0x51, 0x1d, 0x36, 0x14, 0xfc, 0xcc, 0x6c, 0xda,
	0xdd, 0xde, 0x55, 0xcb, 0xd0, 0xe6, 0xf9, 0xb7, 0x64, 0x4d, 0xe2, 0xb2, 0x4b, 0xa3, 0xd1, 0xea,
	0x9d, 0xd8, 0xcd, 0x86, 0x75, 0xa8, 0x2d, 0xa0, 0x5d, 0xd8, 0x56, 0x66, 0x9a, 0xed, 0x23, 0x96,
	0x1b, 0x3d, 0xb3, 0xd3, 0x16, 0x2a, 0x0f, 0xd0, 0x36, 0x6c, 0x2a, 0x2a, 0x87, 0x57, 0xed, 0x06,
	0xfb, 0x80, 0xd9, 0xec, 0xb4, 0xb5, 0xc5, 0xdc, 0x6f, 0x1b, 0x67, 0x9d, 0x5f, 0x9b, 0xda, 0x12,
	0xda, 0x82, 0xc7, 0xea, 0x54, 0xa3, 0xd9, 0xb1, 0x38, 0xb9, 0x56, 0x42, 0x3b, 0xb0, 0xa5, 0x0a,
	0x2d, 0xa3, 0xdb, 0xbd, 0x38, 0xb7, 0x0f, 0x8d, 0x03, 0xcb, 0xec, 0x6a, 0x65, 0x96, 0x9c, 0x8a,
	0x02, 0x4f, 0x4e, 0x78, 0xfe, 0x1b, 0xf9, 0xfc, 0xc2, 0x3d, 0x59, 0x87, 0x8d, 0x6e, 0xaf, 0xd3,
	0x3c, 0x4d, 0xea, 0x43, 0xda, 0x8f, 0x1b, 0x80, 0x52, 0xb2, 0x96, 0x79, 0x66, 0xf6, 0x8c, 0x43,
	0xad, 0xc0, 0xaa, 0x41, 0x0a, 0x37, 0xdb, 0x47, 0x66, 0xdb, 0xec, 0x19, 0x5a, 0xf1, 0xf9, 0x6f,
	0xa1, 0x62, 0xa5, 0x5e, 0x3d, 0x9e, 0x40, 0xcd, 0x32, 0x8e, 0x2c, 0xa3, 0x7b, 0x72, 0x0b, 0xbb,
	0x22, 0x3d, 0x6c, 0x98, 0xad, 0x2b, 0xc1, 0xae, 0xe0, 0x27, 0x9d, 0x0b, 0xab, 0x75, 0xa5, 0x15,
	0x9f, 0x7f, 0x05, 0x65, 0x2b, 0x79, 0x32, 0x61, 0x76, 0x5b, 0x46, 0xd7, 0xe8, 0xe5, 0x31, 0x23,
	0x58, 0x49, 0xc9, 0x0e, 0x1b, 0x8c, 0x75, 0x1d, 0xb4, 0x14, 0x76, 0xd6, 0x69, 0xf7, 0x4e, 0xb4,
	0xe2, 0xfe, 0x7f, 0x2a, 0xb0, 0x7c, 0xcc, 0x02, 0x55, 0xee, 0x30, 0x7a, 0x0d, 0xab, 0x99, 0x57,
	0x14, 0xb4, 0x9b, 0x09, 0xe5, 0xd9, 0x97, 0x9a, 0xba, 0x7e, 0x9f, 0x0a, 0x89, 0xf4, 0x39, 0xf4,
	0x15, 0x2c, 0xa7, 0xdf, 0x5d, 0x50, 0xf6, 0x4e, 0x9c, 0x79, 0xa7, 0xa9, 0xef, 0xdc, 0x29, 0xe7,
	0x94, 0xaf, 0xf9, 0xeb, 0x4e, 0xfa, 0xc9, 0x65, 0xc6, 0xdc, 0xd9, 0x87, 0x9a, 0x19, 0x73, 0x73,
	0x5e, 0x6d, 0xf4, 0x39, 0xf4, 0x0d, 0xac, 0xcd, 0x3c, 0x6b, 0xa0, 0x1f, 0xe4, 0xae, 0x54, 0x7d,
	0xe2, 0xa8, 0x7f, 0x7a, 0xbf, 0x12, 0xff, 0xc2, 0x25, 0xac, 0xa8, 0xaf, 0x10, 0xe8, 0xe9, 0xac,
	0x65, 0x19, 0xee, 0xdd, 0x7b, 0x34, 0x54, 0xd3, 0xd3, 0x57, 0xe9, 0x7c, 0xd3, 0xd5, 0x1b, 0xde,
	0x2d, 0xa6, 0x67, 0x6f, 0x6e, 0x89, 0xe9, 0x69, 0xfa, 0x1c, 0xd3, 0x33, 0xdc, 0xbb, 0xf7, 0x68,
	0xa4, 0x88, 0x53, 0x1d, 0x7b, 0x1e, 0xb1, 0xda, 0xf8, 0xe7, 0x11, 0x67, 0x5a, 0x7e, 0x7d, 0x0e,
	0xb9, 0xe2, 0x2a, 0xa0, 0x5e, 0x4a, 0xd0, 0xa7, 0x39, 0x53, 0x67, 0x2e, 0x72, 0xf5, 0xcf, 0x3e,
	0x40, 0x8b, 0x7f, 0xe4, 0x3b, 0xd8, 0xba, 0xa3, 0x2b, 0x46, 0x2f, 0x72, 0x36, 0xef, 0xf6, 0xae,
	0xbb, 0xbe, 0xf7, 0x31, 0xea, 0xa9, 0x45, 0x66, 0x94, 0xf2, 0x16, 0x39, 0xdb, 0x6d, 0xe7, 0x2d,
	0x32, 0xa7, 0x45, 0xd5, 0xe7, 0xd0, 0x01, 0x2c, 0x25, 0xe5, 0x62, 0xf3, 0xb6, 0x96, 0xf4, 0xdb,
	0x7a, 0xfd, 0x36, 0x51, 0x12, 0xa1, 0x33, 0x4d, 0xce, 0x4c, 0x84, 0xe6, 0xb5, 0x50, 0x33, 0x11,
	0x9a, 0xdf, 0x2b, 0xcd, 0xa1, 0x53, 0x80, 0x69, 0x7b, 0x83, 0xb2, 0xbd, 0xb3, 0xd2, 0x48, 0xd5,
	0xb7, 0xef, 0x90, 0x72, 0xb2, 0x13, 0x56, 0x7a, 0xe5, 0x09, 0x8e, 0xb6, 0x6e, 0x3b, 0xdb, 0x19,
	0xd5, 0x93, 0xdb, 0x85, 0x9c, 0xe9, 0x88, 0x57, 0xac, 0x0b, 0x82, 0x8f, 0x78, 0x2b, 0x32, 0x0e,
	0x28, 0xda, 0x9c, 0x2a, 0x7d, 0xbd, 0xbf, 0xc7, 0xfa, 0x27, 0xcb, 0x09, 0x86, 0x9c, 0x6d, 0x43,
	0x11, 0x71, 0x75, 0x85, 0xa7, 0x8b, 0x83, 0xfe, 0xf9, 0xbb, 0xe1, 0xf7, 0xe7, 0x39, 0xf8, 0xc9,
	0xeb, 0x97, 0xc3, 0xd0, 0x77, 0x82, 0xe1, 0xde, 0x4f, 0xf7, 0x29, 0xdd, 0x73, 0xc3, 0xd1, 0x4b,
	0xfe, 0x2f, 0x85, 0x1b, 0xfa, 0x2f, 0x09, 0x8e, 0xdf, 0x7b, 0x2e, 0x26, 0xea, 0xbf, 0x1e, 0x6f,
	0x16, 0xb9, 0xc2, 0x17, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x54, 0xc3, 0x13, 0x62, 0x1b, 0x19,
	0x00, 0x00,
}
