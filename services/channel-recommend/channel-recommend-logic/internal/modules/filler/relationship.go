package filler

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/channel"
	channel_recommend_relationship "golang.52tt.com/protocol/services/channel-recommend-relationship"
	channel_recommend_svr "golang.52tt.com/protocol/services/channel-recommend-svr"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/conf"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/modules/filler_base_info"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/rpc"
)

const RelationshipFillerKey = "relationship_filler"

// RelationshipFiller 关系填充器
type RelationshipFiller struct {
	baseFiller
	mapCid2Relationship map[uint32]uint32
}

// Init 初始化
func (m *RelationshipFiller) Init(ctx context.Context, info *filler_base_info.FillBaseInfo) error {
	noRelationshipCidList := make([]uint32, 0)
	tmpMap := make(map[uint32]uint32)
	for _, item := range info.CidList {
		// 不存在已有的关系才需要额外获取
		if info.MapCid2Recommend[item].RelationShip == 0 {
			noRelationshipCidList = append(noRelationshipCidList, item)
		} else {
			tmpMap[item] = info.MapCid2Recommend[item].RelationShip
		}

	}

	if len(noRelationshipCidList) == 0 {
		m.mapCid2Relationship = tmpMap
		return nil
	}

	resp, err := rpc.ChannelRelationShipCli.BatchGetRecommendRelationshipByChannelId(ctx, &channel_recommend_relationship.BatchGetRecommendRelationshipByChannelIdReq{
		Uid:           info.Uid,
		ChannelIdList: noRelationshipCidList,
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetRecommendRelationshipByChannelId failed Uid:%d err:%v", info.Uid, err)
		return err
	}

	for cid, rel := range resp.GetRelationshipsMap() {
		tmpMap[cid] = rel
	}

	m.mapCid2Relationship = tmpMap
	return nil
}

// Fill 填充
func (m *RelationshipFiller) Fill(recChannels []*channel.RecommendChannelInfo) {
	for _, item := range recChannels {
		cid := item.GetChannelBaseInfo().GetChannelId()
		log.DebugWithCtx(context.Background(), "Fill RelationshipFiller cid:%d relationship:%v", cid, m.mapCid2Relationship)
		if relationship, ok := m.mapCid2Relationship[cid]; ok {
			detail := m.baseInfo.DyConfigMgr.GetChannelCertInfo(tranRelationship2DetailType(relationship), 0, &conf.ChannelCertExtInfo{
				MainUserSex:      uint32(m.baseInfo.UserInfo.GetSex()),
				MicFemaleCount:   m.GetChannelFemaleCnt(item.GetChannelBaseInfo().GetChannelId()),
				MicMaleCount:     m.GetChannelMaleCnt(item.GetChannelBaseInfo().GetChannelId()),
				RelationshipName: "",
			})
			if detail != nil {
				item.RealTimeChannelDetail = append(item.RealTimeChannelDetail, detail)
			}
		}
	}
}

func tranRelationship2DetailType(relationship uint32) uint32 {
	switch relationship {
	case uint32(channel_recommend_svr.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_MIC):
		return uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_FRIEND_MIC)
	case uint32(channel_recommend_svr.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FRIEND_CHANNEL):
		return uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_FRIEND_CHANNEL)
	case uint32(channel_recommend_svr.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_COLLECT):
		return uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_COLLECT)
	case uint32(channel_recommend_svr.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_FOLLOW):
		return uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_FOLLOW)
	case uint32(channel_recommend_svr.RecommendRelationshipType_RECOMMMENT_RELATIONSHIP_TYPE_USED_ENTER):
		return uint32(channel.RealTimeChannelDetail_ENUM_REAL_TIME_DETAIL_TYPE_USED_ENTER)
	default:
		return 0
	}
}
