- name: esport-skill # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/tt-rev/esport/esport-skill/main.go
            - --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug: # debug参数
            ${_INCLUDE_:- ../include/debug_args.yaml | nindent 12}
            - ./services/tt-rev/esport/esport-skill/main.go
            - -- --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            ${_INCLUDE_:- ../include/common_deps.yaml | nindent 12}
            - "services/tt-rev/esport/esport-skill" # 服务代码目录
            - "services/tt-rev/esport/common"
            - "services/tt-rev/common"
            - "services/recommend-dialog"
            - "services/helper-from-cpp"
            - "services/notify"
          ignoreFilePattern: [ ]
        portForward: []