package groupsvr

import (
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	groupPB "golang.52tt.com/protocol/services/groupsvr"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	UpdateBulletinSeq(ctx context.Context, groupId uint32, info *groupPB.GroupBulletinInfo) protocol.ServerError
	UpdateGroupAdminChangeTimeline(ctx context.Context, in *groupPB.UpdateGroupAdminChangeTimelineReq) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
