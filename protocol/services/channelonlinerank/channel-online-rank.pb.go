// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-online-rank/channel-online-rank.proto

package channelonlinerank // import "golang.52tt.com/protocol/services/channelonlinerank"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelOnlineRankValSourceType int32

const (
	ChannelOnlineRankValSourceType_UNSPECIFIED    ChannelOnlineRankValSourceType = 0
	ChannelOnlineRankValSourceType_SEND_GIFT      ChannelOnlineRankValSourceType = 1
	ChannelOnlineRankValSourceType_KNIGHT         ChannelOnlineRankValSourceType = 2
	ChannelOnlineRankValSourceType_ACTIVITY       ChannelOnlineRankValSourceType = 3
	ChannelOnlineRankValSourceType_WEREWOLF_TBEAN ChannelOnlineRankValSourceType = 4
)

var ChannelOnlineRankValSourceType_name = map[int32]string{
	0: "UNSPECIFIED",
	1: "SEND_GIFT",
	2: "KNIGHT",
	3: "ACTIVITY",
	4: "WEREWOLF_TBEAN",
}
var ChannelOnlineRankValSourceType_value = map[string]int32{
	"UNSPECIFIED":    0,
	"SEND_GIFT":      1,
	"KNIGHT":         2,
	"ACTIVITY":       3,
	"WEREWOLF_TBEAN": 4,
}

func (x ChannelOnlineRankValSourceType) String() string {
	return proto.EnumName(ChannelOnlineRankValSourceType_name, int32(x))
}
func (ChannelOnlineRankValSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{0}
}

type OnlineRankValSourceType int32

const (
	OnlineRankValSourceType_ENUM_SEND_PRESENT  OnlineRankValSourceType = 0
	OnlineRankValSourceType_ENUM_ACTIVITY      OnlineRankValSourceType = 1
	OnlineRankValSourceType_ENUM_TBEAN_CONSUME OnlineRankValSourceType = 2
	OnlineRankValSourceType_ENUM_YKW_EXPOSURE  OnlineRankValSourceType = 3
)

var OnlineRankValSourceType_name = map[int32]string{
	0: "ENUM_SEND_PRESENT",
	1: "ENUM_ACTIVITY",
	2: "ENUM_TBEAN_CONSUME",
	3: "ENUM_YKW_EXPOSURE",
}
var OnlineRankValSourceType_value = map[string]int32{
	"ENUM_SEND_PRESENT":  0,
	"ENUM_ACTIVITY":      1,
	"ENUM_TBEAN_CONSUME": 2,
	"ENUM_YKW_EXPOSURE":  3,
}

func (x OnlineRankValSourceType) String() string {
	return proto.EnumName(OnlineRankValSourceType_name, int32(x))
}
func (OnlineRankValSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{1}
}

type ConsumeStatusType int32

const (
	ConsumeStatusType_ENUM_DELAY   ConsumeStatusType = 0
	ConsumeStatusType_ENUM_ABANDON ConsumeStatusType = 1
	ConsumeStatusType_ENUM_COMMIT  ConsumeStatusType = 2
	ConsumeStatusType_ENUM_ALL     ConsumeStatusType = 10
)

var ConsumeStatusType_name = map[int32]string{
	0:  "ENUM_DELAY",
	1:  "ENUM_ABANDON",
	2:  "ENUM_COMMIT",
	10: "ENUM_ALL",
}
var ConsumeStatusType_value = map[string]int32{
	"ENUM_DELAY":   0,
	"ENUM_ABANDON": 1,
	"ENUM_COMMIT":  2,
	"ENUM_ALL":     10,
}

func (x ConsumeStatusType) String() string {
	return proto.EnumName(ConsumeStatusType_name, int32(x))
}
func (ConsumeStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{2}
}

type GetMemberRankListReq struct {
	OperUid              uint32   `protobuf:"varint,1,opt,name=oper_uid,json=operUid,proto3" json:"oper_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	InvisibleVaild       bool     `protobuf:"varint,4,opt,name=invisible_vaild,json=invisibleVaild,proto3" json:"invisible_vaild,omitempty"`
	IsLiveChannel        bool     `protobuf:"varint,5,opt,name=is_live_channel,json=isLiveChannel,proto3" json:"is_live_channel,omitempty"`
	Start                int64    `protobuf:"varint,6,opt,name=start,proto3" json:"start,omitempty"`
	Stop                 int64    `protobuf:"varint,7,opt,name=stop,proto3" json:"stop,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberRankListReq) Reset()         { *m = GetMemberRankListReq{} }
func (m *GetMemberRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberRankListReq) ProtoMessage()    {}
func (*GetMemberRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{0}
}
func (m *GetMemberRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberRankListReq.Unmarshal(m, b)
}
func (m *GetMemberRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetMemberRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberRankListReq.Merge(dst, src)
}
func (m *GetMemberRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberRankListReq.Size(m)
}
func (m *GetMemberRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberRankListReq proto.InternalMessageInfo

func (m *GetMemberRankListReq) GetOperUid() uint32 {
	if m != nil {
		return m.OperUid
	}
	return 0
}

func (m *GetMemberRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMemberRankListReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *GetMemberRankListReq) GetInvisibleVaild() bool {
	if m != nil {
		return m.InvisibleVaild
	}
	return false
}

func (m *GetMemberRankListReq) GetIsLiveChannel() bool {
	if m != nil {
		return m.IsLiveChannel
	}
	return false
}

func (m *GetMemberRankListReq) GetStart() int64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetMemberRankListReq) GetStop() int64 {
	if m != nil {
		return m.Stop
	}
	return 0
}

type GetMemberRankListResp struct {
	ChannelId            uint32               `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MemberList           []*ChannelMemberRank `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	AllMemberSize        uint32               `protobuf:"varint,3,opt,name=all_member_size,json=allMemberSize,proto3" json:"all_member_size,omitempty"`
	MyRankInfo           *ChannelMemberRank   `protobuf:"bytes,4,opt,name=my_rank_info,json=myRankInfo,proto3" json:"my_rank_info,omitempty"`
	DValue               uint32               `protobuf:"varint,5,opt,name=d_value,json=dValue,proto3" json:"d_value,omitempty"`
	Rank                 uint32               `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMemberRankListResp) Reset()         { *m = GetMemberRankListResp{} }
func (m *GetMemberRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberRankListResp) ProtoMessage()    {}
func (*GetMemberRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{1}
}
func (m *GetMemberRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberRankListResp.Unmarshal(m, b)
}
func (m *GetMemberRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetMemberRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberRankListResp.Merge(dst, src)
}
func (m *GetMemberRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetMemberRankListResp.Size(m)
}
func (m *GetMemberRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberRankListResp proto.InternalMessageInfo

func (m *GetMemberRankListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMemberRankListResp) GetMemberList() []*ChannelMemberRank {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetMemberRankListResp) GetAllMemberSize() uint32 {
	if m != nil {
		return m.AllMemberSize
	}
	return 0
}

func (m *GetMemberRankListResp) GetMyRankInfo() *ChannelMemberRank {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

func (m *GetMemberRankListResp) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *GetMemberRankListResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type UserRankMetadata struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	NobilityLevel        int32    `protobuf:"varint,2,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	NobInsisible         bool     `protobuf:"varint,3,opt,name=nob_insisible,json=nobInsisible,proto3" json:"nob_insisible,omitempty"`
	TotalConsume         int32    `protobuf:"varint,4,opt,name=total_consume,json=totalConsume,proto3" json:"total_consume,omitempty"`
	Consume              int32    `protobuf:"varint,5,opt,name=consume,proto3" json:"consume,omitempty"`
	YkwLevel             int32    `protobuf:"varint,6,opt,name=ykw_level,json=ykwLevel,proto3" json:"ykw_level,omitempty"`
	Timestamp            int32    `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRankMetadata) Reset()         { *m = UserRankMetadata{} }
func (m *UserRankMetadata) String() string { return proto.CompactTextString(m) }
func (*UserRankMetadata) ProtoMessage()    {}
func (*UserRankMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{2}
}
func (m *UserRankMetadata) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRankMetadata.Unmarshal(m, b)
}
func (m *UserRankMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRankMetadata.Marshal(b, m, deterministic)
}
func (dst *UserRankMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRankMetadata.Merge(dst, src)
}
func (m *UserRankMetadata) XXX_Size() int {
	return xxx_messageInfo_UserRankMetadata.Size(m)
}
func (m *UserRankMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRankMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_UserRankMetadata proto.InternalMessageInfo

func (m *UserRankMetadata) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRankMetadata) GetNobilityLevel() int32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *UserRankMetadata) GetNobInsisible() bool {
	if m != nil {
		return m.NobInsisible
	}
	return false
}

func (m *UserRankMetadata) GetTotalConsume() int32 {
	if m != nil {
		return m.TotalConsume
	}
	return 0
}

func (m *UserRankMetadata) GetConsume() int32 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *UserRankMetadata) GetYkwLevel() int32 {
	if m != nil {
		return m.YkwLevel
	}
	return 0
}

func (m *UserRankMetadata) GetTimestamp() int32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type ChannelMemberVip struct {
	CurrLevelId          uint32   `protobuf:"varint,1,opt,name=curr_level_id,json=currLevelId,proto3" json:"curr_level_id,omitempty"`
	CurrLevelName        string   `protobuf:"bytes,2,opt,name=curr_level_name,json=currLevelName,proto3" json:"curr_level_name,omitempty"`
	CurrLevelValue       uint32   `protobuf:"varint,3,opt,name=curr_level_value,json=currLevelValue,proto3" json:"curr_level_value,omitempty"`
	NextLevelId          uint32   `protobuf:"varint,4,opt,name=next_level_id,json=nextLevelId,proto3" json:"next_level_id,omitempty"`
	NextLevelName        string   `protobuf:"bytes,5,opt,name=next_level_name,json=nextLevelName,proto3" json:"next_level_name,omitempty"`
	NextLevelMinValue    uint32   `protobuf:"varint,6,opt,name=next_level_min_value,json=nextLevelMinValue,proto3" json:"next_level_min_value,omitempty"`
	NobilityLevel        uint32   `protobuf:"varint,7,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	Invisible            bool     `protobuf:"varint,8,opt,name=invisible,proto3" json:"invisible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMemberVip) Reset()         { *m = ChannelMemberVip{} }
func (m *ChannelMemberVip) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberVip) ProtoMessage()    {}
func (*ChannelMemberVip) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{3}
}
func (m *ChannelMemberVip) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMemberVip.Unmarshal(m, b)
}
func (m *ChannelMemberVip) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMemberVip.Marshal(b, m, deterministic)
}
func (dst *ChannelMemberVip) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMemberVip.Merge(dst, src)
}
func (m *ChannelMemberVip) XXX_Size() int {
	return xxx_messageInfo_ChannelMemberVip.Size(m)
}
func (m *ChannelMemberVip) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMemberVip.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMemberVip proto.InternalMessageInfo

func (m *ChannelMemberVip) GetCurrLevelId() uint32 {
	if m != nil {
		return m.CurrLevelId
	}
	return 0
}

func (m *ChannelMemberVip) GetCurrLevelName() string {
	if m != nil {
		return m.CurrLevelName
	}
	return ""
}

func (m *ChannelMemberVip) GetCurrLevelValue() uint32 {
	if m != nil {
		return m.CurrLevelValue
	}
	return 0
}

func (m *ChannelMemberVip) GetNextLevelId() uint32 {
	if m != nil {
		return m.NextLevelId
	}
	return 0
}

func (m *ChannelMemberVip) GetNextLevelName() string {
	if m != nil {
		return m.NextLevelName
	}
	return ""
}

func (m *ChannelMemberVip) GetNextLevelMinValue() uint32 {
	if m != nil {
		return m.NextLevelMinValue
	}
	return 0
}

func (m *ChannelMemberVip) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *ChannelMemberVip) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

// 房间成员排名信息
type ChannelMemberRank struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ts                   uint32            `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	RankValue            uint32            `protobuf:"varint,3,opt,name=rank_value,json=rankValue,proto3" json:"rank_value,omitempty"`
	VipLevelInfo         *ChannelMemberVip `protobuf:"bytes,4,opt,name=vip_level_info,json=vipLevelInfo,proto3" json:"vip_level_info,omitempty"`
	Rank                 uint32            `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	TotalConsum          uint32            `protobuf:"varint,6,opt,name=total_consum,json=totalConsum,proto3" json:"total_consum,omitempty"`
	UkwLevel             uint32            `protobuf:"varint,7,opt,name=ukw_level,json=ukwLevel,proto3" json:"ukw_level,omitempty"`
	RankTotalConsum      uint32            `protobuf:"varint,8,opt,name=rank_total_consum,json=rankTotalConsum,proto3" json:"rank_total_consum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelMemberRank) Reset()         { *m = ChannelMemberRank{} }
func (m *ChannelMemberRank) String() string { return proto.CompactTextString(m) }
func (*ChannelMemberRank) ProtoMessage()    {}
func (*ChannelMemberRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{4}
}
func (m *ChannelMemberRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMemberRank.Unmarshal(m, b)
}
func (m *ChannelMemberRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMemberRank.Marshal(b, m, deterministic)
}
func (dst *ChannelMemberRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMemberRank.Merge(dst, src)
}
func (m *ChannelMemberRank) XXX_Size() int {
	return xxx_messageInfo_ChannelMemberRank.Size(m)
}
func (m *ChannelMemberRank) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMemberRank.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMemberRank proto.InternalMessageInfo

func (m *ChannelMemberRank) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMemberRank) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ChannelMemberRank) GetRankValue() uint32 {
	if m != nil {
		return m.RankValue
	}
	return 0
}

func (m *ChannelMemberRank) GetVipLevelInfo() *ChannelMemberVip {
	if m != nil {
		return m.VipLevelInfo
	}
	return nil
}

func (m *ChannelMemberRank) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ChannelMemberRank) GetTotalConsum() uint32 {
	if m != nil {
		return m.TotalConsum
	}
	return 0
}

func (m *ChannelMemberRank) GetUkwLevel() uint32 {
	if m != nil {
		return m.UkwLevel
	}
	return 0
}

func (m *ChannelMemberRank) GetRankTotalConsum() uint32 {
	if m != nil {
		return m.RankTotalConsum
	}
	return 0
}

type RefreshTop3Task struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Ts                   uint32   `protobuf:"varint,4,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RefreshTop3Task) Reset()         { *m = RefreshTop3Task{} }
func (m *RefreshTop3Task) String() string { return proto.CompactTextString(m) }
func (*RefreshTop3Task) ProtoMessage()    {}
func (*RefreshTop3Task) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{5}
}
func (m *RefreshTop3Task) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RefreshTop3Task.Unmarshal(m, b)
}
func (m *RefreshTop3Task) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RefreshTop3Task.Marshal(b, m, deterministic)
}
func (dst *RefreshTop3Task) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshTop3Task.Merge(dst, src)
}
func (m *RefreshTop3Task) XXX_Size() int {
	return xxx_messageInfo_RefreshTop3Task.Size(m)
}
func (m *RefreshTop3Task) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshTop3Task.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshTop3Task proto.InternalMessageInfo

func (m *RefreshTop3Task) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *RefreshTop3Task) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *RefreshTop3Task) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *RefreshTop3Task) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type ReloadConsumeTask struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReloadConsumeTask) Reset()         { *m = ReloadConsumeTask{} }
func (m *ReloadConsumeTask) String() string { return proto.CompactTextString(m) }
func (*ReloadConsumeTask) ProtoMessage()    {}
func (*ReloadConsumeTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{6}
}
func (m *ReloadConsumeTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReloadConsumeTask.Unmarshal(m, b)
}
func (m *ReloadConsumeTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReloadConsumeTask.Marshal(b, m, deterministic)
}
func (dst *ReloadConsumeTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReloadConsumeTask.Merge(dst, src)
}
func (m *ReloadConsumeTask) XXX_Size() int {
	return xxx_messageInfo_ReloadConsumeTask.Size(m)
}
func (m *ReloadConsumeTask) XXX_DiscardUnknown() {
	xxx_messageInfo_ReloadConsumeTask.DiscardUnknown(m)
}

var xxx_messageInfo_ReloadConsumeTask proto.InternalMessageInfo

func (m *ReloadConsumeTask) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReloadConsumeTask) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ReloadConsumeTask) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 获取房间消费土豪榜
type MemberConsumeInfo struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ConsumeCnt           uint32            `protobuf:"varint,2,opt,name=consume_cnt,json=consumeCnt,proto3" json:"consume_cnt,omitempty"`
	VipLevelInfo         *ChannelMemberVip `protobuf:"bytes,3,opt,name=vip_level_info,json=vipLevelInfo,proto3" json:"vip_level_info,omitempty"`
	IsAutoHiddenConsume  bool              `protobuf:"varint,4,opt,name=is_auto_hidden_consume,json=isAutoHiddenConsume,proto3" json:"is_auto_hidden_consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MemberConsumeInfo) Reset()         { *m = MemberConsumeInfo{} }
func (m *MemberConsumeInfo) String() string { return proto.CompactTextString(m) }
func (*MemberConsumeInfo) ProtoMessage()    {}
func (*MemberConsumeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{7}
}
func (m *MemberConsumeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberConsumeInfo.Unmarshal(m, b)
}
func (m *MemberConsumeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberConsumeInfo.Marshal(b, m, deterministic)
}
func (dst *MemberConsumeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberConsumeInfo.Merge(dst, src)
}
func (m *MemberConsumeInfo) XXX_Size() int {
	return xxx_messageInfo_MemberConsumeInfo.Size(m)
}
func (m *MemberConsumeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberConsumeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MemberConsumeInfo proto.InternalMessageInfo

func (m *MemberConsumeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberConsumeInfo) GetConsumeCnt() uint32 {
	if m != nil {
		return m.ConsumeCnt
	}
	return 0
}

func (m *MemberConsumeInfo) GetVipLevelInfo() *ChannelMemberVip {
	if m != nil {
		return m.VipLevelInfo
	}
	return nil
}

func (m *MemberConsumeInfo) GetIsAutoHiddenConsume() bool {
	if m != nil {
		return m.IsAutoHiddenConsume
	}
	return false
}

type GetMemberWeekRankListReq struct {
	Cid   uint32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	Begin uint32 `protobuf:"varint,2,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	// 需要获取自己的排名信息才填以下字段
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	ViewCnt              uint32   `protobuf:"varint,5,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberWeekRankListReq) Reset()         { *m = GetMemberWeekRankListReq{} }
func (m *GetMemberWeekRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberWeekRankListReq) ProtoMessage()    {}
func (*GetMemberWeekRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{8}
}
func (m *GetMemberWeekRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberWeekRankListReq.Unmarshal(m, b)
}
func (m *GetMemberWeekRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberWeekRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetMemberWeekRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberWeekRankListReq.Merge(dst, src)
}
func (m *GetMemberWeekRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberWeekRankListReq.Size(m)
}
func (m *GetMemberWeekRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberWeekRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberWeekRankListReq proto.InternalMessageInfo

func (m *GetMemberWeekRankListReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMemberWeekRankListReq) GetViewCnt() uint32 {
	if m != nil {
		return m.ViewCnt
	}
	return 0
}

type GetMemberWeekRankListResp struct {
	RankInfoList         []*MemberConsumeInfo `protobuf:"bytes,1,rep,name=rank_info_list,json=rankInfoList,proto3" json:"rank_info_list,omitempty"`
	MyRankInfo           *MemberConsumeInfo   `protobuf:"bytes,2,opt,name=my_rank_info,json=myRankInfo,proto3" json:"my_rank_info,omitempty"`
	DValue               uint32               `protobuf:"varint,3,opt,name=d_value,json=dValue,proto3" json:"d_value,omitempty"`
	Rank                 uint32               `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMemberWeekRankListResp) Reset()         { *m = GetMemberWeekRankListResp{} }
func (m *GetMemberWeekRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberWeekRankListResp) ProtoMessage()    {}
func (*GetMemberWeekRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{9}
}
func (m *GetMemberWeekRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberWeekRankListResp.Unmarshal(m, b)
}
func (m *GetMemberWeekRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberWeekRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetMemberWeekRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberWeekRankListResp.Merge(dst, src)
}
func (m *GetMemberWeekRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetMemberWeekRankListResp.Size(m)
}
func (m *GetMemberWeekRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberWeekRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberWeekRankListResp proto.InternalMessageInfo

func (m *GetMemberWeekRankListResp) GetRankInfoList() []*MemberConsumeInfo {
	if m != nil {
		return m.RankInfoList
	}
	return nil
}

func (m *GetMemberWeekRankListResp) GetMyRankInfo() *MemberConsumeInfo {
	if m != nil {
		return m.MyRankInfo
	}
	return nil
}

func (m *GetMemberWeekRankListResp) GetDValue() uint32 {
	if m != nil {
		return m.DValue
	}
	return 0
}

func (m *GetMemberWeekRankListResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type FixOnlineRankReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixOnlineRankReq) Reset()         { *m = FixOnlineRankReq{} }
func (m *FixOnlineRankReq) String() string { return proto.CompactTextString(m) }
func (*FixOnlineRankReq) ProtoMessage()    {}
func (*FixOnlineRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{10}
}
func (m *FixOnlineRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixOnlineRankReq.Unmarshal(m, b)
}
func (m *FixOnlineRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixOnlineRankReq.Marshal(b, m, deterministic)
}
func (dst *FixOnlineRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixOnlineRankReq.Merge(dst, src)
}
func (m *FixOnlineRankReq) XXX_Size() int {
	return xxx_messageInfo_FixOnlineRankReq.Size(m)
}
func (m *FixOnlineRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FixOnlineRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_FixOnlineRankReq proto.InternalMessageInfo

func (m *FixOnlineRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type FixOnlineRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixOnlineRankResp) Reset()         { *m = FixOnlineRankResp{} }
func (m *FixOnlineRankResp) String() string { return proto.CompactTextString(m) }
func (*FixOnlineRankResp) ProtoMessage()    {}
func (*FixOnlineRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{11}
}
func (m *FixOnlineRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixOnlineRankResp.Unmarshal(m, b)
}
func (m *FixOnlineRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixOnlineRankResp.Marshal(b, m, deterministic)
}
func (dst *FixOnlineRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixOnlineRankResp.Merge(dst, src)
}
func (m *FixOnlineRankResp) XXX_Size() int {
	return xxx_messageInfo_FixOnlineRankResp.Size(m)
}
func (m *FixOnlineRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FixOnlineRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_FixOnlineRankResp proto.InternalMessageInfo

type CheckChannelOnlineMemberReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChannelOnlineMemberReq) Reset()         { *m = CheckChannelOnlineMemberReq{} }
func (m *CheckChannelOnlineMemberReq) String() string { return proto.CompactTextString(m) }
func (*CheckChannelOnlineMemberReq) ProtoMessage()    {}
func (*CheckChannelOnlineMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{12}
}
func (m *CheckChannelOnlineMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelOnlineMemberReq.Unmarshal(m, b)
}
func (m *CheckChannelOnlineMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelOnlineMemberReq.Marshal(b, m, deterministic)
}
func (dst *CheckChannelOnlineMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelOnlineMemberReq.Merge(dst, src)
}
func (m *CheckChannelOnlineMemberReq) XXX_Size() int {
	return xxx_messageInfo_CheckChannelOnlineMemberReq.Size(m)
}
func (m *CheckChannelOnlineMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelOnlineMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelOnlineMemberReq proto.InternalMessageInfo

func (m *CheckChannelOnlineMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckChannelOnlineMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckChannelOnlineMemberResp) Reset()         { *m = CheckChannelOnlineMemberResp{} }
func (m *CheckChannelOnlineMemberResp) String() string { return proto.CompactTextString(m) }
func (*CheckChannelOnlineMemberResp) ProtoMessage()    {}
func (*CheckChannelOnlineMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{13}
}
func (m *CheckChannelOnlineMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelOnlineMemberResp.Unmarshal(m, b)
}
func (m *CheckChannelOnlineMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelOnlineMemberResp.Marshal(b, m, deterministic)
}
func (dst *CheckChannelOnlineMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelOnlineMemberResp.Merge(dst, src)
}
func (m *CheckChannelOnlineMemberResp) XXX_Size() int {
	return xxx_messageInfo_CheckChannelOnlineMemberResp.Size(m)
}
func (m *CheckChannelOnlineMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelOnlineMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelOnlineMemberResp proto.InternalMessageInfo

type FixOnlineRankV2Req struct {
	Certain              bool     `protobuf:"varint,1,opt,name=certain,proto3" json:"certain,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixOnlineRankV2Req) Reset()         { *m = FixOnlineRankV2Req{} }
func (m *FixOnlineRankV2Req) String() string { return proto.CompactTextString(m) }
func (*FixOnlineRankV2Req) ProtoMessage()    {}
func (*FixOnlineRankV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{14}
}
func (m *FixOnlineRankV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixOnlineRankV2Req.Unmarshal(m, b)
}
func (m *FixOnlineRankV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixOnlineRankV2Req.Marshal(b, m, deterministic)
}
func (dst *FixOnlineRankV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixOnlineRankV2Req.Merge(dst, src)
}
func (m *FixOnlineRankV2Req) XXX_Size() int {
	return xxx_messageInfo_FixOnlineRankV2Req.Size(m)
}
func (m *FixOnlineRankV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_FixOnlineRankV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_FixOnlineRankV2Req proto.InternalMessageInfo

func (m *FixOnlineRankV2Req) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

func (m *FixOnlineRankV2Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FixOnlineRankV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type FixOnlineRankV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FixOnlineRankV2Resp) Reset()         { *m = FixOnlineRankV2Resp{} }
func (m *FixOnlineRankV2Resp) String() string { return proto.CompactTextString(m) }
func (*FixOnlineRankV2Resp) ProtoMessage()    {}
func (*FixOnlineRankV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{15}
}
func (m *FixOnlineRankV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FixOnlineRankV2Resp.Unmarshal(m, b)
}
func (m *FixOnlineRankV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FixOnlineRankV2Resp.Marshal(b, m, deterministic)
}
func (dst *FixOnlineRankV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FixOnlineRankV2Resp.Merge(dst, src)
}
func (m *FixOnlineRankV2Resp) XXX_Size() int {
	return xxx_messageInfo_FixOnlineRankV2Resp.Size(m)
}
func (m *FixOnlineRankV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_FixOnlineRankV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_FixOnlineRankV2Resp proto.InternalMessageInfo

type SettleYkwWeekRankReq struct {
	Certain              bool     `protobuf:"varint,1,opt,name=certain,proto3" json:"certain,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleYkwWeekRankReq) Reset()         { *m = SettleYkwWeekRankReq{} }
func (m *SettleYkwWeekRankReq) String() string { return proto.CompactTextString(m) }
func (*SettleYkwWeekRankReq) ProtoMessage()    {}
func (*SettleYkwWeekRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{16}
}
func (m *SettleYkwWeekRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleYkwWeekRankReq.Unmarshal(m, b)
}
func (m *SettleYkwWeekRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleYkwWeekRankReq.Marshal(b, m, deterministic)
}
func (dst *SettleYkwWeekRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleYkwWeekRankReq.Merge(dst, src)
}
func (m *SettleYkwWeekRankReq) XXX_Size() int {
	return xxx_messageInfo_SettleYkwWeekRankReq.Size(m)
}
func (m *SettleYkwWeekRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleYkwWeekRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_SettleYkwWeekRankReq proto.InternalMessageInfo

func (m *SettleYkwWeekRankReq) GetCertain() bool {
	if m != nil {
		return m.Certain
	}
	return false
}

type SettleYkwWeekRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleYkwWeekRankResp) Reset()         { *m = SettleYkwWeekRankResp{} }
func (m *SettleYkwWeekRankResp) String() string { return proto.CompactTextString(m) }
func (*SettleYkwWeekRankResp) ProtoMessage()    {}
func (*SettleYkwWeekRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{17}
}
func (m *SettleYkwWeekRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleYkwWeekRankResp.Unmarshal(m, b)
}
func (m *SettleYkwWeekRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleYkwWeekRankResp.Marshal(b, m, deterministic)
}
func (dst *SettleYkwWeekRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleYkwWeekRankResp.Merge(dst, src)
}
func (m *SettleYkwWeekRankResp) XXX_Size() int {
	return xxx_messageInfo_SettleYkwWeekRankResp.Size(m)
}
func (m *SettleYkwWeekRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleYkwWeekRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_SettleYkwWeekRankResp proto.InternalMessageInfo

type TestAddPresentEventReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	SendUid              uint32   `protobuf:"varint,2,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	ChannelType          uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	SendTime             uint32   `protobuf:"varint,6,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestAddPresentEventReq) Reset()         { *m = TestAddPresentEventReq{} }
func (m *TestAddPresentEventReq) String() string { return proto.CompactTextString(m) }
func (*TestAddPresentEventReq) ProtoMessage()    {}
func (*TestAddPresentEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{18}
}
func (m *TestAddPresentEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestAddPresentEventReq.Unmarshal(m, b)
}
func (m *TestAddPresentEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestAddPresentEventReq.Marshal(b, m, deterministic)
}
func (dst *TestAddPresentEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestAddPresentEventReq.Merge(dst, src)
}
func (m *TestAddPresentEventReq) XXX_Size() int {
	return xxx_messageInfo_TestAddPresentEventReq.Size(m)
}
func (m *TestAddPresentEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestAddPresentEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestAddPresentEventReq proto.InternalMessageInfo

func (m *TestAddPresentEventReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TestAddPresentEventReq) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *TestAddPresentEventReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TestAddPresentEventReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TestAddPresentEventReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *TestAddPresentEventReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{19}
}
func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (dst *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(dst, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

type GetMemberRankValReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberRankValReq) Reset()         { *m = GetMemberRankValReq{} }
func (m *GetMemberRankValReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberRankValReq) ProtoMessage()    {}
func (*GetMemberRankValReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{20}
}
func (m *GetMemberRankValReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberRankValReq.Unmarshal(m, b)
}
func (m *GetMemberRankValReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberRankValReq.Marshal(b, m, deterministic)
}
func (dst *GetMemberRankValReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberRankValReq.Merge(dst, src)
}
func (m *GetMemberRankValReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberRankValReq.Size(m)
}
func (m *GetMemberRankValReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberRankValReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberRankValReq proto.InternalMessageInfo

func (m *GetMemberRankValReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMemberRankValReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMemberRankValResp struct {
	RankCacheVal         uint32       `protobuf:"varint,1,opt,name=rank_cache_val,json=rankCacheVal,proto3" json:"rank_cache_val,omitempty"`
	RankCacheRecordVal   uint32       `protobuf:"varint,2,opt,name=rank_cache_record_val,json=rankCacheRecordVal,proto3" json:"rank_cache_record_val,omitempty"`
	RankDbVal            uint32       `protobuf:"varint,3,opt,name=rank_db_val,json=rankDbVal,proto3" json:"rank_db_val,omitempty"`
	OrderList            []*OrderInfo `protobuf:"bytes,4,rep,name=orderList,proto3" json:"orderList,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMemberRankValResp) Reset()         { *m = GetMemberRankValResp{} }
func (m *GetMemberRankValResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberRankValResp) ProtoMessage()    {}
func (*GetMemberRankValResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{21}
}
func (m *GetMemberRankValResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberRankValResp.Unmarshal(m, b)
}
func (m *GetMemberRankValResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberRankValResp.Marshal(b, m, deterministic)
}
func (dst *GetMemberRankValResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberRankValResp.Merge(dst, src)
}
func (m *GetMemberRankValResp) XXX_Size() int {
	return xxx_messageInfo_GetMemberRankValResp.Size(m)
}
func (m *GetMemberRankValResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberRankValResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberRankValResp proto.InternalMessageInfo

func (m *GetMemberRankValResp) GetRankCacheVal() uint32 {
	if m != nil {
		return m.RankCacheVal
	}
	return 0
}

func (m *GetMemberRankValResp) GetRankCacheRecordVal() uint32 {
	if m != nil {
		return m.RankCacheRecordVal
	}
	return 0
}

func (m *GetMemberRankValResp) GetRankDbVal() uint32 {
	if m != nil {
		return m.RankDbVal
	}
	return 0
}

func (m *GetMemberRankValResp) GetOrderList() []*OrderInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type OrderInfo struct {
	OrderId              string            `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32            `protobuf:"varint,3,opt,name=cid,proto3" json:"cid,omitempty"`
	TotalPrice           uint32            `protobuf:"varint,4,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	CreateTime           uint32            `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Status               ConsumeStatusType `protobuf:"varint,6,opt,name=status,proto3,enum=channelonlinerank.ConsumeStatusType" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *OrderInfo) Reset()         { *m = OrderInfo{} }
func (m *OrderInfo) String() string { return proto.CompactTextString(m) }
func (*OrderInfo) ProtoMessage()    {}
func (*OrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_online_rank_a49a0fb6d6347f49, []int{22}
}
func (m *OrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderInfo.Unmarshal(m, b)
}
func (m *OrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderInfo.Marshal(b, m, deterministic)
}
func (dst *OrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderInfo.Merge(dst, src)
}
func (m *OrderInfo) XXX_Size() int {
	return xxx_messageInfo_OrderInfo.Size(m)
}
func (m *OrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderInfo proto.InternalMessageInfo

func (m *OrderInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OrderInfo) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *OrderInfo) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *OrderInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *OrderInfo) GetStatus() ConsumeStatusType {
	if m != nil {
		return m.Status
	}
	return ConsumeStatusType_ENUM_DELAY
}

func init() {
	proto.RegisterType((*GetMemberRankListReq)(nil), "channelonlinerank.GetMemberRankListReq")
	proto.RegisterType((*GetMemberRankListResp)(nil), "channelonlinerank.GetMemberRankListResp")
	proto.RegisterType((*UserRankMetadata)(nil), "channelonlinerank.UserRankMetadata")
	proto.RegisterType((*ChannelMemberVip)(nil), "channelonlinerank.ChannelMemberVip")
	proto.RegisterType((*ChannelMemberRank)(nil), "channelonlinerank.ChannelMemberRank")
	proto.RegisterType((*RefreshTop3Task)(nil), "channelonlinerank.RefreshTop3Task")
	proto.RegisterType((*ReloadConsumeTask)(nil), "channelonlinerank.ReloadConsumeTask")
	proto.RegisterType((*MemberConsumeInfo)(nil), "channelonlinerank.MemberConsumeInfo")
	proto.RegisterType((*GetMemberWeekRankListReq)(nil), "channelonlinerank.GetMemberWeekRankListReq")
	proto.RegisterType((*GetMemberWeekRankListResp)(nil), "channelonlinerank.GetMemberWeekRankListResp")
	proto.RegisterType((*FixOnlineRankReq)(nil), "channelonlinerank.FixOnlineRankReq")
	proto.RegisterType((*FixOnlineRankResp)(nil), "channelonlinerank.FixOnlineRankResp")
	proto.RegisterType((*CheckChannelOnlineMemberReq)(nil), "channelonlinerank.CheckChannelOnlineMemberReq")
	proto.RegisterType((*CheckChannelOnlineMemberResp)(nil), "channelonlinerank.CheckChannelOnlineMemberResp")
	proto.RegisterType((*FixOnlineRankV2Req)(nil), "channelonlinerank.FixOnlineRankV2Req")
	proto.RegisterType((*FixOnlineRankV2Resp)(nil), "channelonlinerank.FixOnlineRankV2Resp")
	proto.RegisterType((*SettleYkwWeekRankReq)(nil), "channelonlinerank.SettleYkwWeekRankReq")
	proto.RegisterType((*SettleYkwWeekRankResp)(nil), "channelonlinerank.SettleYkwWeekRankResp")
	proto.RegisterType((*TestAddPresentEventReq)(nil), "channelonlinerank.TestAddPresentEventReq")
	proto.RegisterType((*Empty)(nil), "channelonlinerank.Empty")
	proto.RegisterType((*GetMemberRankValReq)(nil), "channelonlinerank.GetMemberRankValReq")
	proto.RegisterType((*GetMemberRankValResp)(nil), "channelonlinerank.GetMemberRankValResp")
	proto.RegisterType((*OrderInfo)(nil), "channelonlinerank.OrderInfo")
	proto.RegisterEnum("channelonlinerank.ChannelOnlineRankValSourceType", ChannelOnlineRankValSourceType_name, ChannelOnlineRankValSourceType_value)
	proto.RegisterEnum("channelonlinerank.OnlineRankValSourceType", OnlineRankValSourceType_name, OnlineRankValSourceType_value)
	proto.RegisterEnum("channelonlinerank.ConsumeStatusType", ConsumeStatusType_name, ConsumeStatusType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelOnlineRankClient is the client API for ChannelOnlineRank service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelOnlineRankClient interface {
	GetMemberRankList(ctx context.Context, in *GetMemberRankListReq, opts ...grpc.CallOption) (*GetMemberRankListResp, error)
	GetMemberWeekRankList(ctx context.Context, in *GetMemberWeekRankListReq, opts ...grpc.CallOption) (*GetMemberWeekRankListResp, error)
	FixOnlineRank(ctx context.Context, in *FixOnlineRankReq, opts ...grpc.CallOption) (*FixOnlineRankResp, error)
	// v2
	GetMemberRankListV2(ctx context.Context, in *GetMemberRankListReq, opts ...grpc.CallOption) (*GetMemberRankListResp, error)
	GetMemberWeekRankListV2(ctx context.Context, in *GetMemberWeekRankListReq, opts ...grpc.CallOption) (*GetMemberWeekRankListResp, error)
	GetMemberRankVal(ctx context.Context, in *GetMemberRankValReq, opts ...grpc.CallOption) (*GetMemberRankValResp, error)
	SettleYkwWeekRank(ctx context.Context, in *SettleYkwWeekRankReq, opts ...grpc.CallOption) (*SettleYkwWeekRankResp, error)
	FixOnlineRankV2(ctx context.Context, in *FixOnlineRankV2Req, opts ...grpc.CallOption) (*FixOnlineRankV2Resp, error)
	GetOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	TestAddPresentEvent(ctx context.Context, in *TestAddPresentEventReq, opts ...grpc.CallOption) (*Empty, error)
}

type channelOnlineRankClient struct {
	cc *grpc.ClientConn
}

func NewChannelOnlineRankClient(cc *grpc.ClientConn) ChannelOnlineRankClient {
	return &channelOnlineRankClient{cc}
}

func (c *channelOnlineRankClient) GetMemberRankList(ctx context.Context, in *GetMemberRankListReq, opts ...grpc.CallOption) (*GetMemberRankListResp, error) {
	out := new(GetMemberRankListResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetMemberRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) GetMemberWeekRankList(ctx context.Context, in *GetMemberWeekRankListReq, opts ...grpc.CallOption) (*GetMemberWeekRankListResp, error) {
	out := new(GetMemberWeekRankListResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetMemberWeekRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) FixOnlineRank(ctx context.Context, in *FixOnlineRankReq, opts ...grpc.CallOption) (*FixOnlineRankResp, error) {
	out := new(FixOnlineRankResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/FixOnlineRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) GetMemberRankListV2(ctx context.Context, in *GetMemberRankListReq, opts ...grpc.CallOption) (*GetMemberRankListResp, error) {
	out := new(GetMemberRankListResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetMemberRankListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) GetMemberWeekRankListV2(ctx context.Context, in *GetMemberWeekRankListReq, opts ...grpc.CallOption) (*GetMemberWeekRankListResp, error) {
	out := new(GetMemberWeekRankListResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetMemberWeekRankListV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) GetMemberRankVal(ctx context.Context, in *GetMemberRankValReq, opts ...grpc.CallOption) (*GetMemberRankValResp, error) {
	out := new(GetMemberRankValResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetMemberRankVal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) SettleYkwWeekRank(ctx context.Context, in *SettleYkwWeekRankReq, opts ...grpc.CallOption) (*SettleYkwWeekRankResp, error) {
	out := new(SettleYkwWeekRankResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/SettleYkwWeekRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) FixOnlineRankV2(ctx context.Context, in *FixOnlineRankV2Req, opts ...grpc.CallOption) (*FixOnlineRankV2Resp, error) {
	out := new(FixOnlineRankV2Resp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/FixOnlineRankV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) GetOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) GetOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/GetOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/ReplaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOnlineRankClient) TestAddPresentEvent(ctx context.Context, in *TestAddPresentEventReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/channelonlinerank.ChannelOnlineRank/TestAddPresentEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelOnlineRankServer is the server API for ChannelOnlineRank service.
type ChannelOnlineRankServer interface {
	GetMemberRankList(context.Context, *GetMemberRankListReq) (*GetMemberRankListResp, error)
	GetMemberWeekRankList(context.Context, *GetMemberWeekRankListReq) (*GetMemberWeekRankListResp, error)
	FixOnlineRank(context.Context, *FixOnlineRankReq) (*FixOnlineRankResp, error)
	// v2
	GetMemberRankListV2(context.Context, *GetMemberRankListReq) (*GetMemberRankListResp, error)
	GetMemberWeekRankListV2(context.Context, *GetMemberWeekRankListReq) (*GetMemberWeekRankListResp, error)
	GetMemberRankVal(context.Context, *GetMemberRankValReq) (*GetMemberRankValResp, error)
	SettleYkwWeekRank(context.Context, *SettleYkwWeekRankReq) (*SettleYkwWeekRankResp, error)
	FixOnlineRankV2(context.Context, *FixOnlineRankV2Req) (*FixOnlineRankV2Resp, error)
	GetOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	ReplaceOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	TestAddPresentEvent(context.Context, *TestAddPresentEventReq) (*Empty, error)
}

func RegisterChannelOnlineRankServer(s *grpc.Server, srv ChannelOnlineRankServer) {
	s.RegisterService(&_ChannelOnlineRank_serviceDesc, srv)
}

func _ChannelOnlineRank_GetMemberRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetMemberRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetMemberRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetMemberRankList(ctx, req.(*GetMemberRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_GetMemberWeekRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberWeekRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetMemberWeekRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetMemberWeekRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetMemberWeekRankList(ctx, req.(*GetMemberWeekRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_FixOnlineRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixOnlineRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).FixOnlineRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/FixOnlineRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).FixOnlineRank(ctx, req.(*FixOnlineRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_GetMemberRankListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetMemberRankListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetMemberRankListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetMemberRankListV2(ctx, req.(*GetMemberRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_GetMemberWeekRankListV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberWeekRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetMemberWeekRankListV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetMemberWeekRankListV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetMemberWeekRankListV2(ctx, req.(*GetMemberWeekRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_GetMemberRankVal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberRankValReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetMemberRankVal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetMemberRankVal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetMemberRankVal(ctx, req.(*GetMemberRankValReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_SettleYkwWeekRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettleYkwWeekRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).SettleYkwWeekRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/SettleYkwWeekRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).SettleYkwWeekRank(ctx, req.(*SettleYkwWeekRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_FixOnlineRankV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FixOnlineRankV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).FixOnlineRankV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/FixOnlineRankV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).FixOnlineRankV2(ctx, req.(*FixOnlineRankV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_GetOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_GetOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).GetOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/GetOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).GetOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_ReplaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).ReplaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/ReplaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).ReplaceOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOnlineRank_TestAddPresentEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestAddPresentEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOnlineRankServer).TestAddPresentEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelonlinerank.ChannelOnlineRank/TestAddPresentEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOnlineRankServer).TestAddPresentEvent(ctx, req.(*TestAddPresentEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOnlineRank_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelonlinerank.ChannelOnlineRank",
	HandlerType: (*ChannelOnlineRankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMemberRankList",
			Handler:    _ChannelOnlineRank_GetMemberRankList_Handler,
		},
		{
			MethodName: "GetMemberWeekRankList",
			Handler:    _ChannelOnlineRank_GetMemberWeekRankList_Handler,
		},
		{
			MethodName: "FixOnlineRank",
			Handler:    _ChannelOnlineRank_FixOnlineRank_Handler,
		},
		{
			MethodName: "GetMemberRankListV2",
			Handler:    _ChannelOnlineRank_GetMemberRankListV2_Handler,
		},
		{
			MethodName: "GetMemberWeekRankListV2",
			Handler:    _ChannelOnlineRank_GetMemberWeekRankListV2_Handler,
		},
		{
			MethodName: "GetMemberRankVal",
			Handler:    _ChannelOnlineRank_GetMemberRankVal_Handler,
		},
		{
			MethodName: "SettleYkwWeekRank",
			Handler:    _ChannelOnlineRank_SettleYkwWeekRank_Handler,
		},
		{
			MethodName: "FixOnlineRankV2",
			Handler:    _ChannelOnlineRank_FixOnlineRankV2_Handler,
		},
		{
			MethodName: "GetOrderCount",
			Handler:    _ChannelOnlineRank_GetOrderCount_Handler,
		},
		{
			MethodName: "GetOrderList",
			Handler:    _ChannelOnlineRank_GetOrderList_Handler,
		},
		{
			MethodName: "ReplaceOrder",
			Handler:    _ChannelOnlineRank_ReplaceOrder_Handler,
		},
		{
			MethodName: "TestAddPresentEvent",
			Handler:    _ChannelOnlineRank_TestAddPresentEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-online-rank/channel-online-rank.proto",
}

func init() {
	proto.RegisterFile("channel-online-rank/channel-online-rank.proto", fileDescriptor_channel_online_rank_a49a0fb6d6347f49)
}

var fileDescriptor_channel_online_rank_a49a0fb6d6347f49 = []byte{
	// 1751 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xdd, 0x72, 0x1a, 0xc9,
	0x15, 0x16, 0x20, 0x04, 0x1c, 0x01, 0x1a, 0xda, 0x92, 0x8d, 0x64, 0xc5, 0x56, 0x66, 0xbd, 0xb6,
	0xe2, 0xc4, 0x52, 0x56, 0xae, 0xbd, 0x49, 0xed, 0x8d, 0x8c, 0x90, 0x97, 0xac, 0x84, 0x54, 0x03,
	0xc2, 0xf1, 0x56, 0xaa, 0xa6, 0x06, 0xa6, 0x6d, 0x75, 0x34, 0xcc, 0x8c, 0xa7, 0x1b, 0xbc, 0xda,
	0xfb, 0xbc, 0x44, 0x2e, 0x92, 0x87, 0x49, 0x55, 0x2e, 0x36, 0xaf, 0x90, 0x27, 0xc8, 0x45, 0x9e,
	0x21, 0xd5, 0xa7, 0x1b, 0x18, 0x98, 0xd1, 0x4f, 0x6d, 0xd5, 0x5e, 0x89, 0x3e, 0xe7, 0xf4, 0x39,
	0xdd, 0xdf, 0x77, 0x7e, 0x7a, 0x04, 0xaf, 0x06, 0x97, 0x8e, 0xef, 0x53, 0xef, 0x55, 0xe0, 0x7b,
	0xcc, 0xa7, 0xaf, 0x22, 0xc7, 0xbf, 0xda, 0x4f, 0x91, 0xed, 0x85, 0x51, 0x20, 0x02, 0x52, 0xd3,
	0x2a, 0xa5, 0x91, 0x8a, 0xad, 0xa7, 0x11, 0x1d, 0x04, 0xfe, 0x80, 0x79, 0xf4, 0xd5, 0xf8, 0x60,
	0x3f, 0xbe, 0x50, 0x7b, 0xcc, 0xff, 0x65, 0x60, 0xfd, 0x2d, 0x15, 0xa7, 0x74, 0xd8, 0xa7, 0x91,
	0xe5, 0xf8, 0x57, 0x27, 0x8c, 0x0b, 0x8b, 0x7e, 0x22, 0x9b, 0x50, 0x0c, 0x42, 0x1a, 0xd9, 0x23,
	0xe6, 0xd6, 0x33, 0x3b, 0x99, 0xdd, 0x8a, 0x55, 0x90, 0xeb, 0x0b, 0xe6, 0x92, 0x5f, 0x01, 0xe8,
	0x48, 0x36, 0x73, 0xeb, 0x59, 0x54, 0x96, 0xb4, 0xa4, 0xe5, 0x92, 0x5f, 0x43, 0x79, 0xa2, 0x16,
	0xd7, 0x21, 0xad, 0xe7, 0xd0, 0x60, 0x55, 0xcb, 0xba, 0xd7, 0x21, 0x25, 0x2f, 0x60, 0x8d, 0xf9,
	0x63, 0xc6, 0x59, 0xdf, 0xa3, 0xf6, 0xd8, 0x61, 0x9e, 0x5b, 0x5f, 0xde, 0xc9, 0xec, 0x16, 0xad,
	0xea, 0x54, 0xdc, 0x93, 0x52, 0xf2, 0x1c, 0xd6, 0x18, 0xb7, 0x3d, 0x36, 0xa6, 0xb6, 0xde, 0x5f,
	0xcf, 0xa3, 0x61, 0x85, 0xf1, 0x13, 0x36, 0xa6, 0x0d, 0x25, 0x24, 0xeb, 0x90, 0xe7, 0xc2, 0x89,
	0x44, 0x7d, 0x65, 0x27, 0xb3, 0x9b, 0xb3, 0xd4, 0x82, 0x10, 0x58, 0xe6, 0x22, 0x08, 0xeb, 0x05,
	0x14, 0xe2, 0x6f, 0xf3, 0x6f, 0x59, 0xd8, 0x48, 0xb9, 0x30, 0x0f, 0x17, 0xae, 0x95, 0x59, 0xbc,
	0x56, 0x13, 0x56, 0x87, 0xb8, 0xc9, 0xf6, 0x18, 0x17, 0xf5, 0xec, 0x4e, 0x6e, 0x77, 0xf5, 0xe0,
	0xd9, 0x5e, 0x02, 0xf3, 0x3d, 0x7d, 0xa6, 0x59, 0x04, 0x0b, 0xd4, 0x46, 0x19, 0x49, 0xde, 0xc8,
	0xf1, 0x3c, 0x5b, 0xbb, 0xe2, 0xec, 0xc7, 0x09, 0x40, 0x15, 0xc7, 0xd3, 0x7b, 0x3a, 0xec, 0x47,
	0x4a, 0x8e, 0xa1, 0x3c, 0xbc, 0xb6, 0xa5, 0x43, 0x9b, 0xf9, 0x1f, 0x02, 0xc4, 0xe7, 0xfe, 0xf1,
	0xae, 0xe5, 0xdf, 0x96, 0xff, 0x21, 0x20, 0x8f, 0xa0, 0xe0, 0xda, 0x63, 0xc7, 0x1b, 0x51, 0x44,
	0xae, 0x62, 0xad, 0xb8, 0x3d, 0xb9, 0x92, 0xe0, 0xc8, 0xed, 0x88, 0x58, 0xc5, 0xc2, 0xdf, 0xe6,
	0x7f, 0x33, 0x60, 0x5c, 0x70, 0xe5, 0xe5, 0x94, 0x0a, 0xc7, 0x75, 0x84, 0x43, 0x0c, 0xc8, 0xcd,
	0x92, 0x40, 0xfe, 0x24, 0x5f, 0x42, 0xd5, 0x0f, 0xfa, 0xcc, 0x63, 0xe2, 0xda, 0xf6, 0xe8, 0x98,
	0x7a, 0x98, 0x04, 0x79, 0xab, 0x32, 0x91, 0x9e, 0x48, 0x21, 0xf9, 0x02, 0xa4, 0xc0, 0x66, 0x3e,
	0x57, 0x94, 0xe2, 0x45, 0x8b, 0x56, 0xd9, 0x0f, 0xfa, 0xad, 0x89, 0x4c, 0x1a, 0x89, 0x40, 0x38,
	0x9e, 0x3d, 0x08, 0x7c, 0x3e, 0x1a, 0x52, 0xbc, 0x68, 0xde, 0x2a, 0xa3, 0xb0, 0xa1, 0x64, 0xa4,
	0x0e, 0x85, 0x89, 0x3a, 0x8f, 0xea, 0xc9, 0x92, 0x3c, 0x86, 0xd2, 0xf5, 0xd5, 0x67, 0x7d, 0x8a,
	0x15, 0xd4, 0x15, 0xaf, 0xaf, 0x3e, 0xab, 0x03, 0x6c, 0x43, 0x49, 0xb0, 0x21, 0xe5, 0xc2, 0x19,
	0xaa, 0x24, 0xc8, 0x5b, 0x33, 0x81, 0xf9, 0x53, 0x16, 0x8c, 0x39, 0xec, 0x7a, 0x2c, 0x24, 0x26,
	0x54, 0x06, 0xa3, 0x28, 0x52, 0x0e, 0x67, 0x79, 0xb0, 0x2a, 0x85, 0xe8, 0xb4, 0x85, 0x49, 0x19,
	0xb3, 0xf1, 0x9d, 0x21, 0xc5, 0xfb, 0x97, 0xac, 0xca, 0xd4, 0xaa, 0xed, 0x0c, 0x29, 0xd9, 0x05,
	0x23, 0x66, 0xa7, 0x38, 0x50, 0x5c, 0x57, 0xa7, 0x86, 0x8a, 0x0b, 0x13, 0x2a, 0x3e, 0xfd, 0x41,
	0xcc, 0xa2, 0x2e, 0xab, 0xa8, 0x52, 0x18, 0x8b, 0x1a, 0xb3, 0xc1, 0xa8, 0x79, 0x15, 0x75, 0x6a,
	0x85, 0x51, 0xf7, 0x61, 0x3d, 0x66, 0x37, 0x64, 0xbe, 0x8e, 0xac, 0x78, 0xae, 0x4d, 0x8d, 0x4f,
	0x99, 0xaf, 0x82, 0x27, 0xd9, 0x2c, 0xa8, 0x84, 0x9c, 0x67, 0x73, 0x1b, 0x4a, 0xd3, 0xe2, 0xac,
	0x17, 0x91, 0xc9, 0x99, 0xc0, 0xfc, 0x47, 0x16, 0x6a, 0x89, 0x44, 0x4c, 0x49, 0x9d, 0x2a, 0x64,
	0x05, 0xd7, 0x3d, 0x23, 0x2b, 0xb8, 0x2c, 0x3a, 0xcc, 0xf1, 0x38, 0x3a, 0x25, 0x29, 0x51, 0x67,
	0x6b, 0x41, 0x75, 0xcc, 0xc2, 0x09, 0x2e, 0xb3, 0x3a, 0xf8, 0xe2, 0xae, 0x3a, 0xe8, 0xb1, 0xd0,
	0x2a, 0x8f, 0x59, 0xa8, 0xd0, 0x93, 0x85, 0x30, 0xc9, 0xf7, 0xfc, 0x2c, 0xdf, 0x65, 0xab, 0x8a,
	0x27, 0x9f, 0xc6, 0x68, 0x35, 0x96, 0x7b, 0x32, 0xc1, 0x46, 0xd3, 0x04, 0x53, 0xc0, 0x14, 0x47,
	0x93, 0x04, 0x7b, 0x09, 0x35, 0x3c, 0xfd, 0x9c, 0x93, 0x22, 0x1a, 0xad, 0x49, 0x45, 0x77, 0xe6,
	0xc8, 0xfc, 0x04, 0x6b, 0x16, 0xfd, 0x10, 0x51, 0x7e, 0xd9, 0x0d, 0xc2, 0xd7, 0x5d, 0x87, 0x23,
	0x3c, 0x83, 0x19, 0x3c, 0x03, 0x96, 0xec, 0x9d, 0xd9, 0x64, 0xef, 0xdc, 0x84, 0xe2, 0x88, 0xb9,
	0xaa, 0x09, 0xe5, 0x76, 0x72, 0xb2, 0x31, 0x8f, 0x98, 0x8b, 0xbd, 0x45, 0x81, 0xbb, 0x3c, 0x01,
	0xd7, 0xfc, 0x1e, 0x6a, 0x16, 0xf5, 0x02, 0xc7, 0xd5, 0x75, 0xf4, 0xf3, 0x83, 0x6a, 0x22, 0x73,
	0x53, 0x22, 0xcd, 0x7f, 0x65, 0xa0, 0xa6, 0xa0, 0xd6, 0xce, 0x11, 0xe4, 0x24, 0xe1, 0x4f, 0x61,
	0x55, 0xd7, 0xaa, 0x3d, 0xf0, 0x85, 0xf6, 0x0d, 0x5a, 0xd4, 0xf0, 0x45, 0x0a, 0xc5, 0xb9, 0x9f,
	0x4b, 0xf1, 0x6b, 0x78, 0xc8, 0xb8, 0xed, 0x8c, 0x44, 0x60, 0x5f, 0x32, 0xd7, 0xa5, 0xfe, 0x5c,
	0x53, 0x29, 0x5a, 0x0f, 0x18, 0x3f, 0x1c, 0x89, 0xe0, 0x5b, 0xd4, 0xe9, 0x63, 0x9b, 0x7f, 0xcd,
	0x40, 0x7d, 0x3a, 0x10, 0xde, 0x51, 0x7a, 0x15, 0x9f, 0x82, 0x49, 0xb0, 0xd6, 0x21, 0xdf, 0xa7,
	0x1f, 0x99, 0xaf, 0x6f, 0xa2, 0x16, 0x52, 0xea, 0xb1, 0x21, 0x13, 0x1a, 0x21, 0xb5, 0x98, 0xa0,
	0xb1, 0x3c, 0x43, 0x63, 0x13, 0x8a, 0x63, 0x46, 0x3f, 0x23, 0x14, 0x2a, 0x11, 0x0b, 0x72, 0xdd,
	0xf0, 0x85, 0xf9, 0x9f, 0x0c, 0x6c, 0xde, 0x70, 0x0e, 0x1e, 0x92, 0x3f, 0x42, 0x75, 0x3a, 0x0b,
	0x14, 0xf7, 0x99, 0x1b, 0x07, 0x50, 0x82, 0x16, 0xab, 0x1c, 0xe9, 0x71, 0x80, 0x69, 0xb2, 0x38,
	0x5a, 0xb2, 0x37, 0x8e, 0x96, 0xa4, 0xa7, 0x1b, 0x46, 0x4b, 0x2e, 0x75, 0xb4, 0x2c, 0xc7, 0x46,
	0xcb, 0x57, 0x60, 0x1c, 0xb3, 0x1f, 0xce, 0xd0, 0x37, 0x0e, 0x29, 0xfa, 0xe9, 0x8e, 0x89, 0x6b,
	0x3e, 0x80, 0xda, 0xc2, 0x16, 0x1e, 0x9a, 0xdf, 0xc0, 0xe3, 0xc6, 0x25, 0x1d, 0x5c, 0xe9, 0x54,
	0x50, 0x5a, 0xdd, 0x72, 0xee, 0x76, 0xf9, 0x04, 0xb6, 0x6f, 0xde, 0xcd, 0x43, 0xd3, 0x06, 0x32,
	0x17, 0xb2, 0x77, 0x20, 0x9d, 0xca, 0xf1, 0x43, 0x23, 0xe1, 0x30, 0x1f, 0x3d, 0x16, 0xad, 0xc9,
	0xf2, 0xae, 0xa7, 0x50, 0xb2, 0x6c, 0x36, 0xe0, 0x41, 0x22, 0x00, 0x0f, 0xcd, 0xdf, 0xc3, 0x7a,
	0x87, 0x0a, 0xe1, 0xd1, 0xf7, 0x57, 0x9f, 0x27, 0xdc, 0xdf, 0x1a, 0xd9, 0x7c, 0x04, 0x1b, 0x29,
	0x3b, 0x78, 0x68, 0xfe, 0x33, 0x03, 0x0f, 0xbb, 0x94, 0x8b, 0x43, 0xd7, 0x3d, 0x8f, 0x28, 0xa7,
	0xbe, 0x68, 0x8e, 0xa9, 0x3f, 0x7d, 0xd3, 0x45, 0x2e, 0x8d, 0x26, 0xd0, 0x94, 0xac, 0x02, 0xae,
	0x5b, 0x98, 0x98, 0x9c, 0xfa, 0x2e, 0x3e, 0xf7, 0xd4, 0x35, 0x0a, 0x72, 0x9d, 0x7c, 0xee, 0xe5,
	0x16, 0xef, 0xb8, 0x0e, 0xf9, 0x30, 0x62, 0x03, 0xaa, 0xd9, 0x56, 0x8b, 0x44, 0x4f, 0xc9, 0x27,
	0x7b, 0xca, 0x63, 0x28, 0x61, 0x48, 0x39, 0x91, 0x75, 0xe7, 0xc5, 0x33, 0x74, 0xd9, 0x90, 0x9a,
	0x05, 0xc8, 0x37, 0x87, 0xa1, 0xb8, 0x36, 0x8f, 0xe1, 0xc1, 0xdc, 0x73, 0xad, 0xe7, 0x78, 0xba,
	0x30, 0x17, 0x1a, 0xcd, 0xed, 0x54, 0x98, 0xff, 0x5e, 0x7c, 0xe8, 0xa2, 0x23, 0x1e, 0x92, 0x67,
	0xba, 0xb2, 0x06, 0xce, 0xe0, 0x52, 0x3e, 0x46, 0x3d, 0xed, 0x14, 0x6b, 0xa6, 0x21, 0x85, 0x3d,
	0xc7, 0x23, 0x5f, 0xc1, 0x46, 0xcc, 0x4a, 0x3e, 0xa4, 0x23, 0xcc, 0x7d, 0x1d, 0x88, 0x4c, 0x8d,
	0x2d, 0x54, 0xc9, 0x2d, 0x4f, 0x60, 0x15, 0xb7, 0xb8, 0x7d, 0x34, 0x8c, 0xcd, 0xb6, 0xa3, 0xbe,
	0xd4, 0xff, 0x01, 0x4a, 0x88, 0xbe, 0xac, 0xc9, 0xfa, 0x32, 0x56, 0xf3, 0x76, 0x4a, 0x0d, 0x9e,
	0x21, 0x43, 0xb2, 0xf6, 0x66, 0xe6, 0xe6, 0x4f, 0x19, 0x28, 0x4d, 0x15, 0xb7, 0xf1, 0xaa, 0x71,
	0xca, 0xce, 0x70, 0xd2, 0x2d, 0x2d, 0x37, 0x6b, 0x69, 0x4f, 0x41, 0x4d, 0x3c, 0x3b, 0xce, 0x23,
	0xa0, 0xe8, 0x1c, 0xc9, 0x94, 0x3d, 0x3c, 0xa2, 0x8e, 0xa0, 0x8a, 0xab, 0xbc, 0xee, 0xe1, 0x28,
	0x92, 0x6c, 0x91, 0x6f, 0x60, 0x85, 0x0b, 0x47, 0x8c, 0x38, 0xf2, 0x58, 0x4d, 0x7f, 0xa6, 0xaa,
	0x2e, 0xd2, 0x41, 0x3b, 0x99, 0x00, 0x96, 0xde, 0xf3, 0xd2, 0x87, 0x27, 0x73, 0xf5, 0xa8, 0xd9,
	0xe9, 0x04, 0xa3, 0x68, 0x40, 0x31, 0x55, 0xd6, 0x60, 0xf5, 0xa2, 0xdd, 0x39, 0x6f, 0x36, 0x5a,
	0xc7, 0xad, 0xe6, 0x91, 0xb1, 0x44, 0x2a, 0x50, 0xea, 0x34, 0xdb, 0x47, 0xf6, 0xdb, 0xd6, 0x71,
	0xd7, 0xc8, 0x10, 0x80, 0x95, 0xef, 0xda, 0xad, 0xb7, 0xdf, 0x76, 0x8d, 0x2c, 0x29, 0x43, 0xf1,
	0xb0, 0xd1, 0x6d, 0xf5, 0x5a, 0xdd, 0xf7, 0x46, 0x8e, 0x10, 0xa8, 0xbe, 0x6b, 0x5a, 0xcd, 0x77,
	0x67, 0x27, 0xc7, 0x76, 0xf7, 0x4d, 0xf3, 0xb0, 0x6d, 0x2c, 0xbf, 0x8c, 0xe0, 0xd1, 0x4d, 0x81,
	0x36, 0xa0, 0xd6, 0x6c, 0x5f, 0x9c, 0xda, 0xe8, 0xfc, 0xdc, 0x6a, 0x76, 0x9a, 0xed, 0xae, 0xb1,
	0x44, 0x6a, 0x50, 0x41, 0xf1, 0xd4, 0x71, 0x86, 0x3c, 0x04, 0x82, 0x22, 0x74, 0x6a, 0x37, 0xce,
	0xda, 0x9d, 0x8b, 0xd3, 0xa6, 0x91, 0x9d, 0x7a, 0x78, 0xff, 0xdd, 0x3b, 0xbb, 0xf9, 0xa7, 0xf3,
	0xb3, 0xce, 0x85, 0xd5, 0x34, 0x72, 0x2f, 0xbb, 0x50, 0x4b, 0x00, 0x40, 0xaa, 0x00, 0x68, 0x7b,
	0xd4, 0x3c, 0x39, 0x7c, 0x6f, 0x2c, 0x11, 0x03, 0xca, 0x2a, 0xcc, 0x9b, 0xc3, 0xf6, 0xd1, 0x59,
	0xdb, 0xc8, 0xc8, 0x8b, 0xa3, 0xa4, 0x71, 0x76, 0x7a, 0xda, 0xd2, 0xb7, 0x53, 0x26, 0x27, 0x27,
	0x06, 0x1c, 0xfc, 0xbd, 0x34, 0x7d, 0x75, 0xcd, 0x6e, 0x44, 0x2e, 0xa1, 0x96, 0xf8, 0xc2, 0x21,
	0x2f, 0x52, 0x28, 0x49, 0xfb, 0xf0, 0xdb, 0xda, 0xbd, 0x9f, 0x21, 0x0f, 0xcd, 0x25, 0x22, 0x62,
	0xdf, 0x52, 0xf1, 0x91, 0x45, 0x7e, 0x7b, 0x9b, 0x93, 0x85, 0x21, 0xbb, 0xf5, 0xbb, 0xfb, 0x1b,
	0x63, 0xd4, 0x3f, 0x43, 0x65, 0xae, 0x87, 0x92, 0xb4, 0xa7, 0xc2, 0xe2, 0xb0, 0xd9, 0x7a, 0x76,
	0xb7, 0x11, 0x7a, 0xff, 0xcb, 0x42, 0xc3, 0x91, 0x81, 0x7b, 0x07, 0xbf, 0x0c, 0x7e, 0x63, 0x78,
	0x94, 0x7a, 0xd1, 0xde, 0xc1, 0x2f, 0x8b, 0x20, 0x05, 0x63, 0xb1, 0x17, 0x92, 0xe7, 0x77, 0x9d,
	0x5b, 0x75, 0xde, 0xad, 0x17, 0xf7, 0xb2, 0xc3, 0x30, 0x97, 0x50, 0x4b, 0xcc, 0xa8, 0x54, 0x20,
	0xd3, 0x66, 0x5f, 0x2a, 0x90, 0xe9, 0x23, 0x6f, 0x89, 0xf4, 0x61, 0x6d, 0x61, 0xac, 0x92, 0x2f,
	0xef, 0xe2, 0x1b, 0x67, 0xfb, 0xd6, 0xf3, 0xfb, 0x98, 0x61, 0x8c, 0x23, 0xa8, 0xbc, 0xa5, 0x02,
	0xbb, 0x6e, 0x23, 0x18, 0xf9, 0x82, 0x6c, 0xee, 0x59, 0x93, 0x7f, 0xa8, 0xf4, 0x0e, 0xf6, 0x64,
	0x23, 0xb4, 0x1c, 0xff, 0x23, 0x95, 0x5e, 0x1f, 0xce, 0xa9, 0xd0, 0x5c, 0x7b, 0x39, 0x86, 0xf2,
	0xc4, 0x0b, 0x56, 0xca, 0x2d, 0x4e, 0xe6, 0x55, 0xaa, 0xdd, 0xbb, 0x7c, 0xe6, 0xc7, 0xa2, 0xa1,
	0xe7, 0x0c, 0x28, 0x2a, 0xc8, 0xf6, 0x9c, 0x71, 0x5c, 0x95, 0x3c, 0x0f, 0x4e, 0xd6, 0x69, 0x31,
	0x3d, 0x48, 0x79, 0x2d, 0x90, 0xdf, 0xa4, 0xc0, 0x92, 0xfe, 0xaa, 0xd8, 0xaa, 0xa7, 0x98, 0xaa,
	0xd9, 0xbd, 0xf4, 0xe6, 0xeb, 0xef, 0x5f, 0x7f, 0x0c, 0x3c, 0xc7, 0xff, 0xb8, 0xf7, 0xf5, 0x81,
	0x10, 0x7b, 0x83, 0x60, 0xb8, 0x8f, 0xff, 0x77, 0x1a, 0x04, 0xde, 0x3e, 0xa7, 0xd1, 0x98, 0x0d,
	0x28, 0xdf, 0x4f, 0x6c, 0xef, 0xaf, 0xa0, 0xd1, 0xeb, 0xff, 0x07, 0x00, 0x00, 0xff, 0xff, 0x72,
	0x9b, 0xab, 0x3f, 0x01, 0x13, 0x00, 0x00,
}
