apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-muse-play-sing-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - testing-apiv2.ttyuyin.com
  http:
  - match:
    - uri:
        prefix: /ga.api.muse_play_sing.MusePlaySingLogic/
    route:
    - destination:
        host: muse-play-sing-logic.quicksilver.svc.cluster.local
        port:
          number: 80
