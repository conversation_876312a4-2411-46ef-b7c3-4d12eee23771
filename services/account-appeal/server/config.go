package server

import (
	"fmt"
	"golang.52tt.com/pkg/config"
)

type ServiceConfigT struct {
	mysqlConfig *config.MysqlConfig
}

func (sc *ServiceConfigT) Parse(configer config.Configer) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	sc.mysqlConfig = config.NewMysqlConfigWithSection(configer, "mysql")
	return
}


func (sc *ServiceConfigT) GetMysqlConnectionString() string{
	return sc.mysqlConfig.ConnectionString()
}