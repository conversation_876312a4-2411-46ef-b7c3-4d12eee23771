// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\griffin\clients\smash-egg-notify\iclient.go

// Package smash_egg_notify is a generated GoMock package.
package smash_egg_notify

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	smash_egg_notify "golang.52tt.com/protocol/services/smash-egg-notify"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddSmashLightEffects mocks base method.
func (m *MockIClient) AddSmashLightEffects(ctx context.Context, req *smash_egg_notify.AddSmashLightEffectsReq) (*smash_egg_notify.AddSmashLightEffectsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSmashLightEffects", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.AddSmashLightEffectsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSmashLightEffects indicates an expected call of AddSmashLightEffects.
func (mr *MockIClientMockRecorder) AddSmashLightEffects(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSmashLightEffects", reflect.TypeOf((*MockIClient)(nil).AddSmashLightEffects), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelLightEffectByPackId mocks base method.
func (m *MockIClient) DelLightEffectByPackId(ctx context.Context, req *smash_egg_notify.DelLightEffectByPackIdReq) (*smash_egg_notify.DelLightEffectByPackIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelLightEffectByPackId", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.DelLightEffectByPackIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelLightEffectByPackId indicates an expected call of DelLightEffectByPackId.
func (mr *MockIClientMockRecorder) DelLightEffectByPackId(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelLightEffectByPackId", reflect.TypeOf((*MockIClient)(nil).DelLightEffectByPackId), ctx, req)
}

// GetAllSmashLightEffects mocks base method.
func (m *MockIClient) GetAllSmashLightEffects(ctx context.Context, req *smash_egg_notify.GetAllSmashLightEffectsReq) (*smash_egg_notify.GetAllSmashLightEffectsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSmashLightEffects", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.GetAllSmashLightEffectsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSmashLightEffects indicates an expected call of GetAllSmashLightEffects.
func (mr *MockIClientMockRecorder) GetAllSmashLightEffects(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSmashLightEffects", reflect.TypeOf((*MockIClient)(nil).GetAllSmashLightEffects), ctx, req)
}

// GetAllSmashPropConfig mocks base method.
func (m *MockIClient) GetAllSmashPropConfig(ctx context.Context, req *smash_egg_notify.GetAllSmashPropConfigReq) (*smash_egg_notify.GetAllSmashPropConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllSmashPropConfig", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.GetAllSmashPropConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSmashPropConfig indicates an expected call of GetAllSmashPropConfig.
func (mr *MockIClientMockRecorder) GetAllSmashPropConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSmashPropConfig", reflect.TypeOf((*MockIClient)(nil).GetAllSmashPropConfig), ctx, req)
}

// GetCurrentNotify mocks base method.
func (m *MockIClient) GetCurrentNotify(ctx context.Context, req *smash_egg_notify.GetCurrentNotifyReq) (*smash_egg_notify.GetCurrentNotifyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentNotify", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.GetCurrentNotifyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCurrentNotify indicates an expected call of GetCurrentNotify.
func (mr *MockIClientMockRecorder) GetCurrentNotify(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentNotify", reflect.TypeOf((*MockIClient)(nil).GetCurrentNotify), ctx, req)
}

// GetLightEffectInfoByPackId mocks base method.
func (m *MockIClient) GetLightEffectInfoByPackId(ctx context.Context, packId uint32) (*smash_egg_notify.GetLightEffectInfoByPackIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLightEffectInfoByPackId", ctx, packId)
	ret0, _ := ret[0].(*smash_egg_notify.GetLightEffectInfoByPackIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLightEffectInfoByPackId indicates an expected call of GetLightEffectInfoByPackId.
func (mr *MockIClientMockRecorder) GetLightEffectInfoByPackId(ctx, packId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLightEffectInfoByPackId", reflect.TypeOf((*MockIClient)(nil).GetLightEffectInfoByPackId), ctx, packId)
}

// GetNotify mocks base method.
func (m *MockIClient) GetNotify(ctx context.Context, notifyType uint32) (*smash_egg_notify.GetNotifyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotify", ctx, notifyType)
	ret0, _ := ret[0].(*smash_egg_notify.GetNotifyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetNotify indicates an expected call of GetNotify.
func (mr *MockIClientMockRecorder) GetNotify(ctx, notifyType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotify", reflect.TypeOf((*MockIClient)(nil).GetNotify), ctx, notifyType)
}

// GetSmashActivityVisionConfigById mocks base method.
func (m *MockIClient) GetSmashActivityVisionConfigById(ctx context.Context, req *smash_egg_notify.GetSmashActivityVisionConfigByIdReq) (*smash_egg_notify.GetSmashActivityVisionConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSmashActivityVisionConfigById", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.GetSmashActivityVisionConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSmashActivityVisionConfigById indicates an expected call of GetSmashActivityVisionConfigById.
func (mr *MockIClientMockRecorder) GetSmashActivityVisionConfigById(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSmashActivityVisionConfigById", reflect.TypeOf((*MockIClient)(nil).GetSmashActivityVisionConfigById), ctx, req)
}

// GetSmashPropConfigById mocks base method.
func (m *MockIClient) GetSmashPropConfigById(ctx context.Context, req *smash_egg_notify.GetSmashPropConfigByIdReq) (*smash_egg_notify.GetSmashPropConfigByIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSmashPropConfigById", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.GetSmashPropConfigByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSmashPropConfigById indicates an expected call of GetSmashPropConfigById.
func (mr *MockIClientMockRecorder) GetSmashPropConfigById(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSmashPropConfigById", reflect.TypeOf((*MockIClient)(nil).GetSmashPropConfigById), ctx, req)
}

// SetNotify mocks base method.
func (m *MockIClient) SetNotify(ctx context.Context, req *smash_egg_notify.SetNotifyReq) (*smash_egg_notify.SetNotifyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNotify", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.SetNotifyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetNotify indicates an expected call of SetNotify.
func (mr *MockIClientMockRecorder) SetNotify(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNotify", reflect.TypeOf((*MockIClient)(nil).SetNotify), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateSmashLightEffect mocks base method.
func (m *MockIClient) UpdateSmashLightEffect(ctx context.Context, req *smash_egg_notify.UpdateSmashLightEffectReq) (*smash_egg_notify.UpdateSmashLightEffectResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSmashLightEffect", ctx, req)
	ret0, _ := ret[0].(*smash_egg_notify.UpdateSmashLightEffectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSmashLightEffect indicates an expected call of UpdateSmashLightEffect.
func (mr *MockIClientMockRecorder) UpdateSmashLightEffect(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSmashLightEffect", reflect.TypeOf((*MockIClient)(nil).UpdateSmashLightEffect), ctx, req)
}
