package mgr

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	app_pb "golang.52tt.com/protocol/app/pia"
	pb "golang.52tt.com/protocol/services/pia"
)

func Test_convertor_BgmStatusToLogic(t *testing.T) {
	curTime := time.Now()
	type args struct {
		info *BgmProgress
	}
	tests := []struct {
		name string
		args args
		want *app_pb.DramaBgmStatus
	}{
		{
			name: "正常",
			args: args{
				info: &BgmProgress{
					ID:        "123",
					Version:   123,
					Progress:  123,
					Status:    BgmStatusPlaying,
					StartTime: curTime,
					ChannelID: 123,
					OperationSnapshot: []*TimeLineElement{
						{
							Id:        "0",
							Status:    1,
							BeginTime: 123,
							EndTime:   0,
							Index:     0,
						},
					},
				},
			},
			want: &app_pb.DramaBgmStatus{
				BgmId:         "123",
				BgmPhase:      app_pb.DramaBGMPhase_DRAMA_BGM_PHASE_PLAY,
				BgmProgress:   123,
				Version:       123,
				OperationTime: 123,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.BgmStatusToLogic(tt.args.info), "BgmStatusToLogic(%v)", tt.args.info)
		})
	}
}

func Test_convertor_BgmStatusToPB(t *testing.T) {
	curTime := time.Now()
	type args struct {
		info *BgmProgress
	}
	tests := []struct {
		name string
		args args
		want *pb.DramaBgmStatus
	}{
		{
			name: "正常",
			args: args{
				info: &BgmProgress{
					ID:        "123",
					Version:   123,
					Progress:  123,
					Status:    BgmStatusPlaying,
					StartTime: curTime,
					ChannelID: 123,
					OperationSnapshot: []*TimeLineElement{
						{
							Id:        "0",
							Status:    1,
							BeginTime: 123,
							EndTime:   0,
							Index:     0,
						},
					},
				},
			},
			want: &pb.DramaBgmStatus{
				BgmId:         "123",
				BgmPhase:      pb.DramaBGMPhase_DRAMA_BGM_PHASE_PLAY,
				BgmProgress:   123,
				Version:       123,
				OperationTime: 123,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.BgmStatusToPB(tt.args.info), "BgmStatusToPB(%v)", tt.args.info)
		})
	}
}

func Test_convertor_ChannelDramaStatusToLogic(t *testing.T) {
	// todo: 补全
	type args struct {
		info *ChannelRunningDramaInfo
	}
	startTime := time.Now()
	tests := []struct {
		name string
		args args
		want *app_pb.ChannelDramaStatus
	}{
		{
			name: "正常",
			args: args{
				info: &ChannelRunningDramaInfo{
					ChannelId:     0,
					DramaID:       0,
					DramaType:     0,
					StartTime:     startTime,
					RunningStatus: 0,
					DramaRawInfo: &DramaDomainEntity{
						Info:   nil,
						Detail: nil,
					},
					CurContentIndex:  0,
					CurContentOffset: 0,
					Version:          0,
					SelectTime:       time.Time{},
					ContentDuration:  nil,
				},
			},
			want: &app_pb.ChannelDramaStatus{
				DramaPhase: 0,
				ChannelId:  0,
				DramaInfo: &app_pb.DramaV2{
					DramaSubInfo: nil,
					RoleList:     nil,
					ContentList:  nil,
					BgmUrl:       nil,
					PictureList:  nil,
					PlayType:     0,
				},
				Version: 0,
				Progress: &app_pb.ChannelDramaProgress{
					CurIndex:   0,
					TimeOffset: 0,
				},
				StartTime: startTime.Unix(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.ChannelDramaStatusToLogic(tt.args.info), "ChannelDramaStatusToLogic(%v)", tt.args.info)
		})
	}
}

func Test_convertor_ChannelMicRoleBindingInfoToPB(t *testing.T) {
	type args struct {
		info *ChannelMicRoleBindingInfo
	}
	tests := []struct {
		name string
		args args
		want *pb.MicRoleMap
	}{
		{
			name: "正常",
			args: args{
				info: &ChannelMicRoleBindingInfo{
					ChannelId: 123,
					Version:   123,
					BindingList: map[uint32]*MicRoleBindingInfo{
						1: {
							MicIndex: 1,
							RoleIDs:  []string{"123"},
							JoinTime: 123,
						},
						0: {
							MicIndex: 0,
							RoleIDs:  []string{},
							JoinTime: 123,
						},
					},
				},
			},
			want: &pb.MicRoleMap{
				Map: map[uint32]*pb.MicRoleMap_RoleInfoList{
					0: {
						Id:       []string{},
						JoinTime: 123,
					},
					1: {
						Id:       []string{"123"},
						JoinTime: 123,
					},
				},
				Version: 123,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.ChannelMicRoleBindingInfoToPB(tt.args.info), "ChannelMicRoleBindingInfoToPB(%v)", tt.args.info)
		})
	}
}

func Test_convertor_ChannelRunningDramaInfoToPB(t *testing.T) {
	// todo
	t.Skip()
	type args struct {
		info *ChannelPlayingDramaInfo
	}
	tests := []struct {
		name string
		args args
		want *pb.ChannelDramaStatus
	}{
		{
			name: "正常",
			args: args{
				info: &ChannelPlayingDramaInfo{},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.Equalf(t, tt.want, c.ChannelRunningDramaInfoToPB(tt.args.info), "ChannelRunningDramaInfoToPB(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToLogicBgmList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*app_pb.PiaBGM
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					BGMList: []*BGMV2{
						{
							ID:        "123",
							Name:      "123",
							URL:       "123",
							BeginTime: 123,
							EndTime:   123,
						},
						{
							ID:        "321",
							Name:      "321",
							URL:       "321",
							BeginTime: 321,
							EndTime:   321,
						},
					},
				},
			},
			want: []*app_pb.PiaBGM{
				{
					Id:   "123",
					Name: "123",
					Url:  "123",
					Duration: &app_pb.PiaDuration{
						BeginTime: 123,
						EndTime:   123,
					},
				},
				{
					Id:   "321",
					Name: "321",
					Url:  "321",
					Duration: &app_pb.PiaDuration{
						BeginTime: 321,
						EndTime:   321,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			list := c.DramaInfoToLogicBgmList(tt.args.info)
			for i, item := range tt.want {
				assert.EqualValuesf(t, item, list[i], "DramaInfoToLogicBgmList(%v)", tt.args.info)
			}
		})
	}
}

func Test_convertor_DramaInfoToLogicContentList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*app_pb.PiaContent
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					ContentList: []*Content{
						{
							ID:        "qwe",
							RoleID:    "123",
							Dialogue:  "123",
							BeginTime: 123,
							EndTime:   123,
							RoleName:  "543",
						},
					},
				},
			},
			want: []*app_pb.PiaContent{
				{
					Id:       "qwe",
					RoleId:   "123",
					Dialogue: "123",
					Duration: &app_pb.PiaDuration{
						BeginTime: 123,
						EndTime:   123,
					},
					RoleName: "543",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToLogicContentList(tt.args.info), "DramaInfoToLogicContentList(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToLogicDrama(t *testing.T) {
	// todo
	t.Skip()
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want *app_pb.DramaV2
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					ID:            0,
					SearchID:      "",
					Title:         "",
					Source:        "",
					Author:        "",
					Type:          "",
					Authorization: "",
					HistoricalBg:  "",
					Tags:          nil,
					CoverUrl:      "",
					Desc:          "",
					ContentList:   nil,
					BGMList:       nil,
					MaleCnt:       0,
					FemaleCnt:     0,
					RoleList:      nil,
					PictureList:   nil,
					WordCnt:       0,
					PlayType:      0,
					IsFreeze:      false,
					IsPrivate:     false,
					CreateUserId:  0,
					UpdateUserId:  0,
					CreateTime:    0,
					UpdateTime:    0,
					Deleted:       false,
				},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToLogicDrama(tt.args.info), "DramaInfoToLogicDrama(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToLogicPictureList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*app_pb.PiaPicture
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					PictureList: []*Picture{
						{
							ID:        "123",
							Name:      "456",
							URL:       "qwer",
							BeginTime: 123,
							EndTime:   123,
						},
					},
				},
			},
			want: []*app_pb.PiaPicture{
				{
					Id:  "123",
					Url: "qwer",
					Duration: &app_pb.PiaDuration{
						BeginTime: 123,
						EndTime:   123,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToLogicPictureList(tt.args.info), "DramaInfoToLogicPictureList(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToLogicRoleList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*app_pb.PiaRole
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					RoleList: []*DramaRole{
						{
							RoleID:       "123",
							RoleName:     "zcxv",
							Sex:          1,
							Avatar:       "ytrr",
							Introduction: "hgffd",
							Color:        "gafsf",
						},
					},
				},
			},
			want: []*app_pb.PiaRole{
				{
					Id:           "123",
					Name:         "zcxv",
					Sex:          1,
					Avatar:       "ytrr",
					Introduction: "hgffd",
					Color:        "gafsf",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToLogicRoleList(tt.args.info), "DramaInfoToLogicRoleList(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToLogicSubInfo(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want *app_pb.DramaSubInfo
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					ID:        123,
					Title:     "123",
					CoverUrl:  "123",
					Author:    "123",
					Desc:      "123",
					MaleCnt:   123,
					FemaleCnt: 23,
					WordCnt:   123,
					Tags:      []string{"123"},
					Type:      "123",
				},
			},
			want: &app_pb.DramaSubInfo{
				Id:        123,
				Title:     "123",
				CoverUrl:  "123",
				Author:    "123",
				Desc:      "123",
				MaleCnt:   123,
				FemaleCnt: 23,
				WordCnt:   123,
				TagList:   []string{"123"},
				Type:      "123",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToLogicSubInfo(tt.args.info), "DramaInfoToLogicSubInfo(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToPBBgmList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*pb.PiaBGM
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					BGMList: []*BGMV2{
						{
							ID:        "123",
							Name:      "123",
							URL:       "123",
							BeginTime: 123,
							EndTime:   123,
						},
						{
							ID:        "321",
							Name:      "321",
							URL:       "321",
							BeginTime: 321,
							EndTime:   321,
						},
					},
				},
			},
			want: []*pb.PiaBGM{
				{
					Id:   "123",
					Name: "123",
					Url:  "123",
					Duration: &pb.PiaDuration{
						BeginTime: 123,
						EndTime:   123,
					},
				},
				{
					Id:   "321",
					Name: "321",
					Url:  "321",
					Duration: &pb.PiaDuration{
						BeginTime: 321,
						EndTime:   321,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			list := c.DramaInfoToPBBgmList(tt.args.info)
			for i, item := range tt.want {
				assert.EqualValuesf(t, item, list[i], "DramaInfoToPBBgmList(%v)", tt.args.info)
			}
		})
	}
}

func Test_convertor_DramaInfoToPBContentList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*pb.PiaContent
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					ContentList: []*Content{
						{
							ID:        "qwe",
							RoleID:    "123",
							Dialogue:  "123",
							BeginTime: 123,
							EndTime:   123,
							RoleName:  "543",
						},
					},
				},
			},
			want: []*pb.PiaContent{
				{
					Id:       "qwe",
					RoleId:   "123",
					Dialogue: "123",
					Duration: &pb.PiaDuration{
						BeginTime: 123,
						EndTime:   123,
					},
					RoleName: "543",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToPBContentList(tt.args.info), "DramaInfoToPBContentList(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToPBDrama(t *testing.T) {
	// todo
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want *pb.DramaV2
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToPBDrama(tt.args.info), "DramaInfoToPBDrama(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToPBPictureList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*pb.PiaPicture
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					PictureList: []*Picture{
						{
							ID:        "123",
							Name:      "456",
							URL:       "qwer",
							BeginTime: 123,
							EndTime:   123,
						},
					},
				},
			},
			want: []*pb.PiaPicture{
				{
					Id:  "123",
					Url: "qwer",
					Duration: &pb.PiaDuration{
						BeginTime: 123,
						EndTime:   123,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToPBPictureList(tt.args.info), "DramaInfoToPBPictureList(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToPBRoleList(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want []*pb.PiaRole
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					RoleList: []*DramaRole{
						{
							RoleID:       "123",
							RoleName:     "zcxv",
							Sex:          1,
							Avatar:       "ytrr",
							Introduction: "hgffd",
							Color:        "gafsf",
						},
					},
				},
			},
			want: []*pb.PiaRole{
				{
					Id:           "123",
					Name:         "zcxv",
					Sex:          1,
					Avatar:       "ytrr",
					Introduction: "hgffd",
					Color:        "gafsf",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToPBRoleList(tt.args.info), "DramaInfoToPBRoleList(%v)", tt.args.info)
		})
	}
}

func Test_convertor_DramaInfoToPBSubInfo(t *testing.T) {
	type args struct {
		info *DramaEntityV2
	}
	tests := []struct {
		name string
		args args
		want *pb.DramaSubInfo
	}{
		{
			name: "正常",
			args: args{
				info: &DramaEntityV2{
					ID:        123,
					Title:     "123",
					CoverUrl:  "123",
					Author:    "123",
					Desc:      "123",
					MaleCnt:   123,
					FemaleCnt: 23,
					WordCnt:   123,
					Tags:      []string{"123"},
					Type:      "123",
				},
			},
			want: &pb.DramaSubInfo{
				Id:        123,
				Title:     "123",
				CoverUrl:  "123",
				Author:    "123",
				Desc:      "123",
				MaleCnt:   123,
				FemaleCnt: 23,
				WordCnt:   123,
				TagList:   []string{"123"},
				Type:      "123",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.EqualValuesf(t, tt.want, c.DramaInfoToPBSubInfo(tt.args.info), "DramaInfoToPBSubInfo(%v)", tt.args.info)
		})
	}
}

func Test_convertor_BgmVolStatusToLogic(t *testing.T) {
	type args struct {
		info *BgmVolSetting
	}
	tests := []struct {
		name string
		args args
		want *app_pb.DramaBgmVolStatus
	}{
		{
			name: "正常",
			args: args{
				info: &BgmVolSetting{
					Vol:       100,
					ChannelID: 112,
				},
			},
			want: &app_pb.DramaBgmVolStatus{
				Vol: 100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.Equalf(t, tt.want.Vol, c.BgmVolStatusToLogic(tt.args.info).Vol, "BgmVolStatusToLogic(%v)", tt.args.info)
		})
	}
}

func Test_convertor_BgmVolStatusToPB(t *testing.T) {
	type args struct {
		info *BgmVolSetting
	}
	tests := []struct {
		name string
		args args
		want *pb.DramaBgmVolStatus
	}{
		{
			name: "正常",
			args: args{
				info: &BgmVolSetting{
					Vol:       100,
					ChannelID: 123,
				},
			},
			want: &pb.DramaBgmVolStatus{
				Vol: 100,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &convertor{}
			assert.Equalf(t, tt.want.Vol, c.BgmVolStatusToPB(tt.args.info).Vol, "BgmVolStatusToPB(%v)", tt.args.info)
		})
	}
}
