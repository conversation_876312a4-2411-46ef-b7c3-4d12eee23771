package conf

//go:generate quicksilver-cli test interface ../conf
//go:generate mockgen -destination=./mocks/business_config.go -package=mocks golang.52tt.com/services/tt-rev-common-logic/internal/conf IBusinessConfManager

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"context"
	"golang.52tt.com/pkg/marketid_helper"
	"strconv"
	"strings"
	"golang.52tt.com/pkg/protocol"
)

const (
	BusinessConfPath = "/data/oss/conf-center/tt/"
	BusinessConfFile = "tt-rev-common-logic.json"
)

var LastConfMd5Sum [md5.Size]byte

type BusinessConf struct {
	// 首充活动入口图片配置
	EntryImgList    []*FirstRechargeEntryInfo `json:"entry_img_list"`
	EntryExtendDesc *FirstRechargeEntryText   `json:"entry_extend_desc"`
	FirstRechargeLocalCacheSec uint32         `json:"first_recharge_local_cache_sec"` // 首充活动本地缓存时间，单位秒
}

type FirstRechargeEntryText struct {
	// 固定格式：自定义文案 + 礼物图标 + 礼物名称；文案不存在时，不展示
	Desc     string `json:"desc"`      // 自定义文案
	GiftUrl  string `json:"gift_url"`  // 首充礼物图标
	GiftName string `json:"gift_name"` // 首充礼物名称
}

type FirstRechargeEntryInfo struct {
	LocateType uint32 `json:"locate_type"` // 1: 房间内入口 2: 房间底部礼物架内入口 3: IM页底部 4: 礼物架中入口
	EntryImg   string `json:"entry_img"`   // 显示的入口图片
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	err = c.CheckConf()
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}
	//mutex sync.RWMutex
	conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	businessConfFilePath := BusinessConfPath + BusinessConfFile
	if devBusinessConfPath := os.Getenv("DEV_BUSINESS_CONF_PATH"); devBusinessConfPath != "" {
		businessConfFilePath = devBusinessConfPath + BusinessConfFile
	}
	_, err := businessConf.Parse(businessConfFilePath)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf: businessConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(businessConfFilePath)

	return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		//bm.mutex.Lock()
		bm.conf = businessConf
		//bm.mutex.Unlock()

		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}

func (c *BusinessConf) CheckConf() error {
	return nil
}

// GetEntryImgInfo 获取活动入口图片配置
func (bm *BusinessConfManager) GetEntryImgInfo() []*FirstRechargeEntryInfo {
	return bm.conf.EntryImgList
}

func (bm *BusinessConfManager) GetEntryExtendDesc() *FirstRechargeEntryText {
	if bm.conf.EntryExtendDesc == nil {
		return &FirstRechargeEntryText{}
	}
	return bm.conf.EntryExtendDesc
}

// CheckWeekCardFeatureVersion 周卡版本+马甲包限制检查
func (bm *BusinessConfManager) CheckWeekCardFeatureVersion(ctx context.Context, clientType, clientVersion, markerId uint32) bool {
	// 通过马甲包获取最低可见入口的版本号
	minVersion := marketid_helper.Get("present_week_card_entry_min_version", markerId, clientType)
	if version, ok := parseCliVersion(minVersion); !ok ||
		protocol.ClientVersion(clientVersion) < version {

		// 未达到指定客户端版本，不可见入口
		log.DebugWithCtx(ctx, "checkIfFeatureVersion fail. clientType:%d, clientVersion:%d, markerId:%d, minVersion:%s", clientType, clientVersion, markerId, minVersion)
		return false
	}

	return true
}

func parseCliVersion(version string) (protocol.ClientVersion, bool) {
	subStrings := strings.Split(version, ".")
	subVers := make([]uint16, 0)
	for _, subString := range subStrings {
		t, err := strconv.ParseUint(subString, 10, 32)
		if err != nil {
			log.Debugf("parse version fail, version: %s, err: %v", version, err)
			continue
		}
		subVers = append(subVers, uint16(t))
	}
	if 3 != len(subVers) {
		return protocol.ClientVersion(0), false
	}

	iVersion := protocol.ClientVersion(protocol.FormatClientVersion(uint8(subVers[0]), uint8(subVers[1]), subVers[2]))
	return iVersion, true
}

func (bm *BusinessConfManager) GetFirstRechargeLocalCacheSec() uint32 {
	return bm.conf.FirstRechargeLocalCacheSec
}