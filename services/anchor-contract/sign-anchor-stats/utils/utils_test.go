package utils

/*
import (
	"strconv"
	"testing"
	"time"
)

// go test -timeout 30s -run ^TestXx$ golang.52tt.com/services/anchor-contract/sign-anchor-stats/utils -v -count=1
func TestXx(t *testing.T) {

	t.Log(GetRateV2(100, 2, -2))
	return

	tx := time.Date(2023, 7, 31, 0, 0, 0, 0, time.Local)
	t.Log(GetWeekMondayZeroTime(tx))
	t.Log(GetWeekMondayZeroTime(time.Now()))
	return

	t.Log(GetRate(1, 3, -2))
	t.Log(GetRate(3, 3, -2))
	return

	t.Log(SecToTimeStr(0))
	t.Log(SecToTimeStr(61))
	t.Log(SecToTimeStr(610))
	t.Log(SecToTimeStr(3600))
	t.Log(SecToTimeStr(3660))
	t.Log(SecToTimeStr(0.5 * 3600))
	return

	t.Log(GetMonthTime(time.Now().AddDate(0, -1, 0)))
	return

	tm, _ := time.ParseInLocation("2006-01-02 15:04:05", "2022-11-21 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	tm, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-11-22 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	tm, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-11-23 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	tm, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-11-24 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	tm, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-11-25 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	tm, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-11-26 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	tm, _ = time.ParseInLocation("2006-01-02 15:04:05", "2022-11-27 08:54:22", time.Local)
	t.Log(CalcWeekDurtion(tm))

	return

	t.Log(TimeStr(0))
	t.Log(TimeStr(60))
	t.Log(TimeStr(61))
	t.Log(TimeStr(121))
	t.Log(TimeStr(3600))
	t.Log(TimeStr(3661))
	t.Log(TimeStr(7495))

	f := float64(12345) / 10000
	s := strconv.FormatFloat(f, 'f', 4, 64)
	t.Log(s)
}
*/
