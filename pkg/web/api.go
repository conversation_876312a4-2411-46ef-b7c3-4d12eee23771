package web

import (
	"time"

	"golang.52tt.com/protocol/common/status"

	"encoding/json"
	"net/http"

	"github.com/astaxie/beego"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
)

type JsonResponse struct {
	Code       int32       `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg        string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	ServerTime uint32      `protobuf:"varint,3,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	Data       interface{} `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
}

func ServeGameErrReq(w http.ResponseWriter, code int32, msg string) {
	ServeAPICodeJson(w, code, msg, nil)
}

func ServeGameBadReq(w http.ResponseWriter, code int32) {
	ServeAPICodeJson(w, code, "req param err", nil)
}

func ServeAPICodeJson(w http.ResponseWriter, code int32, msg string, data proto.MessageV1) error {
	rsp := &JsonResponse{
		Code: code, Msg: msg, ServerTime: uint32(time.Now().Unix()), Data: data,
	}
	enc := json.NewEncoder(w)
	if beego.BConfig.RunMode != beego.PROD {
		enc.SetIndent("", "\t")
	}
	return enc.Encode(rsp)
}

func ServeAPICodeJsonV2(w http.ResponseWriter, code int32, msg string, data interface{}) error {
	rsp := &JsonResponse{
		Code: code, Msg: msg, ServerTime: uint32(time.Now().Unix()), Data: data,
	}
	enc := json.NewEncoder(w)
	if beego.BConfig.RunMode != beego.PROD {
		enc.SetIndent("", "\t")
	}
	return enc.Encode(rsp)
}

func ServeAPIJson(w http.ResponseWriter, data proto.MessageV1) error {
	w.Header().Add("Content-Type", "application/json; charset=utf-8")
	rsp := &JsonResponse{
		Code: 0, Msg: "OK", ServerTime: uint32(time.Now().Unix()), Data: data,
	}
	enc := json.NewEncoder(w)
	if beego.BConfig.RunMode != beego.PROD {
		enc.SetIndent("", "\t")
	}
	return enc.Encode(rsp)
}

func ServeAPIJsonV2(w http.ResponseWriter, data interface{}) error {
	w.Header().Add("Content-Type", "application/json; charset=utf-8")
	rsp := &JsonResponse{
		Code: 0, Msg: "OK", ServerTime: uint32(time.Now().Unix()), Data: data,
	}
	enc := json.NewEncoder(w)
	if beego.BConfig.RunMode != beego.PROD {
		enc.SetIndent("", "\t")
	}
	return enc.Encode(rsp)
}

func NewAuth(authVerify AuthVerify, valicToken bool) *AuthT {
	return &AuthT{authVerify: authVerify, valicToken: valicToken}
}

type AuthT struct {
	valicToken bool
	authVerify AuthVerify
}

func (a *AuthT) SetValieToken(valicToken bool) {
	a.valicToken = valicToken
}

func (a *AuthT) AuthCheck(valicToken bool, w http.ResponseWriter, r *http.Request) (*AuthInfo, bool) {
	if valicToken {
		return a.authVerify.TokenBasedAuth(w, r, unauthorized)
	} else {
		return a.authVerify.UidBasedAuth(w, r)
	}
}

func unauthorized(w http.ResponseWriter, code int, message ...string) error {
	switch code {
	case 0:
		ServeNotAuth(w)
	default:
		rsp := &JsonResponse{
			Code: int32(code), ServerTime: uint32(time.Now().Unix()),
		}
		if len(message) > 0 {
			if msg := message[0]; len(msg) > 0 {
				rsp.Msg = msg
			} else {
				rsp.Msg = status.MessageFromCode(code)
			}
		}
		enc := json.NewEncoder(w)
		return enc.Encode(rsp)
	}
	return nil
}

func ServeAPIError(w http.ResponseWriter) {
	w.WriteHeader(http.StatusInternalServerError)
}

func ServeBadReq(w http.ResponseWriter) {
	w.WriteHeader(http.StatusBadRequest)
}

func ServeNotAuth(w http.ResponseWriter) {
	w.WriteHeader(http.StatusUnauthorized)
}

func ServeAPISimpleJson(w http.ResponseWriter, rsp interface{}) error {
	w.Header().Add("Content-Type", "application/json; charset=utf-8")
	enc := json.NewEncoder(w)
	if beego.BConfig.RunMode != beego.PROD {
		enc.SetIndent("", "\t")
	}
	return enc.Encode(rsp)
}
