// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/channel-open-game-controller (interfaces: IClient)

// Package channel_open_game_controller is a generated GoMock package.
package channel_open_game_controller

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	channel_open_game_controller "golang.52tt.com/protocol/services/channel-open-game-controller"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetChannelGameStatusInfo mocks base method.
func (m *MockIClient) BatchGetChannelGameStatusInfo(arg0 context.Context, arg1 *channel_open_game_controller.BatchGetChannelGameStatusInfoReq) (*channel_open_game_controller.BatchGetChannelGameStatusInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelGameStatusInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.BatchGetChannelGameStatusInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetChannelGameStatusInfo indicates an expected call of BatchGetChannelGameStatusInfo.
func (mr *MockIClientMockRecorder) BatchGetChannelGameStatusInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelGameStatusInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetChannelGameStatusInfo), arg0, arg1)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ExitChannelGame mocks base method.
func (m *MockIClient) ExitChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.ExitChannelGameReq) (*channel_open_game_controller.ExitChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExitChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.ExitChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ExitChannelGame indicates an expected call of ExitChannelGame.
func (mr *MockIClientMockRecorder) ExitChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExitChannelGame", reflect.TypeOf((*MockIClient)(nil).ExitChannelGame), arg0, arg1)
}

// GetChannelGameHost mocks base method.
func (m *MockIClient) GetChannelGameHost(arg0 context.Context, arg1 uint32) (*channel_open_game_controller.GetChannelGameHostResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGameHost", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.GetChannelGameHostResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelGameHost indicates an expected call of GetChannelGameHost.
func (mr *MockIClientMockRecorder) GetChannelGameHost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGameHost", reflect.TypeOf((*MockIClient)(nil).GetChannelGameHost), arg0, arg1)
}

// GetChannelGameStatusInfo mocks base method.
func (m *MockIClient) GetChannelGameStatusInfo(arg0 context.Context, arg1 *channel_open_game_controller.GetChannelGameStatusInfoReq) (*channel_open_game_controller.GetChannelGameStatusInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelGameStatusInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.GetChannelGameStatusInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetChannelGameStatusInfo indicates an expected call of GetChannelGameStatusInfo.
func (mr *MockIClientMockRecorder) GetChannelGameStatusInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelGameStatusInfo", reflect.TypeOf((*MockIClient)(nil).GetChannelGameStatusInfo), arg0, arg1)
}

// GetOpenid mocks base method.
func (m *MockIClient) GetOpenid(arg0 context.Context, arg1 *channel_open_game_controller.GetOpenidReq) (*channel_open_game_controller.GetOpenidResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOpenid", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.GetOpenidResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetOpenid indicates an expected call of GetOpenid.
func (mr *MockIClientMockRecorder) GetOpenid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOpenid", reflect.TypeOf((*MockIClient)(nil).GetOpenid), arg0, arg1)
}

// JoinChannelGame mocks base method.
func (m *MockIClient) JoinChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.JoinChannelGameReq) (*channel_open_game_controller.JoinChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.JoinChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// JoinChannelGame indicates an expected call of JoinChannelGame.
func (mr *MockIClientMockRecorder) JoinChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinChannelGame", reflect.TypeOf((*MockIClient)(nil).JoinChannelGame), arg0, arg1)
}

// KickOutChannelGame mocks base method.
func (m *MockIClient) KickOutChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.KickOutChannelGameReq) (*channel_open_game_controller.KickOutChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickOutChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.KickOutChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// KickOutChannelGame indicates an expected call of KickOutChannelGame.
func (mr *MockIClientMockRecorder) KickOutChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickOutChannelGame", reflect.TypeOf((*MockIClient)(nil).KickOutChannelGame), arg0, arg1)
}

// QuitChannelGame mocks base method.
func (m *MockIClient) QuitChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.QuitChannelGameReq) (*channel_open_game_controller.QuitChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuitChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.QuitChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// QuitChannelGame indicates an expected call of QuitChannelGame.
func (mr *MockIClientMockRecorder) QuitChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuitChannelGame", reflect.TypeOf((*MockIClient)(nil).QuitChannelGame), arg0, arg1)
}

// ReadyChannelGame mocks base method.
func (m *MockIClient) ReadyChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.ReadyChannelGameReq) (*channel_open_game_controller.ReadyChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadyChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.ReadyChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReadyChannelGame indicates an expected call of ReadyChannelGame.
func (mr *MockIClientMockRecorder) ReadyChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadyChannelGame", reflect.TypeOf((*MockIClient)(nil).ReadyChannelGame), arg0, arg1)
}

// SetChannelGame mocks base method.
func (m *MockIClient) SetChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.SetChannelGameReq) (*channel_open_game_controller.SetChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.SetChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGame indicates an expected call of SetChannelGame.
func (mr *MockIClientMockRecorder) SetChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGame", reflect.TypeOf((*MockIClient)(nil).SetChannelGame), arg0, arg1)
}

// SetChannelGameModeInfo mocks base method.
func (m *MockIClient) SetChannelGameModeInfo(arg0 context.Context, arg1 *channel_open_game_controller.SetChannelGameModeInfoReq) (*channel_open_game_controller.SetChannelGameModeInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelGameModeInfo", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.SetChannelGameModeInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetChannelGameModeInfo indicates an expected call of SetChannelGameModeInfo.
func (mr *MockIClientMockRecorder) SetChannelGameModeInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelGameModeInfo", reflect.TypeOf((*MockIClient)(nil).SetChannelGameModeInfo), arg0, arg1)
}

// SetLoading mocks base method.
func (m *MockIClient) SetLoading(arg0 context.Context, arg1 *channel_open_game_controller.SetLoadingReq) (*channel_open_game_controller.SetLoadingResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLoading", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.SetLoadingResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetLoading indicates an expected call of SetLoading.
func (mr *MockIClientMockRecorder) SetLoading(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLoading", reflect.TypeOf((*MockIClient)(nil).SetLoading), arg0, arg1)
}

// StartChannelGame mocks base method.
func (m *MockIClient) StartChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.StartChannelGameReq) (*channel_open_game_controller.StartChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.StartChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartChannelGame indicates an expected call of StartChannelGame.
func (mr *MockIClientMockRecorder) StartChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartChannelGame", reflect.TypeOf((*MockIClient)(nil).StartChannelGame), arg0, arg1)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UnReadyChannelGame mocks base method.
func (m *MockIClient) UnReadyChannelGame(arg0 context.Context, arg1 *channel_open_game_controller.UnReadyChannelGameReq) (*channel_open_game_controller.UnReadyChannelGameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnReadyChannelGame", arg0, arg1)
	ret0, _ := ret[0].(*channel_open_game_controller.UnReadyChannelGameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnReadyChannelGame indicates an expected call of UnReadyChannelGame.
func (mr *MockIClientMockRecorder) UnReadyChannelGame(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnReadyChannelGame", reflect.TypeOf((*MockIClient)(nil).UnReadyChannelGame), arg0, arg1)
}
