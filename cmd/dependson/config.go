package main

type Inventory struct {
	Kubernetes Kubernetes          `yaml:"kubernetes"`
	Categories map[string]Category `yaml:"categories"`
}

type Kubernetes struct {
	BashPath     string `yaml:"base-path"`
	DefaultMode  string `yaml:"default-mode"`
	Default<PERSON><PERSON> string `yaml:"default-chart"`
}

type Category struct {
	Description string   `yaml:"description"`
	Targets     []Target `yaml:"targets"`
}

type Target struct {
	Name string `yaml:"name"`
	Src  string `yaml:"src"`
	Type string `yaml:"type"`
}
