syntax = "proto3";

package ga.channel_team;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel-team";

// 用户进入小队
message JoinChannelTeamReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 join_uid = 3;
    // 申请位置的索引位，从0开始计算
    uint32 location_index = 4;
    // 申请位置名称
    string location_name = 5;
    Scene type = 6;
    enum Scene {
        // 其他
        OTHER = 0;
        // 麦位抱上小队
        HOLD_ON = 1;
        // 选ta加入小队
        PICK_UP = 2;
    }
}

message JoinChannelTeamResp {
    ga.BaseResp base_resp = 1;
}

// 房主获取申请列表
message GetChannelTeamApplyListReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 offset = 3;
    uint32 count = 4;
}

message GetChannelTeamApplyListResp {
    ga.BaseResp base_resp = 1;
    repeated ApplyMember apply_list = 2;
}

message ApplyMember {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    int32 sex = 4;
    string dan = 5;
    string dan_url = 6;
    string desc = 7;
    string location_name = 8;
}

// 房主同意用户申请入队
message AgreeChannelTeamApplyReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 join_uid = 3;
}

message AgreeChannelTeamApplyResp {
    ga.BaseResp base_resp = 1;
}

// 用户退出小队或者房主踢用户退出小队
message TickChannelTeamMemberReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 tick_uid = 3;
}

message TickChannelTeamMemberResp {
    ga.BaseResp base_resp = 1;
}

// 获取小队成员信息
message GetChannelTeamMemberListReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 tab_id = 3;
    repeated string location_list = 4;
}

message GetChannelTeamMemberListResp {
    ga.BaseResp base_resp = 1;
    ChannelTeamInfo info = 2;
}

// 获取开黑过的用户列表
message GetGangUpHistoryReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 offset = 3;
    uint32 count = 4;
}

message GetGangUpHistoryResp {
    ga.BaseResp base_resp = 1;
    repeated GangUpRecord record = 2;
}

message GangUpRecord {
    uint32 uid = 1;
    int64 update_time = 2;
    repeated string game_name_list = 3; // 玩过的游戏
    int32 sex = 4;
    string nickname = 5;
    string account = 6;
    bool is_online = 7;
}

// 获取所有的房间用户
message GetAllChannelMemberReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 offset = 3;
    uint32 count = 4;
}

message GetAllChannelMemberResp {
    ga.BaseResp base_resp = 1;
    repeated ChannelMember member_list = 2;
}

message ChannelMember {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    int32 sex = 4;
    string dan = 5;
    string dan_url = 6;
}

// 设置房间小队昵称
message SetGameNicknameReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    string game_nickname = 3;
}

message SetGameNicknameResp {
    ga.BaseResp base_resp = 1;
}

//// 发送游戏昵称到公屏
//message PushGameNicknameReq {
//    ga.BaseReq base_req = 1;
//    uint32 channel_id = 2;
//    uint32 tab_id = 3;
//}
//
//message PushGameNicknameResp {
//    ga.BaseResp base_resp = 1;
//    FindFrom from = 2;
//    enum FindFrom {
//        // 找不到
//        UNKNOW = 0;
//        // 游戏卡片
//        TAG = 1;
//        // 小队
//        TEAM = 2;
//    }
//    string game_nickname = 3;
//}

// 共用信息

// 查询房间小队情况
message ChannelTeamInfo {
    // 房主uid
    uint32 owner_uid = 1;
    // 房间成员信息
    repeated MemberInfo member_list = 2;
    // 房间小队创建时间
    int64 create_time = 3;
    // 房间小队ui类型，UiTypeType
    uint32 ui_type = 4;
    // 房间名称
    string team_name = 5;
    // 游戏名称
    string game_name = 6;
}

message MemberInfo {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    int32 sex = 4;
    string dan = 5;
    string dan_url = 6;
    // 用户游戏id
    string game_nickname = 7;
    string location_name = 8;
    // 位置图标
    string location_icon = 9;
    // 位置不可用
    bool disabled = 10;
}

enum UiTypeType {
    UNKOWN = 0; // 未知
    WZRY = 1; // 王者荣耀
    HPJY = 2; // 和平精英
}

// ----------------------  push

message PushChannelTeamUpdate {
    ChannelTeamInfo channel_team_info = 1;
    int64 push_time = 2;
    uint32 type = 3; // UpdateType
}

enum UpdateType {
    UPDATE = 0;
    CREATE = 1;
}

// 房间游戏更新 PushChannelTeamUpdate
// 房间成员更新 PushChannelTeamUpdate

// 玩家申请加入 ApplyMember

enum CtrlType {
    // 未知
    NORMAL = 0;
    // 踢出小队
    TICK_OUT = 1;
    // 解散队伍
    BREAK = 2;
    // 加入队伍
    ADD = 3;
    // 主动退出小队
    QUIT = 4;
}

message CtrlMsg {
    uint32 type = 1; // CtrlType
    string content = 2;
}
