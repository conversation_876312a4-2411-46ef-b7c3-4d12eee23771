package cache

import (
	"fmt"
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

func GenRankingKey(weekNum, rankingType uint32) string {
	return fmt.Sprintf("channel_live_ranking_%d_%d", weekNum, rankingType)
}

func GenFinishMemSetKey(weekNum, rankingType uint32) string {
	return fmt.Sprintf("finish_mem_%d_%d", weekNum, rankingType)
}

func GenAnchorRankKey(rankingType uint32) string {
	return fmt.Sprintf("anchor_rank_%d", rankingType)
}

type RankingMem struct {
	Uid   uint32
	Score uint32
}

type ChannelLiveRankingCache struct {
	redisClient *redis.Client
	tracer      opentracing.Tracer
}

func NewChannelLiveRankingCache(r *redis.Client, tracer opentracing.Tracer) *ChannelLiveRankingCache {
	return &ChannelLiveRankingCache{redisClient: r, tracer: tracer}
}

func (r *ChannelLiveRankingCache) AddFinishMemToSet(weekNum, rankingType, uid, actorUid uint32) (bool, error) {
	key := GenFinishMemSetKey(weekNum, rankingType)
	val := fmt.Sprintf("%d_%d", uid, actorUid)

	cnt, err := r.redisClient.SAdd(key, val).Result()
	if err != nil {
		log.Errorf("AddMemToSet fail. rankingType:%d, weekNum:%d, uid:%d, actorUid:%d, err:%v", rankingType, weekNum, uid, actorUid, err)
		return false, err
	}

	// cnt为增加成功数量
	success := cnt > 0

	if -1*time.Second == r.redisClient.TTL(key).Val() {
		r.redisClient.Expire(key, 7*24*time.Hour)
	}
	return success, nil
}

func (r *ChannelLiveRankingCache) AddRankingMemScore(weekNum, rankingType, uid, score uint32) (bool, error) {
	key := GenRankingKey(weekNum, rankingType)
	exist, err := r.redisClient.Exists(key).Result()
	if err != nil {
		log.Errorf("AddRankingMemScore fail. rankingType:%d, weekNum:%d, uid:%d, score:%d, err:%v", rankingType, weekNum, uid, score, err)
		return false, err
	}

	// key不存在, 不必加入集合
	if exist == 0 {
		log.Debugf("AddRankingMemScore key:%s is not exist", key)
		return false, nil
	}

	_, err = r.redisClient.ZAdd(key, redis.Z{Member: fmt.Sprint(uid), Score: float64(score)}).Result()
	if err != nil {
		log.Errorf("AddRankingMemScore fail. rankingType:%d, weekNum:%d, uid:%d, score:%d, err:%v", rankingType, weekNum, uid, score, err)
		return false, err
	}

	if -1*time.Second == r.redisClient.TTL(key).Val() {
		// todo
		r.redisClient.Expire(key, 7*24*time.Hour)
	}

	log.Debugf("AddRankingMemScore rankingType:%d, weekNum:%d, uid:%d, score:%d", rankingType, weekNum, uid, score)
	return true, nil
}

func (r *ChannelLiveRankingCache) RemoveRankingMem(weekNum, rankingType uint32, uidList []uint32) error {
	if len(uidList) == 0 {
		return nil
	}

	key := GenRankingKey(weekNum, rankingType)
	members := make([]interface{}, len(uidList))

	for i, uid := range uidList {
		members[i] = uid
	}

	return r.redisClient.ZRem(key, members...).Err()
}

func (r *ChannelLiveRankingCache) GetRankingList(weekNum, rankingType, offset, limit uint32) ([]*RankingMem, bool, error) {
	key := GenRankingKey(weekNum, rankingType)
	list := make([]*RankingMem, 0, limit)

	zList, err := r.redisClient.ZRevRangeWithScores(key, int64(offset), int64(offset+limit-1)).Result()
	if err != nil {
		log.Errorf("GetRankingList fail. rankingType:%d, weekNum:%d err:%v", rankingType, weekNum, err)
		return list, false, err
	}

	if len(zList) == 0 {
		return list, false, nil
	}

	for _, z := range zList {
		uid, _ := strconv.ParseInt(z.Member.(string), 10, 32)
		list = append(list, &RankingMem{
			Uid:   uint32(uid),
			Score: uint32(z.Score),
		})
	}

	log.Debugf("GetRankingList. rankingType:%d, weekNum:%d list:%+v", rankingType, weekNum, list)
	return list, true, nil
}

func (r *ChannelLiveRankingCache) BatchUpdateRankingList(weekNum, rankingType, queryTimeType uint32, list []*RankingMem) error {
	if len(list) == 0 {
		return nil
	}

	key := GenRankingKey(weekNum, rankingType)
	zList := make([]redis.Z, 0, len(list))

	for _, memInfo := range list {
		zList = append(zList, redis.Z{Member: fmt.Sprint(memInfo.Uid), Score: float64(memInfo.Score)})
	}

	cnt, err := r.redisClient.ZAdd(key, zList...).Result()
	if err != nil {
		log.Errorf("BatchUpdateRankingList fail. rankingType:%d, weekNum:%d err:%v", rankingType, weekNum, err)
		return err
	}

	r.redisClient.Expire(key, 7*24*time.Hour)

	log.Debugf("BatchUpdateRankingList rankingType:%d, weekNum:%d len(list):%d, done(list):%d", rankingType, weekNum, len(list), cnt)
	return nil
}

func (r *ChannelLiveRankingCache) GetRankingMemScore(weekNum, rankingType, uid uint32) (uint32, bool, error) {
	key := GenRankingKey(weekNum, rankingType)
	score, err := r.redisClient.ZScore(key, fmt.Sprint(uid)).Result()
	if err == redis.Nil {
		// 检查key是否存在
		exist, err := r.redisClient.Exists(key).Result()
		if err != nil {
			log.Errorf("GetRankingMemScore fail. rankingType:%d, weekNum:%d, uid:%d, err:%v", rankingType, weekNum, uid, err)
			return 0, false, err
		}

		// key不存在
		if exist == 0 {
			return 0, false, nil
		}
		return 0, true, nil

	} else if err != nil {
		log.Errorf("GetRankingList fail. rankingType:%d, weekNum:%d err:%v", rankingType, weekNum, err)
		return 0, false, err
	}

	return uint32(score), true, nil
}

func (r *ChannelLiveRankingCache) GetRankingMemRank(weekNum, rankingType, uid uint32) (uint32, bool, error) {
	key := GenRankingKey(weekNum, rankingType)
	rank, err := r.redisClient.ZRevRank(key, fmt.Sprint(uid)).Result()
	if err == redis.Nil {
		// 检查key是否存在
		exist, err := r.redisClient.Exists(key).Result()
		if err != nil {
			log.Errorf("GetRankingMemRank fail. rankingType:%d, weekNum:%d, uid:%d, err:%v", rankingType, weekNum, uid, err)
			return 0, false, err
		}

		// key不存在
		if exist == 0 {
			return 0, false, nil
		}
		return 0, true, nil

	} else if err != nil {
		log.Errorf("GetRankingMemRank fail. rankingType:%d, weekNum:%d err:%v", rankingType, weekNum, err)
		return 0, false, err
	}

	return uint32(rank + 1), true, nil
}

func (r *ChannelLiveRankingCache) GetRankingMemScoreByRank(weekNum, rankingType, rank uint32) (mem uint32, score uint32, existKey bool, err error) {
	key := GenRankingKey(weekNum, rankingType)
	zList, err := r.redisClient.ZRevRangeWithScores(key, int64(rank-1), int64(rank)).Result()
	if err == redis.Nil {
		// 检查key是否存在
		exist, err := r.redisClient.Exists(key).Result()
		if err != nil {
			log.Errorf("GetRankingMemScore fail. rankingType:%d, weekNum:%d, err:%v", rankingType, weekNum, err)
			return 0, 0, false, err
		}

		// key不存在
		if exist == 0 {
			return 0, 0, false, nil
		}
		return 0, 0, true, nil

	} else if err != nil {
		log.Errorf("GetRankingMemScoreByRank fail. rankingType:%d, weekNum:%d err:%v", rankingType, weekNum, err)
		return 0, 0, false, err
	}

	if len(zList) > 0 {
		tmpMem, _ := strconv.ParseInt(zList[0].Member.(string), 10, 32)
		mem = uint32(tmpMem)
		score = uint32(zList[0].Score)
	}

	return mem, score, true, nil
}

func (r *ChannelLiveRankingCache) GetLock(key string, expire time.Duration) (bool, error) {
	success, err := r.redisClient.SetNX(key, "1", expire).Result()
	if err != nil {
		log.Errorf("GetLock SetNX fail. key:%s err:%v", key, err)
		return false, err
	}

	log.Debugf("GetLock key:%s ok:%v", key, success)
	return success, nil
}

func (r *ChannelLiveRankingCache) ReleaseLock(key string) error {
	err := r.redisClient.Del(key).Err()
	if err != nil {
		log.Errorf("ReleaseLock Del fail. key:%s err:%v", key, err)
		return err
	}

	log.Debugf("ReleaseLock key:%s", key)
	return nil
}

func (r *ChannelLiveRankingCache) GetAnchorRankList(rankingType, offset, limit uint32) ([]uint32, error) {
	key := GenAnchorRankKey(rankingType)
	list := make([]uint32, 0)

	strList, err := r.redisClient.ZRevRange(key, int64(offset), int64(offset+limit-1)).Result()
	if err != nil {
		return list, err
	}

	if len(strList) == 0 {
		return list, nil
	}

	for _, str := range strList {
		uid, _ := strconv.ParseInt(str, 10, 32)
		list = append(list, uint32(uid))
	}

	log.Debugf("GetAnchorRankList rankingType:%d list:%+v", rankingType, list)
	return list, nil
}

func (r *ChannelLiveRankingCache) AddAnchorRank(rankingType, anchorUid, score uint32) error {
	key := GenAnchorRankKey(rankingType)

	_, err := r.redisClient.ZAdd(key, redis.Z{Member: fmt.Sprint(anchorUid), Score: float64(score)}).Result()

	return err
}

func (r *ChannelLiveRankingCache) UpdateAnchorRank(rankingType uint32, mapUid2Score map[uint32]uint32) error {
	key := GenAnchorRankKey(rankingType)

	members := make([]redis.Z, 0)
	for k, v := range mapUid2Score {
		members = append(members, redis.Z{Score: float64(v), Member: fmt.Sprintf("%d", k)})
	}

	_, err := r.redisClient.ZAddXX(key, members...).Result()

	return err
}

func (r *ChannelLiveRankingCache) DeleteAnchorRank(rankingType, anchorUid uint32) error {
	key := GenAnchorRankKey(rankingType)

	return r.redisClient.ZRem(key, anchorUid).Err()
}
