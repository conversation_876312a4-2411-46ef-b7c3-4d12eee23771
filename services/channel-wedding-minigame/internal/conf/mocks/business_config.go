// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-wedding-minigame/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetAutoStartNextRoundDuration mocks base method.
func (m *MockIBusinessConfManager) GetAutoStartNextRoundDuration() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoStartNextRoundDuration")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetAutoStartNextRoundDuration indicates an expected call of GetAutoStartNextRoundDuration.
func (mr *MockIBusinessConfManagerMockRecorder) GetAutoStartNextRoundDuration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoStartNextRoundDuration", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAutoStartNextRoundDuration))
}

// GetChairGameBackSender mocks base method.
func (m *MockIBusinessConfManager) GetChairGameBackSender() *conf.BackSenderConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameBackSender")
	ret0, _ := ret[0].(*conf.BackSenderConf)
	return ret0
}

// GetChairGameBackSender indicates an expected call of GetChairGameBackSender.
func (mr *MockIBusinessConfManagerMockRecorder) GetChairGameBackSender() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameBackSender", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChairGameBackSender))
}

// GetChairGameGrabDuration mocks base method.
func (m *MockIBusinessConfManager) GetChairGameGrabDuration() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameGrabDuration")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetChairGameGrabDuration indicates an expected call of GetChairGameGrabDuration.
func (mr *MockIBusinessConfManagerMockRecorder) GetChairGameGrabDuration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameGrabDuration", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChairGameGrabDuration))
}

// GetChairGameResConf mocks base method.
func (m *MockIBusinessConfManager) GetChairGameResConf() *conf.ChairGameRes {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameResConf")
	ret0, _ := ret[0].(*conf.ChairGameRes)
	return ret0
}

// GetChairGameResConf indicates an expected call of GetChairGameResConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetChairGameResConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameResConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChairGameResConf))
}

// GetChairGameRewardSetting mocks base method.
func (m *MockIBusinessConfManager) GetChairGameRewardSetting() *conf.ChairGameRewardSetting {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChairGameRewardSetting")
	ret0, _ := ret[0].(*conf.ChairGameRewardSetting)
	return ret0
}

// GetChairGameRewardSetting indicates an expected call of GetChairGameRewardSetting.
func (mr *MockIBusinessConfManagerMockRecorder) GetChairGameRewardSetting() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChairGameRewardSetting", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetChairGameRewardSetting))
}

// GetHostStartButtonDuration mocks base method.
func (m *MockIBusinessConfManager) GetHostStartButtonDuration() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHostStartButtonDuration")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetHostStartButtonDuration indicates an expected call of GetHostStartButtonDuration.
func (mr *MockIBusinessConfManagerMockRecorder) GetHostStartButtonDuration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostStartButtonDuration", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetHostStartButtonDuration))
}

// GetNotUseRewardRollbackTs mocks base method.
func (m *MockIBusinessConfManager) GetNotUseRewardRollbackTs() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotUseRewardRollbackTs")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetNotUseRewardRollbackTs indicates an expected call of GetNotUseRewardRollbackTs.
func (mr *MockIBusinessConfManagerMockRecorder) GetNotUseRewardRollbackTs() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotUseRewardRollbackTs", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetNotUseRewardRollbackTs))
}

// GetPayAppId mocks base method.
func (m *MockIBusinessConfManager) GetPayAppId() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayAppId")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPayAppId indicates an expected call of GetPayAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetPayAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPayAppId))
}

// GetPlatformReward mocks base method.
func (m *MockIBusinessConfManager) GetPlatformReward() *conf.PlatformReward {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlatformReward")
	ret0, _ := ret[0].(*conf.PlatformReward)
	return ret0
}

// GetPlatformReward indicates an expected call of GetPlatformReward.
func (mr *MockIBusinessConfManagerMockRecorder) GetPlatformReward() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlatformReward", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPlatformReward))
}

// GetReissueMaxIntervalHour mocks base method.
func (m *MockIBusinessConfManager) GetReissueMaxIntervalHour() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReissueMaxIntervalHour")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetReissueMaxIntervalHour indicates an expected call of GetReissueMaxIntervalHour.
func (mr *MockIBusinessConfManagerMockRecorder) GetReissueMaxIntervalHour() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReissueMaxIntervalHour", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetReissueMaxIntervalHour))
}

// GetRewardInfoListByPackId mocks base method.
func (m *MockIBusinessConfManager) GetRewardInfoListByPackId(arg0 uint32) []*conf.RewardInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardInfoListByPackId", arg0)
	ret0, _ := ret[0].([]*conf.RewardInfo)
	return ret0
}

// GetRewardInfoListByPackId indicates an expected call of GetRewardInfoListByPackId.
func (mr *MockIBusinessConfManagerMockRecorder) GetRewardInfoListByPackId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardInfoListByPackId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRewardInfoListByPackId), arg0)
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
