package cache

import (
	"context"
	"encoding"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/google/wire"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	"golang.52tt.com/services/tt-rev/offering-room/internal/mgr"
)

var ProviderSetForCache = wire.NewSet(
	NewRelationshipNotExistMarkerCache,
	NewCacheAside,
	NewApplyList,
	NewGameCache,
	NewRecordHostLeaveMic,
)

type cacheAsideImpl struct {
	redisCli       redis.CmdableV2
	rwLocker       sync.RWMutex
	redisCliHelper redis.Helper
}

func NewCacheAside(redisCli redis.CmdableV2) mgr.CacheAside {
	return &cacheAsideImpl{
		redisCli:       redisCli,
		rwLocker:       sync.RWMutex{},
		redisCliHelper: redis.NewHelper(redisCli),
	}
}

func (c *cacheAsideImpl) Get(ctx context.Context, key string, data interface{}, dataSourceFunc func() (interface{}, time.Duration, error)) error {
	// 从缓存中获取
	result, err := c.redisCli.Get(ctx, key).Result()
	// 如果从缓存获取失败，则使用double check的方式从数据库中获取
	if err != nil {
		// 尝试加锁，进行写操作
		source, expireDuration, err := dataSourceFunc()
		if err != nil {
			return fmt.Errorf("cache aside 从数据源中获取数据失败, key: %s, err: %w", key, err)
		}
		err = c.Set(ctx, key, source, expireDuration)
		if err != nil {
			log.Errorf("cache aside 写入缓存失败, key: %s, err: %v", key, err)
		}
		c.copyData(ctx, data, source)
		log.DebugWithCtx(ctx, "cacheAside get data from source, key:%s, data:%v", key, data)
		return nil
	}

	// 反序列化cache中数据
	err = c.unmarshal([]byte(result), data)
	if err != nil {
		return fmt.Errorf("cache aside 反序列化数据失败, key: %s, err: %w", key, err)
	}
	log.DebugWithCtx(ctx, "cacheAside get data from cache, key:%s, data:%v", key, data)
	return nil
}

func (c *cacheAsideImpl) copyData(ctx context.Context, destination, source interface{}) {
	switch t := destination.(type) {
	case *string:
		if sourceData, ok := source.(string); ok {
			*t = sourceData
		}
	case *uint32:
		if sourceData, ok := source.(uint32); ok {
			*t = sourceData
		}
	case **mgr.RelationshipsPage:
		if sourceData, ok := source.(*mgr.RelationshipsPage); ok {
			*t = sourceData
		} else if sourceData, ok := source.(mgr.RelationshipsPage); ok {
			*t = &sourceData
		} else {
			log.ErrorWithCtx(ctx, "cacheAsideImpl got invalid want [**mgr.RelationshipsPage] type:%v, %T", destination, destination)
		}
	case *mgr.RelationshipsPage:
		if sourceData, ok := source.(mgr.RelationshipsPage); ok {
			*t = sourceData
		} else if sourceData, ok := source.(*mgr.RelationshipsPage); ok {
			*t = *sourceData
		} else {
			log.ErrorWithCtx(ctx, "cacheAsideImpl got invalid want [*mgr.RelationshipsPage] type:%v, %T", destination, destination)
		}
	default:
		log.ErrorWithCtx(ctx, "cacheAsideImpl got invalid type:%v, %T", destination, destination)
		return
	}
}

func (c *cacheAsideImpl) Set(ctx context.Context, key string, data interface{}, expireDuration time.Duration) error {
	// 序列化数据
	bytes, err := c.marshal(data)
	if err != nil {
		return fmt.Errorf("cache aside 序列化数据失败, key: %s, err: %w", key, err)
	}
	// 写入缓存
	err = c.redisCli.Set(ctx, key, bytes, expireDuration).Err()
	if err != nil {
		return fmt.Errorf("cache aside 写入缓存失败, key: %s, err: %w", key, err)
	}
	return nil
}

func (c *cacheAsideImpl) Del(ctx context.Context, key ...string) error {
	log.DebugWithCtx(ctx, "cacheAside del, key:%+v", key)
	return c.redisCli.Del(ctx, key...).Err()
}

func (c *cacheAsideImpl) DelByPatten(ctx context.Context, patten string) error {
	keys, err := c.redisCliHelper.ScanIter(ctx, patten, 1000)
	if err != nil {
		return fmt.Errorf("cache aside del by patten scan err, patten: %s, err: %w", patten, err)
	}
	if len(keys) == 0 {
		return nil
	}
	log.DebugWithCtx(ctx, "DelByPatten keys:%v", keys)
	return c.redisCli.Del(ctx, keys...).Err()
}

func (c *cacheAsideImpl) unmarshal(bytes []byte, data interface{}) error {

	switch v := data.(type) {
	case *string:
		*v = string(bytes)
	case *int:
		num, err := strconv.Atoi(string(bytes))
		if err != nil {
			return fmt.Errorf("解析为 int 失败: %w", err)
		}
		*v = num
	case *uint32:
		num, err := strconv.ParseUint(string(bytes), 10, 32)
		if err != nil {
			return fmt.Errorf("解析为 uint32 失败: %w", err)
		}
		*v = uint32(num)
	case *uint64:
		num, err := strconv.ParseUint(string(bytes), 10, 64)
		if err != nil {
			return fmt.Errorf("解析为 uint32 失败: %w", err)
		}
		*v = num
	case *int32:
		num, err := strconv.ParseInt(string(bytes), 10, 32)
		if err != nil {
			return fmt.Errorf("解析为 int32 失败: %w", err)
		}
		*v = int32(num)
	case *int64:
		num, err := strconv.ParseInt(string(bytes), 10, 64)
		if err != nil {
			return fmt.Errorf("解析为 int64 失败: %w", err)
		}
		*v = num
	case encoding.BinaryUnmarshaler:
		err := v.UnmarshalBinary(bytes)
		if err != nil {
			return fmt.Errorf("解析为 BinaryUnmarshaler 失败: %w", err)
		}
	default:
		// 尝试使用json反序列化
		err := json.Unmarshal(bytes, data)
		if err != nil {
			return fmt.Errorf("未知类型: %T, 解析为 json 失败: %w", v, err)
		}
	}
	return nil
}

func (c *cacheAsideImpl) marshal(data interface{}) ([]byte, error) {
	switch v := data.(type) {
	case string:
		return []byte(v), nil
	case int:
		return []byte(strconv.Itoa(v)), nil
	case uint32:
		return []byte(strconv.FormatUint(uint64(v), 10)), nil
	case uint64:
		return []byte(strconv.FormatUint(v, 10)), nil
	case int32:
		return []byte(strconv.FormatInt(int64(v), 10)), nil
	case int64:
		return []byte(strconv.FormatInt(v, 10)), nil
	case encoding.BinaryMarshaler:
		return v.MarshalBinary()
	default:
		// 尝试使用json序列化
		marshal, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("未知类型: %T, 使用 json 序列化失败: %w", v, err)
		}
		return marshal, nil
	}
}
