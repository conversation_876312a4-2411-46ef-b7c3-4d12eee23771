package utils

// curl -s 'http://data-service-gateway.skyengine.com.cn:8080/hela-gateway/45/GetUserFirstReviewAudioUrls?apiToken=e2f2d87b48262b0552e6c1774924b085&start_date=2023-03-28&end_date=2023-03-28&uid=309660093'

type Response struct {
	Msg string `json:"msg"`
}

/*
func TestSendHttp(t *testing.T) {
	httpmock.Activate()

	url := "http://abc"
	method := "GET"
	resp := Response{Msg: "ok"}
	marshal, err := json.Marshal(resp)
	if err != nil {
		panic(err)
	}
	response, err := httpmock.NewJsonResponder(http.StatusOK, resp)
	if err != nil {
		panic(err)
	}
	httpmock.RegisterResponder(method, url, response)

	type args struct {
		apiUrl  string
		data    string
		method  string
		headers map[string]string
	}
	tests := []struct {
		name  string
		args  args
		want  []byte
		want1 int
	}{
		// Add test cases.
		{
			name: "TestSendHttp",
			args: args{
				apiUrl:  url,
				data:    "",
				method:  method,
				headers: nil,
			},
			want:  marshal,
			want1: http.StatusOK,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := SendHttp(tt.args.apiUrl, tt.args.data, tt.args.method, tt.args.headers)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SendHttp() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("SendHttp() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
*/
