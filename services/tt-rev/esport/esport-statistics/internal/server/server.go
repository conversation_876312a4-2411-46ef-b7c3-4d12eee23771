package server

import (
	"context"
	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/protocol"
	errCode "golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/demo/echo"
	pb "golang.52tt.com/protocol/services/esport-statistics"
	esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/cache"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/event"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/mgr"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/store"
	context0 "golang.org/x/net/context"
	"time"
)

type StartConfig struct {
	// [optional] from startup arguments

	// from config file
	RedisConfig    *redisConnect.RedisConfig `json:"redis"`
	MysqlConfig    *mysqlConnect.MysqlConfig `json:"mysql"`
	OrderChangeKfk *config.KafkaConfig       `json:"order_change_kfk"`
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
	log.Infof("server startup with cfg: %+v", *cfg)

	s := &Server{}

	cache_, err := cache.NewCache(ctx, cfg.RedisConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init redis fail, err: %v", err)
		return nil, err
	}

	store_, err := store.NewStore(ctx, cfg.MysqlConfig)
	if nil != err {
		log.ErrorWithCtx(ctx, "init store fail, err: %v", err)
		return nil, err
	}

	refundService, err := esport_trade_appeal.NewClient(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create esport_trade_appeal client: %v", err)
		return nil, err
	}
	m := mgr.NewMgr(cache_, store_, refundService)

	orderChangeKfk, err := event.NewEsportTradeStatusChangeSub(cfg.OrderChangeKfk.ClientID,
		cfg.OrderChangeKfk.GroupID, cfg.OrderChangeKfk.TopicList(), cfg.OrderChangeKfk.BrokerList(), m)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to  NewPresentKafkaSub err %s", err.Error())
		return nil, err
	}
	s.m = m
	s.orderChangeKfk = orderChangeKfk
	return s, nil
}

type Server struct {
	m              *mgr.Manager
	orderChangeKfk *event.EsportTradeStatusChangeSub
}

func (s *Server) GetCoachTodayUv(c context0.Context, request *pb.GetCoachTodayUvRequest) (*pb.GetCoachTodayUvResponse, error) {
	out := &pb.GetCoachTodayUvResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetCoachTodayUv, req:%+v, resp:%+v", request, out)
	}()
	data, err := s.m.GetCoachTodayUv(c, request.GetCoachUid())
	if err != nil {
		log.ErrorWithCtx(c, "GetCoachTodayUv failed, req:%+v, err:%v", request, err)
		return out, err
	}
	out.Uv = data
	return out, nil
}

func (s *Server) GetCoachOrderRunningValue(ctx context.Context, request *pb.GetCoachOrderRunningValueRequest) (*pb.GetCoachOrderRunningValueResponse, error) {
	return s.m.GetCoachOrderRunningValue(ctx, request)
}

func (s *Server) GetCoachCustomerNum(ctx context.Context, request *pb.GetCoachCustomerNumRequest) (*pb.GetCoachCustomerNumResponse, error) {
	return s.m.GetCoachCustomerNum(ctx, request)
}

// GetUserOrderCoachList 获取用户在指定时间段内下过单的大神
func (s *Server) GetUserOrderCoachList(c context.Context, request *pb.GetUserOrderCoachListRequest) (*pb.GetUserOrderCoachListResponse, error) {
	resp := &pb.GetUserOrderCoachListResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetUserOrderCoachList, req:%+v resp:%+v", request, resp)
	}()
	out, err := s.m.GetUserOrderCoachList(c, request)
	resp.CoachOrderTimeMap = out.GetCoachOrderTimeMap()
	return resp, err
}

func (s *Server) BatGetCoachConversionRate(ctx context.Context, request *pb.BatGetCoachConversionRateRequest) (*pb.BatGetCoachConversionRateResponse, error) {
	return s.m.BatGetCoachConversionRate(ctx, int(request.GetPageNum()), int(request.GetPageSize()))
}

func (s *Server) BatGetCoachOrderSum(ctx context.Context, request *pb.BatGetCoachOrderSumRequest) (*pb.BatGetCoachOrderSumResponse, error) {
	return s.m.BatGetCoachOrderSum(ctx, request.GetCoachUidList(), request.GetGameId())
}

func (s *Server) GetUserCoachLeastOrder(c context.Context, request *pb.GetUserCoachLeastOrderRequest) (*pb.GetUserCoachLeastOrderResponse, error) {

	order, err := s.m.GetUserCoachLeastOrder(c, request)
	if err != nil {
		log.ErrorWithCtx(c, "GetUserCoachLeastOrder failed, req:%+v, err:%v", request, err)
		return &pb.GetUserCoachLeastOrderResponse{}, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, "")
	}
	return order, nil
}

// GetUserTotalExpenditureAmountMonthly 获取用户月消费总额
func (s *Server) GetUserTotalExpenditureAmountMonthly(c context.Context, request *pb.GetUserTotalExpenditureAmountMonthlyRequest) (*pb.GetUserTotalExpenditureAmountMonthlyResponse, error) {
	return &pb.GetUserTotalExpenditureAmountMonthlyResponse{}, nil
}

func (s *Server) BatchGetCoachDurationOrderNum(c context.Context, request *pb.BatchGetCoachDurationOrderNumRequest) (*pb.BatchGetCoachDurationOrderNumResponse, error) {
	out := &pb.BatchGetCoachDurationOrderNumResponse{}
	defer func() {
		log.DebugWithCtx(c, "BatchGetCoachDurationOrderNum, req:%+v, resp:%+v", request, out)
	}()
	data, err := s.m.BatchGetCoachDurationOrderNum(c, request.GetUidList(), request.GetGameId(), request.GetDayCnt())
	if err != nil {
		log.ErrorWithCtx(c, "BatchGetCoachWeeklyOrderNum failed, req:%+v, err:%v", request, err)
		return out, err
	}
	out.OrderNumMap = data
	return out, nil
}

func (s *Server) HandleRecallCoach(c context.Context, request *pb.HandleRecallCoachRequest) (*pb.HandleRecallCoachResponse, error) {
	out := &pb.HandleRecallCoachResponse{}
	if err := s.m.HandleRecallCoach(c, request.GetCoachUid()); err != nil {
		log.ErrorWithCtx(c, "HandleRecallCoach failed, req:%+v, err:%v", request, err)
		return out, err
	}
	log.InfoWithCtx(c, "HandleRecallCoach success, req:%+v", request)
	return out, nil
}

func (s *Server) GetUserMostPayGame(c context.Context, request *pb.GetUserMostPayGameRequest) (*pb.GetUserMostPayGameResponse, error) {
	out := &pb.GetUserMostPayGameResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetUserMostPayGame, req:%+v, resp:%+v", request, out)
	}()
	gameId, err := s.m.GetUserMostPayGame(c, request.GetUid())
	if err != nil {
		log.ErrorWithCtx(c, "GetUserMostPayGame failed, uid:%d, err:%v", request.GetUid(), err)
		return out, err
	}
	out.GameId = gameId
	return out, nil
}

func (s *Server) GetCoachAllStatistics(c context.Context, request *pb.GetCoachAllStatisticsRequest) (*pb.GetCoachAllStatisticsResponse, error) {
	out := &pb.GetCoachAllStatisticsResponse{}
	defer func() {
		log.DebugWithCtx(c, "GetCoachAllStatistics, req:%+v, resp:%+v", request, out)
	}()
	out, err := s.m.GetAllCoachTradeData(c, request.GetUid())
	if err != nil {
		log.ErrorWithCtx(c, "GetCoachAllStatistics failed, req:%+v, err:%v", request, err)
		return out, err
	}
	return out, nil
}

func (s *Server) ShutDown() {
	s.m.Close()
}

func (s *Server) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}

// BatchGetUserFirstTakeOrdersTime 批量获取用户首次接单时间
func (s *Server) BatchGetUserFirstTakeOrdersTime(ctx context.Context, req *pb.BatchGetUserFirstTakeOrdersTimeRequest) (*pb.BatchGetUserFirstTakeOrdersTimeResponse, error) {
	out := &pb.BatchGetUserFirstTakeOrdersTimeResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "BatchGetUserFirstTakeOrdersTime, req:%+v, resp:%+v", req, out)
	}()

	out, err := s.m.BatchGetUsersFirstTradeTime(ctx, req.GetUidList())
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUserFirstTakeOrdersTime failed, req:%+v, err:%v", req, err)
		return out, err
	}
	return out, nil
}

// GetCoachStatistics 获取电竞指导接单统计数据（按game_id分类）
func (s *Server) GetCoachStatistics(ctx context.Context, req *pb.GetCoachStatisticsRequest) (*pb.GetCoachStatisticsResponse, error) {
	out := &pb.GetCoachStatisticsResponse{}
	defer func() {
		log.DebugWithCtx(ctx, "GetCoachStatistics, req:%+v, resp:%+v", req, out)
	}()

	out, err := s.m.GetCoachTradeData(ctx, req.GetUid(), req.GetGameId())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachStatistics failed, req:%+v, err:%v", req, err)
		return out, err
	}
	return out, nil
}

func (s *Server) BatchGetOrderCntByGameId(c context.Context, request *pb.BatchGetOrderCntByGameIdRequest) (*pb.BatchGetOrderCntByGameIdResponse, error) {
	out := &pb.BatchGetOrderCntByGameIdResponse{}
	defer func() {
		log.DebugWithCtx(c, "BatchGetOrderCntByGameId, req:%+v, resp:%+v", request, out)
	}()
	out, err := s.m.BatchGetOrderCntByGameId(c, request.GetUidList(), request.GetGameId())
	if err != nil {
		log.ErrorWithCtx(c, "BatchGetOrderCntByGameId failed, req:%+v, err:%v", request, err)
		return out, err
	}
	return out, nil
}

func (s *Server) ReportUserVisitIMPage(c context.Context, request *pb.ReportUserVisitIMPageRequest) (*pb.ReportUserVisitIMPageResponse, error) {
	if request.GetGetOnly() {
		return s.m.GetUserEnterCoachImPageCount(c, request)
	} else {
		return s.m.IncrUserEnterCoachImPageFlag(c, request)
	}
}

func (s *Server) ReportUserVisitGameCard(c context.Context, request *pb.ReportUserVisitGameCardRequest) (*pb.ReportUserVisitGameCardResponse, error) {
	return s.m.ReportUserVisitCoachGameCard(c, request)
}

func (s *Server) GetBeVisitorRecordCount(c context.Context, request *pb.GetBeVisitorRecordCountRequest) (*pb.GetBeVisitorRecordCountResponse, error) {
	return s.m.GetCoachBeVisitUserCount(c, request)
}

func (s *Server) GetBeVisitorRecordList(c context.Context, request *pb.GetBeVisitorRecordListRequest) (*pb.GetBeVisitorRecordListResponse, error) {
	return s.m.GetCoachVisitRecordByPage(c, request)
}

// RebuildUserOrderCoachList 重建用户下单大神列表
func (s *Server) RebuildUserOrderCoachList(c context.Context, request *pb.RebuildUserOrderCoachListRequest) (*pb.RebuildUserOrderCoachListResponse, error) {
	// 单独起一个30分钟过期的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()
	err := s.m.RebuildUserOrderCoachList(ctx)
	if err != nil {
		log.ErrorWithCtx(c, "RebuildUserOrderCoachList failed, err:%v", err)
		return &pb.RebuildUserOrderCoachListResponse{}, protocol.NewExactServerError(nil, errCode.ErrEsportsCommonErr, err.Error())
	}
	return &pb.RebuildUserOrderCoachListResponse{}, nil
}
