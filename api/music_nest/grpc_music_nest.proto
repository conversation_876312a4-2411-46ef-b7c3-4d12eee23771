// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.music_nest;

import "music_nest_logic/music-nest-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/music_nest;music_nest";

service MusicNestLogic {
    option (ga.api.extension.logic_service_name) = "music-nest-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.MusicNestLogic/";
    rpc GetMusicNestCoverAndLiveList(ga.music_nest_logic.GetMusicNestCoverAndLiveListReq) returns (ga.music_nest_logic.GetMusicNestCoverAndLiveListResp) {
        option (ga.api.extension.command) = {
             id: 31321;
        };
    }
    rpc SubMusicNest(ga.music_nest_logic.SubMusicNestReq) returns (ga.music_nest_logic.SubMusicNestResp) {
        option (ga.api.extension.command) = {
             id: 31322;
        };
    }
    rpc GetMusicNestHomePage(ga.music_nest_logic.GetMusicNestHomePageReq) returns (ga.music_nest_logic.GetMusicNestHomePageResp) {
        option (ga.api.extension.command) = {
             id: 31323;
        };
    }
    rpc SubMusicNestActivity(ga.music_nest_logic.SubMusicNestActivityReq) returns (ga.music_nest_logic.SubMusicNestActivityResp) {
        option (ga.api.extension.command) = {
             id: 31324;
        };
    }
    rpc GetMusicNestPerformance(ga.music_nest_logic.GetMusicNestPerformanceReq) returns (ga.music_nest_logic.GetMusicNestPerformanceResp) {
        option (ga.api.extension.command) = {
             id: 31325;
        };
    }
    rpc SetCurrentMusicNestPerformanceStage(ga.music_nest_logic.SetCurrentMusicNestPerformanceStageReq) returns (ga.music_nest_logic.SetCurrentMusicNestPerformanceStageResp) {
        option (ga.api.extension.command) = {
             id: 31326;
        };
    }
    rpc CloseNestDirectionAct(ga.music_nest_logic.CloseNestDirectionActReq) returns (ga.music_nest_logic.CloseNestDirectionActResp) {
        option (ga.api.extension.command) = {
             id: 31328;
        };
    }
    rpc GetSpecifiedChannelVisitedSize(ga.music_nest_logic.GetSpecifiedChannelVisitedSizeReq) returns (ga.music_nest_logic.GetSpecifiedChannelVisitedSizeResp) {
        option (ga.api.extension.command) = {
             id: 31331;
        };
    }
    rpc GetWelcomePop(ga.music_nest_logic.GetWelcomePopReq) returns (ga.music_nest_logic.GetWelcomePopResp) {
        option (ga.api.extension.command) = {
             id: 31341;
        };
    }
    rpc UserClickPop(ga.music_nest_logic.UserClickPopReq) returns (ga.music_nest_logic.UserClickPopResp) {
        option (ga.api.extension.command) = {
             id: 31342;
        };
    }
    rpc GetMusicNestLiveInfo(ga.music_nest_logic.GetMusicNestLiveInfoReq) returns (ga.music_nest_logic.GetMusicNestLiveInfoResp) {
        option (ga.api.extension.command) = {
             id: 31343;
        };
    }
    rpc GetMusicNestSocialCommunity(ga.music_nest_logic.GetMusicNestSocialCommunityReq) returns (ga.music_nest_logic.GetMusicNestSocialCommunityResp) {
        option (ga.api.extension.command) = {
             id: 31563;
        };
    }
}


