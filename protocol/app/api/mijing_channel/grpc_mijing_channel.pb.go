// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/mijing_channel/grpc_mijing_channel.proto

package mijing_channel // import "golang.52tt.com/protocol/app/api/mijing_channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import mijing_channel "golang.52tt.com/protocol/app/mijing_channel"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MijingChannelServiceClient is the client API for MijingChannelService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MijingChannelServiceClient interface {
	// 谜境首页，按分类获取筛选器
	GetCategoryFilter(ctx context.Context, in *mijing_channel.GetCategoryFilterRequest, opts ...grpc.CallOption) (*mijing_channel.GetCategoryFilterResponse, error)
	// 谜境首页，按玩法获取筛选器
	GetTabFilter(ctx context.Context, in *mijing_channel.GetTabFilterRequest, opts ...grpc.CallOption) (*mijing_channel.GetTabFilterResponse, error)
	// 谜境首页拼人和房间
	MijingChannelPlayerList(ctx context.Context, in *mijing_channel.MijingChannelPlayerListRequest, opts ...grpc.CallOption) (*mijing_channel.MijingChannelPlayerListResponse, error)
	// 获取随机用户
	GetMijingRandomUsers(ctx context.Context, in *mijing_channel.GetMijingRandomUsersRequest, opts ...grpc.CallOption) (*mijing_channel.GetMijingRandomUsersResponse, error)
}

type mijingChannelServiceClient struct {
	cc *grpc.ClientConn
}

func NewMijingChannelServiceClient(cc *grpc.ClientConn) MijingChannelServiceClient {
	return &mijingChannelServiceClient{cc}
}

func (c *mijingChannelServiceClient) GetCategoryFilter(ctx context.Context, in *mijing_channel.GetCategoryFilterRequest, opts ...grpc.CallOption) (*mijing_channel.GetCategoryFilterResponse, error) {
	out := new(mijing_channel.GetCategoryFilterResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_channel.MijingChannelService/GetCategoryFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingChannelServiceClient) GetTabFilter(ctx context.Context, in *mijing_channel.GetTabFilterRequest, opts ...grpc.CallOption) (*mijing_channel.GetTabFilterResponse, error) {
	out := new(mijing_channel.GetTabFilterResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_channel.MijingChannelService/GetTabFilter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingChannelServiceClient) MijingChannelPlayerList(ctx context.Context, in *mijing_channel.MijingChannelPlayerListRequest, opts ...grpc.CallOption) (*mijing_channel.MijingChannelPlayerListResponse, error) {
	out := new(mijing_channel.MijingChannelPlayerListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_channel.MijingChannelService/MijingChannelPlayerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingChannelServiceClient) GetMijingRandomUsers(ctx context.Context, in *mijing_channel.GetMijingRandomUsersRequest, opts ...grpc.CallOption) (*mijing_channel.GetMijingRandomUsersResponse, error) {
	out := new(mijing_channel.GetMijingRandomUsersResponse)
	err := c.cc.Invoke(ctx, "/ga.api.mijing_channel.MijingChannelService/GetMijingRandomUsers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MijingChannelServiceServer is the server API for MijingChannelService service.
type MijingChannelServiceServer interface {
	// 谜境首页，按分类获取筛选器
	GetCategoryFilter(context.Context, *mijing_channel.GetCategoryFilterRequest) (*mijing_channel.GetCategoryFilterResponse, error)
	// 谜境首页，按玩法获取筛选器
	GetTabFilter(context.Context, *mijing_channel.GetTabFilterRequest) (*mijing_channel.GetTabFilterResponse, error)
	// 谜境首页拼人和房间
	MijingChannelPlayerList(context.Context, *mijing_channel.MijingChannelPlayerListRequest) (*mijing_channel.MijingChannelPlayerListResponse, error)
	// 获取随机用户
	GetMijingRandomUsers(context.Context, *mijing_channel.GetMijingRandomUsersRequest) (*mijing_channel.GetMijingRandomUsersResponse, error)
}

func RegisterMijingChannelServiceServer(s *grpc.Server, srv MijingChannelServiceServer) {
	s.RegisterService(&_MijingChannelService_serviceDesc, srv)
}

func _MijingChannelService_GetCategoryFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_channel.GetCategoryFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingChannelServiceServer).GetCategoryFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_channel.MijingChannelService/GetCategoryFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingChannelServiceServer).GetCategoryFilter(ctx, req.(*mijing_channel.GetCategoryFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingChannelService_GetTabFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_channel.GetTabFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingChannelServiceServer).GetTabFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_channel.MijingChannelService/GetTabFilter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingChannelServiceServer).GetTabFilter(ctx, req.(*mijing_channel.GetTabFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingChannelService_MijingChannelPlayerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_channel.MijingChannelPlayerListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingChannelServiceServer).MijingChannelPlayerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_channel.MijingChannelService/MijingChannelPlayerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingChannelServiceServer).MijingChannelPlayerList(ctx, req.(*mijing_channel.MijingChannelPlayerListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingChannelService_GetMijingRandomUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mijing_channel.GetMijingRandomUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingChannelServiceServer).GetMijingRandomUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.mijing_channel.MijingChannelService/GetMijingRandomUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingChannelServiceServer).GetMijingRandomUsers(ctx, req.(*mijing_channel.GetMijingRandomUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MijingChannelService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.mijing_channel.MijingChannelService",
	HandlerType: (*MijingChannelServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCategoryFilter",
			Handler:    _MijingChannelService_GetCategoryFilter_Handler,
		},
		{
			MethodName: "GetTabFilter",
			Handler:    _MijingChannelService_GetTabFilter_Handler,
		},
		{
			MethodName: "MijingChannelPlayerList",
			Handler:    _MijingChannelService_MijingChannelPlayerList_Handler,
		},
		{
			MethodName: "GetMijingRandomUsers",
			Handler:    _MijingChannelService_GetMijingRandomUsers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/mijing_channel/grpc_mijing_channel.proto",
}

func init() {
	proto.RegisterFile("api/mijing_channel/grpc_mijing_channel.proto", fileDescriptor_grpc_mijing_channel_4f52cddc32b8bc92)
}

var fileDescriptor_grpc_mijing_channel_4f52cddc32b8bc92 = []byte{
	// 356 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0xcf, 0x4a, 0x2b, 0x31,
	0x14, 0xc6, 0xe9, 0xdc, 0x72, 0x29, 0xc3, 0xdd, 0xdc, 0x50, 0x15, 0x06, 0x94, 0xa2, 0xa0, 0x0b,
	0xdb, 0x0c, 0x56, 0x5c, 0x09, 0x2e, 0x2c, 0xd8, 0x8d, 0x42, 0xa9, 0xba, 0x71, 0x61, 0x49, 0xe3,
	0x21, 0x46, 0x66, 0x92, 0x34, 0x49, 0xb5, 0xdd, 0xd5, 0x2e, 0xfb, 0x06, 0xb3, 0x75, 0xed, 0x23,
	0xf9, 0xe7, 0x55, 0xa4, 0x9d, 0x56, 0x49, 0x3b, 0x62, 0xdd, 0x25, 0xe7, 0xfc, 0xbe, 0xef, 0x7c,
	0x07, 0x8e, 0x5f, 0x26, 0x8a, 0x87, 0x31, 0xbf, 0xe3, 0x82, 0xb5, 0xe8, 0x2d, 0x11, 0x02, 0xa2,
	0x90, 0x69, 0x45, 0x5b, 0x6e, 0x0d, 0x2b, 0x2d, 0xad, 0x44, 0x2b, 0x8c, 0x60, 0xa2, 0x38, 0x76,
	0x9b, 0xc1, 0xfa, 0xd8, 0x04, 0x7a, 0x16, 0x84, 0xe1, 0x52, 0x7c, 0xbd, 0x52, 0x55, 0xb0, 0x35,
	0xe7, 0x9f, 0x65, 0x5d, 0x7d, 0xce, 0xfb, 0xc5, 0xb3, 0x49, 0xa3, 0x96, 0xd6, 0xcf, 0x41, 0xdf,
	0x73, 0x0a, 0xa8, 0xe7, 0xff, 0xaf, 0x83, 0xad, 0x11, 0x0b, 0x4c, 0xea, 0xfe, 0x09, 0x8f, 0x2c,
	0x68, 0xb4, 0x8b, 0x19, 0x99, 0x4b, 0x81, 0x17, 0xa8, 0x26, 0x74, 0xba, 0x60, 0x6c, 0x50, 0x5e,
	0x0e, 0x36, 0x4a, 0x0a, 0x03, 0x9b, 0x85, 0xe1, 0xa0, 0x94, 0x2f, 0xbc, 0x24, 0x1e, 0xe2, 0xfe,
	0xbf, 0x3a, 0xd8, 0x0b, 0xd2, 0x9e, 0x0e, 0xdd, 0xce, 0xf6, 0xf9, 0x04, 0x66, 0xf3, 0x76, 0x7e,
	0xe4, 0x9c, 0x51, 0xaf, 0x89, 0x87, 0x46, 0x39, 0x7f, 0xcd, 0xd9, 0xbe, 0x11, 0x91, 0x3e, 0xe8,
	0x53, 0x6e, 0x2c, 0xda, 0xcb, 0xb0, 0xfb, 0x86, 0x9d, 0x25, 0xa8, 0xfe, 0x46, 0xe2, 0x84, 0x79,
	0x4b, 0x3c, 0xf4, 0x98, 0xf3, 0x8b, 0x75, 0xb0, 0xa9, 0xa0, 0x49, 0xc4, 0x8d, 0x8c, 0x2f, 0x0d,
	0x68, 0x83, 0x70, 0xf6, 0x62, 0x0b, 0xe0, 0x2c, 0x46, 0xb8, 0x34, 0xef, 0x64, 0x78, 0x4f, 0xbc,
	0x60, 0x63, 0x38, 0x28, 0x15, 0x53, 0x69, 0x65, 0x2a, 0xad, 0x44, 0x92, 0x71, 0x3a, 0x1a, 0x94,
	0x3c, 0x26, 0x8f, 0xaf, 0xfd, 0x55, 0x2a, 0x63, 0xdc, 0xe9, 0x3e, 0x10, 0x81, 0xad, 0x4d, 0x8f,
	0x68, 0x7c, 0x9b, 0x57, 0x47, 0x4c, 0x46, 0x44, 0x30, 0x7c, 0x50, 0xb5, 0x16, 0x53, 0x19, 0x87,
	0x93, 0x16, 0x95, 0x51, 0x48, 0x94, 0x0a, 0x17, 0xcf, 0xfd, 0xd0, 0xfd, 0x3e, 0x79, 0x7f, 0x9a,
	0x8d, 0x5a, 0xfb, 0xef, 0x44, 0xb5, 0xff, 0x11, 0x00, 0x00, 0xff, 0xff, 0x46, 0x4c, 0x02, 0x06,
	0x20, 0x03, 0x00, 0x00,
}
