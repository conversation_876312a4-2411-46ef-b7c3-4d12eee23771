package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
	"golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
	comKafka "golang.52tt.com/services/common/kafka"
)

const topicChannelEvent = "simple_channel_ev"

type ChannelEventSub struct {
	kafkaSub   subscriber.Subscriber
	clientPool *model.SClientPool
}

func NewChannelEventSub(kfkConf *config.KafkaConfig, clientPool *model.SClientPool) (*ChannelEventSub, error) {

	/*
		conf := sarama.NewConfig()
		conf.ClientID = clientId
		conf.Consumer.Offsets.Initial = sarama.OffsetNewest
		conf.Consumer.Return.Errors = true

		kafkaSub, err := event.NewKafkaSub(topicChannelEvent, brokers, groupId, topics, conf)
		if err != nil {
			log.Errorf("Failed to create kafka-subscriber %+v", err)
			return nil, err
		}

		kafkaSub.SetIsMetrics()
	*/

	sub := &ChannelEventSub{
		clientPool: clientPool,
	}

	kafkaSub, err := comKafka.NewEventLinkSubscriber(kfkConf, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		log.Errorf("NewChannelEventSub Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	//sub.SetMessageProcessor(sub.handlerEvent)
	sub.kafkaSub = kafkaSub
	return sub, nil
}

func (s *ChannelEventSub) Close() {
	s.kafkaSub.Stop()
}

func (s *ChannelEventSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case topicChannelEvent:
		return s.handlerChannelEvent(msg)
	default:
		return nil, false
	}
}

func (s *ChannelEventSub) handlerChannelEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	channelEvent := &kafkachannalevent.ChSimpleEvent{}
	err := proto.Unmarshal(msg.Value, channelEvent)
	if err != nil {
		log.Debugf("ChannelEventSub handlerChannelEvent Unmarshal err:%v", err)
		return nil, false
	}

	log.Debugf("handlerChannelEvent ev:%v", channelEvent)

	if channelEvent.GetEventType() != uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER) {
		return nil, false
	}

	enterOpt := &kafkachannalevent.ChSimpleEnterOpt{}
	err = proto.Unmarshal(channelEvent.GetOptPbInfo(), enterOpt)
	if err != nil {
		log.Errorf("handlerChannelEvent enterOpt Unmarshal ev:%+v err:%v", channelEvent, err)
		return err, false
	}

	log.Debugf("handlerChannelEvent ev:%v enterOpt:%v", channelEvent, enterOpt)

	if enterOpt.GetSource() != uint32(channelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_FUN_PAGE_CARD_RECOMMENDED_LIST) &&
		enterOpt.GetSource() != uint32(channelPB.ChannelEnterReq_ENUM_CHANNEL_ENTER_FUN_PAGE_CARD_LOTTERY_HALF_PAGE) &&
		enterOpt.GetSource() != uint32(channelPB.ChannelEnterReq_ENUM_CHANNEL_TOP_OVER_LAY) {
		log.Debugf("handlerChannelEvent no need proc ev:%v enterOpt:%v", channelEvent, enterOpt)
		return nil, false
	}

	//处理顶部浮窗进房
	if enterOpt.GetSource() == uint32(channelPB.ChannelEnterReq_ENUM_CHANNEL_TOP_OVER_LAY) {
		_ = s.clientPool.PgcRecListPopCache.HandleEnterTopWinChannel(channelEvent.GetUid(), channelEvent.GetChId())
		return nil, false
	}

	// 获取房间的抽奖信息
	mapId2Info, err := s.clientPool.Cache.BatchGetChLotteryInfo([]uint32{channelEvent.GetChId()})
	if err != nil {
		log.Errorf("handlerChannelEvent BatchGetChLotteryInfo failed ev:%+v err:%v", channelEvent, err)
		return err, false
	}

	if len(mapId2Info) == 0 {
		log.Debugf("handlerChannelEvent no need proc ev:%+v", channelEvent)
		return nil, false
	}

	info := mapId2Info[channelEvent.GetChId()]
	log.Debugf("handlerChannelEvent ev:%+v info:%+v", channelEvent, info)

	if info.ChannelId == channelEvent.GetChId() {
		err = s.clientPool.Cache.IncrEnterChCnt(info.LotteryId)
		if err != nil {
			log.Errorf("handlerChannelEvent IncrEnterChCnt failed  ev:%+v info:%v err:%v", channelEvent, info, err)
			return err, false
		}
	}

	return nil, false
}
