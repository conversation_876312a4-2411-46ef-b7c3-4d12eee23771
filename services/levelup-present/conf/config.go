package conf

import (
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"os"
)

const configFile = "/data/oss/conf-center/tt/levelup-present-conf.json"


var LpConfig LevelupPresentConfig

func Init() {
	err := LpConfig.Load()
	if err != nil {
		log.Errorln(err)
		panic(err)
	}
	log.Debugln("Init ok")
}

type LevelupPresentConfig struct {
	FeishuUrl string `json:"feishu_url"`
	PushFeishu bool `json:"push_feishu"`
	Env string `json:"env"`
}

func (config *LevelupPresentConfig) Load() error {
	path := configFile
	envPath := os.Getenv("CONFIG_PATH")
	if envPath != "" {
		path = envPath
	}
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return err
	}

	err = json.Unmarshal(data, config)
	if err != nil {
		return err
	}

	return nil
}

