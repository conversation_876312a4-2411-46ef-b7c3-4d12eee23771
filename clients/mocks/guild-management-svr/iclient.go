// Code generated by MockGen. DO NOT EDIT.
// Source: /home/<USER>/gopath/src/golang.52tt.com/clients/guild-management-svr/iclient.go

// Package guild_management_svr is a generated GoMock package.
package guild_management_svr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	guild_management_svr "golang.52tt.com/protocol/services/guild-management-svr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddAgentAnchor mocks base method.
func (m *MockIClient) AddAgentAnchor(ctx context.Context, guildId, agentUid uint32, anchorUidList []uint32) (*guild_management_svr.AddAgentAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAgentAnchor", ctx, guildId, agentUid, anchorUidList)
	ret0, _ := ret[0].(*guild_management_svr.AddAgentAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddAgentAnchor indicates an expected call of AddAgentAnchor.
func (mr *MockIClientMockRecorder) AddAgentAnchor(ctx, guildId, agentUid, anchorUidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAgentAnchor", reflect.TypeOf((*MockIClient)(nil).AddAgentAnchor), ctx, guildId, agentUid, anchorUidList)
}

// AddGuildAgent mocks base method.
func (m *MockIClient) AddGuildAgent(ctx context.Context, req *guild_management_svr.AddGuildAgentReq) (*guild_management_svr.AddGuildAgentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGuildAgent", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.AddGuildAgentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddGuildAgent indicates an expected call of AddGuildAgent.
func (mr *MockIClientMockRecorder) AddGuildAgent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGuildAgent", reflect.TypeOf((*MockIClient)(nil).AddGuildAgent), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CheckIsNeedVerify mocks base method.
func (m *MockIClient) CheckIsNeedVerify(ctx context.Context, req *guild_management_svr.CheckIsNeedVerifyReq) (*guild_management_svr.CheckIsNeedVerifyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsNeedVerify", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.CheckIsNeedVerifyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckIsNeedVerify indicates an expected call of CheckIsNeedVerify.
func (mr *MockIClientMockRecorder) CheckIsNeedVerify(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsNeedVerify", reflect.TypeOf((*MockIClient)(nil).CheckIsNeedVerify), ctx, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DelAgentAnchor mocks base method.
func (m *MockIClient) DelAgentAnchor(ctx context.Context, guildId, agentUid uint32, anchorUidList []uint32) (*guild_management_svr.DelAgentAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAgentAnchor", ctx, guildId, agentUid, anchorUidList)
	ret0, _ := ret[0].(*guild_management_svr.DelAgentAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelAgentAnchor indicates an expected call of DelAgentAnchor.
func (mr *MockIClientMockRecorder) DelAgentAnchor(ctx, guildId, agentUid, anchorUidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAgentAnchor", reflect.TypeOf((*MockIClient)(nil).DelAgentAnchor), ctx, guildId, agentUid, anchorUidList)
}

// DelGuildAgent mocks base method.
func (m *MockIClient) DelGuildAgent(ctx context.Context, guildId, agentUid, agentType uint32) (*guild_management_svr.DelGuildAgentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelGuildAgent", ctx, guildId, agentUid, agentType)
	ret0, _ := ret[0].(*guild_management_svr.DelGuildAgentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DelGuildAgent indicates an expected call of DelGuildAgent.
func (mr *MockIClientMockRecorder) DelGuildAgent(ctx, guildId, agentUid, agentType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelGuildAgent", reflect.TypeOf((*MockIClient)(nil).DelGuildAgent), ctx, guildId, agentUid, agentType)
}

// GetAgentAnchor mocks base method.
func (m *MockIClient) GetAgentAnchor(ctx context.Context, agentUid, anchorUid uint32) (*guild_management_svr.GetAgentAnchorResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentAnchor", ctx, agentUid, anchorUid)
	ret0, _ := ret[0].(*guild_management_svr.GetAgentAnchorResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAgentAnchor indicates an expected call of GetAgentAnchor.
func (mr *MockIClientMockRecorder) GetAgentAnchor(ctx, agentUid, anchorUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentAnchor", reflect.TypeOf((*MockIClient)(nil).GetAgentAnchor), ctx, agentUid, anchorUid)
}

// GetAgentAnchorList mocks base method.
func (m *MockIClient) GetAgentAnchorList(ctx context.Context, agentUid, page, pageSize uint32) (*guild_management_svr.GetAgentAnchorListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentAnchorList", ctx, agentUid, page, pageSize)
	ret0, _ := ret[0].(*guild_management_svr.GetAgentAnchorListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAgentAnchorList indicates an expected call of GetAgentAnchorList.
func (mr *MockIClientMockRecorder) GetAgentAnchorList(ctx, agentUid, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentAnchorList", reflect.TypeOf((*MockIClient)(nil).GetAgentAnchorList), ctx, agentUid, page, pageSize)
}

// GetAgentGuild mocks base method.
func (m *MockIClient) GetAgentGuild(ctx context.Context, agentUid uint32) (*guild_management_svr.GetAgentGuildResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAgentGuild", ctx, agentUid)
	ret0, _ := ret[0].(*guild_management_svr.GetAgentGuildResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAgentGuild indicates an expected call of GetAgentGuild.
func (mr *MockIClientMockRecorder) GetAgentGuild(ctx, agentUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAgentGuild", reflect.TypeOf((*MockIClient)(nil).GetAgentGuild), ctx, agentUid)
}

// GetAnchorAgent mocks base method.
func (m *MockIClient) GetAnchorAgent(ctx context.Context, anchorUid uint32) (*guild_management_svr.GetAnchorAgentResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorAgent", ctx, anchorUid)
	ret0, _ := ret[0].(*guild_management_svr.GetAnchorAgentResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAnchorAgent indicates an expected call of GetAnchorAgent.
func (mr *MockIClientMockRecorder) GetAnchorAgent(ctx, anchorUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorAgent", reflect.TypeOf((*MockIClient)(nil).GetAnchorAgent), ctx, anchorUid)
}

// GetEsportGameMonthlyStat mocks base method.
func (m *MockIClient) GetEsportGameMonthlyStat(ctx context.Context, req *guild_management_svr.GetEsportGameMonthlyStatReq) (*guild_management_svr.GetEsportGameMonthlyStatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGameMonthlyStat", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.GetEsportGameMonthlyStatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetEsportGameMonthlyStat indicates an expected call of GetEsportGameMonthlyStat.
func (mr *MockIClientMockRecorder) GetEsportGameMonthlyStat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameMonthlyStat", reflect.TypeOf((*MockIClient)(nil).GetEsportGameMonthlyStat), ctx, req)
}

// GetEsportGuildDailyStat mocks base method.
func (m *MockIClient) GetEsportGuildDailyStat(ctx context.Context, req *guild_management_svr.GetEsportGuildDailyStatReq) (*guild_management_svr.GetEsportGuildDailyStatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGuildDailyStat", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.GetEsportGuildDailyStatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetEsportGuildDailyStat indicates an expected call of GetEsportGuildDailyStat.
func (mr *MockIClientMockRecorder) GetEsportGuildDailyStat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGuildDailyStat", reflect.TypeOf((*MockIClient)(nil).GetEsportGuildDailyStat), ctx, req)
}

// GetEsportGuildMonthlyStat mocks base method.
func (m *MockIClient) GetEsportGuildMonthlyStat(ctx context.Context, req *guild_management_svr.GetEsportGuildMonthlyStatReq) (*guild_management_svr.GetEsportGuildMonthlyStatResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportGuildMonthlyStat", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.GetEsportGuildMonthlyStatResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetEsportGuildMonthlyStat indicates an expected call of GetEsportGuildMonthlyStat.
func (mr *MockIClientMockRecorder) GetEsportGuildMonthlyStat(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGuildMonthlyStat", reflect.TypeOf((*MockIClient)(nil).GetEsportGuildMonthlyStat), ctx, req)
}

// GetEsportPractitionerDaily mocks base method.
func (m *MockIClient) GetEsportPractitionerDaily(ctx context.Context, req *guild_management_svr.GetEsportPractitionerDailyReq) (*guild_management_svr.GetEsportPractitionerDailyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportPractitionerDaily", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.GetEsportPractitionerDailyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetEsportPractitionerDaily indicates an expected call of GetEsportPractitionerDaily.
func (mr *MockIClientMockRecorder) GetEsportPractitionerDaily(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportPractitionerDaily", reflect.TypeOf((*MockIClient)(nil).GetEsportPractitionerDaily), ctx, req)
}

// GetEsportPractitionerMonthly mocks base method.
func (m *MockIClient) GetEsportPractitionerMonthly(ctx context.Context, req *guild_management_svr.GetEsportPractitionerMonthlyReq) (*guild_management_svr.GetEsportPractitionerMonthlyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEsportPractitionerMonthly", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.GetEsportPractitionerMonthlyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetEsportPractitionerMonthly indicates an expected call of GetEsportPractitionerMonthly.
func (mr *MockIClientMockRecorder) GetEsportPractitionerMonthly(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportPractitionerMonthly", reflect.TypeOf((*MockIClient)(nil).GetEsportPractitionerMonthly), ctx, req)
}

// GetGuildAgentList mocks base method.
func (m *MockIClient) GetGuildAgentList(ctx context.Context, guildId, agentType uint32, uidList []uint32) (*guild_management_svr.GetGuildAgentListResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildAgentList", ctx, guildId, agentType, uidList)
	ret0, _ := ret[0].(*guild_management_svr.GetGuildAgentListResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetGuildAgentList indicates an expected call of GetGuildAgentList.
func (mr *MockIClientMockRecorder) GetGuildAgentList(ctx, guildId, agentType, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildAgentList", reflect.TypeOf((*MockIClient)(nil).GetGuildAgentList), ctx, guildId, agentType, uidList)
}

// SetVerifyFlag mocks base method.
func (m *MockIClient) SetVerifyFlag(ctx context.Context, req *guild_management_svr.SetVerifyFlagReq) (*guild_management_svr.SetVerifyFlagResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVerifyFlag", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.SetVerifyFlagResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetVerifyFlag indicates an expected call of SetVerifyFlag.
func (mr *MockIClientMockRecorder) SetVerifyFlag(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVerifyFlag", reflect.TypeOf((*MockIClient)(nil).SetVerifyFlag), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdateAgentDataPermission mocks base method.
func (m *MockIClient) UpdateAgentDataPermission(ctx context.Context, req *guild_management_svr.UpdateAgentDataPermissionReq) (*guild_management_svr.UpdateAgentDataPermissionResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAgentDataPermission", ctx, req)
	ret0, _ := ret[0].(*guild_management_svr.UpdateAgentDataPermissionResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateAgentDataPermission indicates an expected call of UpdateAgentDataPermission.
func (mr *MockIClientMockRecorder) UpdateAgentDataPermission(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAgentDataPermission", reflect.TypeOf((*MockIClient)(nil).UpdateAgentDataPermission), ctx, req)
}
