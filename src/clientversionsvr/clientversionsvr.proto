syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";
package clientversion;


enum VersionType{
    VT_NIL          = 0;
    VT_ANDROID       = 0x0001;
    VT_IOS            = 0x0002;
    VT_IOS_Enterprise = 0x0004;
    VT_ANDROID_LITE       = 0x0008;  //市场裁剪包, 对应ga.BaseReq.market_id=MARKET_LITE
    VT_ANDROID_HUANYOU    = 0x0010;  //tt 欢游, 对应ga.BaseReq.market_id = MARKET_HUANYOU(=2)
    VT_ANDROID_ZAIYA      = 0x0020;  //tt 在呀, 对应ga.BaseReq.market_id = MARKET_ZAIYA(=3)
    VT_ANDROID_TOPSPEED   = 0x0040;  //tt 极速版, 对应ga.BaseReq.market_id = MARKET_TOP_SPEED(=4)
    VT_ANDROID_MAIKE      = 0x0080;  //tt maike, 对应ga.BaseReq.market_id = MARKET_MAIKE(=5)
    VT_ANDROID_MIJING     = 0x0100;  //tt mijing, 对应ga.BaseReq.market_id = MARKET_MIJING(=6)
    VT_BUTT = 0xf00000;
}


// n_val,s_val 指类似 3.1.1 版本号
// 2017.7.31 增 version_code, 单调递增，客户端在做版本新旧比较时，优选 version_code
message VersionNumber{
    optional uint32 n_val = 1;
    optional string s_val = 2;
    optional uint32 version_code = 3;   // 优选
}

message GetLatestVersionReq{
    required uint32 uid = 1;
    required uint32 version_type = 2;         //VersionType
    optional VersionNumber version_number = 3;    //VersionNumber
    optional uint32 guild_id = 4;
    optional string client_ip = 5;
    optional uint32 protocol_cmd = 6;   //命令字
}

message Version{
    required uint32 version_type = 1;
    required VersionNumber version_number = 2;
    required string download_url = 3;
    optional string prompt_title = 4;      // 升级提示title
    optional string prompt_content = 5;    // 升级提示content
    optional string file_md5 = 6;          // 文件md5
    optional string file_header_md5 = 7;     // 文件前100KB的md5
    optional VersionNumber prompt_below_ver = 8;       // 低于这个版本都弹框提示
    optional bool force_upg = 9;                // 强升
    optional VersionNumber force_upg_ver = 10; // 最小强升版本 低于这个版本的号的都需要强制升级
    optional uint32 version_code = 11;      //deprecated, 不使用，用VersionNumber里的
    optional string hc_plugin_url = 12;     //欢城插件配置的地址

    optional uint32 high_freq_prompt_after = 13;       // 之后进行高频提醒, from config
    optional uint32 high_freq_prompt_interval = 14;    // 高频提醒周期, from config
}

message GetLatestVersionRsp{
    optional Version version = 1;
}

message GetVersionDistCountReq{
    required uint32 version_type = 1;
    required VersionNumber version_number = 2;  
}

message GetVersionDistCountResp{
    optional uint32 count = 1;
}

message UserUseVersion{
    required uint32 uid = 1;
    required uint32 version_type = 2;   //VersionType
    required VersionNumber version_number = 3; //VersionNumber
    required uint32 at = 4;
}

message TrackUserVersionReq{
    required UserUseVersion user_version = 1;
}

message TrackUserVersionRsp{
}

message GetUserLatestVersionReq{
    required uint32 uid = 1;
}

message GetUserLatestVersionRsp{
    optional UserUseVersion user_version = 1;
}
//获取版本使用情况
message Time{
    required uint32 year = 1;
    required uint32 month = 2;
    optional uint32 day = 3;
    optional uint32 week_num = 4;   //年度的第几周
}
message VersionUserCount{
    required Time time = 1;
    required uint32 count = 2;
}

message GetVersionUserCountReq{
    enum QueryMethod{
        QM_DAILY = 1;
        QM_WEEKLY = 2;        
        QM_MONTHLY = 3;
        //QM_RANGE = 4;   //保留
    }

    required uint32 query_method = 1;   //QueryMethod
    required uint32 version_type = 2;   //VersionType
    required VersionNumber version_number = 3; //VersionNumber
    required Time begin_time = 4;   //
    optional Time end_time = 5;
}

message GetVersionUserCountRsp{
    repeated VersionUserCount count_list = 1;
}

//for maintain
message DumpVersionUpgradeInfoReq{
}

message PolicyRule{
    required string name = 1;
    repeated string parameters = 2;
    required string action = 3;
}

message Policy{
    required uint32 version_type = 1;
    required VersionNumber version_number = 2;
    repeated PolicyRule rule_list = 3;
}

message DumpVersionUpgradeInfoRsp{
    repeated Version version_list = 1;
    repeated Policy policy_list = 2;
}

service ClientVersionSvr{
    option( tlvpickle.Magic ) = 15388;
    rpc GetLatestVersion ( GetLatestVersionReq ) returns ( GetLatestVersionRsp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "u:t:e:g:h:p:";
        option( tlvpickle.Usage ) = "-u <uid> -t <version_type:0x01=android/0x08=lite> [-e <version,eg:2.9.6>] [-g <guild_id>] [-h <client_ip>] [-p <proto cmdid>]";
    }
    //查询放量数
    rpc GetVersionDistCount ( GetVersionDistCountReq ) returns ( GetVersionDistCountResp ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "t:e:";
        option( tlvpickle.Usage ) = "-t <version_type:0x01=android/0x08> [-e <version,eg:2.9.6>]";
    }

    //
    rpc TrackUserVersion (TrackUserVersionReq) returns (TrackUserVersionRsp){
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "u:t:e:a:";
        option( tlvpickle.Usage ) = "-u <uid> -t <version_type:0x01=android/0x08=lite> [-e <version,eg:2.9.6>] [-a <at unix timestamp>] ";
    }
    rpc GetUserLatestVersion( GetUserLatestVersionReq ) returns ( GetUserLatestVersionRsp ){
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
    }

    rpc GetVersionUserCount ( GetVersionUserCountReq ) returns ( GetVersionUserCountRsp ){
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "m:t:e:b:s:";
        option( tlvpickle.Usage ) = "-m <1=daily/2=weekly/3=monthly> -t <version_type:0x01=android/0x08=lite> -e <version,eg:2.9.6> -b <begin time, fmt:2017-03-04> [-s <stop time, fmt:2017-03-08>]";
    }

    rpc DumpVersionUpgradeInfo( DumpVersionUpgradeInfoReq ) returns ( DumpVersionUpgradeInfoRsp ) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
}


//内部使用
message UserLatestVersionData{
    required uint32 uid = 1;
    required uint32 version_type = 2;
    required uint32 version_number = 3;
    required uint32 at = 4;
    // optional uint32 version_code =5; 暂不记录
}


