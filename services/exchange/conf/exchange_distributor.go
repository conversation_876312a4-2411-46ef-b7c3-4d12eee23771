package conf

import (
    "encoding/json"
    "io/ioutil"
    "os"
)

const distributorConfigFile = "/data/oss/conf-center/tt/exchange_distributor.json"


var DistributorConfig DistributorConfigStruct

func InitDistributorConfig() {
    err := DistributorConfig.Load()
    if err != nil {
        panic(err)
    }
}

type DistributorConfigStruct struct {
    DistributorPercentMap map[string]uint32            `json:"distributor_percent_map"`
    SixTyAgeDistributor   []string                     `json:"six_ty_age_distributor"`
    Fee                   uint32                       `json:"fee"`
    ContractMap           map[string]map[string]string `json:"contract_map"`        //普通协议，适合有签约的，马甲包区分
    AnchorContractMap     map[string]map[string]string `json:"anchor_contract_map"` //主播协议，适合没有签约的，较少的，马甲包区分
    NoContractExtra map[string]map[string]string `json:"no_contract_extra"`
}

func (c *DistributorConfigStruct) Load() error {
    path := distributorConfigFile
    envPath := os.Getenv("DISTRIBUTOR_CONFIG_PATH")
    if envPath != "" {
        path = envPath
    }
    data, err := ioutil.ReadFile(path)
    if err != nil {
        return err
    }

    err = json.Unmarshal(data, c)
    if err != nil {
        return err
    }


    return nil
}

