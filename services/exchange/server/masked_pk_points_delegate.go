package server

import (
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	masked_pk_score "golang.52tt.com/clients/masked-pk-score"
	"golang.52tt.com/pkg/commission"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/tbean"
	masked_pk_score2 "golang.52tt.com/protocol/services/masked-pk-score"
	"golang.52tt.com/services/exchange/cache"
	"golang.52tt.com/services/exchange/model"
	"golang.org/x/net/context"
	"time"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	exchangePB "golang.52tt.com/protocol/services/exchange"
)

type MaskedPkPointsDelegate struct {
	tbeanClient      tbean.Client
	MaskedPkScoreCli *masked_pk_score.Client
	//commissionClient commission.Client
	rc *redis.Client
	rb *redis.Client
	s  *exchangeServer
	sc *serverConfig
}

func newMaskedPkPointsDelegate(sc *serverConfig, c *redis.Client, rb *redis.Client, s *exchangeServer) delegate {
	MaskedPkScoreCli, _ := masked_pk_score.NewClient()
	return &MaskedPkPointsDelegate{
		tbeanClient:      tbean.NewClient(sc.tbeanContextPath),
		MaskedPkScoreCli: MaskedPkScoreCli,
		//commissionClient: commission.NewClient(sc.commissionURLPrefix, sc.commissionMaskedPkAppID, sc.commissionMaskedPkSignKey),
		rc: c,
		rb: rb,
		s:  s,
		sc: sc,
	}
}

func (d *MaskedPkPointsDelegate) getBalance(ctx context.Context, uid uint32) (uint64, error) {
	b, err := d.MaskedPkScoreCli.GetUserMaskPkScore(ctx, uid)
	return uint64(b), err
}

func (d *MaskedPkPointsDelegate) precheckTx(ctx context.Context, tx *model.ExchangeTransaction) error {
	//查看是否在黑名单
	isBlack, err := d.tbeanClient.CheckBlackUser(ctx, fmt.Sprintf("%d", tx.UserID), tbean.BLACK_TYPE_RECHARGE)
	if err != nil { //机房访问问题，就放过
		log.ErrorWithCtx(ctx, "tbeanClient.CheckBlackUser err=%+v", err)
	}
	if isBlack {
		log.InfoWithCtx(ctx, "tbeanClient.CheckBlackUser uid=%d is black", tx.UserID)
		return protocol.NewServerError(status.ErrExchangeBlackUser)
	}

	return nil
}

func (d *MaskedPkPointsDelegate) processTx(ctx context.Context, tx *model.ExchangeTransaction) (outsideOrderId string, outsideTime time.Time, balance uint32, err error) {
	log.DebugWithCtx(ctx, "MaskedPkPointsDelegate processing exchangeTransaction: %+v", tx)

	if tx.SourceType != uint32(exchangePB.CurrencyType_MASKED_PK_POINTS) {
		err = errCurrencyTypeNotSupported
		return
	}

	total := tx.TargetAmount + tx.BonusAmount
	log.InfoWithCtx(ctx, "Exchange MASKEDPK-POINTS-COMMISSION OK %d %d %d", tx.UserID, tx.SourceAmount, total)
	var desc string
	switch tx.TargetType {
	case uint32(exchangePB.CurrencyType_COMMISSION):
		desc = fmt.Sprintf("结算: %d 蒙面pk积分 -> %d 佣金", tx.SourceAmount, total)
	default:
		err = errCurrencyTypeNotSupported
		return
	}

	uid := tx.UserID
	if tx.OrderDesc != "" {
		desc = tx.OrderDesc
	}

	locker := cache.NewLocker(d.rc, d.rb)
	lockV := locker.GenerateValue()
	if !locker.LockPkScore(uid, lockV) {
		err = errors.New("score locked")
		return
	}
	defer locker.UnlockPkScore(uid, lockV)

	// 扣除积分
	_, err = d.MaskedPkScoreCli.AddUserMaskPkScore(ctx, tx.OrderID, uid, uint32(masked_pk_score2.SourceType_ScoreExchange),
		uint32(tx.CreateAt.Unix()), -1*int32(tx.SourceAmount), "")
	if err != nil {
		log.ErrorWithCtx(ctx, "MaskedPkPointsDelegate AddUserMaskPkScore uid(%d) orderId(%s) failed: %v", uid, tx.OrderID, err)
		return
	}

	switch tx.TargetType {
	case uint32(exchangePB.CurrencyType_COMMISSION):
		//if err := d.commissionClient.SettlementAndEncashment(ctx, tx.UserID, uint64(total), string(tx.UserData), desc); err != nil {
		//	log.ErrorWithCtx(ctx, "commissionClient.Settlement %d %d %s %s failed: %v", tx.UserID, total, string(tx.UserData), desc, err)
		//	return 0, err
		//}

		var pwResp *exchangePB.PrivateWithdrawResp
		pwResp, err = d.s.PrivateWithdraw(ctx, &exchangePB.PrivateWithdrawReq{
			OrderId:    tx.OrderID,
			Uid:        tx.UserID,
			Total:      uint64(total),
			Date:       tx.UserData,
			Remark:     desc,
			Appid:      d.sc.commissionMaskedPkAppID,
			SignKey:    d.sc.commissionMaskedPkSignKey,
			CreateTime: tx.CreateAt.Unix(),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "PrivateWithdraw %d %d %s %s failed: %v", tx.UserID, total, tx.UserData, desc, err)
			return
		}
		outsideOrderId = pwResp.GetOrderId()
		outsideTime = time.Unix(pwResp.GetCreateTime(), 0)

		log.InfoWithCtx(ctx, "Settlement MASKEDPK-POINTS-COMMISSION %d %d %s %s OK", tx.UserID, total, tx.UserData, desc)
	}

	if len(tx.FinishMessage) != 0 {
		go pushWithTTAssistant(tx.UserID, tx.FinishMessage)
	}

	return
}

func (d *MaskedPkPointsDelegate) isRetriableError(err error) bool {
	if e, ok := err.(protocol.ServerError); ok && e.Code() == status.ErrUserScoreNotEnough {
		return false
	}
	if e, ok := err.(protocol.ServerError); ok && e.Code() == status.ErrUserScoreOrderExist {
		return false
	}
	if e, ok := err.(commission.APIError); ok && e.Code() == commission.CodeDataExisted {
		return false
	}
	return true
}

/*func (d *MaskedPkPointsDelegate) pushWithTTAssistant(uid uint32, content string) {
	// content is serialized PushMessage
	var pm exchangePB.PushMessage
	if err := json.Unmarshal([]byte(content), &pm); err != nil {
		log.Errorf("Failed to unmarshal %s into PushMessage: %+v", content, err)
		return
	}

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.Platform = apiPB.Platform_UNSPECIFIED
	if len(pm.Highlight) == 0 {
		msg.ImContent.TextNormal = &apiPB.ImTextNormal{
			Content: pm.Content,
		}
		msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT)
	} else {
		msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
			Content:    pm.Content,
			Hightlight: pm.Highlight,
			Url:        pm.Url,
		}
		msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	}

	err := d.apiCenterClient.SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)

	if err != nil {
		log.Errorf("pushWithTTAssistant %d %v failed %v", uid, msg, err)
	} else {
		log.Debugf("pushWithTTAssistant %d %v OK", uid, msg)
	}
}*/
