syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/masked-call";
package masked_call;

service MaskedCall {
    //查询匹配信息，返回匹配系统配置及用户剩余匹配次数
    rpc QueryMatchInfo (QueryMatchInfoReq) returns (QueryMatchInfoResp) {
    }
    //开始匹配
    rpc StartMatch (StartMatchReq) returns (StartMatchResp) {
    }
    //取消匹配
    rpc CancelMatch (CancelMatchReq) returns (CancelMatchResp) {
    }
    //查询通话
    rpc QueryCallInfo (QueryCallInfoReq) returns (QueryCallInfoResp) {
    }
    //更新用户通话状态
    rpc SetConnectStatus (SetConnectStatusReq) returns (SetConnectStatusResp) {
    }
    //公开身份
    rpc Unmask (UnmaskReq) returns (UnmaskResp) {
    }
    //邀请公开身份
    rpc InviteUnmask (InviteUnmaskReq) returns (InviteUnmaskResp) {
    }
    //摇骰子
    rpc Roll (RollReq) returns (RollResp) {
    }
    //评价
    rpc Comment (CommentReq) returns (CommentResp) {
    }
    //举报
    rpc TipOff (TipOffReq) returns (TipOffResp) {
    }
    //上报音频
    rpc ReportAudio (ReportAudioReq) returns (ReportAudioResp) {
    }
}

//查询匹配信息
message QueryMatchInfoReq {
    uint32 uid = 1;
}
message QueryMatchInfoResp {
    uint32 ticket = 1; //剩余次数
    string banner_text = 2;
    string banner_slogan_img_url = 3;
    string banner_background_img_url = 4;

    bool match_opening = 5;
    uint32 time_begin = 6;
    uint32 time_end = 7;
    string closing_alert_text = 8;
}

//开始匹配
message StartMatchReq {
    uint32 uid = 1;
}
message StartMatchResp {
    uint32 timeout = 1; //超时剩余时间，重复发起匹配，返回上一次超时时间
    uint32 ticket = 2; //剩余次数
}

//取消匹配
message CancelMatchReq {
    uint32 uid = 1;
}
message CancelMatchResp {
    uint32 ticket = 1; //剩余次数
}

// --------- 通话推送 ----------
message Member {
    enum MemberStatus {
        CONNECT = 0; //连接中
        READY = 1; //已连接
        CLOSE = 2; //已断开
        BREAK = 3; //异常，连接超时、超时退房
    }

    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 sex = 4;

    uint32 status = 5; // 参考 MemberStatus

    bool masked = 6;
    string tag = 7;
}

message Call {
    enum CallStatus {
        START = 0; //开始
        CLOSE = 1; //结束，用户主动结束
        TIMEOUT = 2; //超时
        BREAK = 3; //异常，成员连接超时、成员超时退房
    }

    uint32 id = 1;
    uint32 channel_id = 2;

    repeated Member members = 3;

    uint32 status = 4; // 参考 CallStatus
    uint32 create_at = 5;
    uint32 timeout_at = 6; // 超时时间戳 (s)

    bool has_interact_roll = 7; // 标识是否出现过骰子互动
    bool has_interact_gift = 8; // 标识是否送过礼物

    uint64 seq = 9; //变更seq
}

//获取通话信息
message QueryCallInfoReq {
    uint32 uid = 1;
    uint32 channel_id = 2; //房间标识
}
message QueryCallInfoResp {
    Call info = 1;

    uint32 default_gift_id = 2; // 默认礼物
}

//设置连接状态
message SetConnectStatusReq {
    enum ConnectStatus {
        CHANNEL = 0; //进房
        MIC = 1; //上麦
        CLOSE = 2; //关闭
    }

    uint32 uid = 1;
    uint32 channel_id = 2; //房间标识
    uint32 status = 3; //参考 ConnectStatus
}
message SetConnectStatusResp {
}

//公开身份
message UnmaskReq {
    uint32 uid = 1;
    uint32 channel_id = 2; //房间标识
}
message UnmaskResp {
}

//邀请公开身份
message InviteUnmaskReq {
    uint32 uid = 1;
    uint32 channel_id = 2; //房间标识
}
message InviteUnmaskResp {
}

//评价
message CommentReq {
    enum Attitude {
        LIKE = 0;
        DISLIKE = 1;
        OTHER = 2;
    }

    uint32 uid = 1;
    uint32 channel_id = 2; //房间标识
    uint32 attitude = 3; //参考 Attitude
}
message CommentResp {
}

//roll点
message RollReq {
    uint32 uid = 1;
    uint32 channel_id = 2; //房间标识
}

message RollResp {
    uint32 point = 1;
}

//举报
message TipOffReq {
    uint32 uid = 1;         //举报用户
    uint32 target_uid = 2;  //被举报用户
    uint32 call_id = 3;     //通话标识
    uint32 type = 4;        //举报类型
    string reason = 5;      //举报原因
	repeated uint32 type_list = 6;  //举报类型复选
}

message TipOffResp {
}

message Audio {
    string url = 1;
    uint64 start_timestamp = 2;
}

message ReportAudioReq {
    uint32 call_id = 1;
    uint32 owner_id = 2;
    repeated Audio audio = 3;
}

message ReportAudioResp {
}