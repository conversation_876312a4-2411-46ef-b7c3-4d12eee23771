// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	cache "golang.52tt.com/services/virtual-image/virtual-image-user/internal/model/reminder/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// AddUserDelayDisplayReminder mocks base method.
func (m *MockICache) AddUserDelayDisplayReminder(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserDelayDisplayReminder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserDelayDisplayReminder indicates an expected call of AddUserDelayDisplayReminder.
func (mr *MockICacheMockRecorder) AddUserDelayDisplayReminder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserDelayDisplayReminder", reflect.TypeOf((*MockICache)(nil).AddUserDelayDisplayReminder), arg0, arg1, arg2)
}

// AddUserInChannelPool mocks base method.
func (m *MockICache) AddUserInChannelPool(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserInChannelPool", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserInChannelPool indicates an expected call of AddUserInChannelPool.
func (mr *MockICacheMockRecorder) AddUserInChannelPool(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserInChannelPool", reflect.TypeOf((*MockICache)(nil).AddUserInChannelPool), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// GetDelayDisplayReminderUidList mocks base method.
func (m *MockICache) GetDelayDisplayReminderUidList(arg0 context.Context, arg1, arg2 int64) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDelayDisplayReminderUidList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDelayDisplayReminderUidList indicates an expected call of GetDelayDisplayReminderUidList.
func (mr *MockICacheMockRecorder) GetDelayDisplayReminderUidList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDelayDisplayReminderUidList", reflect.TypeOf((*MockICache)(nil).GetDelayDisplayReminderUidList), arg0, arg1, arg2)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetUserChannelImDisplayReminder mocks base method.
func (m *MockICache) GetUserChannelImDisplayReminder(arg0 context.Context, arg1 uint32) (*cache.UserChannelImReminder, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserChannelImDisplayReminder", arg0, arg1)
	ret0, _ := ret[0].(*cache.UserChannelImReminder)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserChannelImDisplayReminder indicates an expected call of GetUserChannelImDisplayReminder.
func (mr *MockICacheMockRecorder) GetUserChannelImDisplayReminder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserChannelImDisplayReminder", reflect.TypeOf((*MockICache)(nil).GetUserChannelImDisplayReminder), arg0, arg1)
}

// GetUserFirstUseReminderFlag mocks base method.
func (m *MockICache) GetUserFirstUseReminderFlag(arg0 context.Context, arg1 uint32) (int64, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFirstUseReminderFlag", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserFirstUseReminderFlag indicates an expected call of GetUserFirstUseReminderFlag.
func (mr *MockICacheMockRecorder) GetUserFirstUseReminderFlag(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFirstUseReminderFlag", reflect.TypeOf((*MockICache)(nil).GetUserFirstUseReminderFlag), arg0, arg1)
}

// GetUserInChannelPool mocks base method.
func (m *MockICache) GetUserInChannelPool(arg0 context.Context, arg1, arg2 int64) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInChannelPool", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInChannelPool indicates an expected call of GetUserInChannelPool.
func (mr *MockICacheMockRecorder) GetUserInChannelPool(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInChannelPool", reflect.TypeOf((*MockICache)(nil).GetUserInChannelPool), arg0, arg1, arg2)
}

// GetUserLoginDisplayReminder mocks base method.
func (m *MockICache) GetUserLoginDisplayReminder(arg0 context.Context, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLoginDisplayReminder", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLoginDisplayReminder indicates an expected call of GetUserLoginDisplayReminder.
func (mr *MockICacheMockRecorder) GetUserLoginDisplayReminder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLoginDisplayReminder", reflect.TypeOf((*MockICache)(nil).GetUserLoginDisplayReminder), arg0, arg1)
}

// LPushDisplayRemainderUid mocks base method.
func (m *MockICache) LPushDisplayRemainderUid(arg0 context.Context, arg1 uint32, arg2 []uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPushDisplayRemainderUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// LPushDisplayRemainderUid indicates an expected call of LPushDisplayRemainderUid.
func (mr *MockICacheMockRecorder) LPushDisplayRemainderUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPushDisplayRemainderUid", reflect.TypeOf((*MockICache)(nil).LPushDisplayRemainderUid), arg0, arg1, arg2)
}

// LPushExpireInfo mocks base method.
func (m *MockICache) LPushExpireInfo(arg0 context.Context, arg1 uint32, arg2 []*cache.ExpireInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPushExpireInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// LPushExpireInfo indicates an expected call of LPushExpireInfo.
func (mr *MockICacheMockRecorder) LPushExpireInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPushExpireInfo", reflect.TypeOf((*MockICache)(nil).LPushExpireInfo), arg0, arg1, arg2)
}

// RPopDisplayRemainderUid mocks base method.
func (m *MockICache) RPopDisplayRemainderUid(arg0 context.Context, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPopDisplayRemainderUid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RPopDisplayRemainderUid indicates an expected call of RPopDisplayRemainderUid.
func (mr *MockICacheMockRecorder) RPopDisplayRemainderUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPopDisplayRemainderUid", reflect.TypeOf((*MockICache)(nil).RPopDisplayRemainderUid), arg0, arg1)
}

// RPopExpireInfo mocks base method.
func (m *MockICache) RPopExpireInfo(arg0 context.Context, arg1 uint32) (*cache.ExpireInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPopExpireInfo", arg0, arg1)
	ret0, _ := ret[0].(*cache.ExpireInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RPopExpireInfo indicates an expected call of RPopExpireInfo.
func (mr *MockICacheMockRecorder) RPopExpireInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPopExpireInfo", reflect.TypeOf((*MockICache)(nil).RPopExpireInfo), arg0, arg1)
}

// RemoveDelayDisplayReminder mocks base method.
func (m *MockICache) RemoveDelayDisplayReminder(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveDelayDisplayReminder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveDelayDisplayReminder indicates an expected call of RemoveDelayDisplayReminder.
func (mr *MockICacheMockRecorder) RemoveDelayDisplayReminder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveDelayDisplayReminder", reflect.TypeOf((*MockICache)(nil).RemoveDelayDisplayReminder), arg0, arg1)
}

// RemoveDelayDisplayReminderByTs mocks base method.
func (m *MockICache) RemoveDelayDisplayReminderByTs(arg0 context.Context, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveDelayDisplayReminderByTs", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveDelayDisplayReminderByTs indicates an expected call of RemoveDelayDisplayReminderByTs.
func (mr *MockICacheMockRecorder) RemoveDelayDisplayReminderByTs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveDelayDisplayReminderByTs", reflect.TypeOf((*MockICache)(nil).RemoveDelayDisplayReminderByTs), arg0, arg1)
}

// RemoveUserChannelImDisplayReminder mocks base method.
func (m *MockICache) RemoveUserChannelImDisplayReminder(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveUserChannelImDisplayReminder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveUserChannelImDisplayReminder indicates an expected call of RemoveUserChannelImDisplayReminder.
func (mr *MockICacheMockRecorder) RemoveUserChannelImDisplayReminder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUserChannelImDisplayReminder", reflect.TypeOf((*MockICache)(nil).RemoveUserChannelImDisplayReminder), arg0, arg1)
}

// RemoveUserInChannelPool mocks base method.
func (m *MockICache) RemoveUserInChannelPool(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveUserInChannelPool", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveUserInChannelPool indicates an expected call of RemoveUserInChannelPool.
func (mr *MockICacheMockRecorder) RemoveUserInChannelPool(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUserInChannelPool", reflect.TypeOf((*MockICache)(nil).RemoveUserInChannelPool), arg0, arg1)
}

// RemoveUserLoginDisplayReminder mocks base method.
func (m *MockICache) RemoveUserLoginDisplayReminder(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveUserLoginDisplayReminder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveUserLoginDisplayReminder indicates an expected call of RemoveUserLoginDisplayReminder.
func (mr *MockICacheMockRecorder) RemoveUserLoginDisplayReminder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveUserLoginDisplayReminder", reflect.TypeOf((*MockICache)(nil).RemoveUserLoginDisplayReminder), arg0, arg1)
}

// SetUserChannelImDisplayReminder mocks base method.
func (m *MockICache) SetUserChannelImDisplayReminder(arg0 context.Context, arg1 uint32, arg2 *cache.UserChannelImReminder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserChannelImDisplayReminder", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserChannelImDisplayReminder indicates an expected call of SetUserChannelImDisplayReminder.
func (mr *MockICacheMockRecorder) SetUserChannelImDisplayReminder(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserChannelImDisplayReminder", reflect.TypeOf((*MockICache)(nil).SetUserChannelImDisplayReminder), arg0, arg1, arg2)
}

// SetUserFirstUseReminderFlag mocks base method.
func (m *MockICache) SetUserFirstUseReminderFlag(arg0 context.Context, arg1 uint32, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFirstUseReminderFlag", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFirstUseReminderFlag indicates an expected call of SetUserFirstUseReminderFlag.
func (mr *MockICacheMockRecorder) SetUserFirstUseReminderFlag(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFirstUseReminderFlag", reflect.TypeOf((*MockICache)(nil).SetUserFirstUseReminderFlag), arg0, arg1, arg2)
}

// SetUserLoginDisplayReminder mocks base method.
func (m *MockICache) SetUserLoginDisplayReminder(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserLoginDisplayReminder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserLoginDisplayReminder indicates an expected call of SetUserLoginDisplayReminder.
func (mr *MockICacheMockRecorder) SetUserLoginDisplayReminder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserLoginDisplayReminder", reflect.TypeOf((*MockICache)(nil).SetUserLoginDisplayReminder), arg0, arg1)
}
