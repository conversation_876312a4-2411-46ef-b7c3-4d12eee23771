package store

//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/store.go -package=mocks golang.52tt.com/services/tt-rev/fellow-house/internal/model/user-house/store IStore

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
)

type Store struct {
    db, readOnlyDb mysql.DBx
}

func NewStore(db, readOnlyDb mysql.DBx) *Store {
	s := &Store{
		db: db,
        readOnlyDb: readOnlyDb,
	}

	_ = s.createInUseHouseTable(context.Background())
	return s
}

func (s *Store) Close() error {
	return s.db.Close()
}

func (s *Store) Transaction(ctx context.Context, f func(tx mysql.Txx) error) error {
	tx, err := s.db.Beginx()
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction Beginx fail err %v", err)
		return err
	}

	err = f(tx)
	if err != nil {
		log.ErrorWithCtx(ctx, "Transaction fail err %v", err)
		_ = tx.Rollback()
		return err
	}

	return tx.Commit()
}
