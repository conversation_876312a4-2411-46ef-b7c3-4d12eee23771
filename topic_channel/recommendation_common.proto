syntax="proto3";

package topic_channel.recommendation_common;

option go_package = "golang.52tt.com/protocol/services/topic_channel/recommendation_common";

message ChannelOpenGameStatusForRule {
    uint32 cid = 1;// channel id
    uint32 channel_game_status = 2;
    repeated uint32 game_player_limits = 3; //限制人数, 即达到可以开局的人数
}

message MiniGameRoomInfoForRule {
    uint32 cid = 1;// channel id
    repeated string game_settings = 2; // 游戏设置
}

message UserInfo
{
  uint32 uid = 1;
  uint32 sex = 2;
  uint32 registered_timestamp = 3;
  uint32 update_timestamp = 4;
}

message UserTag
{
  uint32 uid = 1;
  repeated TagInfo tag_list = 2;              // 一般标签
  repeated GameTagInfo game_tag_list = 3;     // 游戏标签 
  uint32 update_timestamp = 4;       // 更新时间戳
}

message TagInfo
{
  uint32 tag_id = 1;
  string tag_name = 2;
  uint32 tag_type = 3;
}

message GameTagInfo 
{
  uint32 tag_id = 1;
  string tag_name = 2;
  uint32 tag_type = 3;
  repeated GameTagExt game_ext_list = 4;      // 游戏标签二级属性
}


message GameCardInputVal {
    string elem_title = 1; // 标题
    string elem_val   = 2; // 输入的值
}

message GameTagExt
{
  string opt_name = 1;
  repeated string value_list = 2;
  repeated GameCardInputVal input_val = 4;    // 输入值
}

message OssColumn {
	uint32 idx = 1;
	string val = 2;
	string key = 3;		// key不为空，则用key，否则用idx
}

message OssStatsRow {
	repeated OssColumn values = 1;
}

message OssStatsBatchEvent {
	string src_ip = 1;
	uint32 server_timestamp = 2;
	string biz_type = 3;
	repeated OssStatsRow row_list = 4;
}

message TcGameSetting {
    message Val {
        string val = 1;
        Op op = 2;
    }
    enum Op {
       Op_EQ = 0; // 等于
       Op_GT = 1; // 大于
       Op_GTE = 2;  // 大于等于
       Op_LT = 3; // 小于
       Op_LTE = 4; // 小于等于
    }
    string key = 1;
    repeated Val val_list = 2;
    uint32 group_id = 3;
}

