package client

import (
	"github.com/ghodss/yaml"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"os"
	"os/user"
	"path"
	"strings"
	"sync"
)

var lock sync.Mutex
var config *serviceTargetConfig

func Init() error {
	var err error
	config, err = loadConfig(getServiceTargetPath())
	return err
}


func getServiceTargetPath() string {
	serviceTargetConfigFile := os.Getenv("SERVICE_TARGET_CONFIG_FILE")
	if serviceTargetConfigFile == "" {
		basePath := ""
		u, err := user.Current()
		if err == nil {
			basePath = u.HomeDir
		} else {
			log.Errorf("user.Current err=%v", err)
		}
		serviceTargetConfigFile = path.Join(basePath, "etc/client/service-targets.yaml")
	}
	return serviceTargetConfigFile
}

type serviceTargetConfig struct {
	Fallback string            `yaml:"fallback"`
	Services map[string]string `yaml:"services"`
}

func loadConfig(f string) (*serviceTargetConfig, error) {
	lock.Lock()
	defer lock.Unlock()
	data, err := ioutil.ReadFile(f)
	if err != nil {
		return nil, err
	}

	var c serviceTargetConfig
	c.Services = make(map[string]string)
	if err = yaml.Unmarshal(data, &c); err != nil {
		log.Warnf("Failed to parse service target config file @%s: %v", f, err)
		return nil, err
	}

	if c.Fallback == "" {
		c.Fallback = "dns:///{name}.52tt.local"
	}

	return &c, nil
}

func TargetForName(serverName string) string {
	lock.Lock()
	defer lock.Unlock()

	target, ok := config.Services[serverName]
	if !ok {
		return strings.Replace(config.Fallback, "{name}", serverName, -1)
	} else {
		return target
	}
}

