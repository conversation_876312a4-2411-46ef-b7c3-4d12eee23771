package recommend_backup

import (
	"context"
	"golang.52tt.com/protocol/services/ugc/content"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc/recommend-backup"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-recommend-backup"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewRecomomendBackUpClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.RecomomendBackUpClient {
	return c.Stub().(pb.RecomomendBackUpClient)
}

func (c *Client) GetRecommendBackup(ctx context.Context, limit uint32, contentType int32) (*pb.GetRecommendBackupRsp, protocol.ServerError) {
	in := &pb.GetRecommendBackupReq{Limit: limit, ContentType: content.ContentType(contentType)}
	resp, err := c.typedStub().GetRecommendBackup(ctx, in)
	return resp, protocol.ToServerError(err)
}
