result=`protoc -I=/mnt/hgfs/gopath/src/golang.52tt.com/services/guild-honor-halls/proto  --go_out=plugins=grpc:/mnt/hgfs/gopath/src guild-honor-halls.proto` 
if [ -n "$result" ] 
then  
echo "protoc err:"  
echo $result  
exit  
fi 
result=`cp /mnt/hgfs/gopath/src/golang.52tt.com/services/guild-honor-halls/proto/guild-honor-halls.proto /mnt/hgfs/gopath/src/golang.52tt.com/third-party/tt-protocol/service/quicksilver/guild-honor-halls` 
if [ -n "$result" ] 
then 
echo "cp err:" 
echo $result 
 exit  
fi 
