package mgr

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/deal_token"
	pb "golang.52tt.com/protocol/services/unified-pay-rmb"
	tpay_api "golang.52tt.com/protocol/services/unified-pay-rmb/tpay-api"
	"golang.52tt.com/services/unified-pay-rmb/pay_config"
	"golang.52tt.com/services/unified-pay-rmb/svr/mocks"
	mocks_config "golang.52tt.com/services/unified-pay-rmb/svr/mocks/pay_config"
)

func Test_RmbPayOrder(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockStore := mocks.NewMockIStore(ctl)
	mockCache := mocks.NewMockICache(ctl)
	mockTpayApi := mocks.NewMockTPayClient(ctl)
	mockConfig := mocks_config.NewMockIPayConfig(ctl)
	mockRiskMgr := mocks.NewMockIRiskMgr(ctl)

	ctx := context.Background()

	req := &pb.RmbPayOrderReq{
		OutTradeNo: "test-myself-2596068-20231010-1",
		PayChannel: "WEBPAY",
		RmbAppId:   "test",
		OsType:     "a",
		Uid:        2596068,
		TotalPrice: 1000,
		OrderTitle: "good",
		MarketId:   0,
	}
	nowToken := deal_token.NewDealTokenData(req.OutTradeNo, req.OutTradeNo, "test", int64(req.Uid), int64(req.TotalPrice))
	req.DealToken, _ = deal_token.Encode(nowToken)
	t.Logf("dealtoken:%s", req.DealToken)

	//now := uint32(time.Now().Unix())
	mockConfig.EXPECT().GetPayRmbConf().Return(&pay_config.PayRmbConf{
		ApiHost:     "http://127.0.0.1",
		ApiClientID: "xxx",
		ApiKey:      "test",
	}).AnyTimes()
	mockConfig.EXPECT().GetPayRmbAppConf(gomock.Any()).Return(
		&pay_config.PayRmbAppConf{
			RmbAppID: "test",
			TimeOut:  5,
			MarketList: []*pay_config.PayMarketConf{
				&pay_config.PayMarketConf{
					MarketID:   0,
					BusinessId: "business_id",
					Fm:         "tt",
				},
			},
		},
	).AnyTimes()
	mockStore.EXPECT().AddRmbPayOrder(gomock.Any(), gomock.Any()).Return(nil)
	mockStore.EXPECT().GetRmbPayOrderInfo(gomock.Any(), gomock.Any(), req.OutTradeNo, gomock.Any()).Return(nil, nil).AnyTimes()
	mockStore.EXPECT().UpdateRmbPayOrderStatus(gomock.Any(), gomock.Any()).Return(nil)

	mockCache.EXPECT().AddPayOrderInfo(gomock.Any(), gomock.Any()).Return(nil)
	mockCache.EXPECT().GetPayOrderInfo(gomock.Any(), req.OutTradeNo, "").Return(nil, nil)

	mockTpayApi.EXPECT().RestOrder(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&tpay_api.ApiRestOrderRsp{
		OrderNo:    "678919",
		CliOrderNo: "xxxxxxxx",
	}, nil)

	mockRiskMgr.EXPECT().CheckRisk(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

	m := NewUnifiedPayRmbMgr(mockConfig, mockStore, mockCache, mockRiskMgr)
	m.tpaycli = mockTpayApi

	rsp, err := m.RmbPayOrder(ctx, req)
	if err != nil {
		t.Errorf("RmbPayOrder() error = %v", err)
	} else {
		t.Logf("RmbPayOrder ok...:%s", rsp.String())
	}

}
