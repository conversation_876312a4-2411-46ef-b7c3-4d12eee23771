package hotreload

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"sync/atomic"
	"time"
)

// ConfigHotReloader 热加载配置接口
type ConfigHotReloader interface {
	OnReload(isInit bool) // 配置内容变更时会回调该方法，第一次加载配置时isInit参数为true，其余为false
}

// InitConfig        	初始化配置
// path           	  	配置文件路径
// reloadInterval    	配置定时检查时间（值为0表示不进行热加载）
// getDefaultConfig     获取默认配置函数
// conf                 保存配置
func InitConfig(path string, reloadInterval time.Duration, getDefaultConfig func() ConfigHotReloader, conf *atomic.Value) error {
	c, lastConf, err := getConfig(path, "", getDefaultConfig)
	if err != nil {
		return err
	}
	if lastConf == "" {
		return fmt.Errorf("config content is empty")
	}

	// 存入atomic.Value
	conf.Store(c)

	// 回调OnReload函数
	c.OnReload(true)

	// 定时检查配置是否更新
	if reloadInterval >= time.Second {
		go func() {
			for range time.NewTicker(reloadInterval).C {
				c, newConf, err := getConfig(path, lastConf, getDefaultConfig)
				if err != nil {
					fmt.Println(time.Now(), "load config error", err)
					continue
				}
				if newConf != "" {
					lastConf = newConf
					conf.Store(c)
					c.OnReload(false)
				}
			}
		}()
	}
	return nil
}

// getConfig 读取配置文件内容
func getConfig(path, lastConf string, getDefaultConfig func() ConfigHotReloader) (ConfigHotReloader, string, error) {
	bs, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, "", err
	}
	newConf := string(bs)
	// 配置内容未变更
	if newConf == lastConf && lastConf != "" {
		return nil, "", nil
	}

	// 将json解析到默认配置结构体中
	defaultConfig := getDefaultConfig()
	err = json.Unmarshal(bs, defaultConfig)
	if err != nil {
		return nil, "", err
	}
	return defaultConfig, newConf, nil
}
