package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	event_link_wrap "golang.52tt.com/pkg/event-link-wrap"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/services/glory-celebrity/mgr"
)

const topicPresentEvent = "present_event_v2"

type PresentEventSub struct {
	KafkaSub *event_link_wrap.SEventLinkAsyncSub
	mgr      *mgr.Manager
}

func NewPresentEventSub(sc *config.ServerConfig, m *mgr.Manager) (*PresentEventSub, error) {
	kafkaConf := config.NewKafkaConfigWithSection(sc.Configer, "kafka_present_event")

	conf := kafka.DefaultConfig()
	conf.ClientID = kafkaConf.ClientID
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	sub := &PresentEventSub{
		mgr: m,
	}
	ctx := context.Background()
	kafkaSub, err := event_link_wrap.NewEventLinkAsyncSub(kafkaConf.BrokerList(), kafkaConf.TopicList(), kafkaConf.GroupID, kafkaConf.ClientID,
		32, sub.handlerEvent, event_link_wrap.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewPresentEventSub Failed to create kafka-subscriber kfk conf:%+v, %v", kafkaConf, err)
		return nil, err
	}
	sub.KafkaSub = kafkaSub
	sub.KafkaSub.Start()
	log.InfoWithCtx(ctx, "NewPresentEventSub success... kfk conf:%+v", kafkaConf)
	return sub, nil
}

func (s *PresentEventSub) Close() {
	s.KafkaSub.Stop()
}

func (s *PresentEventSub) handlerEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case topicPresentEvent:
		return s.handlerPresentEvent(msg)
	default:
	}
	return nil, false
}

func (s *PresentEventSub) handlerPresentEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.Errorf(" handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	log.Debugf("handlerPresentEvent %+v", presentEvent)

	s.mgr.HandlerPresentEvent(presentEvent)

	return nil, false
}
