syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/cplsearch";
package cplsearch;


service CplSearch {
    rpc AddSearchItem (AddSearchItemReq) returns (AddSearchItemResp) {}
    rpc UpdateSearchItem (UpdateSearchItemReq) returns (UpdateSearchItemResp) {}
    rpc GetSearchItemList (GetSearchItemListReq) returns (GetSearchItemListResp) {}
    rpc DeleteSearchItem(DeleteSearchItemReq) returns (DeleteSearchItemResp) {}
    rpc Search (SearchReq) returns (SearchResp) {}
}

message AddSearchItemReq {
    SearchItem item = 1; // 不需要id
}
message AddSearchItemResp {
    SearchItem item = 1; // 多返回id
}

message DeleteSearchItemReq{
    string id = 1;
}
message DeleteSearchItemResp{}

message UpdateSearchItemReq {
    SearchItem item = 1;
}
message UpdateSearchItemResp {}

enum PlatformType {
    UNKNOWN = 0x00;
    ALL = 0x01;
    ANDROID = 0x02;
    IOS = 0x04;
    // PC-TT语音-开黑
    PLATFORM_TYPE_PC_LFG = 0x08;
}

// 对应 SearchItem 的 market_ids
enum ProductType {
    PRODUCT_TYPE_TT = 0;
    // PC-TT-开黑版， 对应 SearchItem 的 market_ids。仅当 PlatformType 为 PLATFORM_TYPE_PC_LFG 时有效
    PRODUCT_TYPE_TT_GANG_UP = 1;
    PRODUCT_TYPE_HUAN_YOU = 2;
    PRODUCT_TYPE_MAIKE = 5;
    PRODUCT_TYPE_MI_JING = 6;
}

message SearchItem{
    string id = 1;
    string title = 2;               // 标题
    string bg = 3;                  // 配图
    repeated string keywords = 4;   // 关键字
    uint32 begin_at = 5;            // 开始生效时间
    uint32 end_at = 6;              // 结束时间
    string account = 7;             // tt账号
    uint32 channel_id = 8;          // 房间id
    Activity activity = 9;          // 活动
    repeated ResultItem result_items = 10; // 搜索结果
    PlatformType platform_type = 11;
    repeated uint32 market_ids = 12; // 产品列表
    string channel_view_id = 13;
}

message ResultItem{
    oneof value{
        string account = 1;           // tt账号
        uint32 channel_id = 2;        // 房间id
        Activity activity = 3;        // 活动
        string channel_view_id = 4;
    }
}

message Activity{
    string title = 1;    // 标题
    string desc = 2;     // 介绍
    string icon = 3;     // 图标
    string jump_url = 4; // 跳转链接
}

message GetSearchItemListReq{
    uint32 page = 1; // 页，从1开始
    uint32 count = 2;
    repeated uint32 market_ids = 3; // 推送产品筛选
}
message GetSearchItemListResp{
    repeated SearchItem items = 1;
    uint32 total_count = 2;         // 配置项总数
}

message SearchReq{
    string keyword = 1; // 关键字
}
message SearchResp{
    SearchItem item = 1;
}
