// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/present-extra-conf/store (interfaces: IStore)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	"golang.52tt.com/services/present-extra-conf/internal/store"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	gorm "github.com/jinzhu/gorm"
)

// MockIStore is a mock of IStore interface.
type MockIStore struct {
	ctrl     *gomock.Controller
	recorder *MockIStoreMockRecorder
}

// MockIStoreMockRecorder is the mock recorder for MockIStore.
type MockIStoreMockRecorder struct {
	mock *MockIStore
}

// NewMockIStore creates a new mock instance.
func NewMockIStore(ctrl *gomock.Controller) *MockIStore {
	mock := &MockIStore{ctrl: ctrl}
	mock.recorder = &MockIStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIStore) EXPECT() *MockIStoreMockRecorder {
	return m.recorder
}

// AddFlashConfig mocks base method.
func (m *MockIStore) AddFlashConfig(arg0 context.Context, arg1 *store.FlashEffectConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFlashConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddFlashConfig indicates an expected call of AddFlashConfig.
func (mr *MockIStoreMockRecorder) AddFlashConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFlashConfig", reflect.TypeOf((*MockIStore)(nil).AddFlashConfig), arg0, arg1)
}

// AddFloatConfig mocks base method.
func (m *MockIStore) AddFloatConfig(arg0 context.Context, arg1 *store.PresentFloatLayer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddFloatConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddFloatConfig indicates an expected call of AddFloatConfig.
func (mr *MockIStoreMockRecorder) AddFloatConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddFloatConfig", reflect.TypeOf((*MockIStore)(nil).AddFloatConfig), arg0, arg1)
}

// Close mocks base method.
func (m *MockIStore) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIStoreMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIStore)(nil).Close))
}

// DelFlashConfig mocks base method.
func (m *MockIStore) DelFlashConfig(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFlashConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFlashConfig indicates an expected call of DelFlashConfig.
func (mr *MockIStoreMockRecorder) DelFlashConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFlashConfig", reflect.TypeOf((*MockIStore)(nil).DelFlashConfig), arg0, arg1)
}

// DelFloatConfig mocks base method.
func (m *MockIStore) DelFloatConfig(arg0 context.Context, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelFloatConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelFloatConfig indicates an expected call of DelFloatConfig.
func (mr *MockIStoreMockRecorder) DelFloatConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelFloatConfig", reflect.TypeOf((*MockIStore)(nil).DelFloatConfig), arg0, arg1)
}

// GetAllFlashConfig mocks base method.
func (m *MockIStore) GetAllFlashConfig(arg0 context.Context) ([]*store.FlashEffectConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllFlashConfig", arg0)
	ret0, _ := ret[0].([]*store.FlashEffectConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFlashConfig indicates an expected call of GetAllFlashConfig.
func (mr *MockIStoreMockRecorder) GetAllFlashConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFlashConfig", reflect.TypeOf((*MockIStore)(nil).GetAllFlashConfig), arg0)
}

// GetAllFloatConfig mocks base method.
func (m *MockIStore) GetAllFloatConfig(arg0 context.Context) ([]*store.PresentFloatLayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllFloatConfig", arg0)
	ret0, _ := ret[0].([]*store.PresentFloatLayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFloatConfig indicates an expected call of GetAllFloatConfig.
func (mr *MockIStoreMockRecorder) GetAllFloatConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFloatConfig", reflect.TypeOf((*MockIStore)(nil).GetAllFloatConfig), arg0)
}

// GetAllPresentFlash mocks base method.
func (m *MockIStore) GetAllPresentFlash(arg0 context.Context) ([]*store.PresentFlashInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPresentFlash", arg0)
	ret0, _ := ret[0].([]*store.PresentFlashInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPresentFlash indicates an expected call of GetAllPresentFlash.
func (mr *MockIStoreMockRecorder) GetAllPresentFlash(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPresentFlash", reflect.TypeOf((*MockIStore)(nil).GetAllPresentFlash), arg0)
}

// GetFlashConfigByIds mocks base method.
func (m *MockIStore) GetFlashConfigByIds(arg0 context.Context, arg1 []uint32) ([]*store.FlashEffectConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlashConfigByIds", arg0, arg1)
	ret0, _ := ret[0].([]*store.FlashEffectConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFlashConfigByIds indicates an expected call of GetFlashConfigByIds.
func (mr *MockIStoreMockRecorder) GetFlashConfigByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlashConfigByIds", reflect.TypeOf((*MockIStore)(nil).GetFlashConfigByIds), arg0, arg1)
}

// GetFlashTable mocks base method.
func (m *MockIStore) GetFlashTable() *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFlashTable")
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// GetFlashTable indicates an expected call of GetFlashTable.
func (mr *MockIStoreMockRecorder) GetFlashTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFlashTable", reflect.TypeOf((*MockIStore)(nil).GetFlashTable))
}

// GetFloatConfigByIds mocks base method.
func (m *MockIStore) GetFloatConfigByIds(arg0 context.Context, arg1 []uint32) ([]*store.PresentFloatLayer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFloatConfigByIds", arg0, arg1)
	ret0, _ := ret[0].([]*store.PresentFloatLayer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFloatConfigByIds indicates an expected call of GetFloatConfigByIds.
func (mr *MockIStoreMockRecorder) GetFloatConfigByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFloatConfigByIds", reflect.TypeOf((*MockIStore)(nil).GetFloatConfigByIds), arg0, arg1)
}

// GetFloatTable mocks base method.
func (m *MockIStore) GetFloatTable() *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFloatTable")
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// GetFloatTable indicates an expected call of GetFloatTable.
func (mr *MockIStoreMockRecorder) GetFloatTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFloatTable", reflect.TypeOf((*MockIStore)(nil).GetFloatTable))
}

// GetLastUpdateTable mocks base method.
func (m *MockIStore) GetLastUpdateTable() *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastUpdateTable")
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// GetLastUpdateTable indicates an expected call of GetLastUpdateTable.
func (mr *MockIStoreMockRecorder) GetLastUpdateTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastUpdateTable", reflect.TypeOf((*MockIStore)(nil).GetLastUpdateTable))
}

// GetLastUpdateTime mocks base method.
func (m *MockIStore) GetLastUpdateTime(arg0 context.Context) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastUpdateTime", arg0)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastUpdateTime indicates an expected call of GetLastUpdateTime.
func (mr *MockIStoreMockRecorder) GetLastUpdateTime(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastUpdateTime", reflect.TypeOf((*MockIStore)(nil).GetLastUpdateTime), arg0)
}

// GetMysql mocks base method.
func (m *MockIStore) GetMysql() *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMysql")
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// GetMysql indicates an expected call of GetMysql.
func (mr *MockIStoreMockRecorder) GetMysql() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMysql", reflect.TypeOf((*MockIStore)(nil).GetMysql))
}

// GetPresentFlashByIds mocks base method.
func (m *MockIStore) GetPresentFlashByIds(arg0 context.Context, arg1 []uint32) ([]*store.PresentFlashInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlashByIds", arg0, arg1)
	ret0, _ := ret[0].([]*store.PresentFlashInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresentFlashByIds indicates an expected call of GetPresentFlashByIds.
func (mr *MockIStoreMockRecorder) GetPresentFlashByIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlashByIds", reflect.TypeOf((*MockIStore)(nil).GetPresentFlashByIds), arg0, arg1)
}

// GetPresentFlashTable mocks base method.
func (m *MockIStore) GetPresentFlashTable() *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresentFlashTable")
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// GetPresentFlashTable indicates an expected call of GetPresentFlashTable.
func (mr *MockIStoreMockRecorder) GetPresentFlashTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresentFlashTable", reflect.TypeOf((*MockIStore)(nil).GetPresentFlashTable))
}

// InitTable mocks base method.
func (m *MockIStore) InitTable() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitTable")
	ret0, _ := ret[0].(error)
	return ret0
}

// InitTable indicates an expected call of InitTable.
func (mr *MockIStoreMockRecorder) InitTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitTable", reflect.TypeOf((*MockIStore)(nil).InitTable))
}

// SetLastUpdateTime mocks base method.
func (m *MockIStore) SetLastUpdateTime(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLastUpdateTime", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLastUpdateTime indicates an expected call of SetLastUpdateTime.
func (mr *MockIStoreMockRecorder) SetLastUpdateTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLastUpdateTime", reflect.TypeOf((*MockIStore)(nil).SetLastUpdateTime), arg0, arg1)
}

// Transaction mocks base method.
func (m *MockIStore) Transaction(arg0 context.Context, arg1 func(*gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIStoreMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIStore)(nil).Transaction), arg0, arg1)
}

// UpdateFlashConfig mocks base method.
func (m *MockIStore) UpdateFlashConfig(arg0 context.Context, arg1 *store.FlashEffectConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFlashConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFlashConfig indicates an expected call of UpdateFlashConfig.
func (mr *MockIStoreMockRecorder) UpdateFlashConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFlashConfig", reflect.TypeOf((*MockIStore)(nil).UpdateFlashConfig), arg0, arg1)
}

// UpdateFloatConfig mocks base method.
func (m *MockIStore) UpdateFloatConfig(arg0 context.Context, arg1 *store.PresentFloatLayer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFloatConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFloatConfig indicates an expected call of UpdateFloatConfig.
func (mr *MockIStoreMockRecorder) UpdateFloatConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFloatConfig", reflect.TypeOf((*MockIStore)(nil).UpdateFloatConfig), arg0, arg1)
}

// UpdatePresentFlash mocks base method.
func (m *MockIStore) UpdatePresentFlash(arg0 context.Context, arg1 *store.PresentFlashInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePresentFlash", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePresentFlash indicates an expected call of UpdatePresentFlash.
func (mr *MockIStoreMockRecorder) UpdatePresentFlash(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePresentFlash", reflect.TypeOf((*MockIStore)(nil).UpdatePresentFlash), arg0, arg1)
}
