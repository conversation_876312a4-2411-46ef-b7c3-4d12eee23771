package official_live_channel

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/official-live-channel"
	"google.golang.org/grpc"
)

const (
	serviceName = "official-live-channel"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewOfficialLiveChannelRelayClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.OfficialLiveChannelRelayClient {
	return c.Stub().(pb.OfficialLiveChannelRelayClient)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetRelay(ctx context.Context, channelId uint32) (*pb.GetRelayResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelay(ctx, &pb.GetRelayReq{
		OfficialChannelId: channelId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRelayBy(ctx context.Context, channelId uint32) (*pb.GetRelayByResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelayBy(ctx, &pb.GetRelayByReq{
		ChannelId: channelId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRelaySchedule(ctx context.Context, channelId uint32, scheduleId uint32) (*pb.GetRelayScheduleResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelaySchedule(ctx, &pb.GetRelayScheduleReq{
		OfficialChannelId: channelId,
		ScheduleId:        scheduleId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRelayDetail(ctx context.Context, channelId uint32) (*pb.GetRelayDetailResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelayDetail(ctx, &pb.GetRelayDetailReq{
		OfficialChannelId: channelId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRelayScheduleDetail(ctx context.Context, channelId uint32) (*pb.GetRelayScheduleDetailResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelayScheduleDetail(ctx, &pb.GetRelayScheduleDetailReq{
		OfficialChannelId: channelId,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) CancelRelay(ctx context.Context, channelId, sectionId uint32) protocol.ServerError {
	_, err := c.typedStub().CancelRelay(ctx, &pb.CancelRelayReq{
		OfficialChannelId: channelId,
		SectionId:         sectionId,
	})
	return protocol.ToServerError(err)
}

func (c *Client) ReportRelayAudio(ctx context.Context, channelId, uid uint32, streamId string) protocol.ServerError {
	_, err := c.typedStub().ReportRelayAudio(ctx, &pb.ReportRelayAudioReq{
		ChannelId: channelId,
		Uid:       uid,
		StreamId:  streamId,
	})
	return protocol.ToServerError(err)
}

func (c *Client) SetRelaySchedule(ctx context.Context, req *pb.SetRelayScheduleReq) (*pb.SetRelayScheduleResp, protocol.ServerError) {
	resp, err := c.typedStub().SetRelaySchedule(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetOfficialChannelDescribe(ctx context.Context, channelIds []uint32) (*pb.GetOfficialChannelDescribeResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOfficialChannelDescribe(ctx, &pb.GetOfficialChannelDescribeReq{
		OfficialChannelIds: channelIds,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetOfficialChannelFollowInfo(ctx context.Context, channelId uint32) (*pb.OfficialChannelFollowInfo, protocol.ServerError) {
	resp, err := c.typedStub().GetOfficialChannelFollowInfo(ctx, &pb.GetOfficialChannelFollowInfoReq{
		OfficialChannelId: channelId,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Info, protocol.ToServerError(err)
}

func (c *Client) BatchGetOfficialChannelFollowInfo(ctx context.Context, channelIds []uint32) (map[uint32]*pb.OfficialChannelFollowInfo, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetOfficialChannelFollowInfo(ctx, &pb.BatchGetOfficialChannelFollowInfoReq{
		OfficialChannelId: channelIds,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.Infos, nil
}

func (c *Client) BatchGetRelayBySchedule(ctx context.Context, channelIds []uint32) (map[uint32]*pb.GetRelayByScheduleResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetRelayByScheduleEx(ctx, &pb.BatchGetRelayByScheduleExReq{
		ChannelIds: channelIds,
	})
	if err != nil {
		return nil, protocol.ToServerError(err)
	}
	return resp.RelayBys, nil
}

func (c *Client) GetRelayScheduleList(ctx context.Context, req *pb.GetRelayScheduleListReq) (*pb.GetRelayScheduleListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRelayScheduleList(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllRelayBy(ctx context.Context, req *pb.GetAllRelayByReq) (*pb.GetAllRelayByResp, protocol.ServerError) {
	resp, err := c.typedStub().GetAllRelayBy(ctx, req)
	return resp, protocol.ToServerError(err)
}
