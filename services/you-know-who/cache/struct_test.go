package cache

import (
	"context"
	"golang.52tt.com/services/you-know-who/mysql"
	"testing"
	"time"
)

func TestConversionUKWCacheFromDB(t *testing.T) {
	now := time.Now()
	type args struct {
		ctx    context.Context
		dbInfo *mysql.YouKnowWhoPermissionTable
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "测试转化结构体",
			args: args{
				ctx: context.Background(),
				dbInfo: &mysql.YouKnowWhoPermissionTable{
					Id:            1,
					Uid:           12345,
					FakeUid:       23456,
					Nickname:      "神秘人77777",
					Status:        1,
					Switch:        1,
					RankSwitch:    1,
					EnterNotice:   1,
					EffectiveTime: 86400,
					TotalTime:     864000,
					OpenTime:      now,
					CreateTime:    now,
					UpdateTime:    now,
					DelFlag:       0,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ConversionUKWCacheFromDB(tt.args.ctx, tt.args.dbInfo)
			t.Log(got)
		})
	}
}
