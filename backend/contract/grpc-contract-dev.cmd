# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

32010:
api-route-configurator --etcd-endpoints *************:2379 create --id 32010 --source api/contract/grpc_contract.proto --lang go --method /ga.api.contract.ContractLogicService/GetMultiPlayerHallTaskEntry
6800:
api-route-configurator --etcd-endpoints *************:2379 create --id 6800 --source api/contract/grpc_contract.proto --lang go --method /ga.api.contract.ContractLogicService/CheckUserInteractEntry
6801:
api-route-configurator --etcd-endpoints *************:2379 create --id 6801 --source api/contract/grpc_contract.proto --lang go --method /ga.api.contract.ContractLogicService/GetUserInteractInfo
6802:
api-route-configurator --etcd-endpoints *************:2379 create --id 6802 --source api/contract/grpc_contract.proto --lang go --method /ga.api.contract.ContractLogicService/GetUserInteractViewPer
6803:
api-route-configurator --etcd-endpoints *************:2379 create --id 6803 --source api/contract/grpc_contract.proto --lang go --method /ga.api.contract.ContractLogicService/SetUserInteractViewPer
6804:
api-route-configurator --etcd-endpoints *************:2379 create --id 6804 --source api/contract/grpc_contract.proto --lang go --method /ga.api.contract.ContractLogicService/ContractClaimObsToken
