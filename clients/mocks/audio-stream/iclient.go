// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/audio-stream/iclient.go

// Package celebrity is a generated GoMock package.
package celebrity

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	audio_stream "golang.52tt.com/protocol/services/audio-stream"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// ChannelInfo mocks base method.
func (m *MockIClient) ChannelInfo(ctx context.Context, channelID string) ([]*audio_stream.AudioInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelInfo", ctx, channelID)
	ret0, _ := ret[0].([]*audio_stream.AudioInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ChannelInfo indicates an expected call of ChannelInfo.
func (mr *MockIClientMockRecorder) ChannelInfo(ctx, channelID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelInfo", reflect.TypeOf((*MockIClient)(nil).ChannelInfo), ctx, channelID)
}

// CheckInfo mocks base method.
func (m *MockIClient) CheckInfo(ctx context.Context, channelID uint32) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInfo", ctx, channelID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInfo indicates an expected call of CheckInfo.
func (mr *MockIClientMockRecorder) CheckInfo(ctx, channelID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInfo", reflect.TypeOf((*MockIClient)(nil).CheckInfo), ctx, channelID)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// Insert mocks base method.
func (m *MockIClient) Insert(ctx context.Context, streamID, channelID, account string, UID uint32, title, publishName string, rtmpURL, hlsURL, hdlURL, picURL []string, kwargs map[string]string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, streamID, channelID, account, UID, title, publishName, rtmpURL, hlsURL, hdlURL, picURL, kwargs)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockIClientMockRecorder) Insert(ctx, streamID, channelID, account, UID, title, publishName, rtmpURL, hlsURL, hdlURL, picURL, kwargs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockIClient)(nil).Insert), ctx, streamID, channelID, account, UID, title, publishName, rtmpURL, hlsURL, hdlURL, picURL, kwargs)
}

// InsertCheckInfo mocks base method.
func (m *MockIClient) InsertCheckInfo(ctx context.Context, channelID uint32, taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertCheckInfo", ctx, channelID, taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertCheckInfo indicates an expected call of InsertCheckInfo.
func (mr *MockIClientMockRecorder) InsertCheckInfo(ctx, channelID, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertCheckInfo", reflect.TypeOf((*MockIClient)(nil).InsertCheckInfo), ctx, channelID, taskID)
}

// Remove mocks base method.
func (m *MockIClient) Remove(ctx context.Context, streamID string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Remove", ctx, streamID)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// Remove indicates an expected call of Remove.
func (mr *MockIClientMockRecorder) Remove(ctx, streamID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Remove", reflect.TypeOf((*MockIClient)(nil).Remove), ctx, streamID)
}

// RemoveCheckInfo mocks base method.
func (m *MockIClient) RemoveCheckInfo(ctx context.Context, taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveCheckInfo", ctx, taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveCheckInfo indicates an expected call of RemoveCheckInfo.
func (mr *MockIClientMockRecorder) RemoveCheckInfo(ctx, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveCheckInfo", reflect.TypeOf((*MockIClient)(nil).RemoveCheckInfo), ctx, taskID)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// Token mocks base method.
func (m *MockIClient) Token(ctx context.Context, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Token", ctx, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Token indicates an expected call of Token.
func (mr *MockIClientMockRecorder) Token(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Token", reflect.TypeOf((*MockIClient)(nil).Token), ctx, key)
}

// UpdateCheckInfo mocks base method.
func (m *MockIClient) UpdateCheckInfo(ctx context.Context, providerTaskId, taskID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCheckInfo", ctx, providerTaskId, taskID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCheckInfo indicates an expected call of UpdateCheckInfo.
func (mr *MockIClientMockRecorder) UpdateCheckInfo(ctx, providerTaskId, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCheckInfo", reflect.TypeOf((*MockIClient)(nil).UpdateCheckInfo), ctx, providerTaskId, taskID)
}
