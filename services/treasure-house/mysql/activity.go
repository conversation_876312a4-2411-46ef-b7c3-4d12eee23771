package mysql

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/treasure-house"
	"gorm.io/gorm"
	"time"
)

func (s *Store) CreateActivityConfig(ctx context.Context,
	activityCfg *TreasureActivityCfg, phaseList []*TreasurePhaseCfg, batchGrantUsers []uint32) error {
	var err error
	isNew := activityCfg.ID == 0

	originPhasesList := make([]*TreasurePhaseCfg, 0)
	err = s.db.WithContext(ctx).Where("activity_id = ?", activityCfg.ID).Find(&originPhasesList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Errorf("CreateActivityConfig Find originPhasesList err: %s, cfg:%+v", err, activityCfg)
		return err
	}
	originPhaseMap := make(map[uint32]*TreasurePhaseCfg)
	originPhasesIds := make([]uint32, 0)
	for _, p := range originPhasesList {
		originPhasesIds = append(originPhasesIds, p.ID)
		originPhaseMap[p.ID] = p
	}

	// 检查条件阶段是否修改了时间
	for _, p := range phaseList {
		if p.PhaseType == uint8(pb.PhaseType_PHASE_TYPE_CONDITION) && p.ID > 0 {
			if originPhase, ok := originPhaseMap[p.ID]; ok {
				if originPhase.StartTime != p.StartTime || originPhase.EndTime != p.EndTime {
					log.WarnWithCtx(ctx, "CreateActivityConfig phase time changed, origin:%+v, now:%+v", originPhase, p)
					return protocol.NewExactServerError(nil, status.ErrTreasureHouseConditionTimeChange)
				}
			}
		}
	}

	createBatchUsers := func(tx *gorm.DB, p *TreasurePhaseCfg) error {
		if pb.PhaseType(p.PhaseType) == pb.PhaseType_PHASE_TYPE_CONDITION && len(batchGrantUsers) > 0 {
			batchGrantUserList := make([]*TreasureBatchGrantUser, 0)
			for _, uid := range batchGrantUsers {
				batchGrantUserList = append(batchGrantUserList, &TreasureBatchGrantUser{
					ActivityID: p.ActivityID,
					PhaseID:    p.ID,
					GetMethod:  p.GetMethod,
					Status:     0,
					Uid:        uid,
				})
			}

			if err = tx.Model(&TreasureBatchGrantUser{}).CreateInBatches(batchGrantUserList, 100).Error; err != nil {
				log.Errorf("CreateActivityConfig CreateInBatches batchGrantUserList err: %s, cfg:%+v", err, phaseList)
				return err
			}
		}
		return nil
	}

	return s.Transaction(ctx, func(tx *gorm.DB) error {

		log.DebugWithCtx(ctx, "CreateActivityConfig transaction start, isNew:%v", isNew)

		// 创建
		if isNew {
			err = tx.Create(activityCfg).Error
			if err != nil {
				log.Errorf("CreateActivityConfig Create activityCfg err: %s, cfg:%+v", err, activityCfg)
				return err
			}

			if len(phaseList) == 0 {
				log.WarnWithCtx(ctx, "CreateActivityConfig phaseList is empty, cfg:%+v", activityCfg)
				return nil
			}

			for _, p := range phaseList {
				p.ActivityID = activityCfg.ID

				if err = tx.Model(&TreasurePhaseCfg{}).Create(p).Error; err != nil {
					log.ErrorWithCtx(ctx, "CreateActivityConfig CreateInBatches phaseList err: %s, cfg:%+v", err, phaseList)
					return err
				}

				// 条件发放阶段，批量发放，记录UID
				if pb.PhaseType(p.PhaseType) == pb.PhaseType_PHASE_TYPE_CONDITION {
					if err = createBatchUsers(tx, p); err != nil {
						log.ErrorWithCtx(ctx, "CreateActivityConfig createBatchUsers err: %s, cfg:%+v", err, phaseList)
						return err
					}
				}
			}
			return nil
		}

		log.DebugWithCtx(ctx, "CreateActivityConfig transaction, activityCfg.ID:%d", activityCfg.ID)

		// 编辑活动
		activityId := activityCfg.ID
		err = tx.Model(&TreasureActivityCfg{}).Where("id = ?", activityId).
			Updates(GenerateUpdateMap(*activityCfg)).Limit(1).Error
		if err != nil {
			log.Errorf("CreateActivityConfig Updates activityCfg err: %s, cfg:%+v", err, activityCfg)
			return err
		}

		// 编辑阶段
		for _, p := range phaseList {
			p.ActivityID = activityId
			isNewPhase := p.ID == 0
			if isNewPhase {
				err = tx.Model(&TreasurePhaseCfg{}).Create(p).Error
			} else {
				err = tx.Model(&TreasurePhaseCfg{}).Where("id = ?", p.ID).Updates(GenerateUpdateMap(*p)).Limit(1).Error
			}
			if err != nil {
				log.Errorf("CreateActivityConfig Create/Updates phaseList err: %s, cfg:%+v", err, p)
				return err
			}

			// 条件发放阶段，批量发放，记录UID
			if isNewPhase && pb.PhaseType(p.PhaseType) == pb.PhaseType_PHASE_TYPE_CONDITION {
				if err = createBatchUsers(tx, p); err != nil {
					log.ErrorWithCtx(ctx, "CreateActivityConfig createBatchUsers err: %s, cfg:%+v", err, phaseList)
					return err
				}
			}
		}

		// 编辑时移除阶段
		removedIds := make([]uint32, 0)
		for _, oid := range originPhasesIds {
			if !containsPhaseID(phaseList, oid) {
				removedIds = append(removedIds, oid)
			}
		}
		if len(removedIds) > 0 {
			err = tx.Model(&TreasurePhaseCfg{}).
				Where("id in (?)", removedIds).
				Delete(&TreasurePhaseCfg{}).Limit(len(originPhasesIds)).Error
			if err != nil {
				log.Errorf("CreateActivityConfig Delete removed ids err: %s, cfg:%+v, removedIds:%+v", err, activityCfg, removedIds)
				return err
			}
		}

		return nil
	})
}

func containsPhaseID(list []*TreasurePhaseCfg, pid uint32) bool {
	for _, v := range list {
		if v.ID == pid {
			return true
		}
	}
	return false
}

func (s *Store) GetActivityConfig(ctx context.Context, activityID uint32) (
	*TreasureActivityCfg, []*TreasurePhaseCfg, error) {
	activityCfg := new(TreasureActivityCfg)
	phaseList := make([]*TreasurePhaseCfg, 0)
	err := s.db.WithContext(ctx).Where("id = ?", activityID).First(activityCfg).Error
	if err != nil {
		log.Errorf("GetActivityConfig First activityCfg err: %s, activityID:%d", err, activityID)
		return nil, nil, err
	}
	err = s.db.WithContext(ctx).Where("activity_id = ?", activityID).Find(&phaseList).Error
	if err != nil {
		log.Errorf("GetActivityConfig Find phaseList err: %s, activityID:%d", err, activityID)
		return nil, nil, err
	}
	return activityCfg, phaseList, nil
}

func (s *Store) GetActivityConfigPb(ctx context.Context, activityID uint32) (*pb.ActivityConfig, error) {
	activityCfg, phaseList, err := s.GetActivityConfig(ctx, activityID)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetActivityConfigPb GetActivityConfig err: %s, activityID:%d", err, activityID)
		return nil, err
	}
	return activityCfg.ToPb(phaseList), nil
}

func (s *Store) GetActivityConfigList(ctx context.Context, req *pb.GetActivityConfigListReq) ([]*pb.ActivityConfig, uint32, error) {
	activityCfgList := make([]*TreasureActivityCfg, 0)
	count := int64(0)
	now := time.Now()
	fromApp := req.GetUid() > 0
	query := s.db.WithContext(ctx).Model(&TreasureActivityCfg{})
	if fromApp {
		// 仅生效中的活动
		query = query.Where("start_time < ? AND (end_time > ? OR forever = ?)", now, now, true).Order("sort asc, id desc")
	} else {
		switch req.GetStatus() {
		case pb.ActivityStatus_ACTIVITY_STATUS_NOT_STARTED:
			// 未开始的按照开始时间临近
			query = query.Where("start_time > ?", now).Order("start_time asc")
		case pb.ActivityStatus_ACTIVITY_STATUS_IN_PROGRESS:
			// 生效中的活动按照珍宝馆排序正序，与客户端一致
			query = query.Where("start_time < ? AND (end_time > ? OR forever = ?)", now, now, true).Order("sort asc, id desc")
		case pb.ActivityStatus_ACTIVITY_STATUS_ENDED:
			// 已结束列表按照结束时间临近
			query = query.Where("end_time < ? AND forever = ?", now, false).Order("end_time desc")
		default:
			// ALL
			query = query.Order("id desc")
		}
	}
	if err := query.Count(&count).Error; err != nil {
		log.Errorf("GetActivityConfigList Count activityCfgList err: %s", err)
		return nil, 0, err
	}

	if err := query.
		Offset(int(req.GetOffset())).
		Limit(int(req.GetLimit())).
		Find(&activityCfgList).Error; err != nil {
		log.Errorf("GetActivityConfigList Find activityCfgList err: %s", err)
		return nil, 0, err
	}

	activityIds := make([]uint32, 0)
	for _, ac := range activityCfgList {
		activityIds = append(activityIds, ac.ID)
	}

	phaseList := make([]*TreasurePhaseCfg, 0)
	if err := s.db.WithContext(ctx).Model(&TreasurePhaseCfg{}).
		Where("activity_id in (?)", activityIds).Find(&phaseList).Error; err != nil {
		log.Errorf("GetActivityConfigList Find phaseList err: %s", err)
		return nil, 0, err
	}

	phasesGroupMap := make(map[uint32][]*TreasurePhaseCfg)
	for _, p := range phaseList {
		phasesGroupMap[p.ActivityID] = append(phasesGroupMap[p.ActivityID], p)
	}

	activityPbList := make([]*pb.ActivityConfig, 0)
	for _, a := range activityCfgList {
		aPb := a.ToPb(phasesGroupMap[a.ID])
		if aPb != nil {
			activityPbList = append(activityPbList, aPb)
		}
	}

	return activityPbList, uint32(count), nil
}

func (s *Store) DeleteActivityConfigList(ctx context.Context, id uint32) error {
	if id == 0 {
		return nil
	}
	return s.db.WithContext(ctx).Where("id = ?", id).Delete(&TreasureActivityCfg{}).Limit(1).Error
}

func (s *Store) GetActivityConfigByRange(ctx context.Context, giftId uint32, startTime, endTime time.Time) ([]*TreasureActivityCfg, error) {
	activityCfgListNoForever := make([]*TreasureActivityCfg, 0)
	foreverTime := time.Time{} // 永久时的结束时间
	if err := s.db.WithContext(ctx).
		Model(&TreasureActivityCfg{}).
		Where("gift_id = ?", giftId).
		Where(`(end_time = ? AND start_time < ?) 
        OR (end_time = ? AND ? < end_time) 
        OR (? < end_time AND ? > start_time AND end_time <> ? AND forever = false)`,
			foreverTime, endTime, foreverTime, startTime, startTime, endTime, foreverTime).Error; err != nil {
		log.ErrorWithCtx(ctx, "GetActivityConfigByRange Find no forever activityCfgList err: %s", err)
		return nil, err
	}
	return activityCfgListNoForever, nil
}
