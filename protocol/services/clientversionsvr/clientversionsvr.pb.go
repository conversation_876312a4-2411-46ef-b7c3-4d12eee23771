// Code generated by protoc-gen-gogo.
// source: src/clientversionsvr/clientversionsvr.proto
// DO NOT EDIT!

/*
	Package clientversion is a generated protocol buffer package.

	It is generated from these files:
		src/clientversionsvr/clientversionsvr.proto

	It has these top-level messages:
		VersionNumber
		GetLatestVersionReq
		Version
		GetLatestVersionRsp
		GetVersionDistCountReq
		GetVersionDistCountResp
		UserUseVersion
		TrackUserVersionReq
		TrackUserVersionRsp
		GetUserLatestVersionReq
		GetUserLatestVersionRsp
		Time
		VersionUserCount
		GetVersionUserCountReq
		GetVersionUserCountRsp
		DumpVersionUpgradeInfoReq
		PolicyRule
		Policy
		DumpVersionUpgradeInfoRsp
		UserLatestVersionData
*/
package clientversion

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type VersionType int32

const (
	VersionType_VT_NIL              VersionType = 0
	VersionType_VT_ANDROID          VersionType = 1
	VersionType_VT_IOS              VersionType = 2
	VersionType_VT_IOS_Enterprise   VersionType = 4
	VersionType_VT_ANDROID_LITE     VersionType = 8
	VersionType_VT_ANDROID_HUANYOU  VersionType = 16
	VersionType_VT_ANDROID_ZAIYA    VersionType = 32
	VersionType_VT_ANDROID_TOPSPEED VersionType = 64
	VersionType_VT_ANDROID_MAIKE    VersionType = 128
	VersionType_VT_ANDROID_MIJING   VersionType = 256
	VersionType_VT_BUTT             VersionType = 15728640
)

var VersionType_name = map[int32]string{
	0:        "VT_NIL",
	1:        "VT_ANDROID",
	2:        "VT_IOS",
	4:        "VT_IOS_Enterprise",
	8:        "VT_ANDROID_LITE",
	16:       "VT_ANDROID_HUANYOU",
	32:       "VT_ANDROID_ZAIYA",
	64:       "VT_ANDROID_TOPSPEED",
	128:      "VT_ANDROID_MAIKE",
	256:      "VT_ANDROID_MIJING",
	15728640: "VT_BUTT",
}
var VersionType_value = map[string]int32{
	"VT_NIL":              0,
	"VT_ANDROID":          1,
	"VT_IOS":              2,
	"VT_IOS_Enterprise":   4,
	"VT_ANDROID_LITE":     8,
	"VT_ANDROID_HUANYOU":  16,
	"VT_ANDROID_ZAIYA":    32,
	"VT_ANDROID_TOPSPEED": 64,
	"VT_ANDROID_MAIKE":    128,
	"VT_ANDROID_MIJING":   256,
	"VT_BUTT":             15728640,
}

func (x VersionType) Enum() *VersionType {
	p := new(VersionType)
	*p = x
	return p
}
func (x VersionType) String() string {
	return proto.EnumName(VersionType_name, int32(x))
}
func (x *VersionType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(VersionType_value, data, "VersionType")
	if err != nil {
		return err
	}
	*x = VersionType(value)
	return nil
}
func (VersionType) EnumDescriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{0} }

type GetVersionUserCountReq_QueryMethod int32

const (
	GetVersionUserCountReq_QM_DAILY   GetVersionUserCountReq_QueryMethod = 1
	GetVersionUserCountReq_QM_WEEKLY  GetVersionUserCountReq_QueryMethod = 2
	GetVersionUserCountReq_QM_MONTHLY GetVersionUserCountReq_QueryMethod = 3
)

var GetVersionUserCountReq_QueryMethod_name = map[int32]string{
	1: "QM_DAILY",
	2: "QM_WEEKLY",
	3: "QM_MONTHLY",
}
var GetVersionUserCountReq_QueryMethod_value = map[string]int32{
	"QM_DAILY":   1,
	"QM_WEEKLY":  2,
	"QM_MONTHLY": 3,
}

func (x GetVersionUserCountReq_QueryMethod) Enum() *GetVersionUserCountReq_QueryMethod {
	p := new(GetVersionUserCountReq_QueryMethod)
	*p = x
	return p
}
func (x GetVersionUserCountReq_QueryMethod) String() string {
	return proto.EnumName(GetVersionUserCountReq_QueryMethod_name, int32(x))
}
func (x *GetVersionUserCountReq_QueryMethod) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetVersionUserCountReq_QueryMethod_value, data, "GetVersionUserCountReq_QueryMethod")
	if err != nil {
		return err
	}
	*x = GetVersionUserCountReq_QueryMethod(value)
	return nil
}
func (GetVersionUserCountReq_QueryMethod) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{13, 0}
}

// n_val,s_val 指类似 3.1.1 版本号
// 2017.7.31 增 version_code, 单调递增，客户端在做版本新旧比较时，优选 version_code
type VersionNumber struct {
	NVal        uint32 `protobuf:"varint,1,opt,name=n_val,json=nVal" json:"n_val"`
	SVal        string `protobuf:"bytes,2,opt,name=s_val,json=sVal" json:"s_val"`
	VersionCode uint32 `protobuf:"varint,3,opt,name=version_code,json=versionCode" json:"version_code"`
}

func (m *VersionNumber) Reset()                    { *m = VersionNumber{} }
func (m *VersionNumber) String() string            { return proto.CompactTextString(m) }
func (*VersionNumber) ProtoMessage()               {}
func (*VersionNumber) Descriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{0} }

func (m *VersionNumber) GetNVal() uint32 {
	if m != nil {
		return m.NVal
	}
	return 0
}

func (m *VersionNumber) GetSVal() string {
	if m != nil {
		return m.SVal
	}
	return ""
}

func (m *VersionNumber) GetVersionCode() uint32 {
	if m != nil {
		return m.VersionCode
	}
	return 0
}

type GetLatestVersionReq struct {
	Uid           uint32         `protobuf:"varint,1,req,name=uid" json:"uid"`
	VersionType   uint32         `protobuf:"varint,2,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber *VersionNumber `protobuf:"bytes,3,opt,name=version_number,json=versionNumber" json:"version_number,omitempty"`
	GuildId       uint32         `protobuf:"varint,4,opt,name=guild_id,json=guildId" json:"guild_id"`
	ClientIp      string         `protobuf:"bytes,5,opt,name=client_ip,json=clientIp" json:"client_ip"`
	ProtocolCmd   uint32         `protobuf:"varint,6,opt,name=protocol_cmd,json=protocolCmd" json:"protocol_cmd"`
}

func (m *GetLatestVersionReq) Reset()         { *m = GetLatestVersionReq{} }
func (m *GetLatestVersionReq) String() string { return proto.CompactTextString(m) }
func (*GetLatestVersionReq) ProtoMessage()    {}
func (*GetLatestVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{1}
}

func (m *GetLatestVersionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLatestVersionReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *GetLatestVersionReq) GetVersionNumber() *VersionNumber {
	if m != nil {
		return m.VersionNumber
	}
	return nil
}

func (m *GetLatestVersionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetLatestVersionReq) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *GetLatestVersionReq) GetProtocolCmd() uint32 {
	if m != nil {
		return m.ProtocolCmd
	}
	return 0
}

type Version struct {
	VersionType            uint32         `protobuf:"varint,1,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber          *VersionNumber `protobuf:"bytes,2,req,name=version_number,json=versionNumber" json:"version_number,omitempty"`
	DownloadUrl            string         `protobuf:"bytes,3,req,name=download_url,json=downloadUrl" json:"download_url"`
	PromptTitle            string         `protobuf:"bytes,4,opt,name=prompt_title,json=promptTitle" json:"prompt_title"`
	PromptContent          string         `protobuf:"bytes,5,opt,name=prompt_content,json=promptContent" json:"prompt_content"`
	FileMd5                string         `protobuf:"bytes,6,opt,name=file_md5,json=fileMd5" json:"file_md5"`
	FileHeaderMd5          string         `protobuf:"bytes,7,opt,name=file_header_md5,json=fileHeaderMd5" json:"file_header_md5"`
	PromptBelowVer         *VersionNumber `protobuf:"bytes,8,opt,name=prompt_below_ver,json=promptBelowVer" json:"prompt_below_ver,omitempty"`
	ForceUpg               bool           `protobuf:"varint,9,opt,name=force_upg,json=forceUpg" json:"force_upg"`
	ForceUpgVer            *VersionNumber `protobuf:"bytes,10,opt,name=force_upg_ver,json=forceUpgVer" json:"force_upg_ver,omitempty"`
	VersionCode            uint32         `protobuf:"varint,11,opt,name=version_code,json=versionCode" json:"version_code"`
	HcPluginUrl            string         `protobuf:"bytes,12,opt,name=hc_plugin_url,json=hcPluginUrl" json:"hc_plugin_url"`
	HighFreqPromptAfter    uint32         `protobuf:"varint,13,opt,name=high_freq_prompt_after,json=highFreqPromptAfter" json:"high_freq_prompt_after"`
	HighFreqPromptInterval uint32         `protobuf:"varint,14,opt,name=high_freq_prompt_interval,json=highFreqPromptInterval" json:"high_freq_prompt_interval"`
}

func (m *Version) Reset()                    { *m = Version{} }
func (m *Version) String() string            { return proto.CompactTextString(m) }
func (*Version) ProtoMessage()               {}
func (*Version) Descriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{2} }

func (m *Version) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *Version) GetVersionNumber() *VersionNumber {
	if m != nil {
		return m.VersionNumber
	}
	return nil
}

func (m *Version) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *Version) GetPromptTitle() string {
	if m != nil {
		return m.PromptTitle
	}
	return ""
}

func (m *Version) GetPromptContent() string {
	if m != nil {
		return m.PromptContent
	}
	return ""
}

func (m *Version) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *Version) GetFileHeaderMd5() string {
	if m != nil {
		return m.FileHeaderMd5
	}
	return ""
}

func (m *Version) GetPromptBelowVer() *VersionNumber {
	if m != nil {
		return m.PromptBelowVer
	}
	return nil
}

func (m *Version) GetForceUpg() bool {
	if m != nil {
		return m.ForceUpg
	}
	return false
}

func (m *Version) GetForceUpgVer() *VersionNumber {
	if m != nil {
		return m.ForceUpgVer
	}
	return nil
}

func (m *Version) GetVersionCode() uint32 {
	if m != nil {
		return m.VersionCode
	}
	return 0
}

func (m *Version) GetHcPluginUrl() string {
	if m != nil {
		return m.HcPluginUrl
	}
	return ""
}

func (m *Version) GetHighFreqPromptAfter() uint32 {
	if m != nil {
		return m.HighFreqPromptAfter
	}
	return 0
}

func (m *Version) GetHighFreqPromptInterval() uint32 {
	if m != nil {
		return m.HighFreqPromptInterval
	}
	return 0
}

type GetLatestVersionRsp struct {
	Version *Version `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
}

func (m *GetLatestVersionRsp) Reset()         { *m = GetLatestVersionRsp{} }
func (m *GetLatestVersionRsp) String() string { return proto.CompactTextString(m) }
func (*GetLatestVersionRsp) ProtoMessage()    {}
func (*GetLatestVersionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{3}
}

func (m *GetLatestVersionRsp) GetVersion() *Version {
	if m != nil {
		return m.Version
	}
	return nil
}

type GetVersionDistCountReq struct {
	VersionType   uint32         `protobuf:"varint,1,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber *VersionNumber `protobuf:"bytes,2,req,name=version_number,json=versionNumber" json:"version_number,omitempty"`
}

func (m *GetVersionDistCountReq) Reset()         { *m = GetVersionDistCountReq{} }
func (m *GetVersionDistCountReq) String() string { return proto.CompactTextString(m) }
func (*GetVersionDistCountReq) ProtoMessage()    {}
func (*GetVersionDistCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{4}
}

func (m *GetVersionDistCountReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *GetVersionDistCountReq) GetVersionNumber() *VersionNumber {
	if m != nil {
		return m.VersionNumber
	}
	return nil
}

type GetVersionDistCountResp struct {
	Count uint32 `protobuf:"varint,1,opt,name=count" json:"count"`
}

func (m *GetVersionDistCountResp) Reset()         { *m = GetVersionDistCountResp{} }
func (m *GetVersionDistCountResp) String() string { return proto.CompactTextString(m) }
func (*GetVersionDistCountResp) ProtoMessage()    {}
func (*GetVersionDistCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{5}
}

func (m *GetVersionDistCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type UserUseVersion struct {
	Uid           uint32         `protobuf:"varint,1,req,name=uid" json:"uid"`
	VersionType   uint32         `protobuf:"varint,2,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber *VersionNumber `protobuf:"bytes,3,req,name=version_number,json=versionNumber" json:"version_number,omitempty"`
	At            uint32         `protobuf:"varint,4,req,name=at" json:"at"`
}

func (m *UserUseVersion) Reset()                    { *m = UserUseVersion{} }
func (m *UserUseVersion) String() string            { return proto.CompactTextString(m) }
func (*UserUseVersion) ProtoMessage()               {}
func (*UserUseVersion) Descriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{6} }

func (m *UserUseVersion) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserUseVersion) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *UserUseVersion) GetVersionNumber() *VersionNumber {
	if m != nil {
		return m.VersionNumber
	}
	return nil
}

func (m *UserUseVersion) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

type TrackUserVersionReq struct {
	UserVersion *UserUseVersion `protobuf:"bytes,1,req,name=user_version,json=userVersion" json:"user_version,omitempty"`
}

func (m *TrackUserVersionReq) Reset()         { *m = TrackUserVersionReq{} }
func (m *TrackUserVersionReq) String() string { return proto.CompactTextString(m) }
func (*TrackUserVersionReq) ProtoMessage()    {}
func (*TrackUserVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{7}
}

func (m *TrackUserVersionReq) GetUserVersion() *UserUseVersion {
	if m != nil {
		return m.UserVersion
	}
	return nil
}

type TrackUserVersionRsp struct {
}

func (m *TrackUserVersionRsp) Reset()         { *m = TrackUserVersionRsp{} }
func (m *TrackUserVersionRsp) String() string { return proto.CompactTextString(m) }
func (*TrackUserVersionRsp) ProtoMessage()    {}
func (*TrackUserVersionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{8}
}

type GetUserLatestVersionReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserLatestVersionReq) Reset()         { *m = GetUserLatestVersionReq{} }
func (m *GetUserLatestVersionReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLatestVersionReq) ProtoMessage()    {}
func (*GetUserLatestVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{9}
}

func (m *GetUserLatestVersionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserLatestVersionRsp struct {
	UserVersion *UserUseVersion `protobuf:"bytes,1,opt,name=user_version,json=userVersion" json:"user_version,omitempty"`
}

func (m *GetUserLatestVersionRsp) Reset()         { *m = GetUserLatestVersionRsp{} }
func (m *GetUserLatestVersionRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserLatestVersionRsp) ProtoMessage()    {}
func (*GetUserLatestVersionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{10}
}

func (m *GetUserLatestVersionRsp) GetUserVersion() *UserUseVersion {
	if m != nil {
		return m.UserVersion
	}
	return nil
}

// 获取版本使用情况
type Time struct {
	Year    uint32 `protobuf:"varint,1,req,name=year" json:"year"`
	Month   uint32 `protobuf:"varint,2,req,name=month" json:"month"`
	Day     uint32 `protobuf:"varint,3,opt,name=day" json:"day"`
	WeekNum uint32 `protobuf:"varint,4,opt,name=week_num,json=weekNum" json:"week_num"`
}

func (m *Time) Reset()                    { *m = Time{} }
func (m *Time) String() string            { return proto.CompactTextString(m) }
func (*Time) ProtoMessage()               {}
func (*Time) Descriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{11} }

func (m *Time) GetYear() uint32 {
	if m != nil {
		return m.Year
	}
	return 0
}

func (m *Time) GetMonth() uint32 {
	if m != nil {
		return m.Month
	}
	return 0
}

func (m *Time) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *Time) GetWeekNum() uint32 {
	if m != nil {
		return m.WeekNum
	}
	return 0
}

type VersionUserCount struct {
	Time  *Time  `protobuf:"bytes,1,req,name=time" json:"time,omitempty"`
	Count uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *VersionUserCount) Reset()         { *m = VersionUserCount{} }
func (m *VersionUserCount) String() string { return proto.CompactTextString(m) }
func (*VersionUserCount) ProtoMessage()    {}
func (*VersionUserCount) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{12}
}

func (m *VersionUserCount) GetTime() *Time {
	if m != nil {
		return m.Time
	}
	return nil
}

func (m *VersionUserCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetVersionUserCountReq struct {
	QueryMethod   uint32         `protobuf:"varint,1,req,name=query_method,json=queryMethod" json:"query_method"`
	VersionType   uint32         `protobuf:"varint,2,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber *VersionNumber `protobuf:"bytes,3,req,name=version_number,json=versionNumber" json:"version_number,omitempty"`
	BeginTime     *Time          `protobuf:"bytes,4,req,name=begin_time,json=beginTime" json:"begin_time,omitempty"`
	EndTime       *Time          `protobuf:"bytes,5,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
}

func (m *GetVersionUserCountReq) Reset()         { *m = GetVersionUserCountReq{} }
func (m *GetVersionUserCountReq) String() string { return proto.CompactTextString(m) }
func (*GetVersionUserCountReq) ProtoMessage()    {}
func (*GetVersionUserCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{13}
}

func (m *GetVersionUserCountReq) GetQueryMethod() uint32 {
	if m != nil {
		return m.QueryMethod
	}
	return 0
}

func (m *GetVersionUserCountReq) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *GetVersionUserCountReq) GetVersionNumber() *VersionNumber {
	if m != nil {
		return m.VersionNumber
	}
	return nil
}

func (m *GetVersionUserCountReq) GetBeginTime() *Time {
	if m != nil {
		return m.BeginTime
	}
	return nil
}

func (m *GetVersionUserCountReq) GetEndTime() *Time {
	if m != nil {
		return m.EndTime
	}
	return nil
}

type GetVersionUserCountRsp struct {
	CountList []*VersionUserCount `protobuf:"bytes,1,rep,name=count_list,json=countList" json:"count_list,omitempty"`
}

func (m *GetVersionUserCountRsp) Reset()         { *m = GetVersionUserCountRsp{} }
func (m *GetVersionUserCountRsp) String() string { return proto.CompactTextString(m) }
func (*GetVersionUserCountRsp) ProtoMessage()    {}
func (*GetVersionUserCountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{14}
}

func (m *GetVersionUserCountRsp) GetCountList() []*VersionUserCount {
	if m != nil {
		return m.CountList
	}
	return nil
}

// for maintain
type DumpVersionUpgradeInfoReq struct {
}

func (m *DumpVersionUpgradeInfoReq) Reset()         { *m = DumpVersionUpgradeInfoReq{} }
func (m *DumpVersionUpgradeInfoReq) String() string { return proto.CompactTextString(m) }
func (*DumpVersionUpgradeInfoReq) ProtoMessage()    {}
func (*DumpVersionUpgradeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{15}
}

type PolicyRule struct {
	Name       string   `protobuf:"bytes,1,req,name=name" json:"name"`
	Parameters []string `protobuf:"bytes,2,rep,name=parameters" json:"parameters,omitempty"`
	Action     string   `protobuf:"bytes,3,req,name=action" json:"action"`
}

func (m *PolicyRule) Reset()                    { *m = PolicyRule{} }
func (m *PolicyRule) String() string            { return proto.CompactTextString(m) }
func (*PolicyRule) ProtoMessage()               {}
func (*PolicyRule) Descriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{16} }

func (m *PolicyRule) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PolicyRule) GetParameters() []string {
	if m != nil {
		return m.Parameters
	}
	return nil
}

func (m *PolicyRule) GetAction() string {
	if m != nil {
		return m.Action
	}
	return ""
}

type Policy struct {
	VersionType   uint32         `protobuf:"varint,1,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber *VersionNumber `protobuf:"bytes,2,req,name=version_number,json=versionNumber" json:"version_number,omitempty"`
	RuleList      []*PolicyRule  `protobuf:"bytes,3,rep,name=rule_list,json=ruleList" json:"rule_list,omitempty"`
}

func (m *Policy) Reset()                    { *m = Policy{} }
func (m *Policy) String() string            { return proto.CompactTextString(m) }
func (*Policy) ProtoMessage()               {}
func (*Policy) Descriptor() ([]byte, []int) { return fileDescriptorClientversionsvr, []int{17} }

func (m *Policy) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *Policy) GetVersionNumber() *VersionNumber {
	if m != nil {
		return m.VersionNumber
	}
	return nil
}

func (m *Policy) GetRuleList() []*PolicyRule {
	if m != nil {
		return m.RuleList
	}
	return nil
}

type DumpVersionUpgradeInfoRsp struct {
	VersionList []*Version `protobuf:"bytes,1,rep,name=version_list,json=versionList" json:"version_list,omitempty"`
	PolicyList  []*Policy  `protobuf:"bytes,2,rep,name=policy_list,json=policyList" json:"policy_list,omitempty"`
}

func (m *DumpVersionUpgradeInfoRsp) Reset()         { *m = DumpVersionUpgradeInfoRsp{} }
func (m *DumpVersionUpgradeInfoRsp) String() string { return proto.CompactTextString(m) }
func (*DumpVersionUpgradeInfoRsp) ProtoMessage()    {}
func (*DumpVersionUpgradeInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{18}
}

func (m *DumpVersionUpgradeInfoRsp) GetVersionList() []*Version {
	if m != nil {
		return m.VersionList
	}
	return nil
}

func (m *DumpVersionUpgradeInfoRsp) GetPolicyList() []*Policy {
	if m != nil {
		return m.PolicyList
	}
	return nil
}

// 内部使用
type UserLatestVersionData struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	VersionType   uint32 `protobuf:"varint,2,req,name=version_type,json=versionType" json:"version_type"`
	VersionNumber uint32 `protobuf:"varint,3,req,name=version_number,json=versionNumber" json:"version_number"`
	At            uint32 `protobuf:"varint,4,req,name=at" json:"at"`
}

func (m *UserLatestVersionData) Reset()         { *m = UserLatestVersionData{} }
func (m *UserLatestVersionData) String() string { return proto.CompactTextString(m) }
func (*UserLatestVersionData) ProtoMessage()    {}
func (*UserLatestVersionData) Descriptor() ([]byte, []int) {
	return fileDescriptorClientversionsvr, []int{19}
}

func (m *UserLatestVersionData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLatestVersionData) GetVersionType() uint32 {
	if m != nil {
		return m.VersionType
	}
	return 0
}

func (m *UserLatestVersionData) GetVersionNumber() uint32 {
	if m != nil {
		return m.VersionNumber
	}
	return 0
}

func (m *UserLatestVersionData) GetAt() uint32 {
	if m != nil {
		return m.At
	}
	return 0
}

func init() {
	proto.RegisterType((*VersionNumber)(nil), "clientversion.VersionNumber")
	proto.RegisterType((*GetLatestVersionReq)(nil), "clientversion.GetLatestVersionReq")
	proto.RegisterType((*Version)(nil), "clientversion.Version")
	proto.RegisterType((*GetLatestVersionRsp)(nil), "clientversion.GetLatestVersionRsp")
	proto.RegisterType((*GetVersionDistCountReq)(nil), "clientversion.GetVersionDistCountReq")
	proto.RegisterType((*GetVersionDistCountResp)(nil), "clientversion.GetVersionDistCountResp")
	proto.RegisterType((*UserUseVersion)(nil), "clientversion.UserUseVersion")
	proto.RegisterType((*TrackUserVersionReq)(nil), "clientversion.TrackUserVersionReq")
	proto.RegisterType((*TrackUserVersionRsp)(nil), "clientversion.TrackUserVersionRsp")
	proto.RegisterType((*GetUserLatestVersionReq)(nil), "clientversion.GetUserLatestVersionReq")
	proto.RegisterType((*GetUserLatestVersionRsp)(nil), "clientversion.GetUserLatestVersionRsp")
	proto.RegisterType((*Time)(nil), "clientversion.Time")
	proto.RegisterType((*VersionUserCount)(nil), "clientversion.VersionUserCount")
	proto.RegisterType((*GetVersionUserCountReq)(nil), "clientversion.GetVersionUserCountReq")
	proto.RegisterType((*GetVersionUserCountRsp)(nil), "clientversion.GetVersionUserCountRsp")
	proto.RegisterType((*DumpVersionUpgradeInfoReq)(nil), "clientversion.DumpVersionUpgradeInfoReq")
	proto.RegisterType((*PolicyRule)(nil), "clientversion.PolicyRule")
	proto.RegisterType((*Policy)(nil), "clientversion.Policy")
	proto.RegisterType((*DumpVersionUpgradeInfoRsp)(nil), "clientversion.DumpVersionUpgradeInfoRsp")
	proto.RegisterType((*UserLatestVersionData)(nil), "clientversion.UserLatestVersionData")
	proto.RegisterEnum("clientversion.VersionType", VersionType_name, VersionType_value)
	proto.RegisterEnum("clientversion.GetVersionUserCountReq_QueryMethod", GetVersionUserCountReq_QueryMethod_name, GetVersionUserCountReq_QueryMethod_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ClientVersionSvr service

type ClientVersionSvrClient interface {
	GetLatestVersion(ctx context.Context, in *GetLatestVersionReq, opts ...grpc.CallOption) (*GetLatestVersionRsp, error)
	// 查询放量数
	GetVersionDistCount(ctx context.Context, in *GetVersionDistCountReq, opts ...grpc.CallOption) (*GetVersionDistCountResp, error)
	//
	TrackUserVersion(ctx context.Context, in *TrackUserVersionReq, opts ...grpc.CallOption) (*TrackUserVersionRsp, error)
	GetUserLatestVersion(ctx context.Context, in *GetUserLatestVersionReq, opts ...grpc.CallOption) (*GetUserLatestVersionRsp, error)
	GetVersionUserCount(ctx context.Context, in *GetVersionUserCountReq, opts ...grpc.CallOption) (*GetVersionUserCountRsp, error)
	DumpVersionUpgradeInfo(ctx context.Context, in *DumpVersionUpgradeInfoReq, opts ...grpc.CallOption) (*DumpVersionUpgradeInfoRsp, error)
}

type clientVersionSvrClient struct {
	cc *grpc.ClientConn
}

func NewClientVersionSvrClient(cc *grpc.ClientConn) ClientVersionSvrClient {
	return &clientVersionSvrClient{cc}
}

func (c *clientVersionSvrClient) GetLatestVersion(ctx context.Context, in *GetLatestVersionReq, opts ...grpc.CallOption) (*GetLatestVersionRsp, error) {
	out := new(GetLatestVersionRsp)
	err := grpc.Invoke(ctx, "/clientversion.ClientVersionSvr/GetLatestVersion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientVersionSvrClient) GetVersionDistCount(ctx context.Context, in *GetVersionDistCountReq, opts ...grpc.CallOption) (*GetVersionDistCountResp, error) {
	out := new(GetVersionDistCountResp)
	err := grpc.Invoke(ctx, "/clientversion.ClientVersionSvr/GetVersionDistCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientVersionSvrClient) TrackUserVersion(ctx context.Context, in *TrackUserVersionReq, opts ...grpc.CallOption) (*TrackUserVersionRsp, error) {
	out := new(TrackUserVersionRsp)
	err := grpc.Invoke(ctx, "/clientversion.ClientVersionSvr/TrackUserVersion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientVersionSvrClient) GetUserLatestVersion(ctx context.Context, in *GetUserLatestVersionReq, opts ...grpc.CallOption) (*GetUserLatestVersionRsp, error) {
	out := new(GetUserLatestVersionRsp)
	err := grpc.Invoke(ctx, "/clientversion.ClientVersionSvr/GetUserLatestVersion", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientVersionSvrClient) GetVersionUserCount(ctx context.Context, in *GetVersionUserCountReq, opts ...grpc.CallOption) (*GetVersionUserCountRsp, error) {
	out := new(GetVersionUserCountRsp)
	err := grpc.Invoke(ctx, "/clientversion.ClientVersionSvr/GetVersionUserCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientVersionSvrClient) DumpVersionUpgradeInfo(ctx context.Context, in *DumpVersionUpgradeInfoReq, opts ...grpc.CallOption) (*DumpVersionUpgradeInfoRsp, error) {
	out := new(DumpVersionUpgradeInfoRsp)
	err := grpc.Invoke(ctx, "/clientversion.ClientVersionSvr/DumpVersionUpgradeInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ClientVersionSvr service

type ClientVersionSvrServer interface {
	GetLatestVersion(context.Context, *GetLatestVersionReq) (*GetLatestVersionRsp, error)
	// 查询放量数
	GetVersionDistCount(context.Context, *GetVersionDistCountReq) (*GetVersionDistCountResp, error)
	//
	TrackUserVersion(context.Context, *TrackUserVersionReq) (*TrackUserVersionRsp, error)
	GetUserLatestVersion(context.Context, *GetUserLatestVersionReq) (*GetUserLatestVersionRsp, error)
	GetVersionUserCount(context.Context, *GetVersionUserCountReq) (*GetVersionUserCountRsp, error)
	DumpVersionUpgradeInfo(context.Context, *DumpVersionUpgradeInfoReq) (*DumpVersionUpgradeInfoRsp, error)
}

func RegisterClientVersionSvrServer(s *grpc.Server, srv ClientVersionSvrServer) {
	s.RegisterService(&_ClientVersionSvr_serviceDesc, srv)
}

func _ClientVersionSvr_GetLatestVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientVersionSvrServer).GetLatestVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/clientversion.ClientVersionSvr/GetLatestVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientVersionSvrServer).GetLatestVersion(ctx, req.(*GetLatestVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientVersionSvr_GetVersionDistCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionDistCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientVersionSvrServer).GetVersionDistCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/clientversion.ClientVersionSvr/GetVersionDistCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientVersionSvrServer).GetVersionDistCount(ctx, req.(*GetVersionDistCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientVersionSvr_TrackUserVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TrackUserVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientVersionSvrServer).TrackUserVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/clientversion.ClientVersionSvr/TrackUserVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientVersionSvrServer).TrackUserVersion(ctx, req.(*TrackUserVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientVersionSvr_GetUserLatestVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLatestVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientVersionSvrServer).GetUserLatestVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/clientversion.ClientVersionSvr/GetUserLatestVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientVersionSvrServer).GetUserLatestVersion(ctx, req.(*GetUserLatestVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientVersionSvr_GetVersionUserCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionUserCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientVersionSvrServer).GetVersionUserCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/clientversion.ClientVersionSvr/GetVersionUserCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientVersionSvrServer).GetVersionUserCount(ctx, req.(*GetVersionUserCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientVersionSvr_DumpVersionUpgradeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DumpVersionUpgradeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientVersionSvrServer).DumpVersionUpgradeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/clientversion.ClientVersionSvr/DumpVersionUpgradeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientVersionSvrServer).DumpVersionUpgradeInfo(ctx, req.(*DumpVersionUpgradeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ClientVersionSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "clientversion.ClientVersionSvr",
	HandlerType: (*ClientVersionSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLatestVersion",
			Handler:    _ClientVersionSvr_GetLatestVersion_Handler,
		},
		{
			MethodName: "GetVersionDistCount",
			Handler:    _ClientVersionSvr_GetVersionDistCount_Handler,
		},
		{
			MethodName: "TrackUserVersion",
			Handler:    _ClientVersionSvr_TrackUserVersion_Handler,
		},
		{
			MethodName: "GetUserLatestVersion",
			Handler:    _ClientVersionSvr_GetUserLatestVersion_Handler,
		},
		{
			MethodName: "GetVersionUserCount",
			Handler:    _ClientVersionSvr_GetVersionUserCount_Handler,
		},
		{
			MethodName: "DumpVersionUpgradeInfo",
			Handler:    _ClientVersionSvr_DumpVersionUpgradeInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/clientversionsvr/clientversionsvr.proto",
}

func (m *VersionNumber) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VersionNumber) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.NVal))
	dAtA[i] = 0x12
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.SVal)))
	i += copy(dAtA[i:], m.SVal)
	dAtA[i] = 0x18
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionCode))
	return i, nil
}

func (m *GetLatestVersionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLatestVersionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	if m.VersionNumber != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber.Size()))
		n1, err := m.VersionNumber.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x30
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.ProtocolCmd))
	return i, nil
}

func (m *Version) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Version) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	if m.VersionNumber == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("version_number")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber.Size()))
		n2, err := m.VersionNumber.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.DownloadUrl)))
	i += copy(dAtA[i:], m.DownloadUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.PromptTitle)))
	i += copy(dAtA[i:], m.PromptTitle)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.PromptContent)))
	i += copy(dAtA[i:], m.PromptContent)
	dAtA[i] = 0x32
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.FileMd5)))
	i += copy(dAtA[i:], m.FileMd5)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.FileHeaderMd5)))
	i += copy(dAtA[i:], m.FileHeaderMd5)
	if m.PromptBelowVer != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.PromptBelowVer.Size()))
		n3, err := m.PromptBelowVer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x48
	i++
	if m.ForceUpg {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.ForceUpgVer != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.ForceUpgVer.Size()))
		n4, err := m.ForceUpgVer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x58
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionCode))
	dAtA[i] = 0x62
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.HcPluginUrl)))
	i += copy(dAtA[i:], m.HcPluginUrl)
	dAtA[i] = 0x68
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.HighFreqPromptAfter))
	dAtA[i] = 0x70
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.HighFreqPromptInterval))
	return i, nil
}

func (m *GetLatestVersionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLatestVersionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Version != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Version.Size()))
		n5, err := m.Version.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GetVersionDistCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVersionDistCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	if m.VersionNumber == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("version_number")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber.Size()))
		n6, err := m.VersionNumber.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *GetVersionDistCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVersionDistCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *UserUseVersion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserUseVersion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	if m.VersionNumber == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("version_number")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber.Size()))
		n7, err := m.VersionNumber.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.At))
	return i, nil
}

func (m *TrackUserVersionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrackUserVersionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserVersion == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("user_version")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.UserVersion.Size()))
		n8, err := m.UserVersion.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *TrackUserVersionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TrackUserVersionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserLatestVersionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLatestVersionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserLatestVersionRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLatestVersionRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserVersion != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.UserVersion.Size()))
		n9, err := m.UserVersion.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *Time) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Time) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Year))
	dAtA[i] = 0x10
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Month))
	dAtA[i] = 0x18
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Day))
	dAtA[i] = 0x20
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.WeekNum))
	return i, nil
}

func (m *VersionUserCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VersionUserCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Time == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("time")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Time.Size()))
		n10, err := m.Time.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetVersionUserCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVersionUserCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.QueryMethod))
	dAtA[i] = 0x10
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	if m.VersionNumber == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("version_number")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber.Size()))
		n11, err := m.VersionNumber.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.BeginTime == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("begin_time")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.BeginTime.Size()))
		n12, err := m.BeginTime.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.EndTime != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.EndTime.Size()))
		n13, err := m.EndTime.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *GetVersionUserCountRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVersionUserCountRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CountList) > 0 {
		for _, msg := range m.CountList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintClientversionsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DumpVersionUpgradeInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DumpVersionUpgradeInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PolicyRule) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PolicyRule) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	if len(m.Parameters) > 0 {
		for _, s := range m.Parameters {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(len(m.Action)))
	i += copy(dAtA[i:], m.Action)
	return i, nil
}

func (m *Policy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Policy) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	if m.VersionNumber == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("version_number")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber.Size()))
		n14, err := m.VersionNumber.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if len(m.RuleList) > 0 {
		for _, msg := range m.RuleList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintClientversionsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DumpVersionUpgradeInfoRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DumpVersionUpgradeInfoRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.VersionList) > 0 {
		for _, msg := range m.VersionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintClientversionsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.PolicyList) > 0 {
		for _, msg := range m.PolicyList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintClientversionsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserLatestVersionData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLatestVersionData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.VersionNumber))
	dAtA[i] = 0x20
	i++
	i = encodeVarintClientversionsvr(dAtA, i, uint64(m.At))
	return i, nil
}

func encodeFixed64Clientversionsvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Clientversionsvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintClientversionsvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *VersionNumber) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.NVal))
	l = len(m.SVal)
	n += 1 + l + sovClientversionsvr(uint64(l))
	n += 1 + sovClientversionsvr(uint64(m.VersionCode))
	return n
}

func (m *GetLatestVersionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.Uid))
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	if m.VersionNumber != nil {
		l = m.VersionNumber.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	n += 1 + sovClientversionsvr(uint64(m.GuildId))
	l = len(m.ClientIp)
	n += 1 + l + sovClientversionsvr(uint64(l))
	n += 1 + sovClientversionsvr(uint64(m.ProtocolCmd))
	return n
}

func (m *Version) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	if m.VersionNumber != nil {
		l = m.VersionNumber.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	l = len(m.DownloadUrl)
	n += 1 + l + sovClientversionsvr(uint64(l))
	l = len(m.PromptTitle)
	n += 1 + l + sovClientversionsvr(uint64(l))
	l = len(m.PromptContent)
	n += 1 + l + sovClientversionsvr(uint64(l))
	l = len(m.FileMd5)
	n += 1 + l + sovClientversionsvr(uint64(l))
	l = len(m.FileHeaderMd5)
	n += 1 + l + sovClientversionsvr(uint64(l))
	if m.PromptBelowVer != nil {
		l = m.PromptBelowVer.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	n += 2
	if m.ForceUpgVer != nil {
		l = m.ForceUpgVer.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	n += 1 + sovClientversionsvr(uint64(m.VersionCode))
	l = len(m.HcPluginUrl)
	n += 1 + l + sovClientversionsvr(uint64(l))
	n += 1 + sovClientversionsvr(uint64(m.HighFreqPromptAfter))
	n += 1 + sovClientversionsvr(uint64(m.HighFreqPromptInterval))
	return n
}

func (m *GetLatestVersionRsp) Size() (n int) {
	var l int
	_ = l
	if m.Version != nil {
		l = m.Version.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	return n
}

func (m *GetVersionDistCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	if m.VersionNumber != nil {
		l = m.VersionNumber.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	return n
}

func (m *GetVersionDistCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.Count))
	return n
}

func (m *UserUseVersion) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.Uid))
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	if m.VersionNumber != nil {
		l = m.VersionNumber.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	n += 1 + sovClientversionsvr(uint64(m.At))
	return n
}

func (m *TrackUserVersionReq) Size() (n int) {
	var l int
	_ = l
	if m.UserVersion != nil {
		l = m.UserVersion.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	return n
}

func (m *TrackUserVersionRsp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserLatestVersionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.Uid))
	return n
}

func (m *GetUserLatestVersionRsp) Size() (n int) {
	var l int
	_ = l
	if m.UserVersion != nil {
		l = m.UserVersion.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	return n
}

func (m *Time) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.Year))
	n += 1 + sovClientversionsvr(uint64(m.Month))
	n += 1 + sovClientversionsvr(uint64(m.Day))
	n += 1 + sovClientversionsvr(uint64(m.WeekNum))
	return n
}

func (m *VersionUserCount) Size() (n int) {
	var l int
	_ = l
	if m.Time != nil {
		l = m.Time.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	n += 1 + sovClientversionsvr(uint64(m.Count))
	return n
}

func (m *GetVersionUserCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.QueryMethod))
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	if m.VersionNumber != nil {
		l = m.VersionNumber.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	if m.BeginTime != nil {
		l = m.BeginTime.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	if m.EndTime != nil {
		l = m.EndTime.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	return n
}

func (m *GetVersionUserCountRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.CountList) > 0 {
		for _, e := range m.CountList {
			l = e.Size()
			n += 1 + l + sovClientversionsvr(uint64(l))
		}
	}
	return n
}

func (m *DumpVersionUpgradeInfoReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PolicyRule) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	n += 1 + l + sovClientversionsvr(uint64(l))
	if len(m.Parameters) > 0 {
		for _, s := range m.Parameters {
			l = len(s)
			n += 1 + l + sovClientversionsvr(uint64(l))
		}
	}
	l = len(m.Action)
	n += 1 + l + sovClientversionsvr(uint64(l))
	return n
}

func (m *Policy) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	if m.VersionNumber != nil {
		l = m.VersionNumber.Size()
		n += 1 + l + sovClientversionsvr(uint64(l))
	}
	if len(m.RuleList) > 0 {
		for _, e := range m.RuleList {
			l = e.Size()
			n += 1 + l + sovClientversionsvr(uint64(l))
		}
	}
	return n
}

func (m *DumpVersionUpgradeInfoRsp) Size() (n int) {
	var l int
	_ = l
	if len(m.VersionList) > 0 {
		for _, e := range m.VersionList {
			l = e.Size()
			n += 1 + l + sovClientversionsvr(uint64(l))
		}
	}
	if len(m.PolicyList) > 0 {
		for _, e := range m.PolicyList {
			l = e.Size()
			n += 1 + l + sovClientversionsvr(uint64(l))
		}
	}
	return n
}

func (m *UserLatestVersionData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClientversionsvr(uint64(m.Uid))
	n += 1 + sovClientversionsvr(uint64(m.VersionType))
	n += 1 + sovClientversionsvr(uint64(m.VersionNumber))
	n += 1 + sovClientversionsvr(uint64(m.At))
	return n
}

func sovClientversionsvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozClientversionsvr(x uint64) (n int) {
	return sovClientversionsvr(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *VersionNumber) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VersionNumber: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VersionNumber: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NVal", wireType)
			}
			m.NVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SVal", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SVal = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionCode", wireType)
			}
			m.VersionCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLatestVersionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLatestVersionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLatestVersionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VersionNumber == nil {
				m.VersionNumber = &VersionNumber{}
			}
			if err := m.VersionNumber.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProtocolCmd", wireType)
			}
			m.ProtocolCmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProtocolCmd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Version) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Version: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Version: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VersionNumber == nil {
				m.VersionNumber = &VersionNumber{}
			}
			if err := m.VersionNumber.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DownloadUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DownloadUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PromptTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PromptTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PromptContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PromptContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FileMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FileMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FileHeaderMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FileHeaderMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PromptBelowVer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PromptBelowVer == nil {
				m.PromptBelowVer = &VersionNumber{}
			}
			if err := m.PromptBelowVer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceUpg", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForceUpg = bool(v != 0)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceUpgVer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ForceUpgVer == nil {
				m.ForceUpgVer = &VersionNumber{}
			}
			if err := m.ForceUpgVer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionCode", wireType)
			}
			m.VersionCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HcPluginUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HcPluginUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HighFreqPromptAfter", wireType)
			}
			m.HighFreqPromptAfter = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HighFreqPromptAfter |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HighFreqPromptInterval", wireType)
			}
			m.HighFreqPromptInterval = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HighFreqPromptInterval |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_number")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("download_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLatestVersionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLatestVersionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLatestVersionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Version == nil {
				m.Version = &Version{}
			}
			if err := m.Version.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVersionDistCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetVersionDistCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetVersionDistCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VersionNumber == nil {
				m.VersionNumber = &VersionNumber{}
			}
			if err := m.VersionNumber.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_number")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVersionDistCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetVersionDistCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetVersionDistCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserUseVersion) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserUseVersion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserUseVersion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VersionNumber == nil {
				m.VersionNumber = &VersionNumber{}
			}
			if err := m.VersionNumber.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_number")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrackUserVersionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TrackUserVersionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TrackUserVersionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserVersion == nil {
				m.UserVersion = &UserUseVersion{}
			}
			if err := m.UserVersion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TrackUserVersionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TrackUserVersionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TrackUserVersionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLatestVersionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLatestVersionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLatestVersionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLatestVersionRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLatestVersionRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLatestVersionRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserVersion == nil {
				m.UserVersion = &UserUseVersion{}
			}
			if err := m.UserVersion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Time) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Time: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Time: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Year", wireType)
			}
			m.Year = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Year |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Month", wireType)
			}
			m.Month = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Month |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Day", wireType)
			}
			m.Day = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Day |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WeekNum", wireType)
			}
			m.WeekNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WeekNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("year")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("month")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VersionUserCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VersionUserCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VersionUserCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Time", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Time == nil {
				m.Time = &Time{}
			}
			if err := m.Time.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVersionUserCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetVersionUserCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetVersionUserCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QueryMethod", wireType)
			}
			m.QueryMethod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QueryMethod |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VersionNumber == nil {
				m.VersionNumber = &VersionNumber{}
			}
			if err := m.VersionNumber.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BeginTime == nil {
				m.BeginTime = &Time{}
			}
			if err := m.BeginTime.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.EndTime == nil {
				m.EndTime = &Time{}
			}
			if err := m.EndTime.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("query_method")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_number")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVersionUserCountRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetVersionUserCountRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetVersionUserCountRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CountList = append(m.CountList, &VersionUserCount{})
			if err := m.CountList[len(m.CountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DumpVersionUpgradeInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DumpVersionUpgradeInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DumpVersionUpgradeInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PolicyRule) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PolicyRule: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PolicyRule: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Parameters", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Parameters = append(m.Parameters, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Action", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Action = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("action")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Policy) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Policy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Policy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VersionNumber == nil {
				m.VersionNumber = &VersionNumber{}
			}
			if err := m.VersionNumber.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RuleList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RuleList = append(m.RuleList, &PolicyRule{})
			if err := m.RuleList[len(m.RuleList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_number")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DumpVersionUpgradeInfoRsp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DumpVersionUpgradeInfoRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DumpVersionUpgradeInfoRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VersionList = append(m.VersionList, &Version{})
			if err := m.VersionList[len(m.VersionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PolicyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PolicyList = append(m.PolicyList, &Policy{})
			if err := m.PolicyList[len(m.PolicyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLatestVersionData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLatestVersionData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLatestVersionData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionType", wireType)
			}
			m.VersionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VersionNumber", wireType)
			}
			m.VersionNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipClientversionsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClientversionsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("version_number")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("at")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipClientversionsvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowClientversionsvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowClientversionsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthClientversionsvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowClientversionsvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipClientversionsvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthClientversionsvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowClientversionsvr   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/clientversionsvr/clientversionsvr.proto", fileDescriptorClientversionsvr)
}

var fileDescriptorClientversionsvr = []byte{
	// 1667 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0xcf, 0x6f, 0x1b, 0x5b,
	0x15, 0xce, 0xd8, 0x4e, 0x6c, 0x1f, 0xc7, 0xe9, 0x70, 0xd3, 0xf8, 0xb9, 0xe6, 0x91, 0x0e, 0x23,
	0xa0, 0x11, 0xad, 0x13, 0xd7, 0x8f, 0x96, 0x57, 0xe3, 0x84, 0xa6, 0x49, 0x5e, 0x6b, 0x5e, 0xe2,
	0xa4, 0xae, 0xe3, 0x12, 0xaa, 0xa7, 0xd1, 0x64, 0xe6, 0xc6, 0x1e, 0x75, 0x7e, 0x65, 0xe6, 0x4e,
	0x5a, 0x23, 0x3d, 0x29, 0x62, 0x81, 0x9e, 0x58, 0x20, 0xc4, 0x0a, 0x09, 0xa4, 0xb7, 0xe9, 0x12,
	0xb1, 0x62, 0xc3, 0x0e, 0x76, 0xdd, 0xc1, 0x8a, 0x25, 0x42, 0x65, 0x41, 0xff, 0x01, 0xf6, 0xe8,
	0xde, 0x19, 0xdb, 0x33, 0xce, 0x58, 0x75, 0x04, 0xaf, 0x3b, 0xfb, 0x9c, 0xef, 0xdc, 0x7b, 0xbe,
	0xef, 0xdc, 0x73, 0xef, 0x19, 0xb8, 0xe9, 0x3a, 0xca, 0x9a, 0xa2, 0x6b, 0xd8, 0x24, 0x67, 0xd8,
	0x71, 0x35, 0xcb, 0x74, 0xcf, 0x9c, 0x0b, 0x86, 0x55, 0xdb, 0xb1, 0x88, 0x85, 0xf2, 0x11, 0x7b,
	0xe9, 0x5b, 0x8a, 0x65, 0x18, 0x96, 0xb9, 0x46, 0xf4, 0x33, 0x5b, 0x53, 0x9e, 0xeb, 0x78, 0xcd,
	0x7d, 0x7e, 0xec, 0x69, 0x3a, 0xd1, 0x4c, 0xd2, 0xb7, 0xb1, 0x1f, 0x24, 0x9a, 0x90, 0xef, 0xf8,
	0x01, 0x4d, 0xcf, 0x38, 0xc6, 0x0e, 0xba, 0x06, 0xb3, 0xa6, 0x74, 0x26, 0xeb, 0x45, 0x4e, 0xe0,
	0x56, 0xf2, 0x0f, 0x52, 0xaf, 0xff, 0x71, 0x7d, 0xa6, 0x95, 0x32, 0x3b, 0xb2, 0x4e, 0x5d, 0x2e,
	0x73, 0x25, 0x04, 0x6e, 0x25, 0x3b, 0x70, 0xb9, 0xd4, 0x75, 0x03, 0xe6, 0x83, 0x7d, 0x25, 0xc5,
	0x52, 0x71, 0x31, 0x19, 0x0a, 0xce, 0x05, 0x9e, 0x2d, 0x4b, 0xc5, 0xe2, 0x17, 0x09, 0x58, 0x7c,
	0x88, 0xc9, 0xae, 0x4c, 0xb0, 0x4b, 0x82, 0x9d, 0x5b, 0xf8, 0x14, 0x15, 0x20, 0xe9, 0x69, 0x6a,
	0x91, 0x13, 0x12, 0xc3, 0x38, 0x6a, 0x08, 0x2f, 0x4c, 0xb3, 0x2e, 0x26, 0x42, 0x80, 0xc1, 0xc2,
	0xed, 0xbe, 0x8d, 0xd1, 0x16, 0x2c, 0x0c, 0x80, 0x26, 0x63, 0xc2, 0x72, 0xc8, 0x55, 0x3f, 0x5c,
	0x8d, 0xc8, 0xb2, 0x1a, 0x61, 0xdb, 0xca, 0x9f, 0x45, 0xc8, 0x5f, 0x87, 0x4c, 0xd7, 0xd3, 0x74,
	0x55, 0xd2, 0xd4, 0x62, 0x2a, 0x44, 0x21, 0xcd, 0xac, 0x0d, 0x15, 0x7d, 0x13, 0xb2, 0xfe, 0x72,
	0x92, 0x66, 0x17, 0x67, 0x43, 0x32, 0x64, 0x7c, 0x73, 0xc3, 0xa6, 0x19, 0x33, 0x69, 0x15, 0x4b,
	0x97, 0x14, 0x43, 0x2d, 0xce, 0x85, 0xa5, 0x18, 0x78, 0xb6, 0x0c, 0x55, 0xfc, 0xcb, 0x2c, 0xa4,
	0x83, 0x6c, 0x2e, 0xd0, 0xe4, 0xa6, 0xa7, 0x49, 0x15, 0xb9, 0x24, 0xcd, 0x1b, 0x30, 0xaf, 0x5a,
	0x2f, 0x4c, 0xdd, 0x92, 0x55, 0xc9, 0x73, 0xf4, 0x62, 0x52, 0x48, 0x0c, 0x89, 0xe4, 0x06, 0x9e,
	0x43, 0x47, 0x0f, 0xb8, 0x18, 0x36, 0x91, 0x88, 0x46, 0x74, 0xcc, 0x34, 0xc9, 0x86, 0xb8, 0x18,
	0x36, 0x69, 0x53, 0x07, 0xba, 0x09, 0x0b, 0x01, 0x50, 0xb1, 0x4c, 0x82, 0x4d, 0x12, 0x11, 0x27,
	0xef, 0xfb, 0xb6, 0x7c, 0x17, 0x55, 0xf9, 0x44, 0xd3, 0xb1, 0x64, 0xa8, 0x77, 0x98, 0x3a, 0x03,
	0x58, 0x9a, 0x5a, 0xf7, 0xd4, 0x3b, 0xe8, 0x16, 0x5c, 0x61, 0x80, 0x1e, 0x96, 0x55, 0xec, 0x30,
	0x5c, 0x3a, 0xbc, 0x1c, 0x75, 0x3e, 0x62, 0x3e, 0x8a, 0xfe, 0x04, 0xf8, 0x60, 0xef, 0x63, 0xac,
	0x5b, 0x2f, 0xa4, 0x33, 0xec, 0x14, 0x33, 0x53, 0xd4, 0x3e, 0xc8, 0xf8, 0x01, 0x0d, 0xea, 0x60,
	0x87, 0xd6, 0xf6, 0xc4, 0x72, 0x14, 0x2c, 0x79, 0x76, 0xb7, 0x98, 0x15, 0xb8, 0x95, 0xcc, 0xa0,
	0xb6, 0xcc, 0x7c, 0x68, 0x77, 0xd1, 0x7d, 0xc8, 0x0f, 0x21, 0x6c, 0x1f, 0x98, 0x62, 0x9f, 0xdc,
	0x20, 0xbc, 0xe3, 0x4b, 0x1f, 0x69, 0x94, 0xdc, 0x84, 0x46, 0x41, 0x2b, 0x90, 0xef, 0x29, 0x92,
	0xad, 0x7b, 0x5d, 0xcd, 0x64, 0x45, 0x9a, 0x0f, 0x6b, 0xdf, 0x53, 0x0e, 0x98, 0x87, 0x16, 0xe9,
	0x1e, 0x14, 0x7a, 0x5a, 0xb7, 0x27, 0x9d, 0x38, 0xf8, 0x54, 0x0a, 0x94, 0x90, 0x4f, 0x08, 0x76,
	0x8a, 0xf9, 0xd0, 0xe2, 0x8b, 0x14, 0xf3, 0x89, 0x83, 0x4f, 0x0f, 0x18, 0x62, 0x93, 0x02, 0xd0,
	0x0f, 0xe1, 0xda, 0x85, 0x50, 0xcd, 0x24, 0xd8, 0xa1, 0x5d, 0xbe, 0x10, 0x8a, 0x2e, 0x44, 0xa3,
	0x1b, 0x01, 0x46, 0x7c, 0x18, 0xd3, 0xcd, 0xae, 0x8d, 0x2a, 0x90, 0x0e, 0xb8, 0xb0, 0x6b, 0x24,
	0x57, 0x2d, 0xc4, 0x2b, 0xd4, 0x1a, 0xc0, 0xc4, 0x9f, 0x73, 0x50, 0x78, 0x88, 0x07, 0x6b, 0x6c,
	0x6b, 0x2e, 0xd9, 0xb2, 0x3c, 0x93, 0xd0, 0xab, 0xe1, 0xbd, 0xf6, 0x86, 0x78, 0x07, 0x3e, 0x88,
	0xcd, 0xc3, 0xb5, 0x51, 0x09, 0x66, 0x15, 0xfa, 0x27, 0x72, 0x35, 0xfa, 0x26, 0xf1, 0xf7, 0x1c,
	0x2c, 0x1c, 0xba, 0xd8, 0x39, 0x74, 0xf1, 0xa0, 0xa7, 0xbf, 0x92, 0x2b, 0xed, 0xd2, 0xbd, 0x7e,
	0x15, 0x12, 0x32, 0x29, 0xa6, 0x42, 0x7b, 0x24, 0x64, 0x22, 0x3e, 0x85, 0xc5, 0xb6, 0x23, 0x2b,
	0xcf, 0x69, 0xca, 0xa1, 0x5b, 0xf8, 0x3e, 0xcc, 0x7b, 0x2e, 0x76, 0xa4, 0x51, 0xf1, 0xe8, 0x7e,
	0xdf, 0x18, 0xdb, 0x2f, 0xca, 0xb3, 0x95, 0xf3, 0x46, 0x8b, 0x88, 0x4b, 0x31, 0x0b, 0xbb, 0xb6,
	0x78, 0x9b, 0xa9, 0x4a, 0x8d, 0xd3, 0xde, 0xfc, 0xe2, 0xb3, 0x09, 0x21, 0xae, 0x1d, 0x93, 0x26,
	0x77, 0xc9, 0x34, 0x3d, 0x48, 0xb5, 0x35, 0x03, 0xa3, 0x22, 0xa4, 0xfa, 0x58, 0x76, 0x22, 0xbb,
	0x33, 0x0b, 0x2d, 0xb6, 0x61, 0x99, 0xa4, 0x17, 0x29, 0x8f, 0x6f, 0xa2, 0x29, 0xab, 0x72, 0x3f,
	0xf2, 0xc8, 0x51, 0x03, 0xbd, 0xd8, 0x5e, 0x60, 0xfc, 0x9c, 0x56, 0x2b, 0xfa, 0x7c, 0x50, 0x6b,
	0xd3, 0x33, 0xc4, 0xa7, 0xc0, 0x07, 0x19, 0xd0, 0xe4, 0xd8, 0xc9, 0x42, 0x37, 0x20, 0x45, 0x34,
	0x03, 0x07, 0x5a, 0x2f, 0x8e, 0x91, 0xa0, 0x59, 0xb6, 0x18, 0x60, 0x74, 0xfc, 0x22, 0x19, 0xf9,
	0xc7, 0xef, 0xaf, 0x89, 0x70, 0xfb, 0x0c, 0x17, 0x0f, 0xda, 0xe7, 0xd4, 0xc3, 0x4e, 0x5f, 0x32,
	0x30, 0xe9, 0x59, 0x51, 0xa1, 0x73, 0xcc, 0xb3, 0xc7, 0x1c, 0xef, 0xf9, 0x5c, 0x56, 0x01, 0x8e,
	0x31, 0xbd, 0xdb, 0x18, 0xf9, 0xd4, 0x64, 0xf2, 0x59, 0x06, 0x63, 0xd5, 0x5a, 0x85, 0x0c, 0x36,
	0x55, 0x3f, 0x62, 0x96, 0xd5, 0x3c, 0x36, 0x22, 0x8d, 0x4d, 0x95, 0xfe, 0x10, 0x6b, 0x90, 0x7b,
	0x1c, 0x22, 0x38, 0x0f, 0x99, 0xc7, 0x7b, 0xd2, 0xf6, 0x66, 0x63, 0xf7, 0x88, 0xe7, 0x50, 0x1e,
	0xb2, 0x8f, 0xf7, 0xa4, 0xa7, 0x3b, 0x3b, 0x9f, 0xee, 0x1e, 0xf1, 0x09, 0xb4, 0x00, 0xf0, 0x78,
	0x4f, 0xda, 0xdb, 0x6f, 0xb6, 0x1f, 0xed, 0x1e, 0xf1, 0x49, 0xf1, 0xc7, 0xf1, 0x82, 0xba, 0x36,
	0xda, 0x00, 0x60, 0xa2, 0x4b, 0xba, 0xe6, 0xd2, 0xbb, 0x20, 0xb9, 0x92, 0xab, 0x5e, 0x8f, 0xa7,
	0x3e, 0x8a, 0xcb, 0xb2, 0x90, 0x5d, 0xcd, 0x25, 0xe2, 0xd7, 0xe1, 0xda, 0xb6, 0x67, 0xd8, 0x03,
	0x88, 0xdd, 0x75, 0x64, 0x15, 0x37, 0xcc, 0x13, 0xab, 0x85, 0x4f, 0x45, 0x15, 0xe0, 0xc0, 0xd2,
	0x35, 0xa5, 0xdf, 0xf2, 0x74, 0x76, 0x3c, 0x4d, 0x39, 0x38, 0x1b, 0xc3, 0x81, 0x8b, 0x5a, 0xd0,
	0x32, 0x80, 0x2d, 0x3b, 0xb2, 0x81, 0x09, 0x76, 0xdc, 0x62, 0x42, 0x48, 0xae, 0x64, 0x5b, 0x21,
	0x0b, 0xfa, 0x10, 0xe6, 0x64, 0x85, 0xd0, 0xe6, 0x08, 0x3f, 0xee, 0x81, 0x4d, 0xfc, 0x03, 0x07,
	0x73, 0xfe, 0x36, 0xef, 0x79, 0xf2, 0xb8, 0x0b, 0x59, 0xc7, 0xd3, 0xb1, 0x2f, 0x5d, 0x92, 0x49,
	0x77, 0x6d, 0x2c, 0x7e, 0x44, 0xbf, 0x95, 0xa1, 0x58, 0xa6, 0xd9, 0x2f, 0xb9, 0x89, 0xa2, 0xb9,
	0x36, 0xba, 0x37, 0xe2, 0x10, 0xaa, 0xc9, 0xa4, 0x37, 0x67, 0xc0, 0x8a, 0x2e, 0x8c, 0xee, 0x42,
	0xce, 0x66, 0x1b, 0xfa, 0x91, 0x09, 0x16, 0xb9, 0x14, 0x9f, 0x12, 0xf8, 0x48, 0x96, 0xd0, 0x6f,
	0x39, 0x58, 0xba, 0x70, 0x37, 0x6d, 0xcb, 0x44, 0xfe, 0xdf, 0xaf, 0xfd, 0x9b, 0xb1, 0xed, 0x35,
	0x80, 0x4e, 0x73, 0xbd, 0x7f, 0xf7, 0xdf, 0x1c, 0xe4, 0x3a, 0xa1, 0x25, 0x01, 0xe6, 0x3a, 0x6d,
	0xa9, 0xd9, 0xd8, 0xe5, 0x67, 0xe8, 0x41, 0xef, 0xb4, 0xa5, 0xcd, 0xe6, 0x76, 0x6b, 0xbf, 0xb1,
	0xcd, 0x73, 0x81, 0xaf, 0xb1, 0xff, 0x84, 0x4f, 0xa0, 0x25, 0xf8, 0x9a, 0xff, 0x5b, 0xda, 0xa1,
	0x2f, 0xbc, 0xed, 0x68, 0x2e, 0xe6, 0x53, 0x68, 0x11, 0xae, 0x8c, 0x42, 0xa4, 0xdd, 0x46, 0x7b,
	0x87, 0xcf, 0xa0, 0x02, 0xa0, 0x90, 0xf1, 0xd1, 0xe1, 0x66, 0xf3, 0x68, 0xff, 0x90, 0xe7, 0xd1,
	0x55, 0xe0, 0x43, 0xf6, 0x9f, 0x6c, 0x36, 0x8e, 0x36, 0x79, 0x01, 0x7d, 0x00, 0x8b, 0x21, 0x6b,
	0x7b, 0xff, 0xe0, 0xc9, 0xc1, 0xce, 0xce, 0x36, 0x7f, 0x1f, 0x2d, 0x45, 0xe0, 0x7b, 0x9b, 0x8d,
	0x4f, 0x77, 0xf8, 0x73, 0x0e, 0x15, 0x58, 0x26, 0x43, 0x73, 0xe3, 0x47, 0x8d, 0xe6, 0x43, 0xfe,
	0x9c, 0xb6, 0x69, 0xba, 0xd3, 0x96, 0x1e, 0x1c, 0xb6, 0xdb, 0xfc, 0xf9, 0xf9, 0x9f, 0xd3, 0xd5,
	0xd7, 0x59, 0xe0, 0xb7, 0x58, 0xb1, 0x02, 0xbe, 0x4f, 0xce, 0x1c, 0xf4, 0x1f, 0x0e, 0xf8, 0xf1,
	0xb1, 0x04, 0x89, 0x63, 0x45, 0x8d, 0xf9, 0x0a, 0x29, 0xbd, 0x13, 0xe3, 0xda, 0xe2, 0x6f, 0xb8,
	0xf3, 0x57, 0x6f, 0x93, 0xdc, 0x2f, 0x5e, 0xbd, 0x4d, 0xce, 0x7b, 0x35, 0x52, 0xc3, 0xb5, 0x6e,
	0xad, 0x57, 0xb3, 0x6b, 0xbf, 0x7e, 0xf5, 0x36, 0xf9, 0x79, 0xd9, 0x13, 0xea, 0x9e, 0xa6, 0x6e,
	0x08, 0x65, 0x22, 0xd4, 0xc3, 0xe5, 0xae, 0x55, 0x5e, 0x56, 0x6e, 0xaf, 0xcb, 0xa6, 0xea, 0x58,
	0x9a, 0xba, 0x56, 0x79, 0x59, 0xf9, 0x78, 0x5d, 0xd7, 0x08, 0xde, 0x10, 0x9e, 0x95, 0xf1, 0x10,
	0x79, 0x0b, 0x77, 0x6b, 0xd5, 0xd5, 0x7b, 0xab, 0x77, 0x37, 0x3e, 0x13, 0x9e, 0x95, 0xbb, 0x42,
	0x7d, 0xf0, 0x31, 0xc2, 0xfe, 0xf7, 0x84, 0xfa, 0xf0, 0xdb, 0x83, 0x19, 0x6c, 0xa1, 0xce, 0xbe,
	0x27, 0x04, 0xc5, 0x50, 0x29, 0x06, 0xfd, 0x91, 0x63, 0xe3, 0xd8, 0xf8, 0xf0, 0x82, 0xbe, 0x7d,
	0x91, 0x56, 0xcc, 0xa0, 0x55, 0xfa, 0xce, 0x34, 0x30, 0xd7, 0x16, 0x9b, 0x54, 0x80, 0x04, 0x15,
	0x20, 0x45, 0xe9, 0x53, 0xe2, 0x3f, 0x78, 0x37, 0xdf, 0x89, 0x54, 0xd1, 0xdf, 0x39, 0xe0, 0xc7,
	0x87, 0x86, 0x0b, 0xe5, 0x8a, 0x19, 0x57, 0x4a, 0xef, 0xc4, 0xb8, 0xb6, 0xf8, 0x53, 0x9a, 0xec,
	0x1c, 0x4d, 0x36, 0xe3, 0x57, 0x4b, 0x66, 0x09, 0x2b, 0xff, 0xcf, 0x4a, 0xc9, 0x42, 0x5d, 0x26,
	0x82, 0x67, 0x6a, 0x2f, 0x05, 0xfa, 0x36, 0xb9, 0x44, 0x36, 0x68, 0x85, 0xd0, 0xcf, 0x38, 0xb8,
	0x1a, 0x37, 0xc3, 0xa0, 0x18, 0xa5, 0xe3, 0x66, 0xa3, 0xd2, 0x54, 0x38, 0xd7, 0x16, 0x4b, 0x94,
	0x64, 0x9a, 0x92, 0x4c, 0x78, 0x8c, 0x5e, 0x76, 0x48, 0x0f, 0x7d, 0x99, 0x08, 0x1f, 0x8a, 0xd1,
	0xdc, 0x31, 0xf9, 0x50, 0x84, 0xc7, 0x87, 0xd2, 0x34, 0x30, 0xd7, 0x16, 0xff, 0xc4, 0xba, 0x22,
	0x43, 0x53, 0x00, 0x83, 0xe9, 0x7c, 0x5c, 0x73, 0x59, 0x2a, 0x5f, 0x72, 0x65, 0x43, 0xa8, 0xdf,
	0x5e, 0x57, 0x65, 0x4d, 0xef, 0xaf, 0x55, 0xd7, 0xe9, 0x2c, 0xa4, 0xf7, 0xd7, 0x3e, 0x5a, 0x67,
	0xb3, 0x94, 0xde, 0xbf, 0x44, 0x05, 0x62, 0x0b, 0x20, 0x94, 0x8f, 0x85, 0x3a, 0x9b, 0x11, 0x98,
	0xf8, 0xb7, 0x84, 0x13, 0x83, 0xd4, 0xaa, 0x95, 0xdb, 0xdf, 0x2f, 0x57, 0x3e, 0x2a, 0x57, 0xbe,
	0x47, 0xeb, 0xe6, 0x0a, 0x75, 0x97, 0x58, 0x76, 0x9c, 0xff, 0xe3, 0x8d, 0xcf, 0xd0, 0xe7, 0x50,
	0x88, 0x7f, 0x5b, 0xd0, 0xca, 0x18, 0xf9, 0x89, 0xef, 0x76, 0x69, 0x4a, 0xa4, 0x6b, 0x8b, 0x57,
	0xa8, 0x50, 0x59, 0x2a, 0xd4, 0x0c, 0x95, 0x67, 0xa6, 0x34, 0xf7, 0xc5, 0xab, 0xb7, 0xc9, 0xdf,
	0xbd, 0x7c, 0xc0, 0xbf, 0x7e, 0xb3, 0xcc, 0xfd, 0xed, 0xcd, 0x32, 0xf7, 0xcf, 0x37, 0xcb, 0xdc,
	0xaf, 0xfe, 0xb5, 0x3c, 0xf3, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xd4, 0x60, 0xd4, 0xc0, 0xff,
	0x11, 0x00, 0x00,
}
