// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-red-packet-logic/channel-red-packet-logic.proto

package channel_red_packet_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-red-packet-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_red_packet_logic "golang.52tt.com/protocol/app/channel-red-packet-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelRedPacketLogicClient is the client API for ChannelRedPacketLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelRedPacketLogicClient interface {
	GetRedPacketConf(ctx context.Context, in *channel_red_packet_logic.GetRedPacketConfReq, opts ...grpc.CallOption) (*channel_red_packet_logic.GetRedPacketConfResp, error)
	GetRedPacketList(ctx context.Context, in *channel_red_packet_logic.GetRedPacketListReq, opts ...grpc.CallOption) (*channel_red_packet_logic.GetRedPacketListResp, error)
	SendRedPacket(ctx context.Context, in *channel_red_packet_logic.SendRedPacketReq, opts ...grpc.CallOption) (*channel_red_packet_logic.SendRedPacketResp, error)
	ReportRedPacketClickCnt(ctx context.Context, in *channel_red_packet_logic.ReportRedPacketClickCntReq, opts ...grpc.CallOption) (*channel_red_packet_logic.ReportRedPacketClickCntResp, error)
}

type channelRedPacketLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelRedPacketLogicClient(cc *grpc.ClientConn) ChannelRedPacketLogicClient {
	return &channelRedPacketLogicClient{cc}
}

func (c *channelRedPacketLogicClient) GetRedPacketConf(ctx context.Context, in *channel_red_packet_logic.GetRedPacketConfReq, opts ...grpc.CallOption) (*channel_red_packet_logic.GetRedPacketConfResp, error) {
	out := new(channel_red_packet_logic.GetRedPacketConfResp)
	err := c.cc.Invoke(ctx, "/logic.channel_red_packet_logic.ChannelRedPacketLogic/GetRedPacketConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRedPacketLogicClient) GetRedPacketList(ctx context.Context, in *channel_red_packet_logic.GetRedPacketListReq, opts ...grpc.CallOption) (*channel_red_packet_logic.GetRedPacketListResp, error) {
	out := new(channel_red_packet_logic.GetRedPacketListResp)
	err := c.cc.Invoke(ctx, "/logic.channel_red_packet_logic.ChannelRedPacketLogic/GetRedPacketList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRedPacketLogicClient) SendRedPacket(ctx context.Context, in *channel_red_packet_logic.SendRedPacketReq, opts ...grpc.CallOption) (*channel_red_packet_logic.SendRedPacketResp, error) {
	out := new(channel_red_packet_logic.SendRedPacketResp)
	err := c.cc.Invoke(ctx, "/logic.channel_red_packet_logic.ChannelRedPacketLogic/SendRedPacket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRedPacketLogicClient) ReportRedPacketClickCnt(ctx context.Context, in *channel_red_packet_logic.ReportRedPacketClickCntReq, opts ...grpc.CallOption) (*channel_red_packet_logic.ReportRedPacketClickCntResp, error) {
	out := new(channel_red_packet_logic.ReportRedPacketClickCntResp)
	err := c.cc.Invoke(ctx, "/logic.channel_red_packet_logic.ChannelRedPacketLogic/ReportRedPacketClickCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelRedPacketLogicServer is the server API for ChannelRedPacketLogic service.
type ChannelRedPacketLogicServer interface {
	GetRedPacketConf(context.Context, *channel_red_packet_logic.GetRedPacketConfReq) (*channel_red_packet_logic.GetRedPacketConfResp, error)
	GetRedPacketList(context.Context, *channel_red_packet_logic.GetRedPacketListReq) (*channel_red_packet_logic.GetRedPacketListResp, error)
	SendRedPacket(context.Context, *channel_red_packet_logic.SendRedPacketReq) (*channel_red_packet_logic.SendRedPacketResp, error)
	ReportRedPacketClickCnt(context.Context, *channel_red_packet_logic.ReportRedPacketClickCntReq) (*channel_red_packet_logic.ReportRedPacketClickCntResp, error)
}

func RegisterChannelRedPacketLogicServer(s *grpc.Server, srv ChannelRedPacketLogicServer) {
	s.RegisterService(&_ChannelRedPacketLogic_serviceDesc, srv)
}

func _ChannelRedPacketLogic_GetRedPacketConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_red_packet_logic.GetRedPacketConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRedPacketLogicServer).GetRedPacketConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.channel_red_packet_logic.ChannelRedPacketLogic/GetRedPacketConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRedPacketLogicServer).GetRedPacketConf(ctx, req.(*channel_red_packet_logic.GetRedPacketConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRedPacketLogic_GetRedPacketList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_red_packet_logic.GetRedPacketListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRedPacketLogicServer).GetRedPacketList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.channel_red_packet_logic.ChannelRedPacketLogic/GetRedPacketList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRedPacketLogicServer).GetRedPacketList(ctx, req.(*channel_red_packet_logic.GetRedPacketListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRedPacketLogic_SendRedPacket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_red_packet_logic.SendRedPacketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRedPacketLogicServer).SendRedPacket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.channel_red_packet_logic.ChannelRedPacketLogic/SendRedPacket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRedPacketLogicServer).SendRedPacket(ctx, req.(*channel_red_packet_logic.SendRedPacketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRedPacketLogic_ReportRedPacketClickCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_red_packet_logic.ReportRedPacketClickCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRedPacketLogicServer).ReportRedPacketClickCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.channel_red_packet_logic.ChannelRedPacketLogic/ReportRedPacketClickCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRedPacketLogicServer).ReportRedPacketClickCnt(ctx, req.(*channel_red_packet_logic.ReportRedPacketClickCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRedPacketLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.channel_red_packet_logic.ChannelRedPacketLogic",
	HandlerType: (*ChannelRedPacketLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRedPacketConf",
			Handler:    _ChannelRedPacketLogic_GetRedPacketConf_Handler,
		},
		{
			MethodName: "GetRedPacketList",
			Handler:    _ChannelRedPacketLogic_GetRedPacketList_Handler,
		},
		{
			MethodName: "SendRedPacket",
			Handler:    _ChannelRedPacketLogic_SendRedPacket_Handler,
		},
		{
			MethodName: "ReportRedPacketClickCnt",
			Handler:    _ChannelRedPacketLogic_ReportRedPacketClickCnt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-red-packet-logic/channel-red-packet-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-red-packet-logic/channel-red-packet-logic.proto", fileDescriptor_channel_red_packet_logic_a2ce2d2227fad255)
}

var fileDescriptor_channel_red_packet_logic_a2ce2d2227fad255 = []byte{
	// 320 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x72, 0xce, 0xc9, 0x4f, 0xcf,
	0x4c, 0x2e, 0x2e, 0x2b, 0xd2, 0x4d, 0xcf, 0xd7, 0x4f, 0xce, 0x48, 0xcc, 0xcb, 0x4b, 0xcd, 0xd1,
	0x2d, 0x4a, 0x4d, 0xd1, 0x2d, 0x48, 0x4c, 0xce, 0x4e, 0x2d, 0xd1, 0x05, 0x4b, 0xe3, 0x94, 0xd0,
	0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x92, 0x83, 0x70, 0xa0, 0xaa, 0xe2, 0x8b, 0x52, 0x53, 0xe2,
	0x21, 0xaa, 0xe2, 0xc1, 0x12, 0x52, 0x9a, 0xc8, 0x96, 0xa4, 0x27, 0x96, 0xa4, 0x96, 0x27, 0x56,
	0xea, 0xe7, 0x17, 0x94, 0x64, 0xe6, 0xe7, 0x15, 0xc3, 0x68, 0x88, 0x51, 0x52, 0xca, 0x89, 0x05,
	0x05, 0x38, 0xad, 0x8b, 0x87, 0x28, 0x32, 0xba, 0xce, 0xc2, 0x25, 0xea, 0x0c, 0x51, 0x13, 0x94,
	0x9a, 0x12, 0x00, 0x56, 0xe1, 0x03, 0x52, 0x20, 0xd4, 0xc4, 0xc8, 0x25, 0xe0, 0x9e, 0x5a, 0x02,
	0x17, 0x75, 0xce, 0xcf, 0x4b, 0x13, 0x32, 0xd0, 0x4b, 0x4f, 0xc4, 0xe9, 0x38, 0x3d, 0x74, 0xe5,
	0x41, 0xa9, 0x85, 0x52, 0x86, 0x24, 0xea, 0x28, 0x2e, 0x50, 0xe2, 0xfc, 0x74, 0x7e, 0xa7, 0x1e,
	0x0b, 0xc7, 0xf3, 0x8f, 0x8c, 0x18, 0x8e, 0xf0, 0xc9, 0x2c, 0x2e, 0x21, 0xc1, 0x11, 0x20, 0xe5,
	0xa4, 0x39, 0x02, 0xa2, 0x03, 0xe1, 0x88, 0x17, 0x1f, 0x19, 0x85, 0x2a, 0xb9, 0x78, 0x83, 0x53,
	0xf3, 0x52, 0xe0, 0x6a, 0x84, 0x74, 0xf1, 0x1a, 0x87, 0xa2, 0x16, 0x64, 0xbb, 0x1e, 0x29, 0xca,
	0x11, 0x56, 0xbf, 0xfc, 0xc8, 0x28, 0x34, 0x9d, 0x91, 0x4b, 0x3c, 0x28, 0xb5, 0x20, 0xbf, 0x08,
	0x29, 0x98, 0x72, 0x32, 0x93, 0xb3, 0x9d, 0xf3, 0x4a, 0x84, 0xcc, 0xf1, 0x1a, 0x8b, 0x43, 0x17,
	0xc8, 0x3d, 0x16, 0xe4, 0x69, 0x44, 0xb8, 0xec, 0xd5, 0x47, 0x46, 0x29, 0xf9, 0x5f, 0xe7, 0x77,
	0xea, 0x49, 0x71, 0x49, 0xe0, 0x4a, 0x60, 0x4e, 0x1e, 0x51, 0x6e, 0xe9, 0xf9, 0x39, 0x89, 0x79,
	0xe9, 0x7a, 0xa6, 0x46, 0x25, 0x25, 0x7a, 0xc9, 0xf9, 0xb9, 0xfa, 0xe0, 0x24, 0x97, 0x9c, 0x9f,
	0xa3, 0x5f, 0x9c, 0x5a, 0x54, 0x96, 0x99, 0x9c, 0x5a, 0xac, 0x4f, 0x4c, 0x96, 0x49, 0x62, 0x03,
	0xeb, 0x33, 0x06, 0x04, 0x00, 0x00, 0xff, 0xff, 0xb0, 0x8d, 0x33, 0xa0, 0x61, 0x03, 0x00, 0x00,
}
