package main

import (
	"context"

	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"

	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/super-player/super-player-privilege/internal/conf"

	pb "golang.52tt.com/protocol/services/super-player-privilege"

	"golang.52tt.com/services/super-player/super-player-privilege/internal"

	// use server startup
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库

	_ "gitlab.ttyuyin.com/avengers/tyr/core/service/grpc/resolver/debug"
)

func main() {
	var (
		svr *internal.Server
		cfg = &conf.StartConfig{}
		err error
	)

	// config file support yaml & json, default super-player-privilege.json/yaml
	if err := server.NewServer("super-player-privilege", cfg).
		AddGrpcServer(startup.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				// register EventLink
				kafka.InitEventLinkSubWithGrpcSvr(s)

				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterSuperPlayerPrivilegeServer(s, svr)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
