package common

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/protocol/common/status"
	antiPB "golang.52tt.com/protocol/services/antisvr"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/present-go-logic/internal/model/info"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"strconv"
	"time"
)

type optValidInterceptor struct {
}

var OptValidInterceptor *optValidInterceptor

func WithOptValidInterceptor() interceptor.Interceptor {
	return OptValidInterceptor
}

func (s *optValidInterceptor) Handle(info *info.RequestBaseInfo) error {
	ctx := info.GetCtx()
	serviceInfo := info.GetServiceInfo()
	// t豆礼物都有效
	if userpresent.PresentPriceType_PRESENT_PRICE_TBEAN == userpresent.PresentPriceType(info.GetPresentConfig().GetPriceType()) {
		info.IsOpValid = true
		return nil
	}

	// 没工会不管
	if info.SendPresentUser.GetCurrentGuildId() == 0 {
		info.IsOpValid = true
		return nil
	}

	// 检查用户是否有效 / 检查送礼CD

	uid := info.SendPresentUser.GetUid()

	isCdValid, sErr := CheckGuildMemberOptCD(ctx, serviceInfo, uid)
	if sErr != nil {
		info.IsOpValid = false
		log.ErrorWithCtx(ctx, "optValidInterceptor CheckGuildMemberOptCD err ,uid:%+v ,guild:%d ,err:%v",
			uid, info.SendPresentUser.GetCurrentGuildId(), sErr)
		return nil
	}

	isFakeDevice, sErr := CheckFakeDevice(ctx, uid)
	if sErr != nil {
		info.IsOpValid = false
		log.ErrorWithCtx(ctx, "checkValidUserOpt -- checkFakeDevice failed. uid %d err %v", uid, sErr)
		return nil
	}

	if isCdValid && (!isFakeDevice) {
		info.IsOpValid = true
		return nil
	}

	return nil
}

func CheckGuildMemberOptCD(ctx context.Context, serviceInfo *grpc.ServiceInfo, uid uint32) (isValid bool, err error) {
	//公司出口ip白名单
	if strconv.Itoa(int(serviceInfo.ClientIP)) == "*************" || strconv.Itoa(int(serviceInfo.ClientIP)) == "**************" ||
		strconv.Itoa(int(serviceInfo.ClientIP)) == "*************" {
		isValid = true
		return isValid, nil
	}

	profile, err := client.AntiCli.GetUserProfile(ctx, uint64(uid))
	if err != nil {
		log.ErrorWithCtx(ctx, "checkValidUserOpt -- GetUserProfile failed. uid %d err %v", uid, err)
		return false, err
	}

	if profile.Profile != int32(antiPB.USER_PROFILE_USER_PROFILE_NORMAL) {
		//无效用户
		return false, err
	}

	cdKey := "CD_GUILD_MEMBER" + "_" + strconv.Itoa(int(serviceInfo.ClientIP))
	//  这里填的3的enum：
	//	enum ENUM_CD_GUILD_MEMBER
	//	{
	//		GUILD_MEMBER_CHECK_IN = 1,
	//		GUILD_MEMBER_DONATE = 2,
	//		GUILD_SEND_PRESENT = 3,
	//	};
	oldVal, err := client.CooldownCli.FetchAndAddGeneralVisit(ctx, uid, cdKey, 3, 2*3600, 1, 2*3600)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkValidUserOpt -- FetchAndAddGeneralVisit failed. uid %d err %v key %s", uid, err, cdKey)
		return false, err
	}
	if oldVal >= 3 {
		log.ErrorWithCtx(ctx, "checkValidUserOpt -- fail. uid_ip limit, uid:%v, strIp:%s, oldValue:%v", uid, strconv.Itoa(int(serviceInfo.ClientIP)), oldVal)
		return false, protocol.NewExactServerError(nil, status.ErrGuildMemberOptCdIp)
	}

	cdKey = "CD_GUILD_MEMBER" + "_" + strconv.Itoa(int(serviceInfo.ClientIP)) + "_" + time.Now().Format("20060102")
	oldVal, err = client.CooldownCli.FetchAndAddPeriodVisit(ctx, uid, cdKey, 3, 86400, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkValidUserOpt -- FetchAndAddPeriodVisit failed. uid %d err %v key %s", uid, err, cdKey)
		return false, err
	}
	if oldVal >= 1 {
		log.ErrorWithCtx(ctx, "checkValidUserOpt -- fail. cd_deviceId limit, uid:%v, deviceId:%s, oldValue:%v", uid, serviceInfo.DeviceID, oldVal)
		return false, protocol.NewExactServerError(nil, status.ErrGuildMemberOptCdDevice)
	}

	return true, nil
}

func CheckFakeDevice(ctx context.Context, uid uint32) (isValid bool, err error) {
	info, err := client.UserAuthHistoryCli.GetUserLastLoginInfo(ctx, uint64(uid))
	if err != nil {
		log.ErrorWithCtx(ctx, "checkDeviceFake -- GetUserLastLoginInfo failed. uid %d err %v", uid, err)
		return false, err
	}
	if info.DeviceInfo != "" {
		return false, nil
	}
	return false, nil
}
