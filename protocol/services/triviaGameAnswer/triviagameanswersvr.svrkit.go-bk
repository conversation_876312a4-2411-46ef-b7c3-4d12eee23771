package triviagame_answer

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/triviaGameAnswer/triviagameanswersvr.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for TriviaGameAnswerSvr service
const TriviaGameAnswerSvrMagic = uint16(15235)

// Client API for TriviaGameAnswerSvr service

type TriviaGameAnswerSvrClientInterface interface {
	// 答题
	Answer(ctx context.Context, uin uint32, in *AnswerReq, opts ...svrkit.CallOption) (*AnswerResp, error)
	// 获取答题统计结果
	GetQuestionResultStat(ctx context.Context, uin uint32, in *GetQuestionResultStatReq, opts ...svrkit.CallOption) (*GetQuestionResultStatResp, error)
	// 获取用户统计数据(废弃)
	GetUserAnswerStat(ctx context.Context, uin uint32, in *GetUserAnswerStatReq, opts ...svrkit.CallOption) (*GetUserAnswerStatResp, error)
	// 获取指定问题的 正确回答用户列表
	GetAnswerCorrectUidList(ctx context.Context, uin uint32, in *GetAnswerCorrectUidListReq, opts ...svrkit.CallOption) (*GetAnswerCorrectUidListResp, error)
	// 检查用户在当前活动当前阶段 有没有答题资格
	CheckUserAnswerQualify(ctx context.Context, uin uint32, in *CheckUserAnswerQualifyReq, opts ...svrkit.CallOption) (*CheckUserAnswerQualifyResp, error)
	// 给活动获胜用户发奖
	AwardMoneyToWinUser(ctx context.Context, uin uint32, in *AwardMoneyToWinUserReq, opts ...svrkit.CallOption) (*AwardMoneyToWinUserResp, error)
	// 淘汰用户
	WashedoutUser(ctx context.Context, uin uint32, in *WashedoutUserReq, opts ...svrkit.CallOption) (*WashedoutUserResp, error)
}

type TriviaGameAnswerSvrClient struct {
	cc *svrkit.ClientConn
}

func NewTriviaGameAnswerSvrClient(cc *svrkit.ClientConn) TriviaGameAnswerSvrClientInterface {
	return &TriviaGameAnswerSvrClient{cc}
}

const (
	commandtriviaGameAnswerSvrGetSelfSvnInfo          = 9995
	commandtriviaGameAnswerSvrEcho                    = 9999
	commandtriviaGameAnswerSvrAnswer                  = 1
	commandtriviaGameAnswerSvrGetQuestionResultStat   = 2
	commandtriviaGameAnswerSvrGetUserAnswerStat       = 3
	commandtriviaGameAnswerSvrGetAnswerCorrectUidList = 4
	commandtriviaGameAnswerSvrCheckUserAnswerQualify  = 5
	commandtriviaGameAnswerSvrAwardMoneyToWinUser     = 6
	commandtriviaGameAnswerSvrWashedoutUser           = 7
)

func (c *TriviaGameAnswerSvrClient) Answer(ctx context.Context, uin uint32, in *AnswerReq, opts ...svrkit.CallOption) (*AnswerResp, error) {
	out := new(AnswerResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrAnswer, "/triviagame_answer.TriviaGameAnswerSvr/Answer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *TriviaGameAnswerSvrClient) GetQuestionResultStat(ctx context.Context, uin uint32, in *GetQuestionResultStatReq, opts ...svrkit.CallOption) (*GetQuestionResultStatResp, error) {
	out := new(GetQuestionResultStatResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrGetQuestionResultStat, "/triviagame_answer.TriviaGameAnswerSvr/GetQuestionResultStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *TriviaGameAnswerSvrClient) GetUserAnswerStat(ctx context.Context, uin uint32, in *GetUserAnswerStatReq, opts ...svrkit.CallOption) (*GetUserAnswerStatResp, error) {
	out := new(GetUserAnswerStatResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrGetUserAnswerStat, "/triviagame_answer.TriviaGameAnswerSvr/GetUserAnswerStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *TriviaGameAnswerSvrClient) GetAnswerCorrectUidList(ctx context.Context, uin uint32, in *GetAnswerCorrectUidListReq, opts ...svrkit.CallOption) (*GetAnswerCorrectUidListResp, error) {
	out := new(GetAnswerCorrectUidListResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrGetAnswerCorrectUidList, "/triviagame_answer.TriviaGameAnswerSvr/GetAnswerCorrectUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *TriviaGameAnswerSvrClient) CheckUserAnswerQualify(ctx context.Context, uin uint32, in *CheckUserAnswerQualifyReq, opts ...svrkit.CallOption) (*CheckUserAnswerQualifyResp, error) {
	out := new(CheckUserAnswerQualifyResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrCheckUserAnswerQualify, "/triviagame_answer.TriviaGameAnswerSvr/CheckUserAnswerQualify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *TriviaGameAnswerSvrClient) AwardMoneyToWinUser(ctx context.Context, uin uint32, in *AwardMoneyToWinUserReq, opts ...svrkit.CallOption) (*AwardMoneyToWinUserResp, error) {
	out := new(AwardMoneyToWinUserResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrAwardMoneyToWinUser, "/triviagame_answer.TriviaGameAnswerSvr/AwardMoneyToWinUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *TriviaGameAnswerSvrClient) WashedoutUser(ctx context.Context, uin uint32, in *WashedoutUserReq, opts ...svrkit.CallOption) (*WashedoutUserResp, error) {
	out := new(WashedoutUserResp)
	err := c.cc.Invoke(ctx, uin, commandtriviaGameAnswerSvrWashedoutUser, "/triviagame_answer.TriviaGameAnswerSvr/WashedoutUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
