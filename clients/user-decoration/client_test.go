package user_decoration

import (
	"context"
	"google.golang.org/grpc"
	"os"
	"testing"

	"gitlab.ttyuyin.com/golang/gudetama/log"

	"google.golang.org/grpc/grpclog"

	. "github.com/smartystreets/goconvey/convey"

	pb "golang.52tt.com/protocol/services/user-decoration"
)

var cli *Client

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	var err error
	cli, err = NewClient(grpc.WithBlock())
	if err != nil {
		log.Fatalln(err)
	}
}

func TestClient_UserDecorations(t *testing.T) {
	<PERSON>vey("get user decorations from mysql", t, func() {
		resp, err := cli.UserDecorations(context.TODO(), 1969210, pb.Type_FLOAT)
		So(err, ShouldBeNil)
		for _, d := range resp {
			t.Logf("%#v", d.GetDecoration())
			t.Logf("%#v", d.Get<PERSON>ec<PERSON>eta<PERSON>())
			t.Logf("%#v", d.GetTim())
		}
	})
}