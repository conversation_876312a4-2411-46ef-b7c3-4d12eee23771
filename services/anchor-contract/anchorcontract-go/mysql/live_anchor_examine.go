package mysql

import (
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/anchorcontract-go"
)

/*
const createLiveAnchorExamineTbl = `CREATE TABLE IF NOT EXISTS tbl_live_anchor_examine (
	id int unsigned NOT NULL AUTO_INCREMENT,
	uid int unsigned NOT NULL,
	guild_id int unsigned NOT NULL,
	is_set_time int unsigned NOT NULL DEFAULT 0 COMMENT '是否设置了考核时间',
	status int unsigned NOT NULL DEFAULT 0,
	channel_tag_id int unsigned NOT NULL DEFAULT 0 COMMENT '直播间标签id',
	giver varchar(30) NOT NULL DEFAULT "" COMMENT '发放人',
	handler varchar(30) NOT NULL DEFAULT "" COMMENT '处理人',
	remarks varchar(80) NOT NULL DEFAULT "" COMMENT '备注',
	examine_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '考核时间',
	create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (id),
	INDEX index_guild (guild_id),
	INDEX index_examine_time (examine_time),
	INDEX index_uid (uid)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='语音主播考核表'`
*/

const tblLiveAnchorExamineName = "tbl_live_anchor_examine"

var queryLiveAnchorExamine = "id,uid,guild_id,status,examine_time,channel_tag_id,is_set_time,update_time,create_time,giver,handler,remarks"

// 语音主播考核
type LiveAnchorExamine struct {
	Id           uint32    `db:"id"`
	Uid          uint32    `db:"uid"`
	GuildId      uint32    `db:"guild_id"`
	Status       uint32    `db:"status"`
	ChannelTagId uint32    `db:"channel_tag_id"`
	IsSetTime    bool      `db:"is_set_time"`
	Giver        string    `db:"giver"`
	Handler      string    `db:"handler"`
	Remarks      string    `db:"remarks"`
	ExamineTime  time.Time `db:"examine_time"`
	UpdateTime   time.Time `db:"update_time"`
	CreateTime   time.Time `db:"create_time"`
}

func (t *LiveAnchorExamine) TableName() string {
	return tblLiveAnchorExamineName
}

func (s *Store) AddLiveAnchorExamine(tx *gorm.DB, info *LiveAnchorExamine) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	err := db.Table(tblLiveAnchorExamineName).Create(info).Error
	if err != nil {
		log.Errorf("AddLiveAnchorExamine fail to Create info:%+v err:%v", info, err)
		return err
	}

	log.Infof("AddLiveAnchorExamine  info:%+v", info)
	return nil
}

func (s *Store) UpdateLiveAnchorExamineTime(tx *gorm.DB, id uint32, examineTime time.Time) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set examine_time=?, is_set_time=? where id=?", tblLiveAnchorExamineName)

	err := db.Exec(sql, examineTime, true, id).Error
	if err != nil {
		log.Errorf("UpdateLiveAnchorExamineTime fail to Exec id:%v examineTime:%v err:%v", id, examineTime, err)
		return err
	}

	log.Infof("UpdateLiveAnchorExamineTime id:%v examineTime:%v", id, examineTime)
	return nil
}

func (s *Store) UpdateLiveAnchorExamineStatus(tx *gorm.DB, id, status uint32, handler, remarks string) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set status=?, handler=?, remarks=? where id=?", tblLiveAnchorExamineName)

	err := db.Exec(sql, status, handler, remarks, id).Error
	if err != nil {
		log.Errorf("UpdateLiveAnchorExamineStatus fail to Exec id:%v status:%v handler:%v remarks:%v err:%v", id, status, handler, remarks, err)
		return err
	}

	log.Infof("UpdateLiveAnchorExamineStatus id:%v status:%v handler:%v remarks:%v", id, status, handler, remarks)
	return nil
}

func (s *Store) CancelLiveAnchorExamine(tx *gorm.DB, uid, guildId uint32, handler, remarks string) error {
	db := s.db
	if tx != nil {
		db = tx
	}

	sql := fmt.Sprintf("update %s set status=?, handler=?, remarks=? where uid=? and guild_id=? and status=0", tblLiveAnchorExamineName)

	err := db.Exec(sql, uint32(pb.LiveAnchorExamine_ENUM_CANCEL), handler, remarks, uid, guildId).Error
	if err != nil {
		log.Errorf("CancelLiveAnchorExamine fail to Exec uid:%v, guildId:%v, handler:%v remarks:%v err:%v", uid, guildId, handler, remarks, err)
		return err
	}

	log.Infof("CancelLiveAnchorExamine uid:%v, guildId:%v handler:%v remarks:%v", uid, guildId, handler, remarks)
	return nil
}

func (s *Store) GetUserLiveAnchorExamine(uid uint32, statusList []uint32) ([]*LiveAnchorExamine, error) {
	list := make([]*LiveAnchorExamine, 0, 100)
	if len(statusList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblLiveAnchorExamineName).Select(queryLiveAnchorExamine).
		Where("uid=? and status in(?)", uid, statusList).Order("id desc").Limit(100).
		Scan(&list).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return list, nil
		}

		log.Errorf("GetUserLiveAnchorExamine fail to Select. uid:%v, err:%v", uid, err)
		return list, err
	}

	return list, nil
}

func (s *Store) BatchGetUserLiveAnchorExamine(uidList, statusList []uint32) ([]*LiveAnchorExamine, error) {
	list := make([]*LiveAnchorExamine, 0, 100)
	if len(statusList) == 0 || len(uidList) == 0 {
		return list, nil
	}

	err := s.db.Table(tblLiveAnchorExamineName).Select(queryLiveAnchorExamine).
		Where("uid in(?) and status in(?)", uidList, statusList).Order("id desc").
		Scan(&list).Error

	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return list, nil
		}

		log.Errorf("BatchGetUserLiveAnchorExamine fail to Select. uidList:%v, err:%v", uidList, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetAllLiveAnchorExamine(begin, limit, fromTime, toTime uint32, statusList []uint32) ([]*LiveAnchorExamine, error) {
	list := make([]*LiveAnchorExamine, 0, limit)
	if limit == 0 || len(statusList) == 0 {
		return list, nil
	}

	query := "status in(?)"
	query = timerangeLimit(fromTime, toTime, query)

	err := s.db.Table(tblLiveAnchorExamineName).Select(queryLiveAnchorExamine).
		Where(query, statusList).Order("create_time desc").Offset(begin).Limit(limit).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetAllLiveAnchorExamine fail to Select. err:%v", err)
		return list, err
	}

	return list, nil
}

func timerangeLimit(fromTime, toTime uint32, query string) string {
	if fromTime == 0 || toTime == 0 {
		return query
	}

	query = query + fmt.Sprintf(" and create_time >= '%v' and create_time <'%v'",
		time.Unix(int64(fromTime), 0).Format("2006-01-02 15:04:05"), time.Unix(int64(toTime), 0).Format("2006-01-02 15:04:05"))

	return query
}

func (s *Store) GetAllLiveAnchorExamineCnt(statusList []uint32, fromTime, toTime uint32) (uint32, error) {
	if len(statusList) == 0 {
		return 0, nil
	}

	query := "status in(?)"
	query = timerangeLimit(fromTime, toTime, query)

	rows, err := s.db.Table(tblLiveAnchorExamineName).Select("count(1)").
		Where(query, statusList).Rows()

	if err != nil {
		log.Errorf("GetAllLiveAnchorExamineCnt fail to Select. err:%v", err)
		return 0, err
	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	defer rows.Close()
	var total uint32

	if rows.Next() {
		_ = rows.Scan(&total)
	}

	return total, nil
}

func (s *Store) GetGuildLiveAnchorExamine(guildId, begin, limit, fromTime, toTime uint32, statusList []uint32) ([]*LiveAnchorExamine, error) {
	list := make([]*LiveAnchorExamine, 0, limit)
	if limit == 0 || len(statusList) == 0 {
		return list, nil
	}

	query := "guild_id=? and status in(?)"
	query = timerangeLimit(fromTime, toTime, query)

	err := s.db.Table(tblLiveAnchorExamineName).Select(queryLiveAnchorExamine).
		Where(query, guildId, statusList).Order("create_time desc").Offset(begin).Limit(limit).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetGuildLiveAnchorExamine fail to Select. guildId:%v, err:%v", guildId, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetGuildLiveAnchorExamineCnt(guildId, fromTime, toTime uint32, statusList []uint32) (uint32, error) {
	if len(statusList) == 0 {
		return 0, nil
	}

	query := "guild_id=? and status in(?)"
	query = timerangeLimit(fromTime, toTime, query)
	rows, err := s.db.Table(tblLiveAnchorExamineName).Select("count(1)").Where(query, guildId, statusList).Rows()

	if err != nil {
		log.Errorf("GetGuildLiveAnchorExamineCnt fail to Select. guildId:%v, err:%v", guildId, err)
		return 0, err
	}
	if rows.Err() != nil {
		log.Errorln(rows.Err())
	}

	defer rows.Close()
	var total uint32

	if rows.Next() {
		_ = rows.Scan(&total)
	}

	return total, nil
}

func (s *Store) GetTimeoutLiveAnchorExamine(expireTime time.Time, status uint32) ([]*LiveAnchorExamine, error) {
	list := make([]*LiveAnchorExamine, 0)

	err := s.db.Table(tblLiveAnchorExamineName).Select(queryLiveAnchorExamine).
		Where("examine_time<? and status=? and is_set_time=1 ", expireTime, status).Limit(100).
		Scan(&list).Error

	if err != nil {
		log.Errorf("GetTimeoutLiveAnchorExamine fail to Select. expireTime:%v, err:%v", expireTime, err)
		return list, err
	}

	return list, nil
}
