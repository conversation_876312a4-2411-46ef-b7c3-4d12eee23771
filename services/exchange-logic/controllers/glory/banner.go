package glory

import (
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    Exchange "golang.52tt.com/protocol/services/exchange"
    "golang.52tt.com/services/exchange-logic/controllers"
    "golang.52tt.com/services/exchange-logic/models"
    "time"
)

type BannerController struct {
    controllers.APIController
}

func (c *BannerController) Get() {
    ctx := c.Context()
    uid := c.AuthInfo().UserID
    osType, _ := c.GetUint32("os_type", 0)
    marketId, _ := c.GetUint32("market_id", 0)
    result := &Exchange.GetAllBannerResp{}

    tNow := time.Now().Unix()

    banner, err := models.ExchangeGloryClient.GetAllBanner(ctx)
    if err != nil {
        serverErr := protocol.ToServerError(err)
        log.ErrorWithCtx(ctx, "ExchangeGloryClient.GetAllBanner err=%v", serverErr)
        c.ServeAPIJsonWithError(serverErr.Code(), serverErr.Message())
    }

    for _, elem := range banner.GetList() {

        if elem.BeginTime > tNow || elem.EndTime < tNow {
            continue
        }

        if osType > 0 && elem.OsType > 0 && osType != elem.OsType {
            continue
        }

        showMarket := false
        for _, marketElem := range elem.MarketId {
            if marketId == marketElem {
                showMarket = true
                break
            }
        }

        if !showMarket {
            continue
        }

        if elem.AccountLevel > 0 {
            _, level, serverErr := models.ExpClient.GetUserExp(ctx, uid)
            if serverErr != nil {
                log.ErrorWithCtx(ctx, "ExpClient.GetUserExp err=%v", serverErr)
                c.ServeAPIJsonWithError(serverErr.Code(), serverErr.Message())
            }
            if level < elem.AccountLevel {
                continue
            }
        }

        if elem.RichLevel > 0 {
            numericResp, serverErr := models.NumericClient.GetPersonalNumericV2(ctx, uid)
            if serverErr != nil {
                log.ErrorWithCtx(ctx, "NumericClient.GetPersonalNumericV2 err=%v", serverErr)
                c.ServeAPIJsonWithError(serverErr.Code(), serverErr.Message())
            }
            if numericResp.Rich < elem.RichLevel {
                continue
            }
        }

        if elem.NobilityLevel > 0 {
            nobilityInfo, serverErr := models.NobilityClient.GetNobilityInfo(ctx, uid, false)
            if serverErr != nil {
                log.ErrorWithCtx(ctx, "NobilityClient.GetNobilityInfo err=%v", serverErr)
                c.ServeAPIJsonWithError(serverErr.Code(), serverErr.Message())
            }
            if nobilityInfo.Level < elem.NobilityLevel {
                continue
            }
        }

        data := &Exchange.GloryBannerData{
            Id:      elem.GetId(),
            ImgUrl:  elem.GetImgUrl(),
            JumpUrl: elem.GetJumpUrl(),
        }
        result.List = append(result.List, data)

    }

    c.ServeAPIJson(result)
}


