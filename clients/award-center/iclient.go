package award_center

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/risk-control/award-center"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	AddBusiness(ctx context.Context, req *pb.AddBusinessConfReq) (*pb.AddBusinessConfResp, protocol.ServerError)
	GetBusinessConf(ctx context.Context, businessId uint32) (*pb.GetBusinessConfResp, protocol.ServerError)
	DelBusinessConf(ctx context.Context, businessId uint32) (*pb.DelBusinessConfResp, protocol.ServerError)
	AddAwardConf(ctx context.Context, req *pb.AddAwardConfReq) (*pb.AddAwardConfResp, protocol.ServerError)
	GetAwardConf(ctx context.Context, businessId, giftType uint32, giftId string) (*pb.GetAwardConfResp, protocol.ServerError)
	DelAwardConf(ctx context.Context, businessId, giftType uint32, giftId string) (*pb.DelAwardConfResp, protocol.ServerError)
	Award(ctx context.Context, req *pb.AwardReq) protocol.ServerError
	RollbackAward(ctx context.Context, orderId string) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
