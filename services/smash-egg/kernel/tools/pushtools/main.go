package main

import (
	"context"
	"flag"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	headImage "golang.52tt.com/clients/headimage"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/protocol"
	ga "golang.52tt.com/protocol/app/push"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"google.golang.org/grpc"
	"time"
)

var (
	accountCli   *account.Client
	channelCli   *channel.Client
	headImageCli *headImage.Client
	pushCli      *push.Client
)

func main() {
	// 初始日期
	var uid uint
	// 结束日期
	var channelId uint
	var pushUid uint
	var username string
	var nickname string

	flag.UintVar(&uid, "uid", 0, "中奖用户uid")
	flag.UintVar(&channelId, "cid", 0, "中奖房间channelId")
	flag.UintVar(&pushUid, "pid", 0, "推送的uid")
	flag.StringVar(&username, "uname", "", "推送的uid")
	flag.StringVar(&nickname, "nick", "", "推送的uid")
	flag.Parse()
	opt := grpc.WithBlock()
	accountCli, _ = account.NewClient(opt)
	channelCli = channel.NewClient(opt)
	headImageCli = headImage.NewClient(opt)
	pushCli, _ = push.NewClient(opt)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	pushBingo(ctx, uint32(uid), uint32(channelId), uint32(pushUid), username, nickname)
}

func pushBingo(ctx context.Context, uid, channelId, pushUid uint32, username, nickname string) {
	user, err := accountCli.GetUser(ctx, uid)
	if err != nil {
		fmt.Printf("get user failed:%+v", err)
		return
	}
	channelInfo, err := channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		fmt.Printf("get channel failed:%+v", err)
		return
	}
	faceMd5, err := headImageCli.GetHeadImageMd5(ctx, uid, user.Username)
	if nil != err {
		fmt.Printf("get user head image failed:%+v", err)
		return
	}

	msg := ga.CommonBreakingNewsV3{
		FromUid:          pushUid,
		FromAccount:      username,
		FromNick:         nickname,
		FromFaceMd5:      faceMd5,
		ChannelId:        channelId,
		ChannelDisplayid: channelInfo.GetDisplayId(),
		ChannelBindid:    channelInfo.GetBindId(),
		ChannelType:      channelInfo.GetChannelType(),
		ChannelName:      channelInfo.GetName(),
		NewsPrefix:       "难以置信！",
		NewsContent:      "魔力转转",
		BreakingNewsBaseOpt: &ga.CommBreakingNewsBaseOpt{
			RollingCount:     1,
			RollingTime:      10,
			AnnounceScope:    uint32(ga.CommBreakingNewsBaseOpt_INSIDE_CHANNEL),
			AnnouncePosition: uint32(ga.CommBreakingNewsBaseOpt_UPPER),
			JumpType:         uint32(ga.CommBreakingNewsBaseOpt_JUMP_CLICK_INSIDE),
			JumpPosition:     uint32(ga.CommBreakingNewsBaseOpt_JUMP_SMASH_EGGS_UI_CLICK),
		},
		PresentNewsBaseOpt: &ga.PresentBreakingNewsBaseOpt{
			GiftName:    "魔幻水晶*1",
			GiftCount:   1,
			GiftIconUrl: "https://tt-ugc-cdnqn.52tt.com/images-oss/147-510147-d57b0b4c54f5523e018725fa768eab04",
		},
	}

	msg.BreakingNewsBaseOpt.TriggerType = uint32(ga.CommBreakingNewsBaseOpt_SMASH_EGG_BINGO)
	errb := broadcast(ctx, &msg, ga.PushMessage_COMMON_BREAKING_EVENT_V3)
	if nil != errb {
		fmt.Printf("broadcast %+v failed: %+v", msg, err)
		return
	}

	fmt.Printf("broadcast success %+v", msg)
}

func broadcast(ctx context.Context, msg proto.MessageV1, cmd ga.PushMessage_CMD_TYPE) (err error) {
	data, err := proto.Marshal(msg)
	if nil != err {
		return err
	}
	pushMsg := ga.PushMessage{
		Cmd:     uint32(cmd),
		Content: data,
		SeqId:   uint32(time.Now().Unix()),
	}
	mb, err := pushMsg.Marshal()
	if nil != err {
		fmt.Printf("Marshal msg failed:%+v", err)
		return err
	}
	err = pushCli.PushMulticast(ctx, 0, "0@group", []uint32{}, &pushPB.CompositiveNotification{
		Sequence:           uint32(time.Now().Unix()),
		TerminalTypePolicy: push.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    mb,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 600,
		},
	})
	if nil != err {
		fmt.Printf("pushService msg failed:%+v", err)
		return err
	}
	return nil
}
