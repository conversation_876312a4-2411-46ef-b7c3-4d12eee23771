// Code generated by MockGen. DO NOT EDIT.
// Source: F:\quicksilver\services\headwear-go\cache\cache_api.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	redis "github.com/go-redis/redis"
	gomock "github.com/golang/mock/gomock"
	headwear_go "golang.52tt.com/protocol/services/headwear-go"
)

// MockIHeadwearCache is a mock of IHeadwearCache interface.
type MockIHeadwearCache struct {
	ctrl     *gomock.Controller
	recorder *MockIHeadwearCacheMockRecorder
}

// MockIHeadwearCacheMockRecorder is the mock recorder for MockIHeadwearCache.
type MockIHeadwearCacheMockRecorder struct {
	mock *MockIHeadwearCache
}

// NewMockIHeadwearCache creates a new mock instance.
func NewMockIHeadwearCache(ctrl *gomock.Controller) *MockIHeadwearCache {
	mock := &MockIHeadwearCache{ctrl: ctrl}
	mock.recorder = &MockIHeadwearCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIHeadwearCache) EXPECT() *MockIHeadwearCacheMockRecorder {
	return m.recorder
}

// BatGetUserHeadwearInUse mocks base method.
func (m *MockIHeadwearCache) BatGetUserHeadwearInUse(ctx context.Context, uidList []uint32) (map[uint32]*headwear_go.UserHeadwearInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserHeadwearInUse", ctx, uidList)
	ret0, _ := ret[0].(map[uint32]*headwear_go.UserHeadwearInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserHeadwearInUse indicates an expected call of BatGetUserHeadwearInUse.
func (mr *MockIHeadwearCacheMockRecorder) BatGetUserHeadwearInUse(ctx, uidList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserHeadwearInUse", reflect.TypeOf((*MockIHeadwearCache)(nil).BatGetUserHeadwearInUse), ctx, uidList)
}

// BatchGetHeadwearConfigByIdList mocks base method.
func (m *MockIHeadwearCache) BatchGetHeadwearConfigByIdList(ctx context.Context, headwearIdList []uint32) (map[uint32]*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetHeadwearConfigByIdList", ctx, headwearIdList)
	ret0, _ := ret[0].(map[uint32]*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetHeadwearConfigByIdList indicates an expected call of BatchGetHeadwearConfigByIdList.
func (mr *MockIHeadwearCacheMockRecorder) BatchGetHeadwearConfigByIdList(ctx, headwearIdList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetHeadwearConfigByIdList", reflect.TypeOf((*MockIHeadwearCache)(nil).BatchGetHeadwearConfigByIdList), ctx, headwearIdList)
}

// BatchSetHeadwearConfig mocks base method.
func (m *MockIHeadwearCache) BatchSetHeadwearConfig(ctx context.Context, confList []*headwear_go.HeadwearConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetHeadwearConfig", ctx, confList)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchSetHeadwearConfig indicates an expected call of BatchSetHeadwearConfig.
func (mr *MockIHeadwearCacheMockRecorder) BatchSetHeadwearConfig(ctx, confList interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetHeadwearConfig", reflect.TypeOf((*MockIHeadwearCache)(nil).BatchSetHeadwearConfig), ctx, confList)
}

// ClearHeadwearConfig mocks base method.
func (m *MockIHeadwearCache) ClearHeadwearConfig() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearHeadwearConfig")
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearHeadwearConfig indicates an expected call of ClearHeadwearConfig.
func (mr *MockIHeadwearCacheMockRecorder) ClearHeadwearConfig() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearHeadwearConfig", reflect.TypeOf((*MockIHeadwearCache)(nil).ClearHeadwearConfig))
}

// DelUserHeadwearInUse mocks base method.
func (m *MockIHeadwearCache) DelUserHeadwearInUse(uid uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserHeadwearInUse", uid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserHeadwearInUse indicates an expected call of DelUserHeadwearInUse.
func (mr *MockIHeadwearCacheMockRecorder) DelUserHeadwearInUse(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserHeadwearInUse", reflect.TypeOf((*MockIHeadwearCache)(nil).DelUserHeadwearInUse), uid)
}

// GetAllHeadwearConfigList mocks base method.
func (m *MockIHeadwearCache) GetAllHeadwearConfigList(ctx context.Context) (map[uint32]*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllHeadwearConfigList", ctx)
	ret0, _ := ret[0].(map[uint32]*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllHeadwearConfigList indicates an expected call of GetAllHeadwearConfigList.
func (mr *MockIHeadwearCacheMockRecorder) GetAllHeadwearConfigList(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllHeadwearConfigList", reflect.TypeOf((*MockIHeadwearCache)(nil).GetAllHeadwearConfigList), ctx)
}

// GetDB mocks base method.
func (m *MockIHeadwearCache) GetDB() *redis.Ring {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDB")
	ret0, _ := ret[0].(*redis.Ring)
	return ret0
}

// GetDB indicates an expected call of GetDB.
func (mr *MockIHeadwearCacheMockRecorder) GetDB() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDB", reflect.TypeOf((*MockIHeadwearCache)(nil).GetDB))
}

// GetHeadwearConfigById mocks base method.
func (m *MockIHeadwearCache) GetHeadwearConfigById(ctx context.Context, headwearId uint32) (*headwear_go.HeadwearConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfigById", ctx, headwearId)
	ret0, _ := ret[0].(*headwear_go.HeadwearConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeadwearConfigById indicates an expected call of GetHeadwearConfigById.
func (mr *MockIHeadwearCacheMockRecorder) GetHeadwearConfigById(ctx, headwearId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfigById", reflect.TypeOf((*MockIHeadwearCache)(nil).GetHeadwearConfigById), ctx, headwearId)
}

// GetHeadwearConfigCount mocks base method.
func (m *MockIHeadwearCache) GetHeadwearConfigCount(ctx context.Context) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeadwearConfigCount", ctx)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeadwearConfigCount indicates an expected call of GetHeadwearConfigCount.
func (mr *MockIHeadwearCacheMockRecorder) GetHeadwearConfigCount(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeadwearConfigCount", reflect.TypeOf((*MockIHeadwearCache)(nil).GetHeadwearConfigCount), ctx)
}

// GetUserHeadwearInUse mocks base method.
func (m *MockIHeadwearCache) GetUserHeadwearInUse(ctx context.Context, uid uint32) (*headwear_go.UserHeadwearInfo, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserHeadwearInUse", ctx, uid)
	ret0, _ := ret[0].(*headwear_go.UserHeadwearInfo)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserHeadwearInUse indicates an expected call of GetUserHeadwearInUse.
func (mr *MockIHeadwearCacheMockRecorder) GetUserHeadwearInUse(ctx, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserHeadwearInUse", reflect.TypeOf((*MockIHeadwearCache)(nil).GetUserHeadwearInUse), ctx, uid)
}

// Lock mocks base method.
func (m *MockIHeadwearCache) Lock(key string, ttl time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lock", key, ttl)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Lock indicates an expected call of Lock.
func (mr *MockIHeadwearCacheMockRecorder) Lock(key, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockIHeadwearCache)(nil).Lock), key, ttl)
}

// PopGiveHeadwearToUser mocks base method.
func (m *MockIHeadwearCache) PopGiveHeadwearToUser() (*headwear_go.GiveHeadwearToUserReq, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopGiveHeadwearToUser")
	ret0, _ := ret[0].(*headwear_go.GiveHeadwearToUserReq)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PopGiveHeadwearToUser indicates an expected call of PopGiveHeadwearToUser.
func (mr *MockIHeadwearCacheMockRecorder) PopGiveHeadwearToUser() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopGiveHeadwearToUser", reflect.TypeOf((*MockIHeadwearCache)(nil).PopGiveHeadwearToUser))
}

// PushGiveHeadwearToUser mocks base method.
func (m *MockIHeadwearCache) PushGiveHeadwearToUser(isCheckCnt bool, giveItems ...*headwear_go.GiveHeadwearToUserReq) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{isCheckCnt}
	for _, a := range giveItems {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushGiveHeadwearToUser", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushGiveHeadwearToUser indicates an expected call of PushGiveHeadwearToUser.
func (mr *MockIHeadwearCacheMockRecorder) PushGiveHeadwearToUser(isCheckCnt interface{}, giveItems ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{isCheckCnt}, giveItems...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushGiveHeadwearToUser", reflect.TypeOf((*MockIHeadwearCache)(nil).PushGiveHeadwearToUser), varargs...)
}

// SetHeadwearConfig mocks base method.
func (m *MockIHeadwearCache) SetHeadwearConfig(ctx context.Context, conf *headwear_go.HeadwearConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeadwearConfig", ctx, conf)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHeadwearConfig indicates an expected call of SetHeadwearConfig.
func (mr *MockIHeadwearCacheMockRecorder) SetHeadwearConfig(ctx, conf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeadwearConfig", reflect.TypeOf((*MockIHeadwearCache)(nil).SetHeadwearConfig), ctx, conf)
}

// SetUserHeadwearInUse mocks base method.
func (m *MockIHeadwearCache) SetUserHeadwearInUse(ctx context.Context, uid uint32, info *headwear_go.UserHeadwearInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserHeadwearInUse", ctx, uid, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserHeadwearInUse indicates an expected call of SetUserHeadwearInUse.
func (mr *MockIHeadwearCacheMockRecorder) SetUserHeadwearInUse(ctx, uid, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserHeadwearInUse", reflect.TypeOf((*MockIHeadwearCache)(nil).SetUserHeadwearInUse), ctx, uid, info)
}
