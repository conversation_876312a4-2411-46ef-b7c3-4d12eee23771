package server

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/deal_token"
	pb "golang.52tt.com/protocol/services/backpackgo"
	"golang.52tt.com/services/backpack-go/conf"
	"golang.52tt.com/services/backpack-go/manager"
	"golang.52tt.com/services/backpack-go/mocks"
	"reflect"
	"testing"
	"time"
)

func TestBackpackServerServer_CommitItem(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	mockManager := mocks.NewMockIManager(ctl)

	nowTs := time.Unix(time.Now().Unix(), 0)
	req := &pb.CommitItemReq{
		OrderId:              "",
		Uid:                  0,
		FreezeTime:           uint32(nowTs.Unix()),
		CommitTime:           uint32(nowTs.Unix()),
		CommitCount:          0,
	}

	dealToken := deal_token.NewDealTokenData(req.GetOrderId(), req.GetOrderId(), "backpack-go", int64(req.GetUid()), 0)
	str, _ := deal_token.Encode(dealToken)
	rsp := &pb.CommitItemResp{
		DealToken:str,
	}

	gomock.InOrder(
		mockManager.EXPECT().CommitItem(ctx, req.GetUid(), req.GetCommitCount(), nowTs,nowTs, req.GetOrderId()).Return( nil),
	)

	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}
	type args struct {
		ctx context.Context
		req *pb.CommitItemReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.CommitItemResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestBackpackServerServer_CommitItem",
			fields:  fields{
				sc:  nil,
				mgr: mockManager,
			},
			args:    args{
				ctx: ctx,
				req: req,
			},
			wantOut: rsp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &BackpackServerServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}
			gotOut, err := s.CommitItem(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CommitItem() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("CommitItem() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestBackpackServerServer_FreezeItem(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
	mockManager := mocks.NewMockIManager(ctl)
	req := &pb.FreezeItemReq{
	}
	rsp := &pb.FreezeItemResp{
	}

	gomock.InOrder(
		mockManager.EXPECT().FreezeItem(ctx, req).Return( nil),
	)

	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}
	type args struct {
		ctx context.Context
		req *pb.FreezeItemReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.FreezeItemResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestBackpackServerServer_FreezeItem",
			fields:  fields{
				sc:  nil,
				mgr: mockManager,
			},
			args:    args{
				ctx: ctx,
				req: req,
			},
			wantOut: rsp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &BackpackServerServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}
			gotOut, err := s.FreezeItem(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FreezeItem() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("FreezeItem() gotOut = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func TestBackpackServerServer_ShutDown(t *testing.T) {


	type fields struct {
		sc  *conf.ServiceConfigT
		mgr *manager.Manager
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
/*			s := &BackpackServerServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}*/
		})
	}
}

func TestNewBackpackServerServer(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()
/*	mockManager := mocks.NewMockIManager(ctl)
	req := &pb.FreezeItemReq{
	}
	rsp := &pb.FreezeItemResp{
	}*/

	ctx = context.WithValue(ctx,"configfile","xx")
	type args struct {
		ctx context.Context
		cfg config.Configer
	}
	tests := []struct {
		name    string
		args    args
		want    *BackpackServerServer
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestNewBackpackServerServer",
			args:    args{
				ctx: ctx,
				cfg: nil ,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NewBackpackServerServer(tt.args.ctx, tt.args.cfg)
/*			if (err != nil) != tt.wantErr {
				t.Errorf("NewBackpackServerServer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewBackpackServerServer() got = %v, want %v", got, tt.want)
			}*/
		})
	}
}

func TestGainTimeRange(t *testing.T) {

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	ctx := context.Background()

	mockManager := mocks.NewMockIManager(ctl)
	req := &pb.TimeRangeReq{
		BeginTime:            1667877092,
		EndTime:              1667877092,
		Params:               "{\"backpack_source_id\":5}",
	}
	rsp := &pb.CountResp{
		Count:                0,
		Value:                0,
	}

	//GainTimeRange(ctx, time.Unix(req.GetBeginTime(),0) , time.Unix(req.GetEndTime(),0) , params.BackpackSourceId)
	gomock.InOrder(
		mockManager.EXPECT().GainTimeRange(ctx, time.Unix(req.GetBeginTime(),0) , time.Unix(req.GetEndTime(),0) , uint32(5)).Return( uint32(0),uint32(0),nil),
	)

	type fields struct {
		sc  *conf.ServiceConfigT
		mgr manager.IManager
	}

	type args struct {
		ctx context.Context
		req *pb.TimeRangeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantOut *pb.CountResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "TestGainTimeRange",
			args:    args{
				ctx: ctx,
				req:req,
			},
			wantOut:    rsp,
			wantErr: false,
			fields: fields{
				sc:  nil,
				mgr: mockManager,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			s := &BackpackServerServer{
				sc:  tt.fields.sc,
				mgr: tt.fields.mgr,
			}

			s.GainTimeRange(tt.args.ctx, tt.args.req)
		})
	}
}