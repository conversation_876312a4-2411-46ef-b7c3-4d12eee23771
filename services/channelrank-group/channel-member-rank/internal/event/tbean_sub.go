package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkatbean"
	"time"
)

type TBeanEventLinkSub struct {
	tBeanEvent subscriber.Subscriber
	handle     func(ctx context.Context, tBeanEvent *kafkatbean.TBeanConsumeEvent) error
}

func NewTBeanEventLinkSub(ctx context.Context, kfkConf *config.KafkaConfig, handle func(ctx context.Context, tBeanEvent *kafkatbean.TBeanConsumeEvent) error) (*TBeanEventLinkSub, error) {
	e := TBeanEventLinkSub{}
	cfg := kafka.DefaultConfig()
	cfg.ClientID = kfkConf.ClientID
	cfg.Consumer.Offsets.Initial = kafka.OffsetNewest
	cfg.Consumer.Return.Errors = true
	kafkaSub, err := kafka.NewSubscriber(kfkConf.BrokerList(), cfg, subscriber.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTBeanEventLinkSub NewSubscriber err", err)
		panic(err)
	}
	err = kafkaSub.SubscribeContext(kfkConf.GroupID, kfkConf.TopicList(), subscriber.ProcessorContextFunc(e.handlerTBeanEvent))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewTBeanEventLinkSub SubscribeContext err", err)
		panic(err)
	}
	e.tBeanEvent = kafkaSub
	e.handle = handle
	return &e, nil
}

func (s *TBeanEventLinkSub) handlerTBeanEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	tBeanEvent := &kafkatbean.TBeanConsumeEvent{}
	err := proto.Unmarshal(msg.Value, tBeanEvent)
	if err != nil {
		log.Errorf(" handlerTBeanEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	log.Debugf("handlerTBeanEvent %+v", tBeanEvent)
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	err = s.handle(ctx, tBeanEvent)
	if err != nil {
		log.Errorf("HandlerTBeanEvent handle err:%v", err)
		return err, true
	}
	return nil, false
}

func (s *TBeanEventLinkSub) Close() {
	_ = s.tBeanEvent.Stop()
}
