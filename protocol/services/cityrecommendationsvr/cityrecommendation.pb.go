// Code generated by protoc-gen-gogo.
// source: services/cityrecommendation/cityrecommendation.proto
// DO NOT EDIT!

/*
	Package cityrecommendation is a generated protocol buffer package.

	It is generated from these files:
		services/cityrecommendation/cityrecommendation.proto

	It has these top-level messages:
		AddAddrReflectionReq
		AddAddrReflectionResp
		OperCityUserFilterReq
		OperCityUserFilterResp
		BatchOperCityUserFilterReq
		BatchOperCityUserFilterResp
		RecommendMsg
		GetCityUserRecommendReq
		GetCityUserRecommendResp
*/
package cityrecommendation

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 类型
type FilterType int32

const (
	FilterType_UnkownRecommend FilterType = 0
	FilterType_GameRecommend   FilterType = 1
	FilterType_FeedRecommend   FilterType = 2
)

var FilterType_name = map[int32]string{
	0: "UnkownRecommend",
	1: "GameRecommend",
	2: "FeedRecommend",
}
var FilterType_value = map[string]int32{
	"UnkownRecommend": 0,
	"GameRecommend":   1,
	"FeedRecommend":   2,
}

func (x FilterType) Enum() *FilterType {
	p := new(FilterType)
	*p = x
	return p
}
func (x FilterType) String() string {
	return proto.EnumName(FilterType_name, int32(x))
}
func (x *FilterType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FilterType_value, data, "FilterType")
	if err != nil {
		return err
	}
	*x = FilterType(value)
	return nil
}
func (FilterType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCityrecommendation, []int{0} }

type OperType int32

const (
	OperType_AddRecommend    OperType = 0
	OperType_DeleteRecommend OperType = 1
)

var OperType_name = map[int32]string{
	0: "AddRecommend",
	1: "DeleteRecommend",
}
var OperType_value = map[string]int32{
	"AddRecommend":    0,
	"DeleteRecommend": 1,
}

func (x OperType) Enum() *OperType {
	p := new(OperType)
	*p = x
	return p
}
func (x OperType) String() string {
	return proto.EnumName(OperType_name, int32(x))
}
func (x *OperType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OperType_value, data, "OperType")
	if err != nil {
		return err
	}
	*x = OperType(value)
	return nil
}
func (OperType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCityrecommendation, []int{1} }

// /////////
// 添加用户地理位置信息(废弃)
type AddAddrReflectionReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	AdCode   uint32 `protobuf:"varint,2,req,name=ad_code,json=adCode" json:"ad_code"`
	Province string `protobuf:"bytes,3,opt,name=province" json:"province"`
	City     string `protobuf:"bytes,4,opt,name=city" json:"city"`
}

func (m *AddAddrReflectionReq) Reset()         { *m = AddAddrReflectionReq{} }
func (m *AddAddrReflectionReq) String() string { return proto.CompactTextString(m) }
func (*AddAddrReflectionReq) ProtoMessage()    {}
func (*AddAddrReflectionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{0}
}

func (m *AddAddrReflectionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddAddrReflectionReq) GetAdCode() uint32 {
	if m != nil {
		return m.AdCode
	}
	return 0
}

func (m *AddAddrReflectionReq) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *AddAddrReflectionReq) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

type AddAddrReflectionResp struct {
}

func (m *AddAddrReflectionResp) Reset()         { *m = AddAddrReflectionResp{} }
func (m *AddAddrReflectionResp) String() string { return proto.CompactTextString(m) }
func (*AddAddrReflectionResp) ProtoMessage()    {}
func (*AddAddrReflectionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{1}
}

// (废弃)
type OperCityUserFilterReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex         uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
	FilterType  uint32 `protobuf:"varint,3,req,name=filter_type,json=filterType" json:"filter_type"`
	FilterValue uint32 `protobuf:"varint,4,req,name=filter_value,json=filterValue" json:"filter_value"`
	AdCode      uint32 `protobuf:"varint,5,req,name=ad_code,json=adCode" json:"ad_code"`
	City        string `protobuf:"bytes,6,req,name=city" json:"city"`
	MTime       uint32 `protobuf:"varint,7,opt,name=m_time,json=mTime" json:"m_time"`
	OperType    uint32 `protobuf:"varint,8,opt,name=oper_type,json=operType" json:"oper_type"`
}

func (m *OperCityUserFilterReq) Reset()         { *m = OperCityUserFilterReq{} }
func (m *OperCityUserFilterReq) String() string { return proto.CompactTextString(m) }
func (*OperCityUserFilterReq) ProtoMessage()    {}
func (*OperCityUserFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{2}
}

func (m *OperCityUserFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OperCityUserFilterReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *OperCityUserFilterReq) GetFilterType() uint32 {
	if m != nil {
		return m.FilterType
	}
	return 0
}

func (m *OperCityUserFilterReq) GetFilterValue() uint32 {
	if m != nil {
		return m.FilterValue
	}
	return 0
}

func (m *OperCityUserFilterReq) GetAdCode() uint32 {
	if m != nil {
		return m.AdCode
	}
	return 0
}

func (m *OperCityUserFilterReq) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *OperCityUserFilterReq) GetMTime() uint32 {
	if m != nil {
		return m.MTime
	}
	return 0
}

func (m *OperCityUserFilterReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

type OperCityUserFilterResp struct {
}

func (m *OperCityUserFilterResp) Reset()         { *m = OperCityUserFilterResp{} }
func (m *OperCityUserFilterResp) String() string { return proto.CompactTextString(m) }
func (*OperCityUserFilterResp) ProtoMessage()    {}
func (*OperCityUserFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{3}
}

type BatchOperCityUserFilterReq struct {
	Uid             uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex             uint32   `protobuf:"varint,2,req,name=sex" json:"sex"`
	FilterType      uint32   `protobuf:"varint,3,req,name=filter_type,json=filterType" json:"filter_type"`
	FilterValueList []uint32 `protobuf:"varint,4,rep,name=filter_value_list,json=filterValueList" json:"filter_value_list,omitempty"`
	AdCode          uint32   `protobuf:"varint,5,req,name=ad_code,json=adCode" json:"ad_code"`
	City            string   `protobuf:"bytes,6,req,name=city" json:"city"`
	MTime           uint32   `protobuf:"varint,7,opt,name=m_time,json=mTime" json:"m_time"`
	OperType        uint32   `protobuf:"varint,8,opt,name=oper_type,json=operType" json:"oper_type"`
}

func (m *BatchOperCityUserFilterReq) Reset()         { *m = BatchOperCityUserFilterReq{} }
func (m *BatchOperCityUserFilterReq) String() string { return proto.CompactTextString(m) }
func (*BatchOperCityUserFilterReq) ProtoMessage()    {}
func (*BatchOperCityUserFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{4}
}

func (m *BatchOperCityUserFilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchOperCityUserFilterReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BatchOperCityUserFilterReq) GetFilterType() uint32 {
	if m != nil {
		return m.FilterType
	}
	return 0
}

func (m *BatchOperCityUserFilterReq) GetFilterValueList() []uint32 {
	if m != nil {
		return m.FilterValueList
	}
	return nil
}

func (m *BatchOperCityUserFilterReq) GetAdCode() uint32 {
	if m != nil {
		return m.AdCode
	}
	return 0
}

func (m *BatchOperCityUserFilterReq) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *BatchOperCityUserFilterReq) GetMTime() uint32 {
	if m != nil {
		return m.MTime
	}
	return 0
}

func (m *BatchOperCityUserFilterReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

type BatchOperCityUserFilterResp struct {
}

func (m *BatchOperCityUserFilterResp) Reset()         { *m = BatchOperCityUserFilterResp{} }
func (m *BatchOperCityUserFilterResp) String() string { return proto.CompactTextString(m) }
func (*BatchOperCityUserFilterResp) ProtoMessage()    {}
func (*BatchOperCityUserFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{5}
}

// 根据筛选条件获取同城推荐
type RecommendMsg struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	City string `protobuf:"bytes,2,req,name=city" json:"city"`
}

func (m *RecommendMsg) Reset()                    { *m = RecommendMsg{} }
func (m *RecommendMsg) String() string            { return proto.CompactTextString(m) }
func (*RecommendMsg) ProtoMessage()               {}
func (*RecommendMsg) Descriptor() ([]byte, []int) { return fileDescriptorCityrecommendation, []int{6} }

func (m *RecommendMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecommendMsg) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

type GetCityUserRecommendReq struct {
	Uid         uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex         uint32   `protobuf:"varint,2,req,name=sex" json:"sex"`
	FilterType  uint32   `protobuf:"varint,3,req,name=filter_type,json=filterType" json:"filter_type"`
	FilterValue uint32   `protobuf:"varint,4,req,name=filter_value,json=filterValue" json:"filter_value"`
	Num         uint32   `protobuf:"varint,5,opt,name=num" json:"num"`
	FriendList  []uint32 `protobuf:"varint,6,rep,name=friend_list,json=friendList" json:"friend_list,omitempty"`
	StartTime   uint32   `protobuf:"varint,7,opt,name=start_time,json=startTime" json:"start_time"`
	EndTime     uint32   `protobuf:"varint,8,opt,name=end_time,json=endTime" json:"end_time"`
}

func (m *GetCityUserRecommendReq) Reset()         { *m = GetCityUserRecommendReq{} }
func (m *GetCityUserRecommendReq) String() string { return proto.CompactTextString(m) }
func (*GetCityUserRecommendReq) ProtoMessage()    {}
func (*GetCityUserRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{7}
}

func (m *GetCityUserRecommendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCityUserRecommendReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GetCityUserRecommendReq) GetFilterType() uint32 {
	if m != nil {
		return m.FilterType
	}
	return 0
}

func (m *GetCityUserRecommendReq) GetFilterValue() uint32 {
	if m != nil {
		return m.FilterValue
	}
	return 0
}

func (m *GetCityUserRecommendReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *GetCityUserRecommendReq) GetFriendList() []uint32 {
	if m != nil {
		return m.FriendList
	}
	return nil
}

func (m *GetCityUserRecommendReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCityUserRecommendReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetCityUserRecommendResp struct {
	RecommendMsgList []*RecommendMsg `protobuf:"bytes,1,rep,name=recommend_msg_list,json=recommendMsgList" json:"recommend_msg_list,omitempty"`
}

func (m *GetCityUserRecommendResp) Reset()         { *m = GetCityUserRecommendResp{} }
func (m *GetCityUserRecommendResp) String() string { return proto.CompactTextString(m) }
func (*GetCityUserRecommendResp) ProtoMessage()    {}
func (*GetCityUserRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCityrecommendation, []int{8}
}

func (m *GetCityUserRecommendResp) GetRecommendMsgList() []*RecommendMsg {
	if m != nil {
		return m.RecommendMsgList
	}
	return nil
}

func init() {
	proto.RegisterType((*AddAddrReflectionReq)(nil), "cityrecommendation.AddAddrReflectionReq")
	proto.RegisterType((*AddAddrReflectionResp)(nil), "cityrecommendation.AddAddrReflectionResp")
	proto.RegisterType((*OperCityUserFilterReq)(nil), "cityrecommendation.OperCityUserFilterReq")
	proto.RegisterType((*OperCityUserFilterResp)(nil), "cityrecommendation.OperCityUserFilterResp")
	proto.RegisterType((*BatchOperCityUserFilterReq)(nil), "cityrecommendation.BatchOperCityUserFilterReq")
	proto.RegisterType((*BatchOperCityUserFilterResp)(nil), "cityrecommendation.BatchOperCityUserFilterResp")
	proto.RegisterType((*RecommendMsg)(nil), "cityrecommendation.RecommendMsg")
	proto.RegisterType((*GetCityUserRecommendReq)(nil), "cityrecommendation.GetCityUserRecommendReq")
	proto.RegisterType((*GetCityUserRecommendResp)(nil), "cityrecommendation.GetCityUserRecommendResp")
	proto.RegisterEnum("cityrecommendation.FilterType", FilterType_name, FilterType_value)
	proto.RegisterEnum("cityrecommendation.OperType", OperType_name, OperType_value)
}
func (m *AddAddrReflectionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddAddrReflectionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.AdCode))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(len(m.Province)))
	i += copy(dAtA[i:], m.Province)
	dAtA[i] = 0x22
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	return i, nil
}

func (m *AddAddrReflectionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddAddrReflectionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *OperCityUserFilterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperCityUserFilterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.FilterType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.FilterValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.AdCode))
	dAtA[i] = 0x32
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	dAtA[i] = 0x38
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.MTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.OperType))
	return i, nil
}

func (m *OperCityUserFilterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OperCityUserFilterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchOperCityUserFilterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchOperCityUserFilterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.FilterType))
	if len(m.FilterValueList) > 0 {
		for _, num := range m.FilterValueList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintCityrecommendation(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.AdCode))
	dAtA[i] = 0x32
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	dAtA[i] = 0x38
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.MTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.OperType))
	return i, nil
}

func (m *BatchOperCityUserFilterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchOperCityUserFilterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RecommendMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	return i, nil
}

func (m *GetCityUserRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCityUserRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.FilterType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.FilterValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.Num))
	if len(m.FriendList) > 0 {
		for _, num := range m.FriendList {
			dAtA[i] = 0x30
			i++
			i = encodeVarintCityrecommendation(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCityrecommendation(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *GetCityUserRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCityUserRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecommendMsgList) > 0 {
		for _, msg := range m.RecommendMsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCityrecommendation(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Cityrecommendation(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Cityrecommendation(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintCityrecommendation(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddAddrReflectionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCityrecommendation(uint64(m.Uid))
	n += 1 + sovCityrecommendation(uint64(m.AdCode))
	l = len(m.Province)
	n += 1 + l + sovCityrecommendation(uint64(l))
	l = len(m.City)
	n += 1 + l + sovCityrecommendation(uint64(l))
	return n
}

func (m *AddAddrReflectionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *OperCityUserFilterReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCityrecommendation(uint64(m.Uid))
	n += 1 + sovCityrecommendation(uint64(m.Sex))
	n += 1 + sovCityrecommendation(uint64(m.FilterType))
	n += 1 + sovCityrecommendation(uint64(m.FilterValue))
	n += 1 + sovCityrecommendation(uint64(m.AdCode))
	l = len(m.City)
	n += 1 + l + sovCityrecommendation(uint64(l))
	n += 1 + sovCityrecommendation(uint64(m.MTime))
	n += 1 + sovCityrecommendation(uint64(m.OperType))
	return n
}

func (m *OperCityUserFilterResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchOperCityUserFilterReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCityrecommendation(uint64(m.Uid))
	n += 1 + sovCityrecommendation(uint64(m.Sex))
	n += 1 + sovCityrecommendation(uint64(m.FilterType))
	if len(m.FilterValueList) > 0 {
		for _, e := range m.FilterValueList {
			n += 1 + sovCityrecommendation(uint64(e))
		}
	}
	n += 1 + sovCityrecommendation(uint64(m.AdCode))
	l = len(m.City)
	n += 1 + l + sovCityrecommendation(uint64(l))
	n += 1 + sovCityrecommendation(uint64(m.MTime))
	n += 1 + sovCityrecommendation(uint64(m.OperType))
	return n
}

func (m *BatchOperCityUserFilterResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RecommendMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCityrecommendation(uint64(m.Uid))
	l = len(m.City)
	n += 1 + l + sovCityrecommendation(uint64(l))
	return n
}

func (m *GetCityUserRecommendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCityrecommendation(uint64(m.Uid))
	n += 1 + sovCityrecommendation(uint64(m.Sex))
	n += 1 + sovCityrecommendation(uint64(m.FilterType))
	n += 1 + sovCityrecommendation(uint64(m.FilterValue))
	n += 1 + sovCityrecommendation(uint64(m.Num))
	if len(m.FriendList) > 0 {
		for _, e := range m.FriendList {
			n += 1 + sovCityrecommendation(uint64(e))
		}
	}
	n += 1 + sovCityrecommendation(uint64(m.StartTime))
	n += 1 + sovCityrecommendation(uint64(m.EndTime))
	return n
}

func (m *GetCityUserRecommendResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecommendMsgList) > 0 {
		for _, e := range m.RecommendMsgList {
			l = e.Size()
			n += 1 + l + sovCityrecommendation(uint64(l))
		}
	}
	return n
}

func sovCityrecommendation(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozCityrecommendation(x uint64) (n int) {
	return sovCityrecommendation(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AddAddrReflectionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddAddrReflectionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddAddrReflectionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdCode", wireType)
			}
			m.AdCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Province", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Province = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ad_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddAddrReflectionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddAddrReflectionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddAddrReflectionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperCityUserFilterReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperCityUserFilterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperCityUserFilterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterType", wireType)
			}
			m.FilterType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterValue", wireType)
			}
			m.FilterValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdCode", wireType)
			}
			m.AdCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MTime", wireType)
			}
			m.MTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter_value")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ad_code")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("city")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OperCityUserFilterResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OperCityUserFilterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OperCityUserFilterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchOperCityUserFilterReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchOperCityUserFilterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchOperCityUserFilterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterType", wireType)
			}
			m.FilterType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCityrecommendation
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.FilterValueList = append(m.FilterValueList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCityrecommendation
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCityrecommendation
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCityrecommendation
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.FilterValueList = append(m.FilterValueList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterValueList", wireType)
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdCode", wireType)
			}
			m.AdCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MTime", wireType)
			}
			m.MTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ad_code")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("city")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchOperCityUserFilterResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchOperCityUserFilterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchOperCityUserFilterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("city")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCityUserRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCityUserRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCityUserRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterType", wireType)
			}
			m.FilterType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterValue", wireType)
			}
			m.FilterValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCityrecommendation
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.FriendList = append(m.FriendList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCityrecommendation
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCityrecommendation
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCityrecommendation
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.FriendList = append(m.FriendList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendList", wireType)
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCityUserRecommendResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCityUserRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCityUserRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendMsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendMsgList = append(m.RecommendMsgList, &RecommendMsg{})
			if err := m.RecommendMsgList[len(m.RecommendMsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCityrecommendation(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCityrecommendation
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipCityrecommendation(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCityrecommendation
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCityrecommendation
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthCityrecommendation
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCityrecommendation
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCityrecommendation(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCityrecommendation = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCityrecommendation   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/cityrecommendation/cityrecommendation.proto", fileDescriptorCityrecommendation)
}

var fileDescriptorCityrecommendation = []byte{
	// 843 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x55, 0xcf, 0x6f, 0x1b, 0x45,
	0x14, 0xce, 0xee, 0x3a, 0x8e, 0xf3, 0x92, 0x28, 0xc9, 0xb4, 0x4d, 0x56, 0xae, 0x9a, 0x0c, 0x0b,
	0x08, 0x63, 0xb0, 0x23, 0x2a, 0x4e, 0xab, 0x95, 0x45, 0x52, 0x94, 0x5c, 0x28, 0x95, 0xac, 0x96,
	0xab, 0x59, 0x76, 0x5f, 0xda, 0x21, 0xfb, 0x63, 0xd8, 0x99, 0x0d, 0xf1, 0x8d, 0x4a, 0x1c, 0x80,
	0x53, 0xc5, 0x89, 0x13, 0xa7, 0xdc, 0xf8, 0x47, 0x7a, 0x84, 0x33, 0x12, 0x42, 0xe1, 0x92, 0x1b,
	0xff, 0x02, 0x9a, 0x5d, 0xdb, 0x99, 0xd4, 0xeb, 0xca, 0xb9, 0x00, 0xc7, 0xfd, 0xde, 0x0f, 0x7f,
	0xdf, 0xf7, 0xe6, 0x3d, 0xc3, 0x87, 0x02, 0xb3, 0x53, 0x16, 0xa0, 0xd8, 0x0b, 0x98, 0x1c, 0x66,
	0x18, 0xa4, 0x71, 0x8c, 0x49, 0xe8, 0x4b, 0x96, 0x26, 0x15, 0x50, 0x97, 0x67, 0xa9, 0x4c, 0x09,
	0x99, 0x8e, 0x34, 0xdf, 0x52, 0x5f, 0x69, 0xb2, 0x27, 0xa3, 0x53, 0xce, 0x82, 0x93, 0x08, 0xf7,
	0xc4, 0xc9, 0x17, 0x39, 0x8b, 0x24, 0x4b, 0xe4, 0x90, 0x63, 0x59, 0xe9, 0x7c, 0x6f, 0xc0, 0xed,
	0xfd, 0x30, 0xdc, 0x0f, 0xc3, 0xac, 0x8f, 0xc7, 0x11, 0x06, 0xaa, 0xb6, 0x8f, 0x5f, 0x91, 0x2d,
	0xb0, 0x72, 0x16, 0xda, 0x06, 0x35, 0x5b, 0x6b, 0x07, 0xb5, 0x97, 0x7f, 0xec, 0x2e, 0xf4, 0x15,
	0x40, 0xee, 0xc1, 0x92, 0x1f, 0x0e, 0x82, 0x34, 0x44, 0xdb, 0xd4, 0x62, 0x75, 0x3f, 0x7c, 0x90,
	0x86, 0x48, 0x28, 0x34, 0x78, 0x96, 0x9e, 0xb2, 0x24, 0x40, 0xdb, 0xa2, 0x46, 0x6b, 0x79, 0x14,
	0x9f, 0xa0, 0xc4, 0x86, 0x9a, 0x62, 0x6b, 0xd7, 0xb4, 0x68, 0x81, 0x38, 0xdb, 0x70, 0xa7, 0x82,
	0x8a, 0xe0, 0xce, 0x0b, 0x13, 0xee, 0x3c, 0xe2, 0x98, 0x3d, 0x60, 0x72, 0xf8, 0x44, 0x60, 0x76,
	0xc8, 0x22, 0x89, 0xd9, 0xeb, 0x58, 0x6e, 0x81, 0x25, 0xf0, 0xec, 0x1a, 0x43, 0x05, 0x90, 0xb7,
	0x61, 0xe5, 0xb8, 0x28, 0x1e, 0x28, 0x0f, 0x6c, 0x4b, 0x8b, 0x43, 0x19, 0x78, 0x3c, 0xe4, 0x48,
	0xde, 0x81, 0xd5, 0x51, 0xda, 0xa9, 0x1f, 0xe5, 0x68, 0xd7, 0xb4, 0xbc, 0x51, 0x83, 0xcf, 0x54,
	0x40, 0x77, 0x63, 0xb1, 0xc2, 0x8d, 0xb1, 0xd6, 0x3a, 0x35, 0xaf, 0x6b, 0x25, 0x77, 0xa1, 0x1e,
	0x0f, 0x24, 0x8b, 0xd1, 0x5e, 0xa2, 0xc6, 0xa4, 0x6e, 0x31, 0x7e, 0xcc, 0x62, 0x24, 0x6f, 0xc0,
	0x72, 0xca, 0xc7, 0x1c, 0x1b, 0x5a, 0xbc, 0xa1, 0x60, 0xc5, 0xd0, 0xb1, 0x61, 0xab, 0xca, 0x11,
	0xc1, 0x9d, 0x9f, 0x4c, 0x68, 0x1e, 0xf8, 0x32, 0x78, 0xf6, 0xaf, 0x3a, 0xd6, 0x86, 0x4d, 0xdd,
	0xb1, 0x41, 0xc4, 0x84, 0xb4, 0x6b, 0xd4, 0x6a, 0xad, 0xf5, 0xd7, 0x35, 0xc3, 0x3e, 0x61, 0x42,
	0xfe, 0x77, 0xa6, 0xdd, 0x83, 0xbb, 0x33, 0x9d, 0x11, 0xdc, 0xf9, 0x08, 0x56, 0xfb, 0xe3, 0x1d,
	0x7a, 0x28, 0x9e, 0xce, 0xb4, 0x6a, 0x4c, 0xd0, 0x7c, 0x95, 0xa0, 0xf2, 0x7e, 0xfb, 0x08, 0xe5,
	0xb8, 0xf7, 0xa4, 0xdb, 0xff, 0xe1, 0xa9, 0x6e, 0x81, 0x95, 0xe4, 0xb1, 0xbd, 0xa8, 0x39, 0xa3,
	0x00, 0xb2, 0x0b, 0x2b, 0xc7, 0x19, 0xc3, 0x24, 0x2c, 0x67, 0x56, 0x2f, 0x66, 0x06, 0x25, 0x54,
	0x8c, 0xeb, 0x4d, 0x00, 0x21, 0xfd, 0x4c, 0x4e, 0x3b, 0xbf, 0x5c, 0xe0, 0x85, 0xfb, 0xbb, 0xd0,
	0x50, 0x2d, 0x8a, 0x14, 0xdd, 0xfc, 0x25, 0x4c, 0x42, 0x95, 0xe0, 0x7c, 0x09, 0x76, 0xb5, 0x33,
	0x82, 0x93, 0x4f, 0x81, 0x4c, 0x8e, 0xd7, 0x20, 0x16, 0x4f, 0x4b, 0x26, 0x06, 0xb5, 0x5a, 0x2b,
	0xf7, 0x69, 0xb7, 0xe2, 0xea, 0xe9, 0x63, 0xea, 0x6f, 0x64, 0xda, 0x97, 0x62, 0xdc, 0x3e, 0x02,
	0x38, 0xbc, 0x72, 0xe8, 0x16, 0xac, 0x3f, 0x49, 0x4e, 0xd2, 0xaf, 0x93, 0x49, 0xd5, 0xc6, 0x02,
	0xd9, 0x84, 0xb5, 0x23, 0x3f, 0xc6, 0x2b, 0xc8, 0x50, 0xd0, 0x21, 0x62, 0x78, 0x05, 0x99, 0xed,
	0x0f, 0xa0, 0xf1, 0x68, 0xf4, 0x78, 0xc8, 0x06, 0xac, 0xee, 0x87, 0xa1, 0xde, 0xe3, 0x16, 0xac,
	0x7f, 0x8c, 0x11, 0x4a, 0xbd, 0xcb, 0xfd, 0x9f, 0x97, 0x80, 0x28, 0x95, 0xfd, 0x6b, 0x8c, 0xc9,
	0x2f, 0x06, 0x6c, 0x4e, 0x1d, 0x37, 0xd2, 0xaa, 0x12, 0x57, 0x75, 0x8e, 0x9b, 0xef, 0xce, 0x99,
	0x29, 0xb8, 0xb3, 0xff, 0xcd, 0xf9, 0xa5, 0x65, 0xfc, 0x70, 0x7e, 0x69, 0x35, 0x72, 0xd7, 0x77,
	0xb9, 0x1b, 0xbb, 0x3f, 0x9e, 0x5f, 0x5a, 0x6d, 0xda, 0xc9, 0xbd, 0x9c, 0x85, 0x3d, 0xda, 0xf1,
	0xbd, 0xd1, 0xfe, 0xf5, 0x68, 0x87, 0x7b, 0xe3, 0xcb, 0xdc, 0xa3, 0x9d, 0xd8, 0x53, 0xbf, 0xd2,
	0x23, 0x7f, 0x1b, 0x40, 0xa6, 0x97, 0x84, 0x54, 0x92, 0xa8, 0x3c, 0x33, 0xcd, 0xf6, 0xbc, 0xa9,
	0x82, 0x3b, 0xcf, 0x0d, 0xc5, 0xd8, 0x54, 0x8c, 0x49, 0xee, 0x0a, 0x97, 0xbb, 0xd2, 0x1d, 0xba,
	0xbe, 0xcb, 0xdc, 0xc8, 0x4d, 0x0b, 0xee, 0x9f, 0x5f, 0x71, 0x17, 0x9e, 0xc0, 0xb3, 0x82, 0xb7,
	0xb6, 0x1d, 0x3d, 0xda, 0x91, 0x9e, 0xbe, 0x06, 0xaf, 0x88, 0x64, 0xa5, 0x2e, 0xda, 0x89, 0xbc,
	0xf2, 0x80, 0xf4, 0x68, 0x27, 0xf5, 0x26, 0xe7, 0xa2, 0x47, 0xc9, 0xef, 0x06, 0xdc, 0xae, 0x7a,
	0x9f, 0xe4, 0xbd, 0x2a, 0x21, 0x33, 0x76, 0xbc, 0xf9, 0xfe, 0xfc, 0xc9, 0x82, 0x3b, 0xcf, 0x94,
	0x6c, 0x4b, 0xc9, 0x5e, 0x55, 0xb2, 0x93, 0x42, 0x38, 0x16, 0x82, 0x1f, 0x4e, 0x0b, 0x4e, 0xbc,
	0x24, 0x8f, 0xe7, 0x11, 0x8e, 0x1e, 0x9e, 0x0d, 0x72, 0x56, 0xee, 0x33, 0xed, 0x76, 0xbb, 0x3d,
	0xf2, 0xdc, 0x84, 0xed, 0x19, 0x97, 0x8f, 0x74, 0xab, 0x38, 0xcf, 0xfe, 0x03, 0x69, 0xee, 0xdd,
	0x28, 0x5f, 0x70, 0xe7, 0xdb, 0x62, 0xbc, 0xb5, 0xd7, 0x8c, 0x37, 0xb8, 0xe9, 0x78, 0x95, 0xa8,
	0x1b, 0x4e, 0xb8, 0x59, 0xff, 0xee, 0xfc, 0xd2, 0xfa, 0x6d, 0x78, 0xb0, 0xf1, 0xf2, 0x62, 0xc7,
	0xf8, 0xf5, 0x62, 0xc7, 0xf8, 0xf3, 0x62, 0xc7, 0x78, 0xf1, 0xd7, 0xce, 0xc2, 0x3f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0x43, 0x61, 0x7b, 0x29, 0x74, 0x09, 0x00, 0x00,
}
