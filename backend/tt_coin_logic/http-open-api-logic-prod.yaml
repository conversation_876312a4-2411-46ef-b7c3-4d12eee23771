apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: http-open-api-logic-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.tt_coin_logic.HTTPOpenApiLogic/
    delegate:
       name: open-api-coin-logic-delegator-80
       namespace: quicksilver


