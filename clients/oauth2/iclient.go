// Code generated by quicksilver-cli. DO NOT EDIT.
package oauth2

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/oauth2"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	GetAccessToken(ctx context.Context, req *pb.GetAccessTokenReq) (*pb.GetAccessTokenResp,protocol.ServerError)
	GetAppInfo(ctx context.Context, req *pb.GetAppInfoReq) (*pb.GetAppInfoResp,protocol.ServerError)
	GetAuthorizeCode(ctx context.Context, req *pb.GetAuthorizeCodeReq) (*pb.GetAuthorizeCodeResp,protocol.ServerError)
	GetBilateralFriends(ctx context.Context, req *pb.GetBilateralFriendsReq) (*pb.GetBilateralFriendsResp,protocol.ServerError)
	GetScope(ctx context.Context, req *pb.GetScopeReq) (*pb.GetScopeResp,protocol.ServerError)
	GetSendVerifyCodeCount(ctx context.Context, req *pb.GetSendVerifyCodeCountReq) (*pb.GetSendVerifyCodeCountResp,protocol.ServerError)
	GetUserInfo(ctx context.Context, req *pb.GetUserInfoReq) (*pb.GetUserInfoResp,protocol.ServerError)
	IncrSendVerifyCodeCount(ctx context.Context, req *pb.IncrSendVerifyCodeCountReq) (*pb.IncrSendVerifyCodeCountResp,protocol.ServerError)
	RefreshToken(ctx context.Context, req *pb.RefreshTokenReq) (*pb.RefreshTokenResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
