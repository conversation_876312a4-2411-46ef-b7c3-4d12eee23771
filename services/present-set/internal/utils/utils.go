package utils

import (
	fmt1 "fmt"
	pb "golang.52tt.com/protocol/services/present-set"
	"time"
)

//func Intersect[T comparable](nums1, nums2 []T) []T {
//	countMap := make(map[T]int)
//	for _, num := range nums1 {
//		countMap[num]++
//	}
//
//	var intersection []T
//	for _, num := range nums2 {
//		if count, exists := countMap[num]; exists && count > 0 {
//			intersection = append(intersection, num)
//			countMap[num]--
//		}
//	}
//
//	return intersection
//}

// CheckSetActive 检查套组生效状态
func CheckSetActive(set *pb.PresentSetConfig) bool {
	now := uint32(time.Now().Unix())
	if set.GetStartTime() > now {
		return false
	}
	if set.GetPeriod() == pb.PresentSetPeriod_PRESENT_SET_PERIOD_LIMITED && set.GetEndTime() < now {
		return false
	}
	return true
}

// CheckEmperorSetActive 检查帝王套生效状态
func CheckEmperorSetActive(set *pb.EmperorSetConfig) bool {
	now := uint32(time.Now().Unix())
	return set.GetStartTime() <= now && set.GetEndTime() > now
}

func IsNullTime(t time.Time) bool {
	return t.IsZero() || t.Unix() == 0
}

func GetUnixTime(t time.Time) uint32 {
	if IsNullTime(t) {
		return 0
	}
	return uint32(t.Unix())
}

// IntersectUint32 交集
func IntersectUint32(nums1, nums2 []uint32) []uint32 {
	countMap := make(map[uint32]int)
	for _, num := range nums1 {
		countMap[num]++
	}

	var intersection []uint32
	for _, num := range nums2 {
		if count, exists := countMap[num]; exists && count > 0 {
			intersection = append(intersection, num)
			countMap[num]--
		}
	}

	return intersection
}

func IsExistUint32(nums []uint32, num uint32) bool {
	for _, n := range nums {
		if n == num {
			return true
		}
	}
	return false
}

type DebugTimer struct {
	startTime   time.Time
	timeNodeMap map[string]time.Time
}

func NewDebugTimer() *DebugTimer {
	return &DebugTimer{
		startTime:   time.Now(),
		timeNodeMap: make(map[string]time.Time),
	}
}

func (dt *DebugTimer) Elapsed() time.Duration {
	return time.Since(dt.startTime)
}

func (dt *DebugTimer) ElapsedTag(tag string) {
	dt.timeNodeMap[tag] = time.Now()
}

func (dt *DebugTimer) GetNodeList() map[string]string {
	nodeList := make(map[string]string)
	for tag, t := range dt.timeNodeMap {
		nodeList[tag] = fmt1.Sprintf("%dms", t.Sub(dt.startTime).Milliseconds())
	}
	return nodeList
}

func (dt *DebugTimer) Reset() {
	dt.startTime = time.Now()
}
