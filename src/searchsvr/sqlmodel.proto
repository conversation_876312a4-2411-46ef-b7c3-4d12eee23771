syntax="proto2";


// namespace
package search.sqlmodel;

//字段类型
enum FieldType{
    TYPE_INT32    = 1;
    TYPE_UINT32   = 2;
    TYPE_INT64    = 3;
    TYPE_UINT64   = 4;
    TYPE_BOOL     = 5;
    TYPE_STRING   = 6;
}

//通用字段值
message FieldValue{
    required uint32 type = 1;   //ValueType
    optional int32 i32_val =2;
    optional int32 u32_val = 3;
    optional int64 i64_val = 4;
    optional uint64 u64_val = 5;
    optional bool   b_val = 6;
    optional bytes str_val = 7;
}

//字段定义
message Field{
    required string name = 1;   //名字    
    optional FieldValue value = 2;  //值
}

//表记录
message Record{
    repeated Field field_list = 1;
}

//insert
message InsertReq{
    required string table_name = 1;  //表名
    //required Record record = 2;    //表记录，只支持一条
    repeated Record record_list = 2; //表记录，支持多条
}
message InsertRsp{
}

message ReplaceReq{
    required string table_name = 1;  //表名
    //required Record record = 2;    //记录
    repeated Record record_list = 2; //表记录，支持多条
}
message ReplaceRsp{
}

// select column
message SelColumn{
    repeated Field field_list = 1;
}

//select 从哪些表
message SelTableRef{
   required string table = 1;
}

//条件表达式运算符
enum Operator{
    OP_COMP_NIL = 0;

    //比较运算符
    OP_COMP_EQ   = 1;
    OP_COMP_LT   = 2;
    OP_COMP_LTE  = 3;
    OP_COMP_GT   = 4;
    OP_COMP_GTE  = 5;

    OP_IN   = 6;
    
    //逻辑运算符
    OP_LOGICAL_AND  = 10;
    OP_LOGICAL_OR   = 11;
}

//条件表达式
message Expr{
    required uint32 op = 1;  //Operator
    optional Field field = 2;
    repeated FieldValue value_list  = 3;
    repeated Expr subexpr_list = 4;
}

//ORDER BY
message OrderbyField{
    enum DIR{
        ASC = 1;
        DESC = 2;
    }
    required Field field = 1;
    optional uint32 dir = 2;
}

message Orderby{
    repeated OrderbyField orderby_list = 1;
}

//
message Limit{    
    required uint32 limit = 1;
    optional uint32 offset = 2;
}

//OPTION clause
message Option{
    required string name = 1;   //option name
    optional FieldValue value = 2;  //option 值
}
message OptionClause{
    repeated Option option_list = 1;
}

message SelectReq{
    required SelTableRef table = 1;
    optional SelColumn sel_column = 2;    
    optional Expr expr_cond = 3;
    optional Orderby orderby = 4;
    optional Limit limit = 5;
    optional OptionClause option_clause = 6;
}

message ResultFiled{
    repeated Field field_list = 1;
}

message ResultRow{
    repeated bytes col_data = 1;
}

message SelectRsp{
    optional ResultFiled fields = 1;
    repeated ResultRow filed_data = 2;
}

message UpdateReq{
    required string table = 1;
    //repeated Field field_list = 2;
    required Record record = 2;
    optional Expr expr_cond = 3;
}

message UpdateRsp{
    //optional uint32 changed_count = 1;
}

message DeleteReq{
    required string table = 1;    
    optional Expr expr_cond = 2;
}

message DeleteRsp{
     //optional uint32 deleted_count = 1;
}

//select count(*) from xxx
message CountReq{
    required string table = 1;    
    optional Expr expr_cond = 2;
}



