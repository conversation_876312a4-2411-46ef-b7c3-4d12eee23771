package server

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    risk_mng_api "golang.52tt.com/clients/risk-mng-api"
    useronlineCli "golang.52tt.com/clients/user-online"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    esport_statistics "golang.52tt.com/protocol/services/esport-statistics"
    esport_trade "golang.52tt.com/protocol/services/esport-trade"
    "golang.52tt.com/protocol/services/esport_hall"
    "golang.52tt.com/protocol/services/esport_role"
    "golang.52tt.com/services/tt-rev/esport/common/user_group"
    "golang.52tt.com/services/tt-rev/esport/esport-internal/internal/config"
    "golang.52tt.com/services/tt-rev/esport/esport-internal/internal/local_cache"
    "google.golang.org/grpc"
)

type StartConfig struct {
}

type Server struct {
    esportTradeCli esport_trade.EsportTradeClient
    esportHallCLi  esport_hall.EsportHallClient
    esportStatCli  esport_statistics.EsportStatisticsClient
    esportRoleCli  esport_role.ESportRoleClient
    esportSkillCli esport_skill.EsportSkillClient
    riskMngApiCli  risk_mng_api.IClient
    userOnlineCli  useronlineCli.IClient
    localCache     *local_cache.LocalCache
    userGroupCli   *user_group.UserGroupCli
}

func NewServer(ctx context.Context, cfg *StartConfig) (*Server, error) {
    log.Infof("server startup with cfg: %+v", *cfg)

    s := &Server{}
    s.esportTradeCli, _ = esport_trade.NewClient(ctx)
    s.esportHallCLi, _ = esport_hall.NewClient(ctx)
    s.esportStatCli, _ = esport_statistics.NewClient(ctx)
    s.esportRoleCli, _ = esport_role.NewClient(ctx, grpc.WithBlock())
    s.riskMngApiCli, _ = risk_mng_api.NewClient()
    s.userOnlineCli, _ = useronlineCli.NewClient()
    s.esportSkillCli, _ = esport_skill.NewClient(ctx)

    s.userGroupCli = user_group.NewUserGroupCli(
        config.GetDynamicConfig().UserGroupApi.DspLpmAdminHost,
        config.GetDynamicConfig().UserGroupApi.DspLpmOfflineGroupHost,
        config.GetDynamicConfig().UserGroupApi.DspLpmApiserverHost)

    localCache, err := local_cache.NewLocalCache(s.esportRoleCli)
    if err != nil {
        return nil, err
    }
    s.localCache = localCache

    return s, nil
}

func (s *Server) ShutDown() {
}
