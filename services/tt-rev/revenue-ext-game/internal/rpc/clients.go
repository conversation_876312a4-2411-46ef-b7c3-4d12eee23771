package rpc

import (
	"context"
	accountClient "golang.52tt.com/clients/account"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	im_api "golang.52tt.com/clients/im-api"
	push "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	ukw "golang.52tt.com/clients/you-know-who"
	channelMsgClient "golang.52tt.com/pkg/channel-msg"
	revenue_audio_stream "golang.52tt.com/protocol/services/revenue-audio-stream"
	"time"
)

type Client struct {
	AccountCli       accountClient.IClient
	PushCli          push.IClient
	SeqGenClient     seqgen.IClient
	UserProfileCli   userprofileapi.IClient
	ImApiClient      im_api.IClient
	ChannelCli       channel.IClient
	ChannelMsgSender channelMsgClient.ISender
	ApiCenterCli     apicenter.IClient
	UkwClient        ukw.IClient
	AudioStreamCli   revenue_audio_stream.RevenueAudioStreamClient
}

func NewClient() (*Client, error) {
	accountCli, err := accountClient.NewClient()
	if err != nil {
		return nil, err
	}

	pushCli, _ := push.NewClient()
	seqGenClient, _ := seqgen.NewClient()
	userProfileCli, _ := userprofileapi.NewClient()
	imApiCli := im_api.NewIClient()
	channelCli := channel.NewClient()
	apiCenterClient := apicenter.NewClient()
	channelMsgSender := channelMsgClient.NewISender()
	ukwClient, _ := ukw.NewClient()

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	audioStreamCli := revenue_audio_stream.MustNewClient(ctx)

	return &Client{
		AccountCli:       accountCli,
		PushCli:          pushCli,
		SeqGenClient:     seqGenClient,
		UserProfileCli:   userProfileCli,
		ImApiClient:      imApiCli,
		ChannelCli:       channelCli,
		ChannelMsgSender: channelMsgSender,
		ApiCenterCli:     apiCenterClient,
		UkwClient:        ukwClient,
		AudioStreamCli:   audioStreamCli,
	}, nil
}

func (c *Client) Close() {
	c.PushCli.Close()
	c.AccountCli.Close()
	c.SeqGenClient.Close()
	c.PushCli.Close()
	c.ImApiClient.Close()
	c.ChannelCli.Close()
	c.ApiCenterCli.Close()
	c.UkwClient.Close()
}
