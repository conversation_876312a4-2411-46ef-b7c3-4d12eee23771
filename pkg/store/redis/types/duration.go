package types

import "time"

type durationCmd interface {
	Val() time.Duration
	Result() (time.Duration, error)
	String() string
}

type DurationCmd struct {
	durationCmd
	*baseErrCmd
	converter DurationConverter
}

type DurationConverter func(time.Duration) time.Duration

func NewDurationCmd(base baseCmd, cmd durationCmd, converter DurationConverter) *DurationCmd {
	return &DurationCmd{
		durationCmd: cmd,
		baseErrCmd:  newBaseErrCmd(base),
		converter:   converter,
	}
}

func (c *DurationCmd) Result() (time.Duration, error) {
	val, err := c.durationCmd.Result()
	if c.converter != nil {
		val = c.converter(val)
	}
	return val, NewErr(err)
}

func (c *DurationCmd) Val() time.Duration {
	val := c.durationCmd.Val()
	if c.converter != nil {
		val = c.converter(val)
	}
	return val
}
