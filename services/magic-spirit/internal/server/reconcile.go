package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	"time"
)

func (m *MagicSpirit) GetAwardTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	//params := unmarshalReconcileParams(req.GetParams())

	orderCnt, totalPrice, _, err := m.mgr.GetMagicAwardTotal(ctx, beginTime, endTime)
	if err != nil {
		log.Errorf("GetAwardTotalCount fail to GetMagicOrderTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Count = orderCnt
	out.Value = uint32(totalPrice)

	return out, nil
}

func (m *MagicSpirit) GetAwardOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	var err error
	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	//params := unmarshalReconcileParams(req.GetParams())

	out.OrderIds, err = m.mgr.GetMagicAwardOrderIdList(ctx, beginTime, endTime)
	if err != nil {
		log.Errorf("GetAwardOrderIds fail to GetMagicAwardOrderIdList. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (m *MagicSpirit) GetConsumeTotalCount(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	out := &reconcile_v2.CountResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	params := unmarshalReconcileParams(req.GetParams())

	orderCnt, totalPrice, _, err := m.mgr.GetMagicOrderTotal(ctx, beginTime, endTime, params.Source)
	if err != nil {
		log.Errorf("GetConsumeTotalCount fail to GetMagicOrderTotal. in:%+v, err:%v", req, err)
		return out, err
	}

	out.Count = orderCnt
	out.Value = uint32(totalPrice)

	return out, nil
}

func (m *MagicSpirit) GetConsumeOrderIds(ctx context.Context, req *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	out := &reconcile_v2.OrderIdsResp{}
	if req.GetBeginTime() == 0 || req.GetEndTime() == 0 {
		return out, nil
	}

	var err error
	beginTime := time.Unix(req.GetBeginTime(), 0)
	endTime := time.Unix(req.GetEndTime(), 0)

	params := unmarshalReconcileParams(req.GetParams())

	out.OrderIds, err = m.mgr.GetMagicConsumeOrderIdList(ctx, beginTime, endTime, params.Source)
	if err != nil {
		log.Errorf("GetConsumeOrderIds fail to GetMagicConsumeOrderIdList. in:%+v, err:%v", req, err)
		return out, err
	}

	return out, nil
}

func (m *MagicSpirit) ReissueMagicOrder(ctx context.Context, req *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error) {
	out := &reconcile_v2.EmptyResp{}

	if req.GetOrderId() == "" {
		log.ErrorWithCtx(ctx, "ReissueMagicOrder.req.GetOrderId() is nil, req:%v", req)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	err := m.mgr.ReissueMagicOrder(ctx, req.GetOrderId())

	return out, err
}

func (m *MagicSpirit) GenFinancialFile(ctx context.Context, req *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error) {
	out := &reconcile_v2.GenFinancialFileResp{}
	return out, m.mgr.GenFinancialFile(ctx, req)
}
