// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/channel-dating-game-record (interfaces: ChannelDatingGameRecordClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_dating_game_record "golang.52tt.com/protocol/services/channel-dating-game-record"
	grpc "google.golang.org/grpc"
)

// MockChannelDatingGameRecordClient is a mock of ChannelDatingGameRecordClient interface.
type MockChannelDatingGameRecordClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelDatingGameRecordClientMockRecorder
}

// MockChannelDatingGameRecordClientMockRecorder is the mock recorder for MockChannelDatingGameRecordClient.
type MockChannelDatingGameRecordClientMockRecorder struct {
	mock *MockChannelDatingGameRecordClient
}

// NewMockChannelDatingGameRecordClient creates a new mock instance.
func NewMockChannelDatingGameRecordClient(ctrl *gomock.Controller) *MockChannelDatingGameRecordClient {
	mock := &MockChannelDatingGameRecordClient{ctrl: ctrl}
	mock.recorder = &MockChannelDatingGameRecordClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelDatingGameRecordClient) EXPECT() *MockChannelDatingGameRecordClientMockRecorder {
	return m.recorder
}

// GetChannelDatingGameRecord mocks base method.
func (m *MockChannelDatingGameRecordClient) GetChannelDatingGameRecord(arg0 context.Context, arg1 *channel_dating_game_record.GetChannelDatingGameRecordRequest, arg2 ...grpc.CallOption) (*channel_dating_game_record.GetChannelDatingGameRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelDatingGameRecord", varargs...)
	ret0, _ := ret[0].(*channel_dating_game_record.GetChannelDatingGameRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelDatingGameRecord indicates an expected call of GetChannelDatingGameRecord.
func (mr *MockChannelDatingGameRecordClientMockRecorder) GetChannelDatingGameRecord(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelDatingGameRecord", reflect.TypeOf((*MockChannelDatingGameRecordClient)(nil).GetChannelDatingGameRecord), varargs...)
}

// GetNameplateInUse mocks base method.
func (m *MockChannelDatingGameRecordClient) GetNameplateInUse(arg0 context.Context, arg1 *channel_dating_game_record.GetNameplateInUseRequest, arg2 ...grpc.CallOption) (*channel_dating_game_record.GetNameplateInUseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNameplateInUse", varargs...)
	ret0, _ := ret[0].(*channel_dating_game_record.GetNameplateInUseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNameplateInUse indicates an expected call of GetNameplateInUse.
func (mr *MockChannelDatingGameRecordClientMockRecorder) GetNameplateInUse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNameplateInUse", reflect.TypeOf((*MockChannelDatingGameRecordClient)(nil).GetNameplateInUse), varargs...)
}

// SetNameplateInUse mocks base method.
func (m *MockChannelDatingGameRecordClient) SetNameplateInUse(arg0 context.Context, arg1 *channel_dating_game_record.SetNameplateInUseRequest, arg2 ...grpc.CallOption) (*channel_dating_game_record.SetNameplateInUseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetNameplateInUse", varargs...)
	ret0, _ := ret[0].(*channel_dating_game_record.SetNameplateInUseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNameplateInUse indicates an expected call of SetNameplateInUse.
func (mr *MockChannelDatingGameRecordClientMockRecorder) SetNameplateInUse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNameplateInUse", reflect.TypeOf((*MockChannelDatingGameRecordClient)(nil).SetNameplateInUse), varargs...)
}

// TestSetChannelDatingGameRecord mocks base method.
func (m *MockChannelDatingGameRecordClient) TestSetChannelDatingGameRecord(arg0 context.Context, arg1 *channel_dating_game_record.SetChannelDatingGameRecordRequest, arg2 ...grpc.CallOption) (*channel_dating_game_record.SetChannelDatingGameRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestSetChannelDatingGameRecord", varargs...)
	ret0, _ := ret[0].(*channel_dating_game_record.SetChannelDatingGameRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestSetChannelDatingGameRecord indicates an expected call of TestSetChannelDatingGameRecord.
func (mr *MockChannelDatingGameRecordClientMockRecorder) TestSetChannelDatingGameRecord(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestSetChannelDatingGameRecord", reflect.TypeOf((*MockChannelDatingGameRecordClient)(nil).TestSetChannelDatingGameRecord), varargs...)
}
