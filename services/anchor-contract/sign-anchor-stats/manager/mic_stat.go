package manager

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
	"runtime/debug"
	"time"
)

// HandleMicEvent 处理上下麦事件
func (m *SignAnchorStatsMgr) HandleMicEvent(event *kafkasimplemic.SimpleMicEvent) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	uid := event.GetMicUserId()
	cid := event.GetChId()

	switch kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE(event.EventType) {
	case kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_HOLD:
		// 上麦检查签约 （下麦不检查，避免在麦上签约到期无法停止）
		simpleInfo, serr := m.channelCli.GetChannelSimpleInfo(ctx, 0, cid)
		if serr != nil {
			log.ErrorWithCtx(ctx, "HandleMicEvent GetChannelSimpleInfo uid:%+v err:%v", uid, serr)
			return serr
		}
		guildId := simpleInfo.GetBindId()
		signGuildId, ok := uint32(0), false
		if signGuildId, _, ok = m.CheckIfNeedRecord(ctx, uid, guildId); !ok {
			log.DebugWithCtx(ctx, "HandleMicEvent CheckIfNeedRecord false %+v", event)
			return nil
		}
		opTime := time.Unix(int64(event.GetOpTsMs()/1000), 0)

		// 上麦 开始统计
		log.InfoWithCtx(ctx, "HandleMicEvent HandleMicOn %+v", event)
		if err := m.RedisCache.HandleMicOn(ctx, signGuildId, cid, uid, opTime); err != nil {
			log.ErrorWithCtx(ctx, "HandleMicEvent HandleMicOn err:%v", err)
			return err
		}
	case kafkasimplemic.ESIMPLE_MIC_EVENT_TYPE_ENUM_SIMPLE_MIC_EV_RELEASE:
		// 下麦 停止统计
		userInfo, err := m.RedisCache.HandleMicOff(ctx, cid, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleMicEvent HandleMicOff err:%v, event:%+v", err, event)
			return err
		}

		intervalSec := uint32(0)
		if userInfo != nil { // 判空很重要，未签约的用户可能没有在麦信息
			gid := userInfo.Gid
			intervalSec = userInfo.IntervalSec
			if err := m.IncrHoldMicTime(ctx, uid, gid, gid, cid, intervalSec, time.Now()); err != nil {
				log.ErrorWithCtx(ctx, "HandleMicEvent IncrHoldMicTime err:%v", err)
			}
		}
		log.InfoWithCtx(ctx, "HandleMicEvent HandleMicOff %+v, intervalSec:%d", event, intervalSec)

	default:
	}
	return nil
}

// TaskHandler 延时任务处理器
type TaskHandler func(context.Context) error

// ErrTaskEmpty 没用任务
var ErrTaskEmpty = fmt.Errorf("task empty")

func (m *SignAnchorStatsMgr) StartDelayTimer() {
	ctx := context.Background()
	go m.TaskLoop(ctx, m.MicHoldTimerTask, "MicHoldTimerTask")
	go m.MonitorMicMQLen(ctx)
}

// MicHoldTimerTask 麦位保持时间统计
func (m *SignAnchorStatsMgr) MicHoldTimerTask(loopCtx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorWithCtx(loopCtx, "MicHoldTimerTask recover, err:%+v, stack:%s", r, string(debug.Stack()))
		}
	}()
	now := time.Now()
	taskList, err := m.RedisCache.ProcessDelayQueue(loopCtx)
	if err != nil {
		log.ErrorWithCtx(loopCtx, "MicHoldTimerTask ProcessDelayQueue err:%v", err)
		return err
	}
	if len(taskList) == 0 {
		return ErrTaskEmpty
	}

	for _, task := range taskList {
		uid, cid, gid, _, taskType, intervalSec := task.Uid, task.Cid, task.Gid, task.LastHoldMicTs, task.MsgType, task.IntervalSec
		log.InfoWithCtx(loopCtx, "MicHoldTimerTask task:%+v, intervalSec:%d, taskType:%s", task, intervalSec, taskType)

		err = m.IncrHoldMicTime(loopCtx, uid, gid, gid, cid, intervalSec, now)
		if err != nil {
			log.ErrorWithCtx(loopCtx, "MicHoldTimerTask IncrHoldMicTime err:%v, cost:%d", err, time.Now().Sub(now).Milliseconds())
			continue
		}
	}

	return nil
}

// TaskLoop 延时任务处理循环
func (m *SignAnchorStatsMgr) TaskLoop(ctx context.Context, taskHandler TaskHandler, taskName string) {
	log.InfoWithCtx(ctx, "TaskLoop %s start", taskName)
	continueEmptyCount := 0
	for {
		select {
		case <-ctx.Done():
			log.InfoWithCtx(ctx, "TaskLoop %s done", taskName)
			return
		case <-m.shutDown:
			log.InfoWithCtx(ctx, "TaskLoop %s shutdown", taskName)
			return
		default:
			func() {
				ctx, cancel := context.WithTimeout(ctx, time.Second*30)
				defer cancel()
				err := taskHandler(ctx)
				if err != nil {
					if errors.Is(err, ErrTaskEmpty) {
						continueEmptyCount++
						if continueEmptyCount >= 3 {
							continueEmptyCount = 0
							time.Sleep(3 * time.Second)
						}
						return
					}
					log.ErrorWithCtx(ctx, "TaskLoop, err:%+v, %s", err, taskName)
					time.Sleep(time.Second)
				}
			}()
		}
	}
}

// MonitorMicMQLen 监控麦位延时队列长度
func (m *SignAnchorStatsMgr) MonitorMicMQLen(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			log.InfoWithCtx(ctx, "MonitorMicMQLen done")
			return
		case <-m.shutDown:
			log.InfoWithCtx(ctx, "MonitorMicMQLen shutdown")
			return
		default:
			// 监控麦位延时队列长度
			micMQLen, err := m.RedisCache.GetMicDelayMQLen(ctx)
			if err != nil {
				log.ErrorWithCtx(ctx, "MonitorMicMQLen GetMicDelayMQLen err:%v", err)
				continue
			}
			log.InfoWithCtx(ctx, "MonitorMicMQLen micMQLen:%d", micMQLen)

			// 清理超时异常任务
			m.RedisCache.RemoveTimeoutDelayItems(ctx)
			time.Sleep(time.Minute)
		}
	}
}
