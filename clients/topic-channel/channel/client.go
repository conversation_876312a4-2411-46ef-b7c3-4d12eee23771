package channel

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/protocol/services/topic_channel/channel"
	"google.golang.org/grpc"
)

const (
	serviceName = "topic-channel"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return channel.NewChannelClient(cc)
		}, dopts...),
	}, nil
}

func (c *Client) typedStub() channel.ChannelClient {
	return c.Stub().(channel.ChannelClient)
}

func (c *Client) AddChannel(ctx context.Context, in *channel.AddChannelReq) (*channel.AddChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().AddChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateChannelInfo(ctx context.Context, in *channel.UpdateChannelInfoReq) (*channel.UpdateChannelInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateChannelInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DismissChannel(ctx context.Context, in *channel.DismissChannelReq) (*channel.DismissChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().DismissChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecommendChannelList(ctx context.Context, in *channel.GetRecommendChannelListReq) (*channel.GetRecommendChannelListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendChannelList(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecommendChannelListByTab(ctx context.Context, in *channel.GetRecommendChannelListByTabReq) (*channel.GetRecommendChannelListByTabResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendChannelListByTab(ctx, in)
	return resp, protocol.ToServerError(err)
}

//returnAll传true，返回所有状态的房间信息。Types字段有传，会根据Types字段过滤。Types字段传空，且returnAll字段不传，只会返回发布中的房间信息。
func (c *Client) GetChannelByIds(ctx context.Context, in *channel.GetChannelByIdsReq) (*channel.GetChannelByIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelByIds(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) KeepChannelAlive(ctx context.Context, in *channel.KeepChannelAliveReq) (*channel.KeepChannelAliveResp, protocol.ServerError) {
	resp, err := c.typedStub().KeepChannelAlive(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DisappearChannel(ctx context.Context, in *channel.DisappearChannelReq) (*channel.DisappearChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().DisappearChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetOnlineInfo(ctx context.Context, in *channel.GetOnlineInfoReq) (*channel.GetOnlineInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetOnlineInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DismissTab(ctx context.Context, in *channel.DismissTabReq) (*channel.DismissTabResp, protocol.ServerError) {
	resp, err := c.typedStub().DismissTab(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) FreezeChannel(ctx context.Context, in *channel.FreezeChannelReq) (*channel.FreezeChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().FreezeChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UnfreezeChannel(ctx context.Context, in *channel.UnfreezeChannelReq) (*channel.UnfreezeChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().UnfreezeChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelFreezeInfo(ctx context.Context, in *channel.GetChannelFreezeInfoReq) (*channel.GetChannelFreezeInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelFreezeInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelPlayModel(ctx context.Context, in *channel.GetChannelPlayModelReq) (*channel.GetChannelPlayModelResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelPlayModel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchChannelTabMq(ctx context.Context, in *channel.SwitchChannelTabMqReq) (*channel.SwitchChannelTabMqResp, protocol.ServerError) {
	resp, err := c.typedStub().SwitchChannelTabMq(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelRoomUserNumber(ctx context.Context, in *channel.GetChannelRoomUserNumberReq) (*channel.GetChannelRoomUserNumberResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelRoomUserNumber(ctx, in)
	return resp, protocol.ToServerError(err)
}

// deprecated 开黑调用gangup/GetGangupExtraHistory
func (c *Client) GetExtraHistory(ctx context.Context, in *channel.GetExtraHistoryReq) (*channel.GetExtraHistoryResp, protocol.ServerError) {
	resp, err := c.typedStub().GetExtraHistory(ctx, in)
	return resp, protocol.ToServerError(err)
}

// deprecated 开黑调用gangup/SetGangupExtraHistory
func (c *Client) SetExtraHistory(ctx context.Context, in *channel.SetExtraHistoryReq) (*channel.SetExtraHistoryResp, protocol.ServerError) {
	resp, err := c.typedStub().SetExtraHistory(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddTemporaryChannel(ctx context.Context, in *channel.AddTemporaryChannelReq) (*channel.AddTemporaryChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().AddTemporaryChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchChannelTab(ctx context.Context, in *channel.SwitchChannelTabReq) (*channel.SwitchChannelTabResp, protocol.ServerError) {
	resp, err := c.typedStub().SwitchChannelTab(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SwitchChannelTabAndBc(ctx context.Context, in *channel.SwitchChannelTabReq) (*channel.SwitchChannelTabResp, protocol.ServerError) {
	resp, err := c.typedStub().SwitchChannelTabAndBC(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateTopicChannelInfo(ctx context.Context, in *channel.UpdateTopicChannelInfoReq) (*channel.UpdateTopicChannelInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateTopicChannelInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateLastEnterRoomTimeByUid(ctx context.Context, in *channel.UpdateLastEnterRoomTimeByUidReq) (*channel.UpdateLastEnterRoomTimeByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateLastEnterRoomTimeByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetLastEnterRoomTimeByUid(ctx context.Context, in *channel.GetLastEnterRoomTimeByUidReq) (*channel.GetLastEnterRoomTimeByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetLastEnterRoomTimeByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdateLastEnterRoomTabIdByUid(ctx context.Context, in *channel.UpdateLastEnterRoomTabIdByUidReq) (*channel.UpdateLastEnterRoomTabIdByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdateLastEnterRoomTabIdByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetLastEnterRoomTabIdByUid(ctx context.Context, in *channel.GetLastEnterRoomTabIdByUidReq) (*channel.GetLastEnterRoomTabIdByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().GetLastEnterRoomTabIdByUid(ctx, in)
	return resp, protocol.ToServerError(err)
}
