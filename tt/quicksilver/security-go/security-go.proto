syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/security-go";
package security_go;

import "tt/quicksilver/extension/options/options.proto";


//签约身份
// buf:lint:ignore ENUM_PASCAL_CASE
enum SIGNING_IDENTITY {
  // 未知
  SIGN_ANCHOR_INDENTITY_UNSPECIFIED = 0;
  //成员
  SIGN_ANCHOR_INDENTITY_MULTIPLAYER = 1;
  //主播
  SIGN_ANCHOR_INDENTITY_RADIO_LIVE= 2;
  //主播+成员
  SIGN_ANCHOR_INDENTITY_MULTIPLAYER_AND_RADIO_LIVE= 3;
}
// 注销白名单用户信息
message UnregWhiteUserInfo {
  // ttid
  string ttid = 1;
  // uid
  uint32 uid = 2;
  // 签约身份
  SIGNING_IDENTITY signing_identity = 3;
  // 总积分
  int64 finally_score = 4;
  // 绑定手机号
  string bind_phone = 5;
  // 设备型号
  string device_model = 6;
  // 客户端类型
  uint32 client_type = 7;
  // 备注
  string remark = 8;
  // 操作时间
  int64 operation_time = 9;
  // 操作人
  string operator_name = 10;
}
// 获取注销白名单用户信息请求
message GetUnregWhiteUserInfoReq {
  // uid列表
  repeated uint32 uid_list = 1;
  // 偏移量
  uint32 offset = 2;
  // 限制数量
  uint32 limit = 3;
  // 开始时间
  int64 begin_time = 4;
  // 结束时间
  int64 end_time = 5;
}
// 获取注销白名单用户信息响应
message GetUnregWhiteUserInfoResp {
  // 注销白名单用户信息列表
  repeated UnregWhiteUserInfo user_info_list = 1;
  // 返回查询记录总数
  uint32 total_cnt = 2;
}

// 添加注销白名单用户请求
message AddUserUnregWhiteReq {
  // 需要添加白名单用户uid列表
  repeated UnregWhiteUserInfo white_user_list = 1;
}
// 添加注销白名单用户响应
message AddUserUnregWhiteResp {
}
// 检查用户是否在注销白名单请求
message CheckUnregWhiteUserReq {
  // uid
  uint32 uid = 1;
}
// 检查用户是否在注销白名单响应
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message CheckUnregWhiteUserResp {
  // 是否在白名单
  bool isWhite = 1;
}

// 验证token
message AccessToken {
  // token
  string token = 1;
}

//安全问题: 预置问题、用户问题
message SecurityQuestion {
  // id
  uint32 id = 1;
  // 问题
  string question = 2;
}

// 安全信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SecuritySummary {
  // uid
  uint32 uid = 1;
  // 安全等级
  uint32 level = 2;
  // 手机号
  string phone = 3;
  // 安全问题
  repeated SecurityQuestion questions = 4;
  // ThirdParty_Attached
  uint32 thirdParty_attached = 5;
  //是否设置过密码
  bool password_set = 6;
  // 手机类型
  PhoneType phone_type = 7;
}

// 手机类型
enum PhoneType {
  // 未知
  UNKNOWN_PHONE = 0;
  // 登录手机
  LOGIN_PHONE = 1;
  // 安全手机
  SECURE_PHONE = 2;
}

//安全级别
// buf:lint:ignore ENUM_PASCAL_CASE
enum Security_Level {
  // 未知
  SECURITY_LEVEL_UNKNOWN = 0;
  // 低
  SECURITY_LEVEL_LOW = 1;
  // 中
  SECURITY_LEVEL_MID = 2;
  // 高
  SECURITY_LEVEL_HIGH = 3;
}

// session
// buf:lint:ignore ENUM_PASCAL_CASE
enum Session_Usage {
  // 未知
  SESSION_USAGE_UNKNOWN = 0;
  // 绑定手机
  SESSION_USAGE_PHONE_BIND = 1;
  // 解绑手机
  SESSION_USAGE_PHONE_UNBIND = 2;
  // 设置问题
  SESSION_USAGE_QUESTION_SET = 3;
  // 修改密码
  SESSION_USAGE_PASSWORD_UPDATE = 4;
  // 更换手机
  SESSION_USAGE_PHONE_REBIND = 5;
  // 解绑第三方
  SESSION_USAGE_DETACH_THIRD_PARTY = 6;
}

// 操作类型
// buf:lint:ignore ENUM_PASCAL_CASE
enum OP_TYPE {
  // 无
  OP_NIL = 0;
  // 绑定手机
  OP_BIND_PHONE = 0x0001;
  // 解绑手机
  OP_UNBIND_PHONE = 0x0002;
  // 更换手机
  OP_REBIND_PHONE = 0x0004;
  // 修改密码
  OP_UPDATE_PASSWD = 0x0008;
  // 设置问题
  OP_SET_QUESTION = 0x0010;
  // 解绑第三方
  OP_DETACH_THIRDPARTY = 0x0020;
}

// session信息
message SessionModel {
  // uid
  uint32 uid = 1;
  // Session_Usage
  uint32 usage = 2;
  // 创建时间
  uint32 create_time = 3;
  // salt
  string salt = 4;
  // 验证手机号
  string validated_phone = 5;
}

// session信息
message SessionInfo {
  // uid
  uint32 uid = 1;
  // Session_Usage
  uint32 usage = 2;
  // 验证手机号
  string validated_phone = 3;
}

// 令牌
message TokenModel {
  // uid
  uint32 uid = 1;
  //过期时间点
  uint32 expired = 2;
}

// 获取token请求
message GetAccessTokenReq {
  // uid
  uint32 uid = 1;
}

// 获取token响应
message GetAccessTokenRsp {
  // token
  AccessToken access_token = 1;
}

// 验证token请求
message ValidateAccessTokenReq {
  // token
  AccessToken access_token = 1;
}

// 验证token响应
message ValidateAccessTokenRsp {
  // uid
  uint32 uid = 1;
}

// 获取session请求
message GetSessionReq {
  // uid
  uint32 uid = 1;
  // SessionInfo
  SessionInfo session = 2;
}

// 获取session响应
message GetSessionRsp {
  // session_id
  string session_id = 1;
}

// 验证session
message ValidateSessionReq {
  // session_id
  string session_id = 1;
}

// 验证session
message ValidateSessionRsp {
  // session信息
  SessionInfo session = 1;
}

// 解绑手机号请求
message UnbindPhoneReq {
  // uid
  uint32 uid = 1;
  // 验证令牌
  AccessToken access_token = 2;
  // 会话Id
  string session_id = 3;
  // 是否有带上summary
  bool without_summary = 4;
  // 来源
  int32 source = 5;
  // 场景
  string scene = 6;
}

// 解绑手机号响应
message UnbindPhoneRsp {
  // 安全信息
  SecuritySummary summary = 1;
}

// 解绑第三方响应
message DetachThirdPartyRsp {
  // 安全信息
  SecuritySummary summary = 1;
}

// 解绑第三方请求
message DetachThirdPartyReq {
  // uid
  uint32 uid = 1;
  // 验证令牌
  AccessToken access_token = 2;
  // 会话Id
  string session_id = 3;
  // 是否有带上summary
  bool without_summary = 4;
  // 来源
  int32 source = 5;
}

// 更新密码
message UpdatePasswordReq {
  // uid
  uint32 uid = 1;
  // 新密码
  string new_passwd = 2;
  // token
  AccessToken access_token = 3;
  // session_id
  string session_id = 4;
  // 是否有带上summary
  bool without_summary = 5;
  // source
  int32 source = 6;  //
}

// 更新密码响应
message UpdatePasswordRsp {
  // 安全信息
  SecuritySummary summary = 1;
}

// 获取注销申请审核状态请求
message GetUnregApplyAuditStatusReq {
  // uid
  uint32 uid = 1;
}

// 获取注销申请审核状态响应
message GetUnregApplyAuditStatusRsp {
  // 状态
  uint32 status = 1;
}

// 排序类型
enum SortType {
  // 未指定
  SORT_UNSPECIFIED = 0;
  // 升序
  SORT_ASC = 1;
  // 降序
  SORT_DESC = 2;
}

// 获取注销申请审核记录请求
message GetUnregApplyAuditRecordReq {
  // uid
  uint32 uid = 1;
  // 状态
  uint32 status = 2;
  // 偏移量
  uint32 offset = 3;
  // 偏移量申请id
  uint32 offset_apply_id = 4;
  // 限制数量
  uint32 limit = 5;
  // 开始时间
  uint32 begin_time = 6;
  // 结束时间
  uint32 end_time = 7;
  // 排序类型
  uint32 sort_type = 8;
}

// 注销申请审核信息
message UnregApplyAuditInfo {
  // 申请id
  uint32 apply_id = 1;
  // uid
  uint32 uid = 2;
  // 状态
  uint32 status = 3;
  // 提交时间
  uint32 apply_at = 4;
  // 运营操作人
  string operator_id = 5;
  // 运营操作时间
  uint32 op_at = 6;
}

// 获取注销申请审核记录响应
message GetUnregApplyAuditRecordRsp {
  // 注销申请审核信息列表
  repeated UnregApplyAuditInfo info_list = 1;
  // 总数
  uint32 total = 2;
}

// 更新自动注销申请状态请求
message UpdateAutoProcUnregApplyStatusReq {
  // uid
  uint32 uid = 1;
  // 状态
  uint32 status = 2;
  // 申请id
  uint32 apply_id = 3;
  // ttid
  string ttid = 4;
  // 取消时间
  uint32 cancel_time = 5 ;
  // 市场id
  uint32 market_id = 6;
}

// 更新自动注销申请状态响应
message UpdateAutoProcUnregApplyStatusRsp{
}

// 获取自动注销记录请求
message GetAutoProcUnregRecordReq {
  // 状态
  uint32 status = 1;
  // uid
  uint32 uid = 2;
  // ttid
  string ttid = 3;
  // 排序 true 为排序由时间由近到远
  bool   sort = 4;
  // 偏移量
  uint32 offset = 5;
  // 限制数量
  uint32 limit = 6;
}

// 自动注销信息
message AutoProcUnregInfo {
  // id
  uint32 id = 1;
  // uid
  uint32 uid = 2;
  // ttid
  string ttid = 3;
  // 审核状态
  uint32 status = 4;
  // 提交时间
  uint32 apply_at = 5;
  // 剩余等待时间,秒
  uint32 remain_wait = 6;
  // 是否已回访
  bool   visit = 7;
  // 注销原因
  string unreg_reason = 8 ;
  // market_id
  uint32 market_id = 9;
}

// 获取自动注销记录响应
message GetAutoProcUnregRecordRsp {
  // 自动注销信息列表
  repeated AutoProcUnregInfo info_list = 1;
  // 总数
  uint32 total = 2;
}

//注销回访
message UpdateUnregVisitReq{
  // id
  uint32 id =1;
  // 是否已回访
  bool visit =2 ;
  // 注销原因
  string unreg_reason = 3;
}

// 注销回访响应
message UpdateUnregVisitRsp {
}

// 登录事件处理请求
message TTAuthEventProcessorReq {
  // 数据
  bytes data = 1;
}

// 登录事件处理响应
message TTAuthEventProcessorResp {
  // 是否成功
  bool is_success = 1;
}

// 自动注销处理请求
message AutoUnregProcessorReq {
  // id
  uint32 id = 1;
  // uid
  uint32 uid = 2;
  // 注销状态
  uint32 status = 3;
  // 马甲包id
  uint32 market_id = 4;
}

// 自动注销处理
message  AutoUnregProcessorResp {
  // 是否成功
  bool is_success = 1;
}

service SecurityGo {
  option (service.options.service_ext) = {
    service_name: "security-go"
  };
  // 获取注销白名单用户信息
  rpc GetUnregWhiteUserInfo(GetUnregWhiteUserInfoReq)
      returns (GetUnregWhiteUserInfoResp) {
  }
  // 添加注销白名单用户
  rpc AddUserUnregWhite(AddUserUnregWhiteReq)
      returns (AddUserUnregWhiteResp){
  }
  // 检查用户是否在注销白名单
  rpc CheckUnregWhiteUser (CheckUnregWhiteUserReq)
      returns (CheckUnregWhiteUserResp) {
  }
  // 验证token
  rpc ValidateAccessToken(ValidateAccessTokenReq)
      returns (ValidateAccessTokenRsp) {
  }
  // 验证session
  rpc ValidateSession(ValidateSessionReq)
      returns (ValidateSessionRsp) {
  }
  //修改密码
  rpc UpdatePassword(UpdatePasswordReq)
      returns (UpdatePasswordRsp) {
  }
  // 获取注销申请审核状态
  rpc GetUnregApplyAuditStatus(GetUnregApplyAuditStatusReq)
      returns (GetUnregApplyAuditStatusRsp) {
  }
  // 获取注销申请审核记录
  rpc GetUnregApplyAuditRecord(GetUnregApplyAuditRecordReq)
      returns (GetUnregApplyAuditRecordRsp) {
  }
  // 更新自动注销申请状态
  rpc UpdateAutoProcUnregApplyStatus(UpdateAutoProcUnregApplyStatusReq)
      returns (UpdateAutoProcUnregApplyStatusRsp) {
  }
  // 获取自动注销记录
  rpc GetAutoProcUnregRecord(GetAutoProcUnregRecordReq)
      returns (GetAutoProcUnregRecordRsp) {
  }
  // 注销回访
  rpc UpdateUnregVisit(UpdateUnregVisitReq)
      returns (UpdateUnregVisitRsp) {
  }

  // 登录事件处理
  rpc TTAuthEventProcessor(TTAuthEventProcessorReq)
      returns (TTAuthEventProcessorResp) {
  }

  // 自动注销处理
  rpc AutoUnregProcessor(AutoUnregProcessorReq)
      returns (AutoUnregProcessorResp) {
  }

}


