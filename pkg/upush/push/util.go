package push

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
)

func toJson(data interface{}) (string, error) {
	bs, err := json.<PERSON>(data)
	if err != nil {
		return "", err
	}
	return string(bs), nil
}

func getSign(address, body, appMasterSecret string) string {
	var str = fmt.Sprintf("POST%v%v%v", address, body, appMasterSecret)
	hash := md5.New()
	hash.Write([]byte(str))
	return fmt.Sprintf("%x", hash.Sum(nil))
}
