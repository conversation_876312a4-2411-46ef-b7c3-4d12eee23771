// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/cybros/arbiter/v2 (interfaces: AudioClient)

// Package image is a generated GoMock package.
package image

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cybros_arbiter_v2 "golang.52tt.com/protocol/services/cybros/arbiter/v2"
	grpc "google.golang.org/grpc"
)

// MockAudioClient is a mock of AudioClient interface.
type MockAudioClient struct {
	ctrl     *gomock.Controller
	recorder *MockAudioClientMockRecorder
}

// MockAudioClientMockRecorder is the mock recorder for MockAudioClient.
type MockAudioClientMockRecorder struct {
	mock *MockAudioClient
}

// NewMockAudioClient creates a new mock instance.
func NewMockAudioClient(ctrl *gomock.Controller) *MockAudioClient {
	mock := &MockAudioClient{ctrl: ctrl}
	mock.recorder = &MockAudioClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAudioClient) EXPECT() *MockAudioClientMockRecorder {
	return m.recorder
}

// AsyncScanAudio mocks base method.
func (m *MockAudioClient) AsyncScanAudio(arg0 context.Context, arg1 *cybros_arbiter_v2.ScanAudioReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.ScanAudioResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AsyncScanAudio", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.ScanAudioResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AsyncScanAudio indicates an expected call of AsyncScanAudio.
func (mr *MockAudioClientMockRecorder) AsyncScanAudio(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AsyncScanAudio", reflect.TypeOf((*MockAudioClient)(nil).AsyncScanAudio), varargs...)
}

// CancelScanTask mocks base method.
func (m *MockAudioClient) CancelScanTask(arg0 context.Context, arg1 *cybros_arbiter_v2.CancelScanTaskReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.CancelScanTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelScanTask", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.CancelScanTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelScanTask indicates an expected call of CancelScanTask.
func (mr *MockAudioClientMockRecorder) CancelScanTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelScanTask", reflect.TypeOf((*MockAudioClient)(nil).CancelScanTask), varargs...)
}

// QueryAudioTaskResult mocks base method.
func (m *MockAudioClient) QueryAudioTaskResult(arg0 context.Context, arg1 *cybros_arbiter_v2.QueryAudioTaskResultReq, arg2 ...grpc.CallOption) (*cybros_arbiter_v2.QueryAudioTaskResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryAudioTaskResult", varargs...)
	ret0, _ := ret[0].(*cybros_arbiter_v2.QueryAudioTaskResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAudioTaskResult indicates an expected call of QueryAudioTaskResult.
func (mr *MockAudioClientMockRecorder) QueryAudioTaskResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAudioTaskResult", reflect.TypeOf((*MockAudioClient)(nil).QueryAudioTaskResult), varargs...)
}
