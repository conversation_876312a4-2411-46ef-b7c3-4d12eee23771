package stages

import (
    "context"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/protocol/common/status"
    "google.golang.org/grpc/codes"
)

var stageChangeNotAllow = protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid,"不允许切换到该阶段")

type IStage interface {
    // GetStage 获取阶段枚举值
    GetStage() uint32

    // CheckBeforeChange 检查能否切换到本阶段
    CheckBeforeChange(ctx context.Context, in *CheckBeforeChangeReq) error

    // GetSceneAfterChange 获取切换到本阶段后的场景动画
    GetSceneAfterChange() uint32
}

type CheckBeforeChangeReq struct {
    OriginStage    uint32
    OriginSubStage uint32
    IsFreeTheme    bool
}

type Stages struct {
    stages map[uint32]IStage
}

func NewStages() *Stages {
    st := &Stages{
        stages: make(map[uint32]IStage),
    }

    st.addStage(&FinishStage{})
    st.addStage(&BrideGroomEnterStage{})
    st.addStage(&LoveDeclarationStage{})
    st.addStage(&ExchangeRingStage{})
    st.addStage(&HighlightStage{})
    st.addStage(&GroupPhotoStage{})

    return st
}

func (s *Stages) addStage(stageImpl IStage) {
    stage := stageImpl.GetStage()
    s.stages[stage] = stageImpl
}

func (s *Stages) GetStage(stage uint32) (IStage, error) {
    stageImpl, ok := s.stages[stage]
    if !ok {
        return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid,"stage not exist")
    }

    return stageImpl, nil
}
