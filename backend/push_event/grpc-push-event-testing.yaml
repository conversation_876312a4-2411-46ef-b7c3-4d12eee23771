apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-push-event-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.push_event.PushEvent/
    rewrite:
      uri: /logic.push.PushEvent/
    delegate:
       name: push-logic-delegator-80
       namespace: quicksilver


