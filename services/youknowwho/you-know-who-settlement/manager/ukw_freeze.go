package manager

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/services/youknowwho"
	"golang.52tt.com/services/youknowwho/common/client"
	"golang.52tt.com/services/youknowwho/common/dyconf"
	"golang.52tt.com/services/youknowwho/common/model"
)

// FreezeUkw 冻结神秘人
func (m *Manager) FreezeUkw(ctx context.Context, info *model.YouKnowWhoPermissionTable, daySec uint64) (error, bool) {

	// 判断是否需要扣除时间
	noDeduction, err := m.IsNeed2Settlement(ctx, info)
	if !noDeduction && nil != err {
		log.ErrorWithCtx(ctx, "doSettlement isNeed2Settlement info:%+v err:%v", info, err)
	}

	timeNow := time.Now()
	if noDeduction && !dyconf.SvrConf.GetSvr().TestMode {
		log.InfoWithCtx(ctx, "FreezeUkw no need deduction because %+v", err)
		daySec = 0
	}
	log.DebugWithCtx(ctx, "FreezeUkw IsNeed2Settlement needDeduction:[%+v], !TestMode:[%+v], daySec:[%+v]",
		noDeduction, !dyconf.SvrConf.GetSvr().TestMode, daySec)
	var remainingTime uint64
	if info.EffectiveTime > daySec {
		remainingTime = info.EffectiveTime - daySec
	} else {
		remainingTime = 0
	}
	permissionInfo := &model.YouKnowWhoPermissionTable{
		Uid:           info.Uid,
		Switch:        model.UKW_SWITCH_OFF,
		Status:        model.UKW_FREEZE,
		RankSwitch:    model.UKW_RANK_SWITCH_OFF,
		EffectiveTime: info.EffectiveTime, // 不做时间处理，由里面的表语句执行
		TotalTime:     info.TotalTime,
		OpenTime:      info.OpenTime,
		CreateTime:    info.CreateTime,
		UpdateTime:    timeNow,
		DelFlag:       model.TABLE_DELETE_FLAG_NO_DELETE,
	}
	flowInfo := &model.YouKnowWhoFlowTable{
		Uid:           info.Uid,
		OldStatus:     model.UKW_OPEN,
		NewStatus:     model.UKW_FREEZE,
		OldRankSwitch: info.RankSwitch,
		NewRankSwitch: model.UKW_RANK_SWITCH_OFF,
		TimeLeft:      info.EffectiveTime,
		EffectiveTime: remainingTime, // 冻结时需要扣除当天的时间
		StartTime:     info.OpenTime,
		ChangeReason:  model.UKW_CHANGE_FREEZE,
		CreateTime:    timeNow,
		UpdateTime:    timeNow,
		DelFlag:       model.TABLE_DELETE_FLAG_NO_DELETE,
	}

	// 如果剩余时间为0，流水表直接变为关闭
	if remainingTime == 0 {
		flowInfo.NewStatus = model.UKW_NO_OPEN
		flowInfo.ChangeReason = model.UKW_CHANGE_CLOSE
	}

	// 执行冻结操作
	err = m.db.FreezeUKW(ctx, permissionInfo, flowInfo, daySec)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeUkw SQL failed, err:[%+v]", err)
		return err, false
	}

	var bylinkType string

	// 如果不是已经到期，发送冻结消息，如果已经到期，直接关闭，发送关闭消息
	if remainingTime != 0 {
		// 发送冻结消息
		freezeInfo := fmt.Sprintf(dyconf.MsgConf.Get().FreezeInfo, m.getRemainingDays(remainingTime))
		msg := fmt.Sprintf("%+v %+v", freezeInfo, dyconf.MsgConf.Get().FreezeRedirectInfo)
		url, _ := client.GetUserWebUrl(ctx, permissionInfo.Uid, client.WebUrlNobility)
		if url != "" {
			hlight := dyconf.MsgConf.Get().FreezeRedirectInfo
			err = client.SendHelpMsg(ctx, info.Uid, msg, hlight, url)
			if err != nil {
				log.ErrorWithCtx(ctx, "FreezeUkw SendHelpMsg failed, err:[%+v]", err)
			}
		}

		// kafka推送冻结消息
		go m.SendKafkaInfoForPermissionChange(protogrpc.NewContextWithInfo(ctx), info, permissionInfo, pb.UKWMsgType_ENUM_FREEZE_UKW_TYPE)

		bylinkType = BYLINK_UKW_SWITCH_CHANGE_FREEZE
	} else {
		// 发送到期消息
		msg := "您购买的神秘人套餐已过期，若想继续使用，快去开通吧！前去购买>"
		hlight := "前去购买>"
		url, _ := client.GetUserWebUrl(ctx, permissionInfo.Uid, client.WebUrlBuyUKW)
		if url != "" {
			_ = client.SendHelpMsg(ctx, info.Uid, msg, hlight, url)
		}

		// kafka推送关闭消息
		go m.SendKafkaInfoForPermissionChange(protogrpc.NewContextWithInfo(ctx), info, permissionInfo, pb.UKWMsgType_ENUM_CLOSE_UKW_TYPE)
		bylinkType = BYLINK_UKW_SWITCH_CHANGE_EXPIRED
	}

	// 神秘人房间推送
	err = m.PushUKWPermissionChannelBroadcastMsg(ctx, info.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeUkw PushUKWPermissionChannelBroadcastMsg failed, uid:[%+v], info:[%+v]",
			info.Uid, info)
	}
	// 推送终端个人推送
	err = m.PushPersonalPermissionChangeMsg(ctx, info.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeUkw PushPersonalPermissionChangeMsg failed, uid:[%+v], err:[%+v]",
			info.Uid, err)
	}

	// 百灵上报
	if info.Switch == model.UKW_SWITCH_ON {
		go m.SendUKWSwitchBylinkMsg(protogrpc.NewContextWithInfo(ctx), info.Uid, bylinkType)
	}

	return nil, true
}

// UnFreezeUkw 解冻神秘人
func (m *Manager) UnFreezeUkw(ctx context.Context, info *model.YouKnowWhoPermissionTable) (error, bool) {

	subCtx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	// 获取用户在线平台信息
	onlineInfo, errSvr := client.UserOlCli.GetLastMobileOnlineInfo(subCtx, info.Uid)
	if errSvr != nil {
		log.ErrorWithCtx(subCtx, "unFreezeUkw GetLastMultiOnlineInfo failed uid:%d err:%v", info.Uid, errSvr.Error())
		return errSvr, false
	}

	// 判断是否需要开启进房提醒
	var enterNotice bool
	if onlineInfo.GetUid() == info.Uid {
		clientType, _, _ := protocol.UnPackTerminalType(onlineInfo.GetTerminalType())
		clientVersion := onlineInfo.GetClientVersion()
		// 如果是新版本，则关闭神秘人开关
		enterNotice = m.GetEnterNotice(ctx, info.Uid, uint32(clientType), clientVersion)
	} else { // 如果没查到直接关闭
		enterNotice = model.UKW_SWITCH_OFF
	}

	timeNow := time.Now()
	permissionInfo := &model.YouKnowWhoPermissionTable{
		Uid:           info.Uid,
		Switch:        model.UKW_SWITCH_OFF,
		Status:        model.UKW_OPEN,
		RankSwitch:    model.UKW_RANK_SWITCH_OFF,
		EnterNotice:   enterNotice,
		EffectiveTime: info.EffectiveTime, // 解冻时当天时间不进行扣费计算
		TotalTime:     info.TotalTime,
		OpenTime:      timeNow,
		CreateTime:    info.CreateTime,
		UpdateTime:    timeNow,
		DelFlag:       model.TABLE_DELETE_FLAG_NO_DELETE,
	}
	flowInfo := &model.YouKnowWhoFlowTable{
		Uid:           info.Uid,
		OldStatus:     model.UKW_FREEZE,
		NewStatus:     model.UKW_OPEN,
		OldRankSwitch: model.UKW_RANK_SWITCH_OFF,
		NewRankSwitch: model.UKW_RANK_SWITCH_OFF,
		TimeLeft:      info.EffectiveTime,
		EffectiveTime: info.EffectiveTime, // 解冻时当天时间不进行扣费计算
		StartTime:     timeNow,
		ChangeReason:  model.UKW_CHANGE_UNFREEZE,
		CreateTime:    timeNow,
		UpdateTime:    timeNow,
		DelFlag:       model.TABLE_DELETE_FLAG_NO_DELETE,
	}

	// 执行解冻操作
	err := m.db.UnFreezeUKW(ctx, permissionInfo, flowInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnFreezeUkw SQL failed, err:[%+v]", err)
		return err, false
	}

	// 发送解冻消息
	unfreezeInfo := fmt.Sprintf(dyconf.MsgConf.Get().UnfreezeInfo, m.getRemainingDays(info.EffectiveTime))
	msg := fmt.Sprintf("%+v %+v", unfreezeInfo, dyconf.MsgConf.Get().UnfreezeRedirectInfo)
	hlight, url := dyconf.MsgConf.Get().UnfreezeRedirectInfo, dyconf.UrlConf.Get().PrivacyAndSafetyUrl
	err = client.SendHelpMsg(ctx, info.Uid, msg, hlight, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnFreezeUkw SendHelpMsg failed, err:[%+v]", err)
	}

	// kafka推送解冻消息
	go m.SendKafkaInfoForPermissionChange(protogrpc.NewContextWithInfo(ctx), info, permissionInfo, pb.UKWMsgType_ENUM_UNFREEZE_UKW_TYPE)

	// 推送终端个人推送
	err = m.PushPersonalPermissionChangeMsg(ctx, info.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "FreezeUkw PushPersonalPermissionChangeMsg failed, uid:[%+v], err:[%+v]",
			info.Uid, err)
	}

	return nil, true
}

// getRemainingDays 根据剩余时间字段获取剩余天数
func (m *Manager) getRemainingDays(time uint64) uint64 {
	return time / (3600 * 24) // 转化成天
}
