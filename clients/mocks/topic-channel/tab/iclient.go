// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/TTProjects/quicksilver/clients/topic-channel/tab/iclient.go

// Package tab is a generated GoMock package.
package tab

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	tab "golang.52tt.com/protocol/services/topic_channel/tab"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddCategoryTitle mocks base method.
func (m *MockIClient) AddCategoryTitle(ctx context.Context, in *tab.AddCategoryTitleReq) (*tab.AddCategoryTitleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*tab.AddCategoryTitleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddCategoryTitle indicates an expected call of AddCategoryTitle.
func (mr *MockIClientMockRecorder) AddCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCategoryTitle", reflect.TypeOf((*MockIClient)(nil).AddCategoryTitle), ctx, in)
}

// AddMultilevelTitle mocks base method.
func (m *MockIClient) AddMultilevelTitle(ctx context.Context, in *tab.AddMultilevelTitleReq) (*tab.AddMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*tab.AddMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddMultilevelTitle indicates an expected call of AddMultilevelTitle.
func (mr *MockIClientMockRecorder) AddMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMultilevelTitle", reflect.TypeOf((*MockIClient)(nil).AddMultilevelTitle), ctx, in)
}

// BatchGetBlockRelations mocks base method.
func (m *MockIClient) BatchGetBlockRelations(ctx context.Context, in *tab.BatchGetBlockRelationsReq) (*tab.BatchGetBlockRelationsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBlockRelations", ctx, in)
	ret0, _ := ret[0].(*tab.BatchGetBlockRelationsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetBlockRelations indicates an expected call of BatchGetBlockRelations.
func (mr *MockIClientMockRecorder) BatchGetBlockRelations(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlockRelations", reflect.TypeOf((*MockIClient)(nil).BatchGetBlockRelations), ctx, in)
}

// BatchGetBlocks mocks base method.
func (m *MockIClient) BatchGetBlocks(ctx context.Context, in *tab.BatchGetBlocksReq) (*tab.BatchGetBlocksResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBlocks", ctx, in)
	ret0, _ := ret[0].(*tab.BatchGetBlocksResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetBlocks indicates an expected call of BatchGetBlocks.
func (mr *MockIClientMockRecorder) BatchGetBlocks(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBlocks", reflect.TypeOf((*MockIClient)(nil).BatchGetBlocks), ctx, in)
}

// BatchGetBusinessBlockInfo mocks base method.
func (m *MockIClient) BatchGetBusinessBlockInfo(ctx context.Context, in *tab.BatchGetBusinessBlockInfoReq) (*tab.BatchGetBusinessBlockInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetBusinessBlockInfo", ctx, in)
	ret0, _ := ret[0].(*tab.BatchGetBusinessBlockInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetBusinessBlockInfo indicates an expected call of BatchGetBusinessBlockInfo.
func (mr *MockIClientMockRecorder) BatchGetBusinessBlockInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetBusinessBlockInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetBusinessBlockInfo), ctx, in)
}

// BatchGetGameLabelItems mocks base method.
func (m *MockIClient) BatchGetGameLabelItems(ctx context.Context, in *tab.BatchGetGameLabelItemsReq) (*tab.BatchGetGameLabelItemsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGameLabelItems", ctx, in)
	ret0, _ := ret[0].(*tab.BatchGetGameLabelItemsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGameLabelItems indicates an expected call of BatchGetGameLabelItems.
func (mr *MockIClientMockRecorder) BatchGetGameLabelItems(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGameLabelItems", reflect.TypeOf((*MockIClient)(nil).BatchGetGameLabelItems), ctx, in)
}

// BatchGetOfficialRoomNameConfig mocks base method.
func (m *MockIClient) BatchGetOfficialRoomNameConfig(ctx context.Context, in *tab.BatchGetOfficialRoomNameConfigReq) (*tab.BatchGetOfficialRoomNameConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetOfficialRoomNameConfig", ctx, in)
	ret0, _ := ret[0].(*tab.BatchGetOfficialRoomNameConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetOfficialRoomNameConfig indicates an expected call of BatchGetOfficialRoomNameConfig.
func (mr *MockIClientMockRecorder) BatchGetOfficialRoomNameConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetOfficialRoomNameConfig", reflect.TypeOf((*MockIClient)(nil).BatchGetOfficialRoomNameConfig), ctx, in)
}

// Blocks mocks base method.
func (m *MockIClient) Blocks(ctx context.Context, in *tab.BlocksReq) (*tab.BlocksResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Blocks", ctx, in)
	ret0, _ := ret[0].(*tab.BlocksResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Blocks indicates an expected call of Blocks.
func (mr *MockIClientMockRecorder) Blocks(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Blocks", reflect.TypeOf((*MockIClient)(nil).Blocks), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DeleteCategoryTitle mocks base method.
func (m *MockIClient) DeleteCategoryTitle(ctx context.Context, in *tab.DeleteCategoryTitleReq) (*tab.DeleteCategoryTitleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*tab.DeleteCategoryTitleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DeleteCategoryTitle indicates an expected call of DeleteCategoryTitle.
func (mr *MockIClientMockRecorder) DeleteCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCategoryTitle", reflect.TypeOf((*MockIClient)(nil).DeleteCategoryTitle), ctx, in)
}

// DeleteMultilevelTitle mocks base method.
func (m *MockIClient) DeleteMultilevelTitle(ctx context.Context, in *tab.DeleteMultilevelTitleReq) (*tab.DeleteMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*tab.DeleteMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteMultilevelTitle indicates an expected call of DeleteMultilevelTitle.
func (mr *MockIClientMockRecorder) DeleteMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMultilevelTitle", reflect.TypeOf((*MockIClient)(nil).DeleteMultilevelTitle), ctx, in)
}

// DeleteTab mocks base method.
func (m *MockIClient) DeleteTab(ctx context.Context, id uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTab", ctx, id)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DeleteTab indicates an expected call of DeleteTab.
func (mr *MockIClientMockRecorder) DeleteTab(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTab", reflect.TypeOf((*MockIClient)(nil).DeleteTab), ctx, id)
}

// FiniteTabs mocks base method.
func (m *MockIClient) FiniteTabs(ctx context.Context, in *tab.FiniteTabsReq) (*tab.FiniteTabsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FiniteTabs", ctx, in)
	ret0, _ := ret[0].(*tab.FiniteTabsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// FiniteTabs indicates an expected call of FiniteTabs.
func (mr *MockIClientMockRecorder) FiniteTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiniteTabs", reflect.TypeOf((*MockIClient)(nil).FiniteTabs), ctx, in)
}

// FiniteTabsByTags mocks base method.
func (m *MockIClient) FiniteTabsByTags(ctx context.Context, in *tab.FiniteTabsByTagsReq) (*tab.FiniteTabsByTagsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FiniteTabsByTags", ctx, in)
	ret0, _ := ret[0].(*tab.FiniteTabsByTagsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// FiniteTabsByTags indicates an expected call of FiniteTabsByTags.
func (mr *MockIClientMockRecorder) FiniteTabsByTags(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FiniteTabsByTags", reflect.TypeOf((*MockIClient)(nil).FiniteTabsByTags), ctx, in)
}

// GetAllTabInfoExt mocks base method.
func (m *MockIClient) GetAllTabInfoExt(ctx context.Context, req *tab.GetAllTabInfoExtReq) (*tab.GetAllTabInfoExtResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabInfoExt", ctx, req)
	ret0, _ := ret[0].(*tab.GetAllTabInfoExtResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllTabInfoExt indicates an expected call of GetAllTabInfoExt.
func (mr *MockIClientMockRecorder) GetAllTabInfoExt(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabInfoExt", reflect.TypeOf((*MockIClient)(nil).GetAllTabInfoExt), ctx, req)
}

// GetAllTabOfCategory mocks base method.
func (m *MockIClient) GetAllTabOfCategory(ctx context.Context, in *tab.GetAllTabOfCategoryReq) (*tab.GetAllTabOfCategoryResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTabOfCategory", ctx, in)
	ret0, _ := ret[0].(*tab.GetAllTabOfCategoryResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllTabOfCategory indicates an expected call of GetAllTabOfCategory.
func (mr *MockIClientMockRecorder) GetAllTabOfCategory(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTabOfCategory", reflect.TypeOf((*MockIClient)(nil).GetAllTabOfCategory), ctx, in)
}

// GetCache mocks base method.
func (m *MockIClient) GetCache(ctx context.Context, req *tab.GetCacheReq) (*tab.GetCacheResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCache", ctx, req)
	ret0, _ := ret[0].(*tab.GetCacheResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCache indicates an expected call of GetCache.
func (mr *MockIClientMockRecorder) GetCache(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCache", reflect.TypeOf((*MockIClient)(nil).GetCache), ctx, req)
}

// GetCategoryTitleList mocks base method.
func (m *MockIClient) GetCategoryTitleList(ctx context.Context, in *tab.GetCategoryTitleReq) (*tab.GetCategoryTitleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryTitleList", ctx, in)
	ret0, _ := ret[0].(*tab.GetCategoryTitleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCategoryTitleList indicates an expected call of GetCategoryTitleList.
func (mr *MockIClientMockRecorder) GetCategoryTitleList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryTitleList", reflect.TypeOf((*MockIClient)(nil).GetCategoryTitleList), ctx, in)
}

// GetHomePageHeadConfig mocks base method.
func (m *MockIClient) GetHomePageHeadConfig(ctx context.Context, in *tab.HomePageHeadConfigReq) (*tab.HomePageHeadConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHomePageHeadConfig", ctx, in)
	ret0, _ := ret[0].(*tab.HomePageHeadConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHomePageHeadConfig indicates an expected call of GetHomePageHeadConfig.
func (mr *MockIClientMockRecorder) GetHomePageHeadConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHomePageHeadConfig", reflect.TypeOf((*MockIClient)(nil).GetHomePageHeadConfig), ctx, in)
}

// GetMinorityGameTabs mocks base method.
func (m *MockIClient) GetMinorityGameTabs(ctx context.Context, in *tab.GetMinorityGameTabsReq) (*tab.GetMinorityGameTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinorityGameTabs", ctx, in)
	ret0, _ := ret[0].(*tab.GetMinorityGameTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinorityGameTabs indicates an expected call of GetMinorityGameTabs.
func (mr *MockIClientMockRecorder) GetMinorityGameTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinorityGameTabs", reflect.TypeOf((*MockIClient)(nil).GetMinorityGameTabs), ctx, in)
}

// GetMultilevelTitleForTT mocks base method.
func (m *MockIClient) GetMultilevelTitleForTT(ctx context.Context, in *tab.GetMultilevelTitleForTTReq) (*tab.GetMultilevelTitleForTTResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultilevelTitleForTT", ctx, in)
	ret0, _ := ret[0].(*tab.GetMultilevelTitleForTTResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultilevelTitleForTT indicates an expected call of GetMultilevelTitleForTT.
func (mr *MockIClientMockRecorder) GetMultilevelTitleForTT(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultilevelTitleForTT", reflect.TypeOf((*MockIClient)(nil).GetMultilevelTitleForTT), ctx, in)
}

// GetNewQuickMatchConfig mocks base method.
func (m *MockIClient) GetNewQuickMatchConfig(ctx context.Context, in *tab.GetNewQuickMatchConfigReq) (*tab.GetNewQuickMatchConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewQuickMatchConfig", ctx, in)
	ret0, _ := ret[0].(*tab.GetNewQuickMatchConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetNewQuickMatchConfig indicates an expected call of GetNewQuickMatchConfig.
func (mr *MockIClientMockRecorder) GetNewQuickMatchConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewQuickMatchConfig", reflect.TypeOf((*MockIClient)(nil).GetNewQuickMatchConfig), ctx, in)
}

// GetQuickMatchConfig mocks base method.
func (m *MockIClient) GetQuickMatchConfig(ctx context.Context, req *tab.GetQuickMatchConfigReq) (*tab.GetQuickMatchConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickMatchConfig", ctx, req)
	ret0, _ := ret[0].(*tab.GetQuickMatchConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetQuickMatchConfig indicates an expected call of GetQuickMatchConfig.
func (mr *MockIClientMockRecorder) GetQuickMatchConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchConfig", reflect.TypeOf((*MockIClient)(nil).GetQuickMatchConfig), ctx, req)
}

// GetRelationOfEtChannel mocks base method.
func (m *MockIClient) GetRelationOfEtChannel(ctx context.Context) (*tab.GetRelationOfEtChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRelationOfEtChannel", ctx)
	ret0, _ := ret[0].(*tab.GetRelationOfEtChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRelationOfEtChannel indicates an expected call of GetRelationOfEtChannel.
func (mr *MockIClientMockRecorder) GetRelationOfEtChannel(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationOfEtChannel", reflect.TypeOf((*MockIClient)(nil).GetRelationOfEtChannel), ctx)
}

// GetRoomNameConfigure mocks base method.
func (m *MockIClient) GetRoomNameConfigure(ctx context.Context, in *tab.GetRoomNameConfigureReq) (*tab.GetRoomNameConfigureResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoomNameConfigure", ctx, in)
	ret0, _ := ret[0].(*tab.GetRoomNameConfigureResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRoomNameConfigure indicates an expected call of GetRoomNameConfigure.
func (mr *MockIClientMockRecorder) GetRoomNameConfigure(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoomNameConfigure", reflect.TypeOf((*MockIClient)(nil).GetRoomNameConfigure), ctx, in)
}

// GetSimpleBanner mocks base method.
func (m *MockIClient) GetSimpleBanner(ctx context.Context, in *tab.GetSimpleBannerReq) (*tab.GetSimpleBannerResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSimpleBanner", ctx, in)
	ret0, _ := ret[0].(*tab.GetSimpleBannerResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSimpleBanner indicates an expected call of GetSimpleBanner.
func (mr *MockIClientMockRecorder) GetSimpleBanner(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleBanner", reflect.TypeOf((*MockIClient)(nil).GetSimpleBanner), ctx, in)
}

// GetTabById mocks base method.
func (m *MockIClient) GetTabById(ctx context.Context, tabId uint32) (*tab.Tab, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabById", ctx, tabId)
	ret0, _ := ret[0].(*tab.Tab)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTabById indicates an expected call of GetTabById.
func (mr *MockIClientMockRecorder) GetTabById(ctx, tabId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabById", reflect.TypeOf((*MockIClient)(nil).GetTabById), ctx, tabId)
}

// GetTabByName mocks base method.
func (m *MockIClient) GetTabByName(ctx context.Context, in *tab.GetTabByNameReq) (*tab.GetTabByNameResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabByName", ctx, in)
	ret0, _ := ret[0].(*tab.GetTabByNameResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTabByName indicates an expected call of GetTabByName.
func (mr *MockIClientMockRecorder) GetTabByName(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByName", reflect.TypeOf((*MockIClient)(nil).GetTabByName), ctx, in)
}

// GetTabByUGameId mocks base method.
func (m *MockIClient) GetTabByUGameId(ctx context.Context, in *tab.GetTabByUGameIdReq) (*tab.GetTabByUGameIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabByUGameId", ctx, in)
	ret0, _ := ret[0].(*tab.GetTabByUGameIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTabByUGameId indicates an expected call of GetTabByUGameId.
func (mr *MockIClientMockRecorder) GetTabByUGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabByUGameId", reflect.TypeOf((*MockIClient)(nil).GetTabByUGameId), ctx, in)
}

// GetTabsByCategoryIds mocks base method.
func (m *MockIClient) GetTabsByCategoryIds(ctx context.Context, in *tab.GetTabsByCategoryIdsReq) (*tab.GetTabsByCategoryIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabsByCategoryIds", ctx, in)
	ret0, _ := ret[0].(*tab.GetTabsByCategoryIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabsByCategoryIds indicates an expected call of GetTabsByCategoryIds.
func (mr *MockIClientMockRecorder) GetTabsByCategoryIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabsByCategoryIds", reflect.TypeOf((*MockIClient)(nil).GetTabsByCategoryIds), ctx, in)
}

// InsertBlock mocks base method.
func (m *MockIClient) InsertBlock(ctx context.Context, in *tab.InsertBlockReq) (*tab.InsertBlockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertBlock", ctx, in)
	ret0, _ := ret[0].(*tab.InsertBlockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// InsertBlock indicates an expected call of InsertBlock.
func (mr *MockIClientMockRecorder) InsertBlock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertBlock", reflect.TypeOf((*MockIClient)(nil).InsertBlock), ctx, in)
}

// InsertRelationOfEtChannel mocks base method.
func (m *MockIClient) InsertRelationOfEtChannel(ctx context.Context, in *tab.InsertRelationOfEtChannelReq) (*tab.InsertRelationOfEtChannelResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertRelationOfEtChannel", ctx, in)
	ret0, _ := ret[0].(*tab.InsertRelationOfEtChannelResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// InsertRelationOfEtChannel indicates an expected call of InsertRelationOfEtChannel.
func (mr *MockIClientMockRecorder) InsertRelationOfEtChannel(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertRelationOfEtChannel", reflect.TypeOf((*MockIClient)(nil).InsertRelationOfEtChannel), ctx, in)
}

// ListReleaseCondition mocks base method.
func (m *MockIClient) ListReleaseCondition(ctx context.Context, in *tab.ListReleaseConditionReq) (*tab.ListReleaseConditionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListReleaseCondition", ctx, in)
	ret0, _ := ret[0].(*tab.ListReleaseConditionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListReleaseCondition indicates an expected call of ListReleaseCondition.
func (mr *MockIClientMockRecorder) ListReleaseCondition(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListReleaseCondition", reflect.TypeOf((*MockIClient)(nil).ListReleaseCondition), ctx, in)
}

// ReSortCategoryTitle mocks base method.
func (m *MockIClient) ReSortCategoryTitle(ctx context.Context, in *tab.ReSortCategoryTitleReq) (*tab.ReSortCategoryTitleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReSortCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*tab.ReSortCategoryTitleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReSortCategoryTitle indicates an expected call of ReSortCategoryTitle.
func (mr *MockIClientMockRecorder) ReSortCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReSortCategoryTitle", reflect.TypeOf((*MockIClient)(nil).ReSortCategoryTitle), ctx, in)
}

// RearrangeTabs mocks base method.
func (m *MockIClient) RearrangeTabs(ctx context.Context, in *tab.RearrangeTabsReq) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RearrangeTabs", ctx, in)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// RearrangeTabs indicates an expected call of RearrangeTabs.
func (mr *MockIClientMockRecorder) RearrangeTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RearrangeTabs", reflect.TypeOf((*MockIClient)(nil).RearrangeTabs), ctx, in)
}

// SearchTabMap mocks base method.
func (m *MockIClient) SearchTabMap(ctx context.Context, in *tab.SearchTabsReq) (map[uint32]*tab.Tab, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTabMap", ctx, in)
	ret0, _ := ret[0].(map[uint32]*tab.Tab)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTabMap indicates an expected call of SearchTabMap.
func (mr *MockIClientMockRecorder) SearchTabMap(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTabMap", reflect.TypeOf((*MockIClient)(nil).SearchTabMap), ctx, in)
}

// SearchTabs mocks base method.
func (m *MockIClient) SearchTabs(ctx context.Context, in *tab.SearchTabsReq) (*tab.SearchTabsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTabs", ctx, in)
	ret0, _ := ret[0].(*tab.SearchTabsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchTabs indicates an expected call of SearchTabs.
func (mr *MockIClientMockRecorder) SearchTabs(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTabs", reflect.TypeOf((*MockIClient)(nil).SearchTabs), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// Tabs mocks base method.
func (m *MockIClient) Tabs(ctx context.Context, skip, limit uint32) (*tab.TabsResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tabs", ctx, skip, limit)
	ret0, _ := ret[0].(*tab.TabsResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Tabs indicates an expected call of Tabs.
func (mr *MockIClientMockRecorder) Tabs(ctx, skip, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tabs", reflect.TypeOf((*MockIClient)(nil).Tabs), ctx, skip, limit)
}

// TabsForTT mocks base method.
func (m *MockIClient) TabsForTT(ctx context.Context, skip, limit uint32) (*tab.TabsForTTResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TabsForTT", ctx, skip, limit)
	ret0, _ := ret[0].(*tab.TabsForTTResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// TabsForTT indicates an expected call of TabsForTT.
func (mr *MockIClientMockRecorder) TabsForTT(ctx, skip, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TabsForTT", reflect.TypeOf((*MockIClient)(nil).TabsForTT), ctx, skip, limit)
}

// UpdateBlock mocks base method.
func (m *MockIClient) UpdateBlock(ctx context.Context, in *tab.UpdateBlockReq) (*tab.UpdateBlockResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBlock", ctx, in)
	ret0, _ := ret[0].(*tab.UpdateBlockResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateBlock indicates an expected call of UpdateBlock.
func (mr *MockIClientMockRecorder) UpdateBlock(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBlock", reflect.TypeOf((*MockIClient)(nil).UpdateBlock), ctx, in)
}

// UpdateCategoryTitle mocks base method.
func (m *MockIClient) UpdateCategoryTitle(ctx context.Context, in *tab.UpdateCategoryTitleReq) (*tab.UpdateCategoryTitleResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCategoryTitle", ctx, in)
	ret0, _ := ret[0].(*tab.UpdateCategoryTitleResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateCategoryTitle indicates an expected call of UpdateCategoryTitle.
func (mr *MockIClientMockRecorder) UpdateCategoryTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCategoryTitle", reflect.TypeOf((*MockIClient)(nil).UpdateCategoryTitle), ctx, in)
}

// UpdateMultilevelTitle mocks base method.
func (m *MockIClient) UpdateMultilevelTitle(ctx context.Context, in *tab.UpdateMultilevelTitleReq) (*tab.UpdateMultilevelTitleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMultilevelTitle", ctx, in)
	ret0, _ := ret[0].(*tab.UpdateMultilevelTitleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateMultilevelTitle indicates an expected call of UpdateMultilevelTitle.
func (mr *MockIClientMockRecorder) UpdateMultilevelTitle(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMultilevelTitle", reflect.TypeOf((*MockIClient)(nil).UpdateMultilevelTitle), ctx, in)
}

// UpdateQuickMatchConfig mocks base method.
func (m *MockIClient) UpdateQuickMatchConfig(ctx context.Context, req *tab.UpdateQuickMatchConfigReq) (*tab.UpdateQuickMatchConfigResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateQuickMatchConfig", ctx, req)
	ret0, _ := ret[0].(*tab.UpdateQuickMatchConfigResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateQuickMatchConfig indicates an expected call of UpdateQuickMatchConfig.
func (mr *MockIClientMockRecorder) UpdateQuickMatchConfig(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateQuickMatchConfig", reflect.TypeOf((*MockIClient)(nil).UpdateQuickMatchConfig), ctx, req)
}

// UpdateTabMiniGameId mocks base method.
func (m *MockIClient) UpdateTabMiniGameId(ctx context.Context, in *tab.UpdateTabMiniGameIdReq) (*tab.UpdateTabMiniGameIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabMiniGameId", ctx, in)
	ret0, _ := ret[0].(*tab.UpdateTabMiniGameIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateTabMiniGameId indicates an expected call of UpdateTabMiniGameId.
func (mr *MockIClientMockRecorder) UpdateTabMiniGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabMiniGameId", reflect.TypeOf((*MockIClient)(nil).UpdateTabMiniGameId), ctx, in)
}

// UpdateTabUGameId mocks base method.
func (m *MockIClient) UpdateTabUGameId(ctx context.Context, in *tab.UpdateTabUGameIdReq) (*tab.UpdateTabUGameIdResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabUGameId", ctx, in)
	ret0, _ := ret[0].(*tab.UpdateTabUGameIdResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UpdateTabUGameId indicates an expected call of UpdateTabUGameId.
func (mr *MockIClientMockRecorder) UpdateTabUGameId(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabUGameId", reflect.TypeOf((*MockIClient)(nil).UpdateTabUGameId), ctx, in)
}
