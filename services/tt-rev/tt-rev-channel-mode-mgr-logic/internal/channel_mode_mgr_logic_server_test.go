package internal

import (
	"context"
	"github.com/golang/mock/gomock"
	mockChannelModeMgr "golang.52tt.com/clients/mocks/tt-rev-channel-mode-mgr"
	channelModeMgrCli "golang.52tt.com/clients/tt-rev-channel-mode-mgr"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	channelModeMgrLogic "golang.52tt.com/protocol/app/tt-rev-channel-mode-mgr-logic"
	tt_rev_channel_mode_mgr_logic "golang.52tt.com/protocol/services/logicsvr-go/tt-rev-channel-mode-mgr-logic"
	tt_rev_channel_mode_mgr "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"
	"reflect"
	"testing"
)

var (
	mockChannelModeClient *mockChannelModeMgr.MockIClient
)

func TestNewChannelModeMgrLogicServer(t *testing.T) {

	type args struct {
		channelModeMgrCli *channelModeMgrCli.Client
	}
	tests := []struct {
		name string
		args args
		want tt_rev_channel_mode_mgr_logic.ChannelModeMgrLogicServer
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

		})
	}
}

func Test_channelModeMgrLogicServer_GetChannelMode(t *testing.T) {
	ctl := gomock.NewController(t)
	mockChannelModeClient = mockChannelModeMgr.NewMockIClient(ctl)

	var uid uint32 = 1000
	ctx := protogrpc.WithServiceInfo(context.Background(), &protogrpc.ServiceInfo{UserID: uid})

	serResp := &tt_rev_channel_mode_mgr.GetChannelModeResp{
		ChannelId: 100,
		Schemes: []*tt_rev_channel_mode_mgr.ChannelScheme{
			{
				SchemeId:     10005,
				SchemeName:   "默认",
				LayoutType:   17,
				IconSelect:   "IconSelect",
				IconUnSelect: "IconUnSelect",
				PcIcon:       "PcIcon",
				IsOpen:       false,
			},
		},
	}

	gomock.InOrder(
		mockChannelModeClient.EXPECT().GetChannelMode(gomock.Any(), gomock.Any()).Return(serResp, nil).AnyTimes(),
	)

	resp := &channelModeMgrLogic.GetChannelModeResp{
		ChannelId: 100,
		Schemes: []*channelModeMgrLogic.ChannelScheme{
			{
				SchemeId:     10005,
				SchemeName:   "默认",
				LayoutType:   17,
				IconSelect:   "IconSelect",
				IconUnSelect: "IconUnSelect",
				PcIcon:       "PcIcon",
				IsOpen:       false,
			},
		},
	}

	type fields struct {
		channelModeMgrClient tt_rev_channel_mode_mgr.ChannelModeMgrClient
	}
	type args struct {
		ctx context.Context
		req *channelModeMgrLogic.GetChannelModeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channelModeMgrLogic.GetChannelModeResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetChannelMode",
			fields: fields{
				channelModeMgrClient: mockChannelModeClient,
			},
			args: args{
				ctx: ctx,
				req: &channelModeMgrLogic.GetChannelModeReq{
					BaseReq:   nil,
					ChannelId: 100,
				},
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Server{
				channelModeMgrClient: tt.fields.channelModeMgrClient,
			}
			got, err := c.GetChannelMode(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelMode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelMode() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_channelModeMgrLogicServer_SetChannelMode(t *testing.T) {

	ctl := gomock.NewController(t)
	mockChannelModeClient = mockChannelModeMgr.NewMockIClient(ctl)
	gomock.InOrder(
		mockChannelModeClient.EXPECT().SetChannelMode(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes(),
	)

	resp := &channelModeMgrLogic.SetChannelModeResp{}
	type fields struct {
		channelModeMgrClient tt_rev_channel_mode_mgr.ChannelModeMgrClient
	}
	type args struct {
		ctx context.Context
		req *channelModeMgrLogic.SetChannelModeReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *channelModeMgrLogic.SetChannelModeResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetChannelMode",
			fields: fields{
				channelModeMgrClient: mockChannelModeClient,
			},
			args: args{
				ctx: context.Background(),
				req: &channelModeMgrLogic.SetChannelModeReq{
					BaseReq:   nil,
					ChannelId: 100,
				},
			},
			want:    resp,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Server{
				channelModeMgrClient: tt.fields.channelModeMgrClient,
			}
			got, err := c.SetChannelMode(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetChannelMode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SetChannelMode() got = %v, want %v", got, tt.want)
			}
		})
	}
}
