package highcontentproxy

import (
	"context"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/highcontentproxy"
	"google.golang.org/grpc"
)

const (
	serviceName = "ugc-highcontent-proxy"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewHighContentProxyClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.HighContentProxyClient { return c.Stub().(pb.HighContentProxyClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetRecommendCard(ctx context.Context, offset, limit uint32) (*pb.GetRecommendCardResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendCard(ctx, &pb.GetRecommendCardReq{
		Offset: offset,
		Limit:  limit,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecommendCardRand(ctx context.Context, cnt uint32) (*pb.GetRecommendCardRandResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendCardRand(ctx, &pb.GetRecommendCardRandReq{
		Cnt: cnt,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecommendRandByUIDs(ctx context.Context, uidList []uint32) (*pb.GetRecommendCardRandResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendRandByUIDs(ctx, &pb.GetRecommendCardRandByUIDsReq{
		UidList: uidList,
	})
	return resp, protocol.ToServerError(err)
}
func (c *Client) GetRecommendAggregation(ctx context.Context, req pb.GetRecommendAggregationReq) (*pb.GetRecommendAggregationResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendAggregation(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRecommendExpert(ctx context.Context) (*pb.GetRecommendExpertResp, protocol.ServerError) {
	resp, err := c.typedStub().GetRecommendExpert(ctx, &pb.GetRecommendExpertReq{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetForceInsertPostInfo(ctx context.Context, req pb.GetForceInsertPostInfoReq) (*pb.GetForceInsertPostInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetForceInsertPostInfo(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdatePostRecommend(ctx context.Context, req pb.UpdatePostRecommendReq) (*pb.UpdatePostRecommendResp, protocol.ServerError) {
	resp, err := c.typedStub().UpdatePostRecommend(ctx, &req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpdatePostTags(ctx context.Context, req pb.UpdatePostTagsReq) (*pb.UpdatePostTagsRsp, protocol.ServerError) {
	resp, err := c.typedStub().UpdatePostTags(ctx, &req)
	return resp, protocol.ToServerError(err)
}
