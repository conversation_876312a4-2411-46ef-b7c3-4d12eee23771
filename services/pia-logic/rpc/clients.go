package rpc

import (
	"github.com/google/wire"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/channel"
	channelScheme "golang.52tt.com/clients/channel-scheme"
	channelstats "golang.52tt.com/clients/channel-stats"
	channelmemberviprank "golang.52tt.com/clients/channelmemberVipRank"
	"golang.52tt.com/clients/channelmic"
	channelol_stat_go "golang.52tt.com/clients/channelol-stat-go"
	"golang.52tt.com/clients/entertainmentrecommendback"
	"golang.52tt.com/clients/pia"
	pushNotification "golang.52tt.com/clients/push-notification/v2"
	youknowwho "golang.52tt.com/clients/you-know-who"
	"google.golang.org/grpc"
)

var ProviderSetForDownStreamClient = wire.NewSet(
	NewGrpcOption,
	channelmemberviprank.NewIClient,
	pia.NewClientOrg,
	channelstats.NewClient,
	channel.NewIClient,
	channelScheme.NewIClient,
	entertainmentrecommendback.NewIClient,
	channelmic.NewIClient,
	channelstats.NewIClient,
	channelol_stat_go.NewIClient,
	account.NewIClient,
	youknowwho.NewIClient,
	pushNotification.NewIClient,
)

func NewGrpcOption() []grpc.DialOption {
	return []grpc.DialOption{
		grpc.WithBlock(),
	}
}
