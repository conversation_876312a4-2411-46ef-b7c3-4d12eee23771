package store

import (
	"context"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mongo"
	"golang.52tt.com/pkg/config"
)

//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/mock_store.go -package=mocks golang.52tt.com/services/tt-rev/esport/esport-grab-order/internal/store IStore
const (
	publishCollName        = "publish"
	grabCenterCollName     = "grab_center"
	grabCollName           = "grab"
	grabAudioAuditCollName = "grab_audio_audit"
)

type Store struct {
	client *mongo.ClientImpl

	publishColl        *mongo.MongoCollectionImpl
	grabCenterColl     *mongo.MongoCollectionImpl
	grabColl           *mongo.MongoCollectionImpl
	grabAudioAuditColl *mongo.MongoCollectionImpl
}

func NewStore(ctx context.Context, cfg *config.MongoConfig) (*Store, error) {
	client, err := mongo.NewClient(ctx, cfg.OptionsForReplicaSet())
	if err != nil {
		return nil, err
	}

	s := &Store{
		publishColl:        client.CreateMongoCollection(cfg.Database, publishCollName),
		grabCenterColl:     client.CreateMongoCollection(cfg.Database, grabCenterCollName),
		grabColl:           client.CreateMongoCollection(cfg.Database, grabCollName),
		grabAudioAuditColl: client.CreateMongoCollection(cfg.Database, grabAudioAuditCollName),
	}
	s.ensureIndex(ctx)

	return s, nil
}

func (s *Store) Close(ctx context.Context) error {
	return s.client.Close(ctx)
}

func (s *Store) ensureIndex(ctx context.Context) {
	s.ensureIndex4PublishRecord(ctx)
	s.ensureIndex4GrabCenterRecord(ctx)
	s.ensureIndex4GrabRecord(ctx)
	s.ensureIndex4GrabAudioAuditRecord(ctx)
}
