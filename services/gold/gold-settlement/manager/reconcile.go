package manager

import (
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/gold-commission"
	reconcileV2 "golang.52tt.com/protocol/services/reconcile-v2"
	"golang.org/x/net/context"
	"time"
)

// GetAmuseGoldSettleOrderCount 多人互动会长佣金结算对账
func (m *Manager) GetAmuseGoldSettleOrderCount(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.CountResp, error) {
	return m.GetGoldSettleOrderCount(ctx, req, pb.GoldType_AMUSE_GOLD)
}
func (m *Manager) GetAmuseGoldSettleOrderList(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.OrderIdsResp, error) {
	return m.GetGoldSettleOrderList(ctx, req, pb.GoldType_AMUSE_GOLD)
}

// GetYuyinGoldSettleOrderCount 语音直播会长佣金结算对账
func (m *Manager) GetYuyinGoldSettleOrderCount(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.CountResp, error) {
	return m.GetGoldSettleOrderCount(ctx, req, pb.GoldType_YUYIN_GOLD)
}
func (m *Manager) GetYuyinGoldSettleOrderList(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.OrderIdsResp, error) {
	return m.GetGoldSettleOrderList(ctx, req, pb.GoldType_YUYIN_GOLD)
}

func (m *Manager) GetGoldSettleOrderCount(ctx context.Context, req *reconcileV2.TimeRangeReq, goldType pb.GoldType) (*reconcileV2.CountResp, error) {
	log.InfoWithCtx(ctx, "GetGoldSettleOrderCount req: %+v", req)
	resp := &reconcileV2.CountResp{}

	store, err := m.mysqlStore.GetStore(goldType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGoldSettleOrderCount GetStore err: %s", err)
		return resp, err
	}

	start, end := time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0)
	totalCnt, sumFee, err := store.GetSettleIncomeCntSum(ctx, start, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGoldSettleOrderCount GetSettleIncomeCntSum err: %s", err)
		return resp, err
	}

	resp.Count = uint32(totalCnt)
	resp.Value = uint32(sumFee)

	log.InfoWithCtx(ctx, "GetGoldSettleOrderCount req: %+v, rsp:%+v", req, resp)
	return resp, nil
}

func (m *Manager) GetGoldSettleOrderList(ctx context.Context, req *reconcileV2.TimeRangeReq, goldType pb.GoldType) (*reconcileV2.OrderIdsResp, error) {
	log.InfoWithCtx(ctx, "GetGoldSettleOrderList req: %+v", req)
	resp := &reconcileV2.OrderIdsResp{}

	store, err := m.mysqlStore.GetStore(goldType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGoldSettleOrderList GetStore err: %s", err)
		return resp, err
	}

	start, end := time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0)
	items, err := store.GetSettleIncomeOrderList(ctx, start, end)
	if err != nil {
		return resp, err
	}
	for _, item := range items {
		resp.OrderIds = append(resp.OrderIds, item.ReconcileOrderID)
	}

	log.InfoWithCtx(ctx, "GetGoldSettleOrderList req: %+v, rsp:%+v", req, resp)
	return resp, nil
}

// GetAmuseExtraSettleOrderCount 多人互动会长额外奖励结算对账
func (m *Manager) GetAmuseExtraSettleOrderCount(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.CountResp, error) {
	log.InfoWithCtx(ctx, "GetAmuseExtraSettleOrderCount req: %+v", req)
	resp := &reconcileV2.CountResp{}

	store, err := m.mysqlStore.GetStore(pb.GoldType_AMUSE_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraSettleOrderCount GetStore err: %s", err)
		return resp, err
	}

	start, end := time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0)
	totalCnt, sumFee, err := store.GetAmuseExtraSettleIncomeCntSum(ctx, start, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraSettleOrderCount GetAmuseExtraSettleIncomeCntSum err: %s", err)
		return resp, err
	}

	resp.Count = uint32(totalCnt)
	resp.Value = uint32(sumFee)

	log.InfoWithCtx(ctx, "GetAmuseExtraSettleOrderCount req: %+v, rsp:%+v", req, resp)
	return resp, nil
}
func (m *Manager) GetAmuseExtraSettleOrderList(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.OrderIdsResp, error) {
	log.InfoWithCtx(ctx, "GetAmuseExtraSettleOrderList req: %+v", req)
	resp := &reconcileV2.OrderIdsResp{}

	store, err := m.mysqlStore.GetStore(pb.GoldType_AMUSE_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraSettleOrderList GetStore err: %s", err)
		return resp, err
	}

	start, end := time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0)
	items, err := store.GetAmuseExtraSettleIncomeOrderList(ctx, start, end)
	if err != nil {
		return resp, err
	}
	for _, item := range items {
		resp.OrderIds = append(resp.OrderIds, item.ReconcileOrderID)
	}

	log.InfoWithCtx(ctx, "GetAmuseExtraSettleOrderList req: %+v, rsp:%+v", req, resp)
	return resp, nil
}

// GetYuyinExtraSettleOrderCount 语音直播会长额外奖励结算对账
func (m *Manager) GetYuyinExtraSettleOrderCount(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.CountResp, error) {
	log.InfoWithCtx(ctx, "GetYuyinExtraSettleOrderCount req: %+v", req)
	resp := &reconcileV2.CountResp{}

	store, err := m.mysqlStore.GetStore(pb.GoldType_YUYIN_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinExtraSettleOrderCount GetStore err: %s", err)
		return resp, err
	}

	start, end := time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0)
	totalCnt, sumFee, err := store.GetYuyinExtraSettleIncomeCntSum(ctx, start, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinExtraSettleOrderCount GetYuyinExtraSettleIncomeCntSum err: %s", err)
		return resp, err
	}

	resp.Count = uint32(totalCnt)
	resp.Value = uint32(sumFee)

	log.InfoWithCtx(ctx, "GetYuyinExtraSettleOrderCount req: %+v, rsp:%+v", req, resp)
	return resp, nil
}
func (m *Manager) GetYuyinExtraSettleOrderList(ctx context.Context, req *reconcileV2.TimeRangeReq) (*reconcileV2.OrderIdsResp, error) {
	log.InfoWithCtx(ctx, "GetYuyinExtraSettleOrderList req: %+v", req)
	resp := &reconcileV2.OrderIdsResp{}

	store, err := m.mysqlStore.GetStore(pb.GoldType_YUYIN_GOLD)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinExtraSettleOrderList GetStore err: %s", err)
		return resp, err
	}

	start, end := time.Unix(req.GetBeginTime(), 0), time.Unix(req.GetEndTime(), 0)
	items, err := store.GetYuyinExtraSettleIncomeOrderList(ctx, start, end)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinExtraSettleOrderList GetYuyinExtraSettleIncomeOrderList err: %s", err)
		return resp, err
	}
	for _, item := range items {
		resp.OrderIds = append(resp.OrderIds, item.ReconcileOrderID)
	}

	log.InfoWithCtx(ctx, "GetYuyinExtraSettleOrderList req: %+v, rsp:%+v", req, resp)
	return resp, nil
}
