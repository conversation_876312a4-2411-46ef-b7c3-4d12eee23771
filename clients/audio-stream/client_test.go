package celebrity

import (
	"context"
	"os"
	"testing"

	"gitlab.ttyuyin.com/golang/gudetama/log"

	. "github.com/smartystreets/goconvey/convey"

	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
)

var cli *Client

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	var err error
	cli, err = NewClient(grpc.WithBlock())
	if err != nil {
		log.Fatalln(err)
	}
}

func TestClient_Insert(t *testing.T) {
	Convey("insert an audio info", t, func() {
		err := cli.Insert(context.TODO(), "abc", "abc", "abc", uint32(123), "abc", "abc", []string{"abc", "def"}, []string{"abc"}, []string{"abc"}, []string{"abc"})
		So(err, ShouldBeNil)
	})
}
