syntax="proto2";

package ga.circle;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/circle";

message GameCircle {
    //圈子类型枚举  -- v1.5 发版前夕加入
    // buf:lint:ignore ENUM_PASCAL_CASE
    // buf:lint:ignore ENUM_FIRST_VALUE_ZERO
    enum CIRCLE_TYPE_CONST{
        TYPE_GAME_CIRCLE = 1;  // 渣渣圈
        TYPE_OFFICIAL_CIRCLE = 2; // 官方圈
    }
    required uint32 circle_id = 1;
    required string name = 2;              //名字
    required uint32 user_num = 3;           //用户数
    required uint32 game_id = 4;           //关联的游戏，无游戏＝0
    required string icon_url = 5;          //头像url地址
    required uint32 rank = 6;			   //用于排序
    optional uint32 guild_member_num = 7;  //公会成员订阅数  -- v1.5
    optional NewGame new_game_info = 8;    //新游信息  -- v1.5
    repeated GameCircleUser manager = 9;   //圈主  -- v1.5
    optional string background_url = 10;   //热门游戏圈的背景图url -- v1.5
    optional bool is_follow = 11;          //是否关注 -- v1.5
    repeated GameCircleUser vice_manager = 12;   //副圈主  -- v1.5
    optional OfficialAccountSimpleInfo official_account = 13;		// 本圈子的公众号帐号
    optional uint32 circle_type = 14; //圈子类型 -- v1.5 发版前夕加入
}

//我的游戏圈列表
message GameCircleGetMyCircleReq {
    required BaseReq base_req = 1;
	optional bool bymyorder = 2;
}

message GameCircleGetMyCircleResp {
    required BaseResp base_resp = 1;
    repeated GameCircle circle_list = 2;
}

//推荐游戏圈列表
message GameCircleGetTopListReq {
    required BaseReq base_req = 1;
    optional uint32 req_type = 2; // -- v1.5. see GameCircleTopListReqType at ga_base.proto
}

message GameCircleGetTopListResp {
    required BaseResp base_resp = 1;
    repeated GameCircle circle_list = 2;
    optional uint32 req_type = 3; // -- v1.5. see GameCircleTopListReqType at ga_base.proto
}

//加入游戏圈
message GameCircleJoinReq {
    required BaseReq base_req = 1;
    repeated uint32 circle_id_list = 2;
}

message GameCircleJoinResp {
    required BaseResp base_resp = 1;
    repeated uint32 circle_id_list = 2;
    repeated uint32 failed_id_list = 3;
}

//退出游戏圈
message GameCircleQuitReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
}

message GameCircleQuitResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
}

//游戏圈信息流的一条主题
message GameCircleTopic {
    // buf:lint:ignore ENUM_PASCAL_CASE
    enum TOPIC_TYPE {
        NORMAL = 0;          //普通类型
        TOP = 1;             //置顶消息
        NEW_OFFICAL = 2;	// 新的官方主题  -- v1.5
    }

    // buf:lint:ignore ENUM_PASCAL_CASE
    // buf:lint:ignore ENUM_FIRST_VALUE_ZERO
    enum TOPIC_STATE {
        STATE_HIGHT_LIGHT   = 1;    // 精华
        STATE_STICK         = 2;    // 置顶
        STATE_ACTIVITY      = 4;    // 活动
        STATE_GAME_DOWNLOAD = 8;    // 游戏下载
    }

    required uint32 circle_id = 1;          // 圈子ID
    required uint32 topic_id = 2;           // 主题ID
    required uint32 type = 3;               // 类型，见上
    required GameCircleUser sender = 4;     // 发送者
    required uint32 send_time = 5;          // 发送时间
    required string title = 6;              // 标题
    required string content = 7;            // 内容
    repeated string img_thumb_url_list = 8; // 缩略图url地址
    repeated string img_url_list = 9;       // 图片url地址
    required uint32 like_count = 10;        // 赞数量
    required uint32 comment_count = 11;     // 评论数
    required bool is_liked = 12;            // 我是否已赞
    // buf:lint:ignore FIELD_LOWER_SNAKE_CASE
    required string clientId = 13;          // 客户端生成，用于客户端对比
    repeated string img_key_list = 14;      // 图片的key
    optional uint32 new_type = 15;          // 新版的置顶类型  -- v1.5
    optional bool is_hight_light = 16;      // 是否加精  -- v1.5, Deprecated, use topic_state instead
    optional uint32 last_time = 17;         // 最后回复时间  --v1.5
    optional uint32 topic_state = 18;       // SEE TOPIC_STATE
    optional CircleTopicGameDownloadInfo download_info = 19;    // TOPIC_STATE包含STATE_GAME_DOWNLOAD时, 提供游戏的下载信息
}

//获取游戏圈topic列表
message GameCircleGetTopicListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 start_topic_id = 3;         //从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 4;            //请求获取的条数
    optional uint32 client_min_topic_id = 5;	//客户端缓存中的最小topicId
    optional uint32 client_max_topic_id = 6;	//客户端缓存中的最大topicId
    optional uint32 req_type = 7;   // -- v1.5. see GameCircleTopicRequestType at
    optional uint32 pos = 8; //页码
	optional uint32 userfrom = 9;   //--V1.5 see ga_base  STAT_FROM_PAGE_TYPE
}

message GameCircleGetTopicListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 start_topic_id = 3;
    required uint32 topic_count = 4;            //实际获取到的条数
    repeated GameCircleTopic topic_list = 5;    //topic列表
    repeated uint32 deleted_topic_id_list = 6;	//[client_min_topic_id, client_max_topic_id]区间中被删除的topicId列表
    optional uint32 req_type = 7; // -- v1.5. see GameCircleTopicRequestType at ga_base.proto
    optional uint32 newest_topic_id = 8;//最新主题id
}

//获取某条topic
message GameCircleGetTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message GameCircleGetTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required GameCircleTopic topic = 4;
}

//发表主题
message GameCircleSendTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required GameCircleTopic topic = 3;
}

message GameCircleSendTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required GameCircleTopic topic = 3;
}

//游戏圈topic评论
message GameCircleTopicComment {
    required uint32 comment_id = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                 //评论所属的topic
    required string content = 4;                  //评论内容
    required GameCircleUser sender = 5;           //发送者
    required uint32 send_time = 6;                  //发送时间
    required uint32 replied_comment_id = 7;         //如果是回复评论，则为评论的id，如果是直接回复主题，为0
    required string replied_account = 8;         //被回复的评论者账号，replied_comment_id不为0时有效
    required string replied_name = 9;            //被回复的评论者名称，replied_comment_id不为0时有效
    required string replied_content = 10;         //被回复的评论内容，replied_comment_id不为0时有效
    required bool replied_delete = 11;			// 被回复的评论，是否已被删除 // -- v.15后， 不隐藏内容， 展示服务器返回的信息。hardcode样式
    optional bool is_deleted = 12;		// -- v1.5. 该评论是否已被删除。 一般出现于运营删评论操作后。 展示服务器返回的信息。hardcode样式
}

//获取评论列表
message GameCircleGetCommentListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                  //所属主题
    required uint32 start_comment_id = 4;          //从这条评论开始获取之前的评论，为0则获取最新评论
    required uint32 comment_count = 5;             //请求获取的条数
	optional bool order_desc = 6;
}

message GameCircleGetCommentListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 start_comment_id = 4;
    required uint32 comment_count = 5;             //实际获取到的条数
    repeated GameCircleTopicComment comment_list = 6;  //结果列表
}

//发表评论
message GameCircleSendCommentReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                     //所属主题
    required uint32 replied_comment_id = 4;             //如果是回复别人评论，则为评论id，回复主题则为0
    required GameCircleTopicComment comment = 5;
}

message GameCircleSendCommentResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 replied_comment_id = 4;
    required GameCircleTopicComment comment = 5;
}

//赞/取消赞 消息
message GameCircleLikeTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                     //所属主题
    required bool is_like = 4;                        //true为赞，false为取消赞
}

message GameCircleLikeTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                     //所属主题
    required bool is_like = 4;
}

//举报主题
message GameCircleReportTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;                     //所属主题
}

message GameCircleReportTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//删除我发的主题
message GameCircleDeleteTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message GameCircleDeleteTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//删除我发的评论
message GameCircleDeleteCommentReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

message GameCircleDeleteCommentResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

//单查游戏圈
message GameCircleGetCircleReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
}

message GameCircleGetCircleResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required GameCircle circle = 3;
}

// 查我发表过的主题
message GameCircleGetUserTopicReq {
    required BaseReq base_req = 1;
    required uint32 start_circle_id = 2;			// circle_id = 0 && topic_id = 0, 表示获取最新的消息
    required uint32 start_topic_id = 3;         	// 否则从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 4;            	//请求获取的条数
    required uint32 uid = 5;						//
}

message GameCircleGetUserTopicResp {
    required BaseResp base_resp = 1;
    required uint32 start_circle_id = 2;			// circle_id = 0 && topic_id = 0, 表示获取最新的消息
    required uint32 start_topic_id = 3;         	// 否则从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 4;            	//请求获取的条数
    repeated GameCircleTopic topic_list = 5;    	//topic列表
    required uint32 uid = 6;						//
}

// 批量设置已读
message GameCircleMarkReadedReq {
    required BaseReq base_req = 1;
    required uint32 svr_msg_id = 2; // 服务消息id(最大的那条id)
}

message GameCircleMarkReadedResp {
    required BaseResp base_resp = 1;
    required uint32 svr_msg_id = 2;
}

// 查点赞列表
message GameCircleGetLikeUserListReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 offset = 4;      // 查询起始
    required uint32 limit = 5;      // 查询数量, 若为0, 则使用服务器缺省值
}

message GameCircleGetLikeUserListResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 offset = 4;
    required uint32 limit = 5;
    repeated GameCircleUser user_list = 6;  // 用户列表, 按点赞时间倒排
}

message GameCircleMuteUserReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;             //被禁言的人
    repeated uint32 circle_id_list = 3;        // 为空表示禁言全部
    required uint32 days = 4;           // =0无限期 兼容旧协议，新协议（默认为0）不再有用
    optional uint32 secs = 5;           // =0无限期
    optional string reason = 6;			// 禁言理由
    optional uint32 because_circle_id = 7;		// 因为某个圈子的某个帖子被禁言
    optional uint32 because_topic_id = 8;		//
}

message GameCircleMuteUserResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    repeated uint32 circle_id_list = 3;
    required uint32 days = 4;
    optional uint32 secs = 5;
}

message GameCircleMuteReasonListReq {
	required BaseReq base_req = 1;
}

message GameCircleMuteReasonListResp {
	required BaseResp base_resp = 1;
	repeated string reason_list = 2;
}

message GameCircleUnmuteUserReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;             //被禁言的人
    repeated uint32 circle_id_list = 3;        // 为空表示禁言全部
}

message GameCircleUnmuteUserResp {
    required BaseResp base_resp = 1;
    required uint32 uid = 2;
    repeated uint32 circle_id_list = 3;
}


//我的圈子排序 -- v1.5
message MyGameCircleOrderReq {
    required BaseReq base_req = 1;
    repeated uint32 gamecircle_id_list = 2;
}
message MyGameCircleOrderResp {
    required BaseResp base_resp = 1;
    repeated uint32 gamecircle_id_list = 2;
}

//加精 -- v1.5
message GameCircleHighlightTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message GameCircleHighlightTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//取消加精 -- v1.5
message GameCircleCancelHighlightTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

message GameCircleCancelHighlightTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
}

//管理员删除评论 -- v1.5
message GameCircleManagerDeleteCommentReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

message GameCircleManagerDeleteCommentResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

//检查圈子更新状态
message CheckCircleUpdateReq{
    required BaseReq base_req = 1;
    repeated TopicUpdateInfo topic_update_info = 2 ;
}

message CheckCircleUpdateResp{
    required BaseResp base_resp = 1;
    repeated TopicUpdateInfo topic_update_info = 2 ;
    optional uint32 newest_circle_id = 3 ; //最新更新的游戏圈
}
