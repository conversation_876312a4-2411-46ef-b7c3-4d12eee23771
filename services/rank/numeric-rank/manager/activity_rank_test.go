package manager

import (
	"bou.ke/monkey"
	"context"
	numeric_logic "golang.52tt.com/protocol/app/numeric-logic"
	pb "golang.52tt.com/protocol/services/numeric-rank"
	"golang.52tt.com/services/rank/numeric-rank/cache"
	"golang.52tt.com/services/rank/numeric-rank/conf"
	"reflect"
	"testing"
)

func TestManager_ReportGloryRank(t *testing.T) {
	ctx := context.Background()
	uid := uint32(123456)

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericRankCache{}), "GetReportLock",
		func(c *cache.NumericRankCache, ctx context.Context, sign string) (bool, error) {
			return true, nil
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericRankCache{}), "SaveActivityRank",
		func(c *cache.NumericRankCache, ctx context.Context, req *pb.ReportGloryRankReq) error {
			return nil
		})

	type fields struct {
		cacheStore cache.INumericRankCache
		sc         conf.IServiceConfigT
	}
	type args struct {
		ctx context.Context
		req *pb.ReportGloryRankReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.ReportGloryRankResp
		wantErr bool
	}{
		{
			name: "ReportGloryRank",
			fields: fields{
				cacheStore: &cache.NumericRankCache{},
				sc:         &conf.ServiceConfigT{},
			},
			args: args{
				ctx: ctx,
				req: &pb.ReportGloryRankReq{
					ActivitySign: "activity_a",
					ActivityName: "activity_a",
					RankSign:     "rank_a",
					RankName:     "rank_a",
					UserList: []*pb.UserRank{
						{
							Uid:   uid,
							Rank:  1,
							Value: 0,
						},
					},
					OfflineTime: 0,
				},
			},
			want:    &pb.ReportGloryRankResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheStore: tt.fields.cacheStore,
				sc:         tt.fields.sc,
			}
			got, err := m.ReportGloryRank(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportGloryRank() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReportGloryRank() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_getUserActivityRankingFromCache(t *testing.T) {
	uid := uint32(123456)

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.NumericRankCache{}), "GetActivityKeyList",
		func(c *cache.NumericRankCache, ctx context.Context) (cache.ActivityKeyList, error) {
			return []string{"activity_a"}, nil
		})

	monkey.PatchInstanceMethod(reflect.TypeOf(&cache.ActivityLocalCache{}), "GetUserActivityRankingByKey",
		func(c *cache.ActivityLocalCache, activityKey string, uid uint32) (cache.UserRankInfo, bool) {
			return cache.UserRankInfo{
				Uid:          uid,
				Ranking:      1,
				ActivitySign: "activity_a",
				ActivityName: "activity_a",
				RankSign:     "",
				RankName:     "",
				OfflineTime:  0,
			}, true
		})

	type fields struct {
		cacheStore cache.INumericRankCache
		sc         conf.IServiceConfigT
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*pb.UserActivityRank
	}{
		{
			name: "getUserActivityRankingFromCache",
			fields: fields{
				cacheStore: &cache.NumericRankCache{},
				sc:         &conf.ServiceConfigT{},
			},
			args: args{
				uid: uid,
			},
			want: []*pb.UserActivityRank{
				{
					RankType:     uint32(numeric_logic.GloryRankType_ActivityRank),
					Uid:          uid,
					ActivitySign: "activity_a",
					ActivityName: "activity_a",
					RankSign:     "",
					RankName:     "",
					ActivityRank: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				cacheStore: tt.fields.cacheStore,
				sc:         tt.fields.sc,
			}
			if got := m.getUserActivityRankingFromCache(tt.args.ctx, tt.args.uid); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getUserActivityRankingFromCache() = %v, want %v", got, tt.want)
			}
		})
	}
}
