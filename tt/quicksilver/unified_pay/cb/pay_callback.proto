syntax="proto3";

package unified_pay.cb;

option go_package = "golang.52tt.com/protocol/services/unified_pay/cb";

import "tt/quicksilver/extension/options/options.proto";

enum FeeType {
    UNKNOWN = 0;
    RED_DIAMOND = 1;
    TBEAN = 2;
}

enum Op {
    COMMIT = 0;
    ROLLBACK = 1;
}

message PayNotify {
    string app_id = 1;
    string out_trade_no = 2;
    uint32 uid = 3;
    string user_name = 4;
    FeeType fee_type = 5;
    uint32 total_fee = 6;
    string body = 7;
    string detail = 8;
    uint32 create_at = 9;
    string trade_no = 10;

    bool   rollbackable = 11;      // 该交易是否支持回滚
}

message PayNotifyResponse {
    bool confirmed = 1;     // confirmed=true，则支付服务根据rollback决定下一步操作；否则延迟一定时间后重新发起Notify
    Op op = 2;      // 若回滚, 则解除冻结；否则提交
}

service PayCallback {
    option (service.options.old_package_name) = "UnifiedPayCallback.PayCallback";

    rpc Notify(PayNotify) returns(PayNotifyResponse) {}
}