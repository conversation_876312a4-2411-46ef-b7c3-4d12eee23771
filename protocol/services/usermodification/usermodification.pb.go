// Code generated by protoc-gen-gogo.
// source: src/usermodification/usermodification.proto
// DO NOT EDIT!

/*
	Package usermodification is a generated protocol buffer package.

	It is generated from these files:
		src/usermodification/usermodification.proto

	It has these top-level messages:
		UpdateUserModificationCountReq
		UpdateUserModificationCountResp
		GetUserModificationCountReq
		GetUserModificationCountResp
		DeleteUserModificationCountReq
		DeleteUserModificationCountResp
		BatchDelUserModificationCountReq
		BatchDelUserModificationCountResp
*/
package usermodification

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type MODIFY_TYPE int32

const (
	MODIFY_TYPE_SEX MODIFY_TYPE = 1
)

var MODIFY_TYPE_name = map[int32]string{
	1: "SEX",
}
var MODIFY_TYPE_value = map[string]int32{
	"SEX": 1,
}

func (x MODIFY_TYPE) Enum() *MODIFY_TYPE {
	p := new(MODIFY_TYPE)
	*p = x
	return p
}
func (x MODIFY_TYPE) String() string {
	return proto.EnumName(MODIFY_TYPE_name, int32(x))
}
func (x *MODIFY_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MODIFY_TYPE_value, data, "MODIFY_TYPE")
	if err != nil {
		return err
	}
	*x = MODIFY_TYPE(value)
	return nil
}
func (MODIFY_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorUsermodification, []int{0} }

type UpdateUserModificationCountReq struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type uint32 `protobuf:"varint,2,req,name=type" json:"type"`
}

func (m *UpdateUserModificationCountReq) Reset()         { *m = UpdateUserModificationCountReq{} }
func (m *UpdateUserModificationCountReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserModificationCountReq) ProtoMessage()    {}
func (*UpdateUserModificationCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{0}
}

func (m *UpdateUserModificationCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserModificationCountReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type UpdateUserModificationCountResp struct {
}

func (m *UpdateUserModificationCountResp) Reset()         { *m = UpdateUserModificationCountResp{} }
func (m *UpdateUserModificationCountResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserModificationCountResp) ProtoMessage()    {}
func (*UpdateUserModificationCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{1}
}

type GetUserModificationCountReq struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type uint32 `protobuf:"varint,2,req,name=type" json:"type"`
}

func (m *GetUserModificationCountReq) Reset()         { *m = GetUserModificationCountReq{} }
func (m *GetUserModificationCountReq) String() string { return proto.CompactTextString(m) }
func (*GetUserModificationCountReq) ProtoMessage()    {}
func (*GetUserModificationCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{2}
}

func (m *GetUserModificationCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserModificationCountReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetUserModificationCountResp struct {
	Count uint32 `protobuf:"varint,1,opt,name=count" json:"count"`
}

func (m *GetUserModificationCountResp) Reset()         { *m = GetUserModificationCountResp{} }
func (m *GetUserModificationCountResp) String() string { return proto.CompactTextString(m) }
func (*GetUserModificationCountResp) ProtoMessage()    {}
func (*GetUserModificationCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{3}
}

func (m *GetUserModificationCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DeleteUserModificationCountReq struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type uint32 `protobuf:"varint,2,req,name=type" json:"type"`
}

func (m *DeleteUserModificationCountReq) Reset()         { *m = DeleteUserModificationCountReq{} }
func (m *DeleteUserModificationCountReq) String() string { return proto.CompactTextString(m) }
func (*DeleteUserModificationCountReq) ProtoMessage()    {}
func (*DeleteUserModificationCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{4}
}

func (m *DeleteUserModificationCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteUserModificationCountReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type DeleteUserModificationCountResp struct {
}

func (m *DeleteUserModificationCountResp) Reset()         { *m = DeleteUserModificationCountResp{} }
func (m *DeleteUserModificationCountResp) String() string { return proto.CompactTextString(m) }
func (*DeleteUserModificationCountResp) ProtoMessage()    {}
func (*DeleteUserModificationCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{5}
}

type BatchDelUserModificationCountReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	Type    uint32   `protobuf:"varint,2,req,name=type" json:"type"`
}

func (m *BatchDelUserModificationCountReq) Reset()         { *m = BatchDelUserModificationCountReq{} }
func (m *BatchDelUserModificationCountReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserModificationCountReq) ProtoMessage()    {}
func (*BatchDelUserModificationCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{6}
}

func (m *BatchDelUserModificationCountReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchDelUserModificationCountReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type BatchDelUserModificationCountResp struct {
}

func (m *BatchDelUserModificationCountResp) Reset()         { *m = BatchDelUserModificationCountResp{} }
func (m *BatchDelUserModificationCountResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelUserModificationCountResp) ProtoMessage()    {}
func (*BatchDelUserModificationCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUsermodification, []int{7}
}

func init() {
	proto.RegisterType((*UpdateUserModificationCountReq)(nil), "usermodification.UpdateUserModificationCountReq")
	proto.RegisterType((*UpdateUserModificationCountResp)(nil), "usermodification.UpdateUserModificationCountResp")
	proto.RegisterType((*GetUserModificationCountReq)(nil), "usermodification.GetUserModificationCountReq")
	proto.RegisterType((*GetUserModificationCountResp)(nil), "usermodification.GetUserModificationCountResp")
	proto.RegisterType((*DeleteUserModificationCountReq)(nil), "usermodification.DeleteUserModificationCountReq")
	proto.RegisterType((*DeleteUserModificationCountResp)(nil), "usermodification.DeleteUserModificationCountResp")
	proto.RegisterType((*BatchDelUserModificationCountReq)(nil), "usermodification.BatchDelUserModificationCountReq")
	proto.RegisterType((*BatchDelUserModificationCountResp)(nil), "usermodification.BatchDelUserModificationCountResp")
	proto.RegisterEnum("usermodification.MODIFY_TYPE", MODIFY_TYPE_name, MODIFY_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for UserModification service

type UserModificationClient interface {
	UpdateUserModificationCount(ctx context.Context, in *UpdateUserModificationCountReq, opts ...grpc.CallOption) (*UpdateUserModificationCountResp, error)
	GetUserModificationCount(ctx context.Context, in *GetUserModificationCountReq, opts ...grpc.CallOption) (*GetUserModificationCountResp, error)
	DeleteUserModificationCount(ctx context.Context, in *DeleteUserModificationCountReq, opts ...grpc.CallOption) (*DeleteUserModificationCountResp, error)
	BatchDelUserModificationCount(ctx context.Context, in *BatchDelUserModificationCountReq, opts ...grpc.CallOption) (*BatchDelUserModificationCountResp, error)
}

type userModificationClient struct {
	cc *grpc.ClientConn
}

func NewUserModificationClient(cc *grpc.ClientConn) UserModificationClient {
	return &userModificationClient{cc}
}

func (c *userModificationClient) UpdateUserModificationCount(ctx context.Context, in *UpdateUserModificationCountReq, opts ...grpc.CallOption) (*UpdateUserModificationCountResp, error) {
	out := new(UpdateUserModificationCountResp)
	err := grpc.Invoke(ctx, "/usermodification.UserModification/UpdateUserModificationCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userModificationClient) GetUserModificationCount(ctx context.Context, in *GetUserModificationCountReq, opts ...grpc.CallOption) (*GetUserModificationCountResp, error) {
	out := new(GetUserModificationCountResp)
	err := grpc.Invoke(ctx, "/usermodification.UserModification/GetUserModificationCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userModificationClient) DeleteUserModificationCount(ctx context.Context, in *DeleteUserModificationCountReq, opts ...grpc.CallOption) (*DeleteUserModificationCountResp, error) {
	out := new(DeleteUserModificationCountResp)
	err := grpc.Invoke(ctx, "/usermodification.UserModification/DeleteUserModificationCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userModificationClient) BatchDelUserModificationCount(ctx context.Context, in *BatchDelUserModificationCountReq, opts ...grpc.CallOption) (*BatchDelUserModificationCountResp, error) {
	out := new(BatchDelUserModificationCountResp)
	err := grpc.Invoke(ctx, "/usermodification.UserModification/BatchDelUserModificationCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for UserModification service

type UserModificationServer interface {
	UpdateUserModificationCount(context.Context, *UpdateUserModificationCountReq) (*UpdateUserModificationCountResp, error)
	GetUserModificationCount(context.Context, *GetUserModificationCountReq) (*GetUserModificationCountResp, error)
	DeleteUserModificationCount(context.Context, *DeleteUserModificationCountReq) (*DeleteUserModificationCountResp, error)
	BatchDelUserModificationCount(context.Context, *BatchDelUserModificationCountReq) (*BatchDelUserModificationCountResp, error)
}

func RegisterUserModificationServer(s *grpc.Server, srv UserModificationServer) {
	s.RegisterService(&_UserModification_serviceDesc, srv)
}

func _UserModification_UpdateUserModificationCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserModificationCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserModificationServer).UpdateUserModificationCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usermodification.UserModification/UpdateUserModificationCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserModificationServer).UpdateUserModificationCount(ctx, req.(*UpdateUserModificationCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserModification_GetUserModificationCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserModificationCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserModificationServer).GetUserModificationCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usermodification.UserModification/GetUserModificationCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserModificationServer).GetUserModificationCount(ctx, req.(*GetUserModificationCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserModification_DeleteUserModificationCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserModificationCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserModificationServer).DeleteUserModificationCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usermodification.UserModification/DeleteUserModificationCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserModificationServer).DeleteUserModificationCount(ctx, req.(*DeleteUserModificationCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserModification_BatchDelUserModificationCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelUserModificationCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserModificationServer).BatchDelUserModificationCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/usermodification.UserModification/BatchDelUserModificationCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserModificationServer).BatchDelUserModificationCount(ctx, req.(*BatchDelUserModificationCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserModification_serviceDesc = grpc.ServiceDesc{
	ServiceName: "usermodification.UserModification",
	HandlerType: (*UserModificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateUserModificationCount",
			Handler:    _UserModification_UpdateUserModificationCount_Handler,
		},
		{
			MethodName: "GetUserModificationCount",
			Handler:    _UserModification_GetUserModificationCount_Handler,
		},
		{
			MethodName: "DeleteUserModificationCount",
			Handler:    _UserModification_DeleteUserModificationCount_Handler,
		},
		{
			MethodName: "BatchDelUserModificationCount",
			Handler:    _UserModification_BatchDelUserModificationCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/usermodification/usermodification.proto",
}

func (m *UpdateUserModificationCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserModificationCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *UpdateUserModificationCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserModificationCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserModificationCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserModificationCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetUserModificationCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserModificationCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *DeleteUserModificationCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteUserModificationCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *DeleteUserModificationCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteUserModificationCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchDelUserModificationCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDelUserModificationCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintUsermodification(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUsermodification(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *BatchDelUserModificationCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDelUserModificationCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Usermodification(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Usermodification(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUsermodification(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UpdateUserModificationCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUsermodification(uint64(m.Uid))
	n += 1 + sovUsermodification(uint64(m.Type))
	return n
}

func (m *UpdateUserModificationCountResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserModificationCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUsermodification(uint64(m.Uid))
	n += 1 + sovUsermodification(uint64(m.Type))
	return n
}

func (m *GetUserModificationCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUsermodification(uint64(m.Count))
	return n
}

func (m *DeleteUserModificationCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUsermodification(uint64(m.Uid))
	n += 1 + sovUsermodification(uint64(m.Type))
	return n
}

func (m *DeleteUserModificationCountResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchDelUserModificationCountReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovUsermodification(uint64(e))
		}
	}
	n += 1 + sovUsermodification(uint64(m.Type))
	return n
}

func (m *BatchDelUserModificationCountResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovUsermodification(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUsermodification(x uint64) (n int) {
	return sovUsermodification(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *UpdateUserModificationCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserModificationCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserModificationCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserModificationCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserModificationCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserModificationCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserModificationCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserModificationCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserModificationCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserModificationCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserModificationCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserModificationCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteUserModificationCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteUserModificationCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteUserModificationCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteUserModificationCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteUserModificationCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteUserModificationCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDelUserModificationCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDelUserModificationCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDelUserModificationCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsermodification
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowUsermodification
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthUsermodification
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowUsermodification
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDelUserModificationCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDelUserModificationCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDelUserModificationCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipUsermodification(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUsermodification
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUsermodification(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUsermodification
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUsermodification
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUsermodification
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUsermodification
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUsermodification(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUsermodification = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUsermodification   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/usermodification/usermodification.proto", fileDescriptorUsermodification)
}

var fileDescriptorUsermodification = []byte{
	// 459 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0x2e, 0x2e, 0x4a, 0xd6,
	0x2f, 0x2d, 0x4e, 0x2d, 0xca, 0xcd, 0x4f, 0xc9, 0x4c, 0xcb, 0x4c, 0x4e, 0x2c, 0xc9, 0xcc, 0xcf,
	0xc3, 0x10, 0xd0, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x40, 0x17, 0x97, 0x52, 0x49, 0xce,
	0xcf, 0xcd, 0xcd, 0xcf, 0xd3, 0x2f, 0xc9, 0x29, 0x2b, 0xc8, 0x4c, 0xce, 0xce, 0x49, 0xd5, 0x2f,
	0xce, 0x4e, 0x2a, 0xcd, 0xcc, 0x29, 0xc9, 0xcc, 0x2b, 0xa9, 0x2c, 0x48, 0x85, 0xe8, 0x53, 0x0a,
	0xe2, 0x92, 0x0b, 0x2d, 0x48, 0x49, 0x2c, 0x49, 0x0d, 0x2d, 0x4e, 0x2d, 0xf2, 0x45, 0xd2, 0xef,
	0x9c, 0x5f, 0x9a, 0x57, 0x12, 0x94, 0x5a, 0x28, 0x24, 0xc6, 0xc5, 0x5c, 0x9a, 0x99, 0x22, 0xc1,
	0xa8, 0xc0, 0xa4, 0xc1, 0xeb, 0xc4, 0x72, 0xe2, 0x9e, 0x3c, 0x43, 0x10, 0x48, 0x40, 0x48, 0x82,
	0x8b, 0x05, 0x64, 0x8e, 0x04, 0x13, 0x92, 0x04, 0x58, 0x44, 0x49, 0x91, 0x4b, 0x1e, 0xaf, 0x99,
	0xc5, 0x05, 0x4a, 0xfe, 0x5c, 0xd2, 0xee, 0xa9, 0x25, 0x54, 0xb4, 0xd3, 0x8a, 0x4b, 0x06, 0xb7,
	0x81, 0xc5, 0x05, 0x42, 0x52, 0x5c, 0xac, 0xc9, 0x20, 0x8e, 0x04, 0xa3, 0x02, 0x23, 0x5c, 0x2b,
	0x44, 0x08, 0x14, 0x06, 0x2e, 0xa9, 0x39, 0xa9, 0xd4, 0x0e, 0x03, 0xbc, 0x66, 0x16, 0x17, 0x28,
	0x85, 0x73, 0x29, 0x38, 0x25, 0x96, 0x24, 0x67, 0xb8, 0xa4, 0xe6, 0xe0, 0xb4, 0x58, 0x92, 0x8b,
	0xa3, 0x34, 0x33, 0x25, 0x3e, 0x27, 0xb3, 0x18, 0xe4, 0x72, 0x66, 0x0d, 0xde, 0x20, 0xf6, 0xd2,
	0xcc, 0x14, 0x9f, 0xcc, 0xe2, 0x12, 0x3c, 0x76, 0x2b, 0x73, 0x29, 0x12, 0x30, 0xb8, 0xb8, 0x40,
	0x4b, 0x8c, 0x8b, 0xdb, 0xd7, 0xdf, 0xc5, 0xd3, 0x2d, 0x32, 0x3e, 0x24, 0x32, 0xc0, 0x55, 0x88,
	0x9d, 0x8b, 0x39, 0xd8, 0x35, 0x42, 0x80, 0xd1, 0xe8, 0x2c, 0x2b, 0x97, 0x00, 0xba, 0x2e, 0xa1,
	0x95, 0x8c, 0x5c, 0xd2, 0x78, 0xa2, 0x54, 0xc8, 0x40, 0x0f, 0x23, 0x59, 0xe2, 0x4f, 0x55, 0x52,
	0x86, 0x24, 0xea, 0x28, 0x2e, 0x50, 0x52, 0x6d, 0x58, 0xf2, 0x82, 0x99, 0xb1, 0x6b, 0xc9, 0x0b,
	0x66, 0x96, 0x52, 0xab, 0x12, 0xab, 0x49, 0x4b, 0x5e, 0x30, 0x0b, 0xe9, 0x96, 0x2a, 0xd8, 0x94,
	0x66, 0xa6, 0xd8, 0x29, 0xe8, 0x96, 0x28, 0xd8, 0x80, 0x3c, 0x6f, 0x27, 0xb4, 0x80, 0x91, 0x4b,
	0x02, 0x57, 0x52, 0x10, 0xd2, 0xc5, 0xb4, 0x16, 0x4f, 0x3a, 0x94, 0xd2, 0x23, 0x45, 0x39, 0xcc,
	0x89, 0x4c, 0x04, 0x9d, 0x08, 0x0a, 0x4e, 0x3c, 0xa9, 0x03, 0x5b, 0x70, 0xe2, 0x4f, 0xa0, 0xd8,
	0x82, 0x93, 0x50, 0xf2, 0x03, 0xbb, 0x95, 0x99, 0xa0, 0x5b, 0xb7, 0x32, 0x72, 0xc9, 0xe2, 0x4d,
	0x4d, 0x42, 0x46, 0x98, 0x76, 0x13, 0x4a, 0xd7, 0x52, 0xc6, 0x24, 0xeb, 0x29, 0x2e, 0x50, 0xd2,
	0x06, 0xb9, 0x98, 0x05, 0xc5, 0xc5, 0x12, 0x50, 0x17, 0x1b, 0xea, 0x94, 0x66, 0xa6, 0x18, 0x21,
	0xb9, 0x5b, 0x8a, 0xad, 0x63, 0xc9, 0x0b, 0xe6, 0x4b, 0x59, 0x4e, 0x02, 0x27, 0x1e, 0xc9, 0x31,
	0x5e, 0x78, 0x24, 0xc7, 0xf8, 0xe0, 0x91, 0x1c, 0xe3, 0x84, 0xc7, 0x72, 0x0c, 0x80, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xf5, 0xb8, 0x27, 0x6e, 0x58, 0x05, 0x00, 0x00,
}
