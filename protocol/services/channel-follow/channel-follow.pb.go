// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-follow/channel-follow.proto

package channel_follow // import "golang.52tt.com/protocol/services/channel-follow"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/revenuenameplate"
import channel "golang.52tt.com/protocol/services/topic_channel/channel"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// ******************统一跟随过滤逻辑********************
type E_ONLINE_STATUS int32

const (
	E_ONLINE_STATUS_E_OFFLINE E_ONLINE_STATUS = 0
	E_ONLINE_STATUS_E_ONLINE  E_ONLINE_STATUS = 1
)

var E_ONLINE_STATUS_name = map[int32]string{
	0: "E_OFFLINE",
	1: "E_ONLINE",
}
var E_ONLINE_STATUS_value = map[string]int32{
	"E_OFFLINE": 0,
	"E_ONLINE":  1,
}

func (x E_ONLINE_STATUS) String() string {
	return proto.EnumName(E_ONLINE_STATUS_name, int32(x))
}
func (E_ONLINE_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{0}
}

// NameplateType 用户铭牌资源类型
type NameplateType int32

const (
	NameplateType_TYPE_ERROR  NameplateType = 0
	NameplateType_TYPE_LOTTER NameplateType = 1
)

var NameplateType_name = map[int32]string{
	0: "TYPE_ERROR",
	1: "TYPE_LOTTER",
}
var NameplateType_value = map[string]int32{
	"TYPE_ERROR":  0,
	"TYPE_LOTTER": 1,
}

func (x NameplateType) String() string {
	return proto.EnumName(NameplateType_name, int32(x))
}
func (NameplateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{1}
}

type EOnlineEventType int32

const (
	EOnlineEventType_ENUM_OL_EVENT_OFFLINE EOnlineEventType = 0
	EOnlineEventType_ENUM_OL_EVENT_ONLINE  EOnlineEventType = 1
	EOnlineEventType_ENUM_OL_EVENT_UNKNOW  EOnlineEventType = 2
)

var EOnlineEventType_name = map[int32]string{
	0: "ENUM_OL_EVENT_OFFLINE",
	1: "ENUM_OL_EVENT_ONLINE",
	2: "ENUM_OL_EVENT_UNKNOW",
}
var EOnlineEventType_value = map[string]int32{
	"ENUM_OL_EVENT_OFFLINE": 0,
	"ENUM_OL_EVENT_ONLINE":  1,
	"ENUM_OL_EVENT_UNKNOW":  2,
}

func (x EOnlineEventType) String() string {
	return proto.EnumName(EOnlineEventType_name, int32(x))
}
func (EOnlineEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{2}
}

type BatchGetTopNCntReq_TypeShip int32

const (
	BatchGetTopNCntReq_friend BatchGetTopNCntReq_TypeShip = 0
	BatchGetTopNCntReq_follow BatchGetTopNCntReq_TypeShip = 1
)

var BatchGetTopNCntReq_TypeShip_name = map[int32]string{
	0: "friend",
	1: "follow",
}
var BatchGetTopNCntReq_TypeShip_value = map[string]int32{
	"friend": 0,
	"follow": 1,
}

func (x BatchGetTopNCntReq_TypeShip) String() string {
	return proto.EnumName(BatchGetTopNCntReq_TypeShip_name, int32(x))
}
func (BatchGetTopNCntReq_TypeShip) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{27, 0}
}

type BatchUpdateUserChannelFollowReq_EUpdateType int32

const (
	BatchUpdateUserChannelFollowReq_E_UnKown BatchUpdateUserChannelFollowReq_EUpdateType = 0
	BatchUpdateUserChannelFollowReq_E_Add    BatchUpdateUserChannelFollowReq_EUpdateType = 1
	BatchUpdateUserChannelFollowReq_E_Del    BatchUpdateUserChannelFollowReq_EUpdateType = 2
)

var BatchUpdateUserChannelFollowReq_EUpdateType_name = map[int32]string{
	0: "E_UnKown",
	1: "E_Add",
	2: "E_Del",
}
var BatchUpdateUserChannelFollowReq_EUpdateType_value = map[string]int32{
	"E_UnKown": 0,
	"E_Add":    1,
	"E_Del":    2,
}

func (x BatchUpdateUserChannelFollowReq_EUpdateType) String() string {
	return proto.EnumName(BatchUpdateUserChannelFollowReq_EUpdateType_name, int32(x))
}
func (BatchUpdateUserChannelFollowReq_EUpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{48, 0}
}

type DelUserChannelFollowListReq struct {
	HandleUid            uint32   `protobuf:"varint,1,opt,name=handle_uid,json=handleUid,proto3" json:"handle_uid,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserChannelFollowListReq) Reset()         { *m = DelUserChannelFollowListReq{} }
func (m *DelUserChannelFollowListReq) String() string { return proto.CompactTextString(m) }
func (*DelUserChannelFollowListReq) ProtoMessage()    {}
func (*DelUserChannelFollowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{0}
}
func (m *DelUserChannelFollowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserChannelFollowListReq.Unmarshal(m, b)
}
func (m *DelUserChannelFollowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserChannelFollowListReq.Marshal(b, m, deterministic)
}
func (dst *DelUserChannelFollowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserChannelFollowListReq.Merge(dst, src)
}
func (m *DelUserChannelFollowListReq) XXX_Size() int {
	return xxx_messageInfo_DelUserChannelFollowListReq.Size(m)
}
func (m *DelUserChannelFollowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserChannelFollowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserChannelFollowListReq proto.InternalMessageInfo

func (m *DelUserChannelFollowListReq) GetHandleUid() uint32 {
	if m != nil {
		return m.HandleUid
	}
	return 0
}

func (m *DelUserChannelFollowListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type DelUserChannelFollowListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserChannelFollowListResp) Reset()         { *m = DelUserChannelFollowListResp{} }
func (m *DelUserChannelFollowListResp) String() string { return proto.CompactTextString(m) }
func (*DelUserChannelFollowListResp) ProtoMessage()    {}
func (*DelUserChannelFollowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{1}
}
func (m *DelUserChannelFollowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserChannelFollowListResp.Unmarshal(m, b)
}
func (m *DelUserChannelFollowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserChannelFollowListResp.Marshal(b, m, deterministic)
}
func (dst *DelUserChannelFollowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserChannelFollowListResp.Merge(dst, src)
}
func (m *DelUserChannelFollowListResp) XXX_Size() int {
	return xxx_messageInfo_DelUserChannelFollowListResp.Size(m)
}
func (m *DelUserChannelFollowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserChannelFollowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserChannelFollowListResp proto.InternalMessageInfo

type UpdateUserInChannelCacheReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserInChannelCacheReq) Reset()         { *m = UpdateUserInChannelCacheReq{} }
func (m *UpdateUserInChannelCacheReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserInChannelCacheReq) ProtoMessage()    {}
func (*UpdateUserInChannelCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{2}
}
func (m *UpdateUserInChannelCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserInChannelCacheReq.Unmarshal(m, b)
}
func (m *UpdateUserInChannelCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserInChannelCacheReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserInChannelCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserInChannelCacheReq.Merge(dst, src)
}
func (m *UpdateUserInChannelCacheReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserInChannelCacheReq.Size(m)
}
func (m *UpdateUserInChannelCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserInChannelCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserInChannelCacheReq proto.InternalMessageInfo

func (m *UpdateUserInChannelCacheReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserInChannelCacheReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UpdateUserInChannelCacheResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserInChannelCacheResp) Reset()         { *m = UpdateUserInChannelCacheResp{} }
func (m *UpdateUserInChannelCacheResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserInChannelCacheResp) ProtoMessage()    {}
func (*UpdateUserInChannelCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{3}
}
func (m *UpdateUserInChannelCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserInChannelCacheResp.Unmarshal(m, b)
}
func (m *UpdateUserInChannelCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserInChannelCacheResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserInChannelCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserInChannelCacheResp.Merge(dst, src)
}
func (m *UpdateUserInChannelCacheResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserInChannelCacheResp.Size(m)
}
func (m *UpdateUserInChannelCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserInChannelCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserInChannelCacheResp proto.InternalMessageInfo

type UpdateUgcChannelCacheTabReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tabId,proto3" json:"tabId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUgcChannelCacheTabReq) Reset()         { *m = UpdateUgcChannelCacheTabReq{} }
func (m *UpdateUgcChannelCacheTabReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUgcChannelCacheTabReq) ProtoMessage()    {}
func (*UpdateUgcChannelCacheTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{4}
}
func (m *UpdateUgcChannelCacheTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUgcChannelCacheTabReq.Unmarshal(m, b)
}
func (m *UpdateUgcChannelCacheTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUgcChannelCacheTabReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUgcChannelCacheTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUgcChannelCacheTabReq.Merge(dst, src)
}
func (m *UpdateUgcChannelCacheTabReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUgcChannelCacheTabReq.Size(m)
}
func (m *UpdateUgcChannelCacheTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUgcChannelCacheTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUgcChannelCacheTabReq proto.InternalMessageInfo

func (m *UpdateUgcChannelCacheTabReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUgcChannelCacheTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type UpdateUgcChannelCacheTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUgcChannelCacheTabResp) Reset()         { *m = UpdateUgcChannelCacheTabResp{} }
func (m *UpdateUgcChannelCacheTabResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUgcChannelCacheTabResp) ProtoMessage()    {}
func (*UpdateUgcChannelCacheTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{5}
}
func (m *UpdateUgcChannelCacheTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUgcChannelCacheTabResp.Unmarshal(m, b)
}
func (m *UpdateUgcChannelCacheTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUgcChannelCacheTabResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUgcChannelCacheTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUgcChannelCacheTabResp.Merge(dst, src)
}
func (m *UpdateUgcChannelCacheTabResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUgcChannelCacheTabResp.Size(m)
}
func (m *UpdateUgcChannelCacheTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUgcChannelCacheTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUgcChannelCacheTabResp proto.InternalMessageInfo

type UpdateUgcChannelCacheDisplayReq struct {
	ChannelId            uint32                       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DisplayTypeList      []channel.ChannelDisplayType `protobuf:"varint,2,rep,packed,name=display_type_list,json=displayTypeList,proto3,enum=topic_channel.channel.ChannelDisplayType" json:"display_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UpdateUgcChannelCacheDisplayReq) Reset()         { *m = UpdateUgcChannelCacheDisplayReq{} }
func (m *UpdateUgcChannelCacheDisplayReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUgcChannelCacheDisplayReq) ProtoMessage()    {}
func (*UpdateUgcChannelCacheDisplayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{6}
}
func (m *UpdateUgcChannelCacheDisplayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUgcChannelCacheDisplayReq.Unmarshal(m, b)
}
func (m *UpdateUgcChannelCacheDisplayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUgcChannelCacheDisplayReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUgcChannelCacheDisplayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUgcChannelCacheDisplayReq.Merge(dst, src)
}
func (m *UpdateUgcChannelCacheDisplayReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUgcChannelCacheDisplayReq.Size(m)
}
func (m *UpdateUgcChannelCacheDisplayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUgcChannelCacheDisplayReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUgcChannelCacheDisplayReq proto.InternalMessageInfo

func (m *UpdateUgcChannelCacheDisplayReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateUgcChannelCacheDisplayReq) GetDisplayTypeList() []channel.ChannelDisplayType {
	if m != nil {
		return m.DisplayTypeList
	}
	return nil
}

type UpdateUgcChannelCacheDisplayResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUgcChannelCacheDisplayResp) Reset()         { *m = UpdateUgcChannelCacheDisplayResp{} }
func (m *UpdateUgcChannelCacheDisplayResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUgcChannelCacheDisplayResp) ProtoMessage()    {}
func (*UpdateUgcChannelCacheDisplayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{7}
}
func (m *UpdateUgcChannelCacheDisplayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUgcChannelCacheDisplayResp.Unmarshal(m, b)
}
func (m *UpdateUgcChannelCacheDisplayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUgcChannelCacheDisplayResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUgcChannelCacheDisplayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUgcChannelCacheDisplayResp.Merge(dst, src)
}
func (m *UpdateUgcChannelCacheDisplayResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUgcChannelCacheDisplayResp.Size(m)
}
func (m *UpdateUgcChannelCacheDisplayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUgcChannelCacheDisplayResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUgcChannelCacheDisplayResp proto.InternalMessageInfo

type GetChannelFollowInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelFollowInfoReq) Reset()         { *m = GetChannelFollowInfoReq{} }
func (m *GetChannelFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelFollowInfoReq) ProtoMessage()    {}
func (*GetChannelFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{8}
}
func (m *GetChannelFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFollowInfoReq.Unmarshal(m, b)
}
func (m *GetChannelFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFollowInfoReq.Merge(dst, src)
}
func (m *GetChannelFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFollowInfoReq.Size(m)
}
func (m *GetChannelFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFollowInfoReq proto.InternalMessageInfo

func (m *GetChannelFollowInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelFollowInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetChannelFollowInfoResp struct {
	Info                 *RoomInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetChannelFollowInfoResp) Reset()         { *m = GetChannelFollowInfoResp{} }
func (m *GetChannelFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelFollowInfoResp) ProtoMessage()    {}
func (*GetChannelFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{9}
}
func (m *GetChannelFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFollowInfoResp.Unmarshal(m, b)
}
func (m *GetChannelFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFollowInfoResp.Merge(dst, src)
}
func (m *GetChannelFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFollowInfoResp.Size(m)
}
func (m *GetChannelFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFollowInfoResp proto.InternalMessageInfo

func (m *GetChannelFollowInfoResp) GetInfo() *RoomInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type RoomInfo struct {
	RoomId               uint32   `protobuf:"varint,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	RoomType             uint32   `protobuf:"varint,2,opt,name=room_type,json=roomType,proto3" json:"room_type,omitempty"`
	BindId               uint32   `protobuf:"varint,3,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	IsLock               bool     `protobuf:"varint,4,opt,name=is_lock,json=isLock,proto3" json:"is_lock,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomInfo) Reset()         { *m = RoomInfo{} }
func (m *RoomInfo) String() string { return proto.CompactTextString(m) }
func (*RoomInfo) ProtoMessage()    {}
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{10}
}
func (m *RoomInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomInfo.Unmarshal(m, b)
}
func (m *RoomInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomInfo.Marshal(b, m, deterministic)
}
func (dst *RoomInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomInfo.Merge(dst, src)
}
func (m *RoomInfo) XXX_Size() int {
	return xxx_messageInfo_RoomInfo.Size(m)
}
func (m *RoomInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RoomInfo proto.InternalMessageInfo

func (m *RoomInfo) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *RoomInfo) GetRoomType() uint32 {
	if m != nil {
		return m.RoomType
	}
	return 0
}

func (m *RoomInfo) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

func (m *RoomInfo) GetIsLock() bool {
	if m != nil {
		return m.IsLock
	}
	return false
}

type BatchGetChannelFollowInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUids           []uint32 `protobuf:"varint,2,rep,packed,name=target_uids,json=targetUids,proto3" json:"target_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelFollowInfoReq) Reset()         { *m = BatchGetChannelFollowInfoReq{} }
func (m *BatchGetChannelFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelFollowInfoReq) ProtoMessage()    {}
func (*BatchGetChannelFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{11}
}
func (m *BatchGetChannelFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelFollowInfoReq.Unmarshal(m, b)
}
func (m *BatchGetChannelFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelFollowInfoReq.Merge(dst, src)
}
func (m *BatchGetChannelFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelFollowInfoReq.Size(m)
}
func (m *BatchGetChannelFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelFollowInfoReq proto.InternalMessageInfo

func (m *BatchGetChannelFollowInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetChannelFollowInfoReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type BatchGetChannelFollowInfoResp struct {
	Infos                map[uint32]*RoomInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetChannelFollowInfoResp) Reset()         { *m = BatchGetChannelFollowInfoResp{} }
func (m *BatchGetChannelFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelFollowInfoResp) ProtoMessage()    {}
func (*BatchGetChannelFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{12}
}
func (m *BatchGetChannelFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelFollowInfoResp.Unmarshal(m, b)
}
func (m *BatchGetChannelFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelFollowInfoResp.Merge(dst, src)
}
func (m *BatchGetChannelFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelFollowInfoResp.Size(m)
}
func (m *BatchGetChannelFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelFollowInfoResp proto.InternalMessageInfo

func (m *BatchGetChannelFollowInfoResp) GetInfos() map[uint32]*RoomInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

// FollowChannelAuthSwitchType
type UpdateFollowChannelAuthSwitchReq struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FollowChannelAuthType uint32   `protobuf:"varint,2,opt,name=follow_channel_auth_type,json=followChannelAuthType,proto3" json:"follow_channel_auth_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UpdateFollowChannelAuthSwitchReq) Reset()         { *m = UpdateFollowChannelAuthSwitchReq{} }
func (m *UpdateFollowChannelAuthSwitchReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthSwitchReq) ProtoMessage()    {}
func (*UpdateFollowChannelAuthSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{13}
}
func (m *UpdateFollowChannelAuthSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFollowChannelAuthSwitchReq.Unmarshal(m, b)
}
func (m *UpdateFollowChannelAuthSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFollowChannelAuthSwitchReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFollowChannelAuthSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFollowChannelAuthSwitchReq.Merge(dst, src)
}
func (m *UpdateFollowChannelAuthSwitchReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFollowChannelAuthSwitchReq.Size(m)
}
func (m *UpdateFollowChannelAuthSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFollowChannelAuthSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFollowChannelAuthSwitchReq proto.InternalMessageInfo

func (m *UpdateFollowChannelAuthSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFollowChannelAuthSwitchReq) GetFollowChannelAuthType() uint32 {
	if m != nil {
		return m.FollowChannelAuthType
	}
	return 0
}

type UpdateFollowChannelAuthSwitchResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFollowChannelAuthSwitchResp) Reset()         { *m = UpdateFollowChannelAuthSwitchResp{} }
func (m *UpdateFollowChannelAuthSwitchResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthSwitchResp) ProtoMessage()    {}
func (*UpdateFollowChannelAuthSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{14}
}
func (m *UpdateFollowChannelAuthSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFollowChannelAuthSwitchResp.Unmarshal(m, b)
}
func (m *UpdateFollowChannelAuthSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFollowChannelAuthSwitchResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFollowChannelAuthSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFollowChannelAuthSwitchResp.Merge(dst, src)
}
func (m *UpdateFollowChannelAuthSwitchResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFollowChannelAuthSwitchResp.Size(m)
}
func (m *UpdateFollowChannelAuthSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFollowChannelAuthSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFollowChannelAuthSwitchResp proto.InternalMessageInfo

type GetChannelTabMapReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelTabMapReq) Reset()         { *m = GetChannelTabMapReq{} }
func (m *GetChannelTabMapReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTabMapReq) ProtoMessage()    {}
func (*GetChannelTabMapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{15}
}
func (m *GetChannelTabMapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTabMapReq.Unmarshal(m, b)
}
func (m *GetChannelTabMapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTabMapReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTabMapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTabMapReq.Merge(dst, src)
}
func (m *GetChannelTabMapReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTabMapReq.Size(m)
}
func (m *GetChannelTabMapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTabMapReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTabMapReq proto.InternalMessageInfo

func (m *GetChannelTabMapReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GetChannelTabMapResp struct {
	ChannelTabMap        map[uint32]*ChannelTab `protobuf:"bytes,1,rep,name=channel_tab_map,json=channelTabMap,proto3" json:"channel_tab_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetChannelTabMapResp) Reset()         { *m = GetChannelTabMapResp{} }
func (m *GetChannelTabMapResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTabMapResp) ProtoMessage()    {}
func (*GetChannelTabMapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{16}
}
func (m *GetChannelTabMapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTabMapResp.Unmarshal(m, b)
}
func (m *GetChannelTabMapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTabMapResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTabMapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTabMapResp.Merge(dst, src)
}
func (m *GetChannelTabMapResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTabMapResp.Size(m)
}
func (m *GetChannelTabMapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTabMapResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTabMapResp proto.InternalMessageInfo

func (m *GetChannelTabMapResp) GetChannelTabMap() map[uint32]*ChannelTab {
	if m != nil {
		return m.ChannelTabMap
	}
	return nil
}

type ChannelTab struct {
	ChannelRoomType      string                       `protobuf:"bytes,1,opt,name=channel_room_type,json=channelRoomType,proto3" json:"channel_room_type,omitempty"`
	FindPlayingText      string                       `protobuf:"bytes,2,opt,name=find_playing_text,json=findPlayingText,proto3" json:"find_playing_text,omitempty"`
	FindPlayingImg       string                       `protobuf:"bytes,3,opt,name=find_playing_img,json=findPlayingImg,proto3" json:"find_playing_img,omitempty"`
	DisplayType          []channel.ChannelDisplayType `protobuf:"varint,4,rep,packed,name=display_type,json=displayType,proto3,enum=topic_channel.channel.ChannelDisplayType" json:"display_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ChannelTab) Reset()         { *m = ChannelTab{} }
func (m *ChannelTab) String() string { return proto.CompactTextString(m) }
func (*ChannelTab) ProtoMessage()    {}
func (*ChannelTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{17}
}
func (m *ChannelTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTab.Unmarshal(m, b)
}
func (m *ChannelTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTab.Marshal(b, m, deterministic)
}
func (dst *ChannelTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTab.Merge(dst, src)
}
func (m *ChannelTab) XXX_Size() int {
	return xxx_messageInfo_ChannelTab.Size(m)
}
func (m *ChannelTab) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTab.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTab proto.InternalMessageInfo

func (m *ChannelTab) GetChannelRoomType() string {
	if m != nil {
		return m.ChannelRoomType
	}
	return ""
}

func (m *ChannelTab) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

func (m *ChannelTab) GetFindPlayingImg() string {
	if m != nil {
		return m.FindPlayingImg
	}
	return ""
}

func (m *ChannelTab) GetDisplayType() []channel.ChannelDisplayType {
	if m != nil {
		return m.DisplayType
	}
	return nil
}

type FollowCntInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FollowCnt            uint32   `protobuf:"varint,2,opt,name=follow_cnt,json=followCnt,proto3" json:"follow_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowCntInfo) Reset()         { *m = FollowCntInfo{} }
func (m *FollowCntInfo) String() string { return proto.CompactTextString(m) }
func (*FollowCntInfo) ProtoMessage()    {}
func (*FollowCntInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{18}
}
func (m *FollowCntInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowCntInfo.Unmarshal(m, b)
}
func (m *FollowCntInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowCntInfo.Marshal(b, m, deterministic)
}
func (dst *FollowCntInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowCntInfo.Merge(dst, src)
}
func (m *FollowCntInfo) XXX_Size() int {
	return xxx_messageInfo_FollowCntInfo.Size(m)
}
func (m *FollowCntInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowCntInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FollowCntInfo proto.InternalMessageInfo

func (m *FollowCntInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FollowCntInfo) GetFollowCnt() uint32 {
	if m != nil {
		return m.FollowCnt
	}
	return 0
}

type GetFollowCntListReq struct {
	OwnerUid             uint32   `protobuf:"varint,1,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	FollowUids           []uint32 `protobuf:"varint,2,rep,packed,name=follow_uids,json=followUids,proto3" json:"follow_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFollowCntListReq) Reset()         { *m = GetFollowCntListReq{} }
func (m *GetFollowCntListReq) String() string { return proto.CompactTextString(m) }
func (*GetFollowCntListReq) ProtoMessage()    {}
func (*GetFollowCntListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{19}
}
func (m *GetFollowCntListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFollowCntListReq.Unmarshal(m, b)
}
func (m *GetFollowCntListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFollowCntListReq.Marshal(b, m, deterministic)
}
func (dst *GetFollowCntListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFollowCntListReq.Merge(dst, src)
}
func (m *GetFollowCntListReq) XXX_Size() int {
	return xxx_messageInfo_GetFollowCntListReq.Size(m)
}
func (m *GetFollowCntListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFollowCntListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFollowCntListReq proto.InternalMessageInfo

func (m *GetFollowCntListReq) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *GetFollowCntListReq) GetFollowUids() []uint32 {
	if m != nil {
		return m.FollowUids
	}
	return nil
}

type GetFollowCntListResp struct {
	FollowData           []*FollowCntInfo `protobuf:"bytes,1,rep,name=follow_data,json=followData,proto3" json:"follow_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetFollowCntListResp) Reset()         { *m = GetFollowCntListResp{} }
func (m *GetFollowCntListResp) String() string { return proto.CompactTextString(m) }
func (*GetFollowCntListResp) ProtoMessage()    {}
func (*GetFollowCntListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{20}
}
func (m *GetFollowCntListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFollowCntListResp.Unmarshal(m, b)
}
func (m *GetFollowCntListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFollowCntListResp.Marshal(b, m, deterministic)
}
func (dst *GetFollowCntListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFollowCntListResp.Merge(dst, src)
}
func (m *GetFollowCntListResp) XXX_Size() int {
	return xxx_messageInfo_GetFollowCntListResp.Size(m)
}
func (m *GetFollowCntListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFollowCntListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFollowCntListResp proto.InternalMessageInfo

func (m *GetFollowCntListResp) GetFollowData() []*FollowCntInfo {
	if m != nil {
		return m.FollowData
	}
	return nil
}

type DelFollowCntReq struct {
	OwnerUid             uint32   `protobuf:"varint,1,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	FollowUid            uint32   `protobuf:"varint,2,opt,name=follow_uid,json=followUid,proto3" json:"follow_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFollowCntReq) Reset()         { *m = DelFollowCntReq{} }
func (m *DelFollowCntReq) String() string { return proto.CompactTextString(m) }
func (*DelFollowCntReq) ProtoMessage()    {}
func (*DelFollowCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{21}
}
func (m *DelFollowCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFollowCntReq.Unmarshal(m, b)
}
func (m *DelFollowCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFollowCntReq.Marshal(b, m, deterministic)
}
func (dst *DelFollowCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFollowCntReq.Merge(dst, src)
}
func (m *DelFollowCntReq) XXX_Size() int {
	return xxx_messageInfo_DelFollowCntReq.Size(m)
}
func (m *DelFollowCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFollowCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFollowCntReq proto.InternalMessageInfo

func (m *DelFollowCntReq) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *DelFollowCntReq) GetFollowUid() uint32 {
	if m != nil {
		return m.FollowUid
	}
	return 0
}

type DelFollowCntResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFollowCntResp) Reset()         { *m = DelFollowCntResp{} }
func (m *DelFollowCntResp) String() string { return proto.CompactTextString(m) }
func (*DelFollowCntResp) ProtoMessage()    {}
func (*DelFollowCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{22}
}
func (m *DelFollowCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFollowCntResp.Unmarshal(m, b)
}
func (m *DelFollowCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFollowCntResp.Marshal(b, m, deterministic)
}
func (dst *DelFollowCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFollowCntResp.Merge(dst, src)
}
func (m *DelFollowCntResp) XXX_Size() int {
	return xxx_messageInfo_DelFollowCntResp.Size(m)
}
func (m *DelFollowCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFollowCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFollowCntResp proto.InternalMessageInfo

type SCommExtChannelFollowItem struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelIsPwd         bool     `protobuf:"varint,4,opt,name=channel_is_pwd,json=channelIsPwd,proto3" json:"channel_is_pwd,omitempty"`
	TouidIsFriend        bool     `protobuf:"varint,5,opt,name=touid_is_friend,json=touidIsFriend,proto3" json:"touid_is_friend,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SCommExtChannelFollowItem) Reset()         { *m = SCommExtChannelFollowItem{} }
func (m *SCommExtChannelFollowItem) String() string { return proto.CompactTextString(m) }
func (*SCommExtChannelFollowItem) ProtoMessage()    {}
func (*SCommExtChannelFollowItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{23}
}
func (m *SCommExtChannelFollowItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SCommExtChannelFollowItem.Unmarshal(m, b)
}
func (m *SCommExtChannelFollowItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SCommExtChannelFollowItem.Marshal(b, m, deterministic)
}
func (dst *SCommExtChannelFollowItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SCommExtChannelFollowItem.Merge(dst, src)
}
func (m *SCommExtChannelFollowItem) XXX_Size() int {
	return xxx_messageInfo_SCommExtChannelFollowItem.Size(m)
}
func (m *SCommExtChannelFollowItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SCommExtChannelFollowItem.DiscardUnknown(m)
}

var xxx_messageInfo_SCommExtChannelFollowItem proto.InternalMessageInfo

func (m *SCommExtChannelFollowItem) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SCommExtChannelFollowItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SCommExtChannelFollowItem) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SCommExtChannelFollowItem) GetChannelIsPwd() bool {
	if m != nil {
		return m.ChannelIsPwd
	}
	return false
}

func (m *SCommExtChannelFollowItem) GetTouidIsFriend() bool {
	if m != nil {
		return m.TouidIsFriend
	}
	return false
}

type FetchCommExtChannelFollowInfoReq struct {
	Uid                  uint32                       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FetchObjs            []*SCommExtChannelFollowItem `protobuf:"bytes,2,rep,name=fetchObjs,proto3" json:"fetchObjs,omitempty"`
	WithCache            bool                         `protobuf:"varint,3,opt,name=withCache,proto3" json:"withCache,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *FetchCommExtChannelFollowInfoReq) Reset()         { *m = FetchCommExtChannelFollowInfoReq{} }
func (m *FetchCommExtChannelFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*FetchCommExtChannelFollowInfoReq) ProtoMessage()    {}
func (*FetchCommExtChannelFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{24}
}
func (m *FetchCommExtChannelFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FetchCommExtChannelFollowInfoReq.Unmarshal(m, b)
}
func (m *FetchCommExtChannelFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FetchCommExtChannelFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *FetchCommExtChannelFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FetchCommExtChannelFollowInfoReq.Merge(dst, src)
}
func (m *FetchCommExtChannelFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_FetchCommExtChannelFollowInfoReq.Size(m)
}
func (m *FetchCommExtChannelFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FetchCommExtChannelFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_FetchCommExtChannelFollowInfoReq proto.InternalMessageInfo

func (m *FetchCommExtChannelFollowInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FetchCommExtChannelFollowInfoReq) GetFetchObjs() []*SCommExtChannelFollowItem {
	if m != nil {
		return m.FetchObjs
	}
	return nil
}

func (m *FetchCommExtChannelFollowInfoReq) GetWithCache() bool {
	if m != nil {
		return m.WithCache
	}
	return false
}

type SExtChannelFollowInfo struct {
	Uid                 uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId           uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FindPlayingImg      string `protobuf:"bytes,3,opt,name=find_playing_img,json=findPlayingImg,proto3" json:"find_playing_img,omitempty"`
	FindPlayingText     string `protobuf:"bytes,4,opt,name=find_playing_text,json=findPlayingText,proto3" json:"find_playing_text,omitempty"`
	FollowLabelImg      string `protobuf:"bytes,5,opt,name=follow_label_img,json=followLabelImg,proto3" json:"follow_label_img,omitempty"`
	FollowLabelText     string `protobuf:"bytes,6,opt,name=follow_label_text,json=followLabelText,proto3" json:"follow_label_text,omitempty"`
	IsPerforming        bool   `protobuf:"varint,7,opt,name=is_performing,json=isPerforming,proto3" json:"is_performing,omitempty"`
	IsFollowAble        bool   `protobuf:"varint,8,opt,name=is_follow_able,json=isFollowAble,proto3" json:"is_follow_able,omitempty"`
	ChannelTabName      string `protobuf:"bytes,9,opt,name=channel_tab_name,json=channelTabName,proto3" json:"channel_tab_name,omitempty"`
	LastLiveTime        uint32 `protobuf:"varint,10,opt,name=last_live_time,json=lastLiveTime,proto3" json:"last_live_time,omitempty"`
	ChannelLevel        uint32 `protobuf:"varint,11,opt,name=channel_level,json=channelLevel,proto3" json:"channel_level,omitempty"`
	IsMusicNextDirector bool   `protobuf:"varint,12,opt,name=is_music_next_director,json=isMusicNextDirector,proto3" json:"is_music_next_director,omitempty"`
	// 个人认证标识
	CertType        uint32   `protobuf:"varint,13,opt,name=cert_type,json=certType,proto3" json:"cert_type,omitempty"`
	Icon            string   `protobuf:"bytes,14,opt,name=icon,proto3" json:"icon,omitempty"`
	Text            string   `protobuf:"bytes,15,opt,name=text,proto3" json:"text,omitempty"`
	Color           []string `protobuf:"bytes,16,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor string   `protobuf:"bytes,17,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	RingColor       []string `protobuf:"bytes,18,rep,name=ring_color,json=ringColor,proto3" json:"ring_color,omitempty"`
	RingColorExtra  []string `protobuf:"bytes,19,rep,name=ring_color_extra,json=ringColorExtra,proto3" json:"ring_color_extra,omitempty"`
	ChannelType     uint32   `protobuf:"varint,20,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// 个人铭牌
	NameplateDetailInfos []*NameplateDetailInfo `protobuf:"bytes,21,rep,name=nameplate_detail_infos,json=nameplateDetailInfos,proto3" json:"nameplate_detail_infos,omitempty"`
	// 房间是否发布
	IsPublishing         bool     `protobuf:"varint,22,opt,name=is_publishing,json=isPublishing,proto3" json:"is_publishing,omitempty"`
	Sex                  uint32   `protobuf:"varint,23,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SExtChannelFollowInfo) Reset()         { *m = SExtChannelFollowInfo{} }
func (m *SExtChannelFollowInfo) String() string { return proto.CompactTextString(m) }
func (*SExtChannelFollowInfo) ProtoMessage()    {}
func (*SExtChannelFollowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{25}
}
func (m *SExtChannelFollowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SExtChannelFollowInfo.Unmarshal(m, b)
}
func (m *SExtChannelFollowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SExtChannelFollowInfo.Marshal(b, m, deterministic)
}
func (dst *SExtChannelFollowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SExtChannelFollowInfo.Merge(dst, src)
}
func (m *SExtChannelFollowInfo) XXX_Size() int {
	return xxx_messageInfo_SExtChannelFollowInfo.Size(m)
}
func (m *SExtChannelFollowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SExtChannelFollowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SExtChannelFollowInfo proto.InternalMessageInfo

func (m *SExtChannelFollowInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SExtChannelFollowInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SExtChannelFollowInfo) GetFindPlayingImg() string {
	if m != nil {
		return m.FindPlayingImg
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetFollowLabelImg() string {
	if m != nil {
		return m.FollowLabelImg
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetFollowLabelText() string {
	if m != nil {
		return m.FollowLabelText
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetIsPerforming() bool {
	if m != nil {
		return m.IsPerforming
	}
	return false
}

func (m *SExtChannelFollowInfo) GetIsFollowAble() bool {
	if m != nil {
		return m.IsFollowAble
	}
	return false
}

func (m *SExtChannelFollowInfo) GetChannelTabName() string {
	if m != nil {
		return m.ChannelTabName
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetLastLiveTime() uint32 {
	if m != nil {
		return m.LastLiveTime
	}
	return 0
}

func (m *SExtChannelFollowInfo) GetChannelLevel() uint32 {
	if m != nil {
		return m.ChannelLevel
	}
	return 0
}

func (m *SExtChannelFollowInfo) GetIsMusicNextDirector() bool {
	if m != nil {
		return m.IsMusicNextDirector
	}
	return false
}

func (m *SExtChannelFollowInfo) GetCertType() uint32 {
	if m != nil {
		return m.CertType
	}
	return 0
}

func (m *SExtChannelFollowInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *SExtChannelFollowInfo) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

func (m *SExtChannelFollowInfo) GetRingColor() []string {
	if m != nil {
		return m.RingColor
	}
	return nil
}

func (m *SExtChannelFollowInfo) GetRingColorExtra() []string {
	if m != nil {
		return m.RingColorExtra
	}
	return nil
}

func (m *SExtChannelFollowInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SExtChannelFollowInfo) GetNameplateDetailInfos() []*NameplateDetailInfo {
	if m != nil {
		return m.NameplateDetailInfos
	}
	return nil
}

func (m *SExtChannelFollowInfo) GetIsPublishing() bool {
	if m != nil {
		return m.IsPublishing
	}
	return false
}

func (m *SExtChannelFollowInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type FetchCommExtChannelFollowInfoResp struct {
	ExtChannelFollowInfos []*SExtChannelFollowInfo `protobuf:"bytes,1,rep,name=extChannelFollowInfos,proto3" json:"extChannelFollowInfos,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                 `json:"-"`
	XXX_unrecognized      []byte                   `json:"-"`
	XXX_sizecache         int32                    `json:"-"`
}

func (m *FetchCommExtChannelFollowInfoResp) Reset()         { *m = FetchCommExtChannelFollowInfoResp{} }
func (m *FetchCommExtChannelFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*FetchCommExtChannelFollowInfoResp) ProtoMessage()    {}
func (*FetchCommExtChannelFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{26}
}
func (m *FetchCommExtChannelFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FetchCommExtChannelFollowInfoResp.Unmarshal(m, b)
}
func (m *FetchCommExtChannelFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FetchCommExtChannelFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *FetchCommExtChannelFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FetchCommExtChannelFollowInfoResp.Merge(dst, src)
}
func (m *FetchCommExtChannelFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_FetchCommExtChannelFollowInfoResp.Size(m)
}
func (m *FetchCommExtChannelFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FetchCommExtChannelFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_FetchCommExtChannelFollowInfoResp proto.InternalMessageInfo

func (m *FetchCommExtChannelFollowInfoResp) GetExtChannelFollowInfos() []*SExtChannelFollowInfo {
	if m != nil {
		return m.ExtChannelFollowInfos
	}
	return nil
}

// ************跟随数***************
type BatchGetTopNCntReq struct {
	Uids                 []uint32                    `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Limit                uint32                      `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TypeShip             BatchGetTopNCntReq_TypeShip `protobuf:"varint,3,opt,name=type_ship,json=typeShip,proto3,enum=channel_follow.BatchGetTopNCntReq_TypeShip" json:"type_ship,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetTopNCntReq) Reset()         { *m = BatchGetTopNCntReq{} }
func (m *BatchGetTopNCntReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopNCntReq) ProtoMessage()    {}
func (*BatchGetTopNCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{27}
}
func (m *BatchGetTopNCntReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopNCntReq.Unmarshal(m, b)
}
func (m *BatchGetTopNCntReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopNCntReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopNCntReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopNCntReq.Merge(dst, src)
}
func (m *BatchGetTopNCntReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopNCntReq.Size(m)
}
func (m *BatchGetTopNCntReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopNCntReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopNCntReq proto.InternalMessageInfo

func (m *BatchGetTopNCntReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetTopNCntReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetTopNCntReq) GetTypeShip() BatchGetTopNCntReq_TypeShip {
	if m != nil {
		return m.TypeShip
	}
	return BatchGetTopNCntReq_friend
}

type BatchGetTopNCntResp struct {
	TopNList             []*BatchGetTopNCntResp_TopN `protobuf:"bytes,1,rep,name=topN_list,json=topNList,proto3" json:"topN_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *BatchGetTopNCntResp) Reset()         { *m = BatchGetTopNCntResp{} }
func (m *BatchGetTopNCntResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopNCntResp) ProtoMessage()    {}
func (*BatchGetTopNCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{28}
}
func (m *BatchGetTopNCntResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopNCntResp.Unmarshal(m, b)
}
func (m *BatchGetTopNCntResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopNCntResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopNCntResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopNCntResp.Merge(dst, src)
}
func (m *BatchGetTopNCntResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopNCntResp.Size(m)
}
func (m *BatchGetTopNCntResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopNCntResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopNCntResp proto.InternalMessageInfo

func (m *BatchGetTopNCntResp) GetTopNList() []*BatchGetTopNCntResp_TopN {
	if m != nil {
		return m.TopNList
	}
	return nil
}

type BatchGetTopNCntResp_TopN struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Owner                uint32   `protobuf:"varint,2,opt,name=owner,proto3" json:"owner,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetTopNCntResp_TopN) Reset()         { *m = BatchGetTopNCntResp_TopN{} }
func (m *BatchGetTopNCntResp_TopN) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopNCntResp_TopN) ProtoMessage()    {}
func (*BatchGetTopNCntResp_TopN) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{28, 0}
}
func (m *BatchGetTopNCntResp_TopN) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopNCntResp_TopN.Unmarshal(m, b)
}
func (m *BatchGetTopNCntResp_TopN) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopNCntResp_TopN.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopNCntResp_TopN) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopNCntResp_TopN.Merge(dst, src)
}
func (m *BatchGetTopNCntResp_TopN) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopNCntResp_TopN.Size(m)
}
func (m *BatchGetTopNCntResp_TopN) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopNCntResp_TopN.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopNCntResp_TopN proto.InternalMessageInfo

func (m *BatchGetTopNCntResp_TopN) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetTopNCntResp_TopN) GetOwner() uint32 {
	if m != nil {
		return m.Owner
	}
	return 0
}

// **************用户跟随开关***************
type FollowChannelAuthItem struct {
	FollowAuth           bool     `protobuf:"varint,1,opt,name=followAuth,proto3" json:"followAuth,omitempty"`
	FollowAuthSwitchType uint32   `protobuf:"varint,2,opt,name=followAuthSwitchType,proto3" json:"followAuthSwitchType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowChannelAuthItem) Reset()         { *m = FollowChannelAuthItem{} }
func (m *FollowChannelAuthItem) String() string { return proto.CompactTextString(m) }
func (*FollowChannelAuthItem) ProtoMessage()    {}
func (*FollowChannelAuthItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{29}
}
func (m *FollowChannelAuthItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowChannelAuthItem.Unmarshal(m, b)
}
func (m *FollowChannelAuthItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowChannelAuthItem.Marshal(b, m, deterministic)
}
func (dst *FollowChannelAuthItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowChannelAuthItem.Merge(dst, src)
}
func (m *FollowChannelAuthItem) XXX_Size() int {
	return xxx_messageInfo_FollowChannelAuthItem.Size(m)
}
func (m *FollowChannelAuthItem) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowChannelAuthItem.DiscardUnknown(m)
}

var xxx_messageInfo_FollowChannelAuthItem proto.InternalMessageInfo

func (m *FollowChannelAuthItem) GetFollowAuth() bool {
	if m != nil {
		return m.FollowAuth
	}
	return false
}

func (m *FollowChannelAuthItem) GetFollowAuthSwitchType() uint32 {
	if m != nil {
		return m.FollowAuthSwitchType
	}
	return 0
}

type UpdateFollowChannelAuthReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UpdateInfo           *FollowChannelAuthItem `protobuf:"bytes,2,opt,name=updateInfo,proto3" json:"updateInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UpdateFollowChannelAuthReq) Reset()         { *m = UpdateFollowChannelAuthReq{} }
func (m *UpdateFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthReq) ProtoMessage()    {}
func (*UpdateFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{30}
}
func (m *UpdateFollowChannelAuthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFollowChannelAuthReq.Unmarshal(m, b)
}
func (m *UpdateFollowChannelAuthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFollowChannelAuthReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFollowChannelAuthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFollowChannelAuthReq.Merge(dst, src)
}
func (m *UpdateFollowChannelAuthReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFollowChannelAuthReq.Size(m)
}
func (m *UpdateFollowChannelAuthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFollowChannelAuthReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFollowChannelAuthReq proto.InternalMessageInfo

func (m *UpdateFollowChannelAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFollowChannelAuthReq) GetUpdateInfo() *FollowChannelAuthItem {
	if m != nil {
		return m.UpdateInfo
	}
	return nil
}

type UpdateFollowChannelAuthResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFollowChannelAuthResp) Reset()         { *m = UpdateFollowChannelAuthResp{} }
func (m *UpdateFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFollowChannelAuthResp) ProtoMessage()    {}
func (*UpdateFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{31}
}
func (m *UpdateFollowChannelAuthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFollowChannelAuthResp.Unmarshal(m, b)
}
func (m *UpdateFollowChannelAuthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFollowChannelAuthResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFollowChannelAuthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFollowChannelAuthResp.Merge(dst, src)
}
func (m *UpdateFollowChannelAuthResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFollowChannelAuthResp.Size(m)
}
func (m *UpdateFollowChannelAuthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFollowChannelAuthResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFollowChannelAuthResp proto.InternalMessageInfo

type GetFollowChannelAuthReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFollowChannelAuthReq) Reset()         { *m = GetFollowChannelAuthReq{} }
func (m *GetFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetFollowChannelAuthReq) ProtoMessage()    {}
func (*GetFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{32}
}
func (m *GetFollowChannelAuthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFollowChannelAuthReq.Unmarshal(m, b)
}
func (m *GetFollowChannelAuthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFollowChannelAuthReq.Marshal(b, m, deterministic)
}
func (dst *GetFollowChannelAuthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFollowChannelAuthReq.Merge(dst, src)
}
func (m *GetFollowChannelAuthReq) XXX_Size() int {
	return xxx_messageInfo_GetFollowChannelAuthReq.Size(m)
}
func (m *GetFollowChannelAuthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFollowChannelAuthReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFollowChannelAuthReq proto.InternalMessageInfo

func (m *GetFollowChannelAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFollowChannelAuthResp struct {
	AuthInfo             *FollowChannelAuthItem `protobuf:"bytes,1,opt,name=authInfo,proto3" json:"authInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetFollowChannelAuthResp) Reset()         { *m = GetFollowChannelAuthResp{} }
func (m *GetFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetFollowChannelAuthResp) ProtoMessage()    {}
func (*GetFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{33}
}
func (m *GetFollowChannelAuthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFollowChannelAuthResp.Unmarshal(m, b)
}
func (m *GetFollowChannelAuthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFollowChannelAuthResp.Marshal(b, m, deterministic)
}
func (dst *GetFollowChannelAuthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFollowChannelAuthResp.Merge(dst, src)
}
func (m *GetFollowChannelAuthResp) XXX_Size() int {
	return xxx_messageInfo_GetFollowChannelAuthResp.Size(m)
}
func (m *GetFollowChannelAuthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFollowChannelAuthResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFollowChannelAuthResp proto.InternalMessageInfo

func (m *GetFollowChannelAuthResp) GetAuthInfo() *FollowChannelAuthItem {
	if m != nil {
		return m.AuthInfo
	}
	return nil
}

type BatGetFollowChannelAuthReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uidList,proto3" json:"uidList,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetFollowChannelAuthReq) Reset()         { *m = BatGetFollowChannelAuthReq{} }
func (m *BatGetFollowChannelAuthReq) String() string { return proto.CompactTextString(m) }
func (*BatGetFollowChannelAuthReq) ProtoMessage()    {}
func (*BatGetFollowChannelAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{34}
}
func (m *BatGetFollowChannelAuthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetFollowChannelAuthReq.Unmarshal(m, b)
}
func (m *BatGetFollowChannelAuthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetFollowChannelAuthReq.Marshal(b, m, deterministic)
}
func (dst *BatGetFollowChannelAuthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetFollowChannelAuthReq.Merge(dst, src)
}
func (m *BatGetFollowChannelAuthReq) XXX_Size() int {
	return xxx_messageInfo_BatGetFollowChannelAuthReq.Size(m)
}
func (m *BatGetFollowChannelAuthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetFollowChannelAuthReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetFollowChannelAuthReq proto.InternalMessageInfo

func (m *BatGetFollowChannelAuthReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetFollowChannelAuthResp struct {
	AuthInfos            []*FollowChannelAuthItem `protobuf:"bytes,1,rep,name=authInfos,proto3" json:"authInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatGetFollowChannelAuthResp) Reset()         { *m = BatGetFollowChannelAuthResp{} }
func (m *BatGetFollowChannelAuthResp) String() string { return proto.CompactTextString(m) }
func (*BatGetFollowChannelAuthResp) ProtoMessage()    {}
func (*BatGetFollowChannelAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{35}
}
func (m *BatGetFollowChannelAuthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetFollowChannelAuthResp.Unmarshal(m, b)
}
func (m *BatGetFollowChannelAuthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetFollowChannelAuthResp.Marshal(b, m, deterministic)
}
func (dst *BatGetFollowChannelAuthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetFollowChannelAuthResp.Merge(dst, src)
}
func (m *BatGetFollowChannelAuthResp) XXX_Size() int {
	return xxx_messageInfo_BatGetFollowChannelAuthResp.Size(m)
}
func (m *BatGetFollowChannelAuthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetFollowChannelAuthResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetFollowChannelAuthResp proto.InternalMessageInfo

func (m *BatGetFollowChannelAuthResp) GetAuthInfos() []*FollowChannelAuthItem {
	if m != nil {
		return m.AuthInfos
	}
	return nil
}

type AnchorChannelFollowInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsFollowAble         bool     `protobuf:"varint,2,opt,name=is_follow_able,json=isFollowAble,proto3" json:"is_follow_able,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	IsChannelLivePk      bool     `protobuf:"varint,5,opt,name=is_channel_live_pk,json=isChannelLivePk,proto3" json:"is_channel_live_pk,omitempty"`
	FollowLabelImg       string   `protobuf:"bytes,6,opt,name=follow_label_img,json=followLabelImg,proto3" json:"follow_label_img,omitempty"`
	FollowLabelText      string   `protobuf:"bytes,7,opt,name=follow_label_text,json=followLabelText,proto3" json:"follow_label_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorChannelFollowInfo) Reset()         { *m = AnchorChannelFollowInfo{} }
func (m *AnchorChannelFollowInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorChannelFollowInfo) ProtoMessage()    {}
func (*AnchorChannelFollowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{36}
}
func (m *AnchorChannelFollowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorChannelFollowInfo.Unmarshal(m, b)
}
func (m *AnchorChannelFollowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorChannelFollowInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorChannelFollowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorChannelFollowInfo.Merge(dst, src)
}
func (m *AnchorChannelFollowInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorChannelFollowInfo.Size(m)
}
func (m *AnchorChannelFollowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorChannelFollowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorChannelFollowInfo proto.InternalMessageInfo

func (m *AnchorChannelFollowInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorChannelFollowInfo) GetIsFollowAble() bool {
	if m != nil {
		return m.IsFollowAble
	}
	return false
}

func (m *AnchorChannelFollowInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AnchorChannelFollowInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *AnchorChannelFollowInfo) GetIsChannelLivePk() bool {
	if m != nil {
		return m.IsChannelLivePk
	}
	return false
}

func (m *AnchorChannelFollowInfo) GetFollowLabelImg() string {
	if m != nil {
		return m.FollowLabelImg
	}
	return ""
}

func (m *AnchorChannelFollowInfo) GetFollowLabelText() string {
	if m != nil {
		return m.FollowLabelText
	}
	return ""
}

type ChannelMicInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32   `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	IsAnchor             bool     `protobuf:"varint,4,opt,name=is_anchor,json=isAnchor,proto3" json:"is_anchor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMicInfo) Reset()         { *m = ChannelMicInfo{} }
func (m *ChannelMicInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMicInfo) ProtoMessage()    {}
func (*ChannelMicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{37}
}
func (m *ChannelMicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMicInfo.Unmarshal(m, b)
}
func (m *ChannelMicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMicInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMicInfo.Merge(dst, src)
}
func (m *ChannelMicInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMicInfo.Size(m)
}
func (m *ChannelMicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMicInfo proto.InternalMessageInfo

func (m *ChannelMicInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMicInfo) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *ChannelMicInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMicInfo) GetIsAnchor() bool {
	if m != nil {
		return m.IsAnchor
	}
	return false
}

type GetAnchorChannelFollowInfoReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorChannelFollowInfoReq) Reset()         { *m = GetAnchorChannelFollowInfoReq{} }
func (m *GetAnchorChannelFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorChannelFollowInfoReq) ProtoMessage()    {}
func (*GetAnchorChannelFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{38}
}
func (m *GetAnchorChannelFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorChannelFollowInfoReq.Unmarshal(m, b)
}
func (m *GetAnchorChannelFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorChannelFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorChannelFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorChannelFollowInfoReq.Merge(dst, src)
}
func (m *GetAnchorChannelFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorChannelFollowInfoReq.Size(m)
}
func (m *GetAnchorChannelFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorChannelFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorChannelFollowInfoReq proto.InternalMessageInfo

func (m *GetAnchorChannelFollowInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetAnchorChannelFollowInfoResp struct {
	Infos                []*AnchorChannelFollowInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetAnchorChannelFollowInfoResp) Reset()         { *m = GetAnchorChannelFollowInfoResp{} }
func (m *GetAnchorChannelFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorChannelFollowInfoResp) ProtoMessage()    {}
func (*GetAnchorChannelFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{39}
}
func (m *GetAnchorChannelFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorChannelFollowInfoResp.Unmarshal(m, b)
}
func (m *GetAnchorChannelFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorChannelFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorChannelFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorChannelFollowInfoResp.Merge(dst, src)
}
func (m *GetAnchorChannelFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorChannelFollowInfoResp.Size(m)
}
func (m *GetAnchorChannelFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorChannelFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorChannelFollowInfoResp proto.InternalMessageInfo

func (m *GetAnchorChannelFollowInfoResp) GetInfos() []*AnchorChannelFollowInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type SetAnchorChannelFollowInfoReq struct {
	Infos                []*AnchorChannelFollowInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SetAnchorChannelFollowInfoReq) Reset()         { *m = SetAnchorChannelFollowInfoReq{} }
func (m *SetAnchorChannelFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetAnchorChannelFollowInfoReq) ProtoMessage()    {}
func (*SetAnchorChannelFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{40}
}
func (m *SetAnchorChannelFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAnchorChannelFollowInfoReq.Unmarshal(m, b)
}
func (m *SetAnchorChannelFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAnchorChannelFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetAnchorChannelFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAnchorChannelFollowInfoReq.Merge(dst, src)
}
func (m *SetAnchorChannelFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetAnchorChannelFollowInfoReq.Size(m)
}
func (m *SetAnchorChannelFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAnchorChannelFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAnchorChannelFollowInfoReq proto.InternalMessageInfo

func (m *SetAnchorChannelFollowInfoReq) GetInfos() []*AnchorChannelFollowInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type SetAnchorChannelFollowInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAnchorChannelFollowInfoResp) Reset()         { *m = SetAnchorChannelFollowInfoResp{} }
func (m *SetAnchorChannelFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetAnchorChannelFollowInfoResp) ProtoMessage()    {}
func (*SetAnchorChannelFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{41}
}
func (m *SetAnchorChannelFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAnchorChannelFollowInfoResp.Unmarshal(m, b)
}
func (m *SetAnchorChannelFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAnchorChannelFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetAnchorChannelFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAnchorChannelFollowInfoResp.Merge(dst, src)
}
func (m *SetAnchorChannelFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetAnchorChannelFollowInfoResp.Size(m)
}
func (m *SetAnchorChannelFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAnchorChannelFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAnchorChannelFollowInfoResp proto.InternalMessageInfo

type DelAnchorChannelFollowInfoReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAnchorChannelFollowInfoReq) Reset()         { *m = DelAnchorChannelFollowInfoReq{} }
func (m *DelAnchorChannelFollowInfoReq) String() string { return proto.CompactTextString(m) }
func (*DelAnchorChannelFollowInfoReq) ProtoMessage()    {}
func (*DelAnchorChannelFollowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{42}
}
func (m *DelAnchorChannelFollowInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAnchorChannelFollowInfoReq.Unmarshal(m, b)
}
func (m *DelAnchorChannelFollowInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAnchorChannelFollowInfoReq.Marshal(b, m, deterministic)
}
func (dst *DelAnchorChannelFollowInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAnchorChannelFollowInfoReq.Merge(dst, src)
}
func (m *DelAnchorChannelFollowInfoReq) XXX_Size() int {
	return xxx_messageInfo_DelAnchorChannelFollowInfoReq.Size(m)
}
func (m *DelAnchorChannelFollowInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAnchorChannelFollowInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelAnchorChannelFollowInfoReq proto.InternalMessageInfo

func (m *DelAnchorChannelFollowInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type DelAnchorChannelFollowInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAnchorChannelFollowInfoResp) Reset()         { *m = DelAnchorChannelFollowInfoResp{} }
func (m *DelAnchorChannelFollowInfoResp) String() string { return proto.CompactTextString(m) }
func (*DelAnchorChannelFollowInfoResp) ProtoMessage()    {}
func (*DelAnchorChannelFollowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{43}
}
func (m *DelAnchorChannelFollowInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAnchorChannelFollowInfoResp.Unmarshal(m, b)
}
func (m *DelAnchorChannelFollowInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAnchorChannelFollowInfoResp.Marshal(b, m, deterministic)
}
func (dst *DelAnchorChannelFollowInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAnchorChannelFollowInfoResp.Merge(dst, src)
}
func (m *DelAnchorChannelFollowInfoResp) XXX_Size() int {
	return xxx_messageInfo_DelAnchorChannelFollowInfoResp.Size(m)
}
func (m *DelAnchorChannelFollowInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAnchorChannelFollowInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelAnchorChannelFollowInfoResp proto.InternalMessageInfo

type SetAnchorChannelFollowAbleReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	IsFollowAble         bool     `protobuf:"varint,2,opt,name=is_follow_able,json=isFollowAble,proto3" json:"is_follow_able,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAnchorChannelFollowAbleReq) Reset()         { *m = SetAnchorChannelFollowAbleReq{} }
func (m *SetAnchorChannelFollowAbleReq) String() string { return proto.CompactTextString(m) }
func (*SetAnchorChannelFollowAbleReq) ProtoMessage()    {}
func (*SetAnchorChannelFollowAbleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{44}
}
func (m *SetAnchorChannelFollowAbleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAnchorChannelFollowAbleReq.Unmarshal(m, b)
}
func (m *SetAnchorChannelFollowAbleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAnchorChannelFollowAbleReq.Marshal(b, m, deterministic)
}
func (dst *SetAnchorChannelFollowAbleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAnchorChannelFollowAbleReq.Merge(dst, src)
}
func (m *SetAnchorChannelFollowAbleReq) XXX_Size() int {
	return xxx_messageInfo_SetAnchorChannelFollowAbleReq.Size(m)
}
func (m *SetAnchorChannelFollowAbleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAnchorChannelFollowAbleReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetAnchorChannelFollowAbleReq proto.InternalMessageInfo

func (m *SetAnchorChannelFollowAbleReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *SetAnchorChannelFollowAbleReq) GetIsFollowAble() bool {
	if m != nil {
		return m.IsFollowAble
	}
	return false
}

type SetAnchorChannelFollowAbleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAnchorChannelFollowAbleResp) Reset()         { *m = SetAnchorChannelFollowAbleResp{} }
func (m *SetAnchorChannelFollowAbleResp) String() string { return proto.CompactTextString(m) }
func (*SetAnchorChannelFollowAbleResp) ProtoMessage()    {}
func (*SetAnchorChannelFollowAbleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{45}
}
func (m *SetAnchorChannelFollowAbleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAnchorChannelFollowAbleResp.Unmarshal(m, b)
}
func (m *SetAnchorChannelFollowAbleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAnchorChannelFollowAbleResp.Marshal(b, m, deterministic)
}
func (dst *SetAnchorChannelFollowAbleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAnchorChannelFollowAbleResp.Merge(dst, src)
}
func (m *SetAnchorChannelFollowAbleResp) XXX_Size() int {
	return xxx_messageInfo_SetAnchorChannelFollowAbleResp.Size(m)
}
func (m *SetAnchorChannelFollowAbleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAnchorChannelFollowAbleResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetAnchorChannelFollowAbleResp proto.InternalMessageInfo

type GetUserChannelFollowListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChannelFollowListReq) Reset()         { *m = GetUserChannelFollowListReq{} }
func (m *GetUserChannelFollowListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelFollowListReq) ProtoMessage()    {}
func (*GetUserChannelFollowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{46}
}
func (m *GetUserChannelFollowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelFollowListReq.Unmarshal(m, b)
}
func (m *GetUserChannelFollowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelFollowListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelFollowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelFollowListReq.Merge(dst, src)
}
func (m *GetUserChannelFollowListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelFollowListReq.Size(m)
}
func (m *GetUserChannelFollowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelFollowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelFollowListReq proto.InternalMessageInfo

func (m *GetUserChannelFollowListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserChannelFollowListResp struct {
	UserChannelFollowList []*GetUserChannelFollowListResp_SChannelFollowItem `protobuf:"bytes,1,rep,name=userChannelFollowList,proto3" json:"userChannelFollowList,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                                           `json:"-"`
	XXX_unrecognized      []byte                                             `json:"-"`
	XXX_sizecache         int32                                              `json:"-"`
}

func (m *GetUserChannelFollowListResp) Reset()         { *m = GetUserChannelFollowListResp{} }
func (m *GetUserChannelFollowListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelFollowListResp) ProtoMessage()    {}
func (*GetUserChannelFollowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{47}
}
func (m *GetUserChannelFollowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelFollowListResp.Unmarshal(m, b)
}
func (m *GetUserChannelFollowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelFollowListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelFollowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelFollowListResp.Merge(dst, src)
}
func (m *GetUserChannelFollowListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelFollowListResp.Size(m)
}
func (m *GetUserChannelFollowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelFollowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelFollowListResp proto.InternalMessageInfo

func (m *GetUserChannelFollowListResp) GetUserChannelFollowList() []*GetUserChannelFollowListResp_SChannelFollowItem {
	if m != nil {
		return m.UserChannelFollowList
	}
	return nil
}

type GetUserChannelFollowListResp_SChannelFollowItem struct {
	FollowedUid          uint32   `protobuf:"varint,1,opt,name=followedUid,proto3" json:"followedUid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserChannelFollowListResp_SChannelFollowItem) Reset() {
	*m = GetUserChannelFollowListResp_SChannelFollowItem{}
}
func (m *GetUserChannelFollowListResp_SChannelFollowItem) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserChannelFollowListResp_SChannelFollowItem) ProtoMessage() {}
func (*GetUserChannelFollowListResp_SChannelFollowItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{47, 0}
}
func (m *GetUserChannelFollowListResp_SChannelFollowItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserChannelFollowListResp_SChannelFollowItem.Unmarshal(m, b)
}
func (m *GetUserChannelFollowListResp_SChannelFollowItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserChannelFollowListResp_SChannelFollowItem.Marshal(b, m, deterministic)
}
func (dst *GetUserChannelFollowListResp_SChannelFollowItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserChannelFollowListResp_SChannelFollowItem.Merge(dst, src)
}
func (m *GetUserChannelFollowListResp_SChannelFollowItem) XXX_Size() int {
	return xxx_messageInfo_GetUserChannelFollowListResp_SChannelFollowItem.Size(m)
}
func (m *GetUserChannelFollowListResp_SChannelFollowItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserChannelFollowListResp_SChannelFollowItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserChannelFollowListResp_SChannelFollowItem proto.InternalMessageInfo

func (m *GetUserChannelFollowListResp_SChannelFollowItem) GetFollowedUid() uint32 {
	if m != nil {
		return m.FollowedUid
	}
	return 0
}

func (m *GetUserChannelFollowListResp_SChannelFollowItem) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

type BatchUpdateUserChannelFollowReq struct {
	Uids                 []uint32                                    `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	ChannelFollowInfos   *SExtChannelFollowInfo                      `protobuf:"bytes,2,opt,name=channelFollowInfos,proto3" json:"channelFollowInfos,omitempty"`
	EOpType              BatchUpdateUserChannelFollowReq_EUpdateType `protobuf:"varint,3,opt,name=eOpType,proto3,enum=channel_follow.BatchUpdateUserChannelFollowReq_EUpdateType" json:"eOpType,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-"`
	XXX_unrecognized     []byte                                      `json:"-"`
	XXX_sizecache        int32                                       `json:"-"`
}

func (m *BatchUpdateUserChannelFollowReq) Reset()         { *m = BatchUpdateUserChannelFollowReq{} }
func (m *BatchUpdateUserChannelFollowReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateUserChannelFollowReq) ProtoMessage()    {}
func (*BatchUpdateUserChannelFollowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{48}
}
func (m *BatchUpdateUserChannelFollowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateUserChannelFollowReq.Unmarshal(m, b)
}
func (m *BatchUpdateUserChannelFollowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateUserChannelFollowReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateUserChannelFollowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateUserChannelFollowReq.Merge(dst, src)
}
func (m *BatchUpdateUserChannelFollowReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateUserChannelFollowReq.Size(m)
}
func (m *BatchUpdateUserChannelFollowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateUserChannelFollowReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateUserChannelFollowReq proto.InternalMessageInfo

func (m *BatchUpdateUserChannelFollowReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchUpdateUserChannelFollowReq) GetChannelFollowInfos() *SExtChannelFollowInfo {
	if m != nil {
		return m.ChannelFollowInfos
	}
	return nil
}

func (m *BatchUpdateUserChannelFollowReq) GetEOpType() BatchUpdateUserChannelFollowReq_EUpdateType {
	if m != nil {
		return m.EOpType
	}
	return BatchUpdateUserChannelFollowReq_E_UnKown
}

type BatchUpdateUserChannelFollowResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateUserChannelFollowResp) Reset()         { *m = BatchUpdateUserChannelFollowResp{} }
func (m *BatchUpdateUserChannelFollowResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateUserChannelFollowResp) ProtoMessage()    {}
func (*BatchUpdateUserChannelFollowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{49}
}
func (m *BatchUpdateUserChannelFollowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateUserChannelFollowResp.Unmarshal(m, b)
}
func (m *BatchUpdateUserChannelFollowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateUserChannelFollowResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateUserChannelFollowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateUserChannelFollowResp.Merge(dst, src)
}
func (m *BatchUpdateUserChannelFollowResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateUserChannelFollowResp.Size(m)
}
func (m *BatchUpdateUserChannelFollowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateUserChannelFollowResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateUserChannelFollowResp proto.InternalMessageInfo

// NameplateDetailInfo 铭牌信息结构
type NameplateDetailInfo struct {
	Id                   uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	BaseUrl              string        `protobuf:"bytes,3,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	DynamicUrl           string        `protobuf:"bytes,4,opt,name=dynamic_url,json=dynamicUrl,proto3" json:"dynamic_url,omitempty"`
	Type                 NameplateType `protobuf:"varint,5,opt,name=type,proto3,enum=channel_follow.NameplateType" json:"type,omitempty"`
	StartTime            uint32        `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	FinishTime           uint32        `protobuf:"varint,7,opt,name=finish_time,json=finishTime,proto3" json:"finish_time,omitempty"`
	IsUse                bool          `protobuf:"varint,8,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NameplateDetailInfo) Reset()         { *m = NameplateDetailInfo{} }
func (m *NameplateDetailInfo) String() string { return proto.CompactTextString(m) }
func (*NameplateDetailInfo) ProtoMessage()    {}
func (*NameplateDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{50}
}
func (m *NameplateDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NameplateDetailInfo.Unmarshal(m, b)
}
func (m *NameplateDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NameplateDetailInfo.Marshal(b, m, deterministic)
}
func (dst *NameplateDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NameplateDetailInfo.Merge(dst, src)
}
func (m *NameplateDetailInfo) XXX_Size() int {
	return xxx_messageInfo_NameplateDetailInfo.Size(m)
}
func (m *NameplateDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NameplateDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NameplateDetailInfo proto.InternalMessageInfo

func (m *NameplateDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *NameplateDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *NameplateDetailInfo) GetBaseUrl() string {
	if m != nil {
		return m.BaseUrl
	}
	return ""
}

func (m *NameplateDetailInfo) GetDynamicUrl() string {
	if m != nil {
		return m.DynamicUrl
	}
	return ""
}

func (m *NameplateDetailInfo) GetType() NameplateType {
	if m != nil {
		return m.Type
	}
	return NameplateType_TYPE_ERROR
}

func (m *NameplateDetailInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *NameplateDetailInfo) GetFinishTime() uint32 {
	if m != nil {
		return m.FinishTime
	}
	return 0
}

func (m *NameplateDetailInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

type HandleOnlineFriendFollowPushReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OnlineEventType      uint32   `protobuf:"varint,2,opt,name=online_event_type,json=onlineEventType,proto3" json:"online_event_type,omitempty"`
	IsUpdateDetail       bool     `protobuf:"varint,3,opt,name=is_update_detail,json=isUpdateDetail,proto3" json:"is_update_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleOnlineFriendFollowPushReq) Reset()         { *m = HandleOnlineFriendFollowPushReq{} }
func (m *HandleOnlineFriendFollowPushReq) String() string { return proto.CompactTextString(m) }
func (*HandleOnlineFriendFollowPushReq) ProtoMessage()    {}
func (*HandleOnlineFriendFollowPushReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{51}
}
func (m *HandleOnlineFriendFollowPushReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleOnlineFriendFollowPushReq.Unmarshal(m, b)
}
func (m *HandleOnlineFriendFollowPushReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleOnlineFriendFollowPushReq.Marshal(b, m, deterministic)
}
func (dst *HandleOnlineFriendFollowPushReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleOnlineFriendFollowPushReq.Merge(dst, src)
}
func (m *HandleOnlineFriendFollowPushReq) XXX_Size() int {
	return xxx_messageInfo_HandleOnlineFriendFollowPushReq.Size(m)
}
func (m *HandleOnlineFriendFollowPushReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleOnlineFriendFollowPushReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleOnlineFriendFollowPushReq proto.InternalMessageInfo

func (m *HandleOnlineFriendFollowPushReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HandleOnlineFriendFollowPushReq) GetOnlineEventType() uint32 {
	if m != nil {
		return m.OnlineEventType
	}
	return 0
}

func (m *HandleOnlineFriendFollowPushReq) GetIsUpdateDetail() bool {
	if m != nil {
		return m.IsUpdateDetail
	}
	return false
}

type HandleOnlineFriendFollowPushResp struct {
	OnlineEventType      uint32   `protobuf:"varint,1,opt,name=online_event_type,json=onlineEventType,proto3" json:"online_event_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleOnlineFriendFollowPushResp) Reset()         { *m = HandleOnlineFriendFollowPushResp{} }
func (m *HandleOnlineFriendFollowPushResp) String() string { return proto.CompactTextString(m) }
func (*HandleOnlineFriendFollowPushResp) ProtoMessage()    {}
func (*HandleOnlineFriendFollowPushResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_follow_d6a68d30e217cad0, []int{52}
}
func (m *HandleOnlineFriendFollowPushResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleOnlineFriendFollowPushResp.Unmarshal(m, b)
}
func (m *HandleOnlineFriendFollowPushResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleOnlineFriendFollowPushResp.Marshal(b, m, deterministic)
}
func (dst *HandleOnlineFriendFollowPushResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleOnlineFriendFollowPushResp.Merge(dst, src)
}
func (m *HandleOnlineFriendFollowPushResp) XXX_Size() int {
	return xxx_messageInfo_HandleOnlineFriendFollowPushResp.Size(m)
}
func (m *HandleOnlineFriendFollowPushResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleOnlineFriendFollowPushResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleOnlineFriendFollowPushResp proto.InternalMessageInfo

func (m *HandleOnlineFriendFollowPushResp) GetOnlineEventType() uint32 {
	if m != nil {
		return m.OnlineEventType
	}
	return 0
}

func init() {
	proto.RegisterType((*DelUserChannelFollowListReq)(nil), "channel_follow.DelUserChannelFollowListReq")
	proto.RegisterType((*DelUserChannelFollowListResp)(nil), "channel_follow.DelUserChannelFollowListResp")
	proto.RegisterType((*UpdateUserInChannelCacheReq)(nil), "channel_follow.UpdateUserInChannelCacheReq")
	proto.RegisterType((*UpdateUserInChannelCacheResp)(nil), "channel_follow.UpdateUserInChannelCacheResp")
	proto.RegisterType((*UpdateUgcChannelCacheTabReq)(nil), "channel_follow.UpdateUgcChannelCacheTabReq")
	proto.RegisterType((*UpdateUgcChannelCacheTabResp)(nil), "channel_follow.UpdateUgcChannelCacheTabResp")
	proto.RegisterType((*UpdateUgcChannelCacheDisplayReq)(nil), "channel_follow.UpdateUgcChannelCacheDisplayReq")
	proto.RegisterType((*UpdateUgcChannelCacheDisplayResp)(nil), "channel_follow.UpdateUgcChannelCacheDisplayResp")
	proto.RegisterType((*GetChannelFollowInfoReq)(nil), "channel_follow.GetChannelFollowInfoReq")
	proto.RegisterType((*GetChannelFollowInfoResp)(nil), "channel_follow.GetChannelFollowInfoResp")
	proto.RegisterType((*RoomInfo)(nil), "channel_follow.RoomInfo")
	proto.RegisterType((*BatchGetChannelFollowInfoReq)(nil), "channel_follow.BatchGetChannelFollowInfoReq")
	proto.RegisterType((*BatchGetChannelFollowInfoResp)(nil), "channel_follow.BatchGetChannelFollowInfoResp")
	proto.RegisterMapType((map[uint32]*RoomInfo)(nil), "channel_follow.BatchGetChannelFollowInfoResp.InfosEntry")
	proto.RegisterType((*UpdateFollowChannelAuthSwitchReq)(nil), "channel_follow.UpdateFollowChannelAuthSwitchReq")
	proto.RegisterType((*UpdateFollowChannelAuthSwitchResp)(nil), "channel_follow.UpdateFollowChannelAuthSwitchResp")
	proto.RegisterType((*GetChannelTabMapReq)(nil), "channel_follow.GetChannelTabMapReq")
	proto.RegisterType((*GetChannelTabMapResp)(nil), "channel_follow.GetChannelTabMapResp")
	proto.RegisterMapType((map[uint32]*ChannelTab)(nil), "channel_follow.GetChannelTabMapResp.ChannelTabMapEntry")
	proto.RegisterType((*ChannelTab)(nil), "channel_follow.ChannelTab")
	proto.RegisterType((*FollowCntInfo)(nil), "channel_follow.FollowCntInfo")
	proto.RegisterType((*GetFollowCntListReq)(nil), "channel_follow.GetFollowCntListReq")
	proto.RegisterType((*GetFollowCntListResp)(nil), "channel_follow.GetFollowCntListResp")
	proto.RegisterType((*DelFollowCntReq)(nil), "channel_follow.DelFollowCntReq")
	proto.RegisterType((*DelFollowCntResp)(nil), "channel_follow.DelFollowCntResp")
	proto.RegisterType((*SCommExtChannelFollowItem)(nil), "channel_follow.SCommExtChannelFollowItem")
	proto.RegisterType((*FetchCommExtChannelFollowInfoReq)(nil), "channel_follow.FetchCommExtChannelFollowInfoReq")
	proto.RegisterType((*SExtChannelFollowInfo)(nil), "channel_follow.SExtChannelFollowInfo")
	proto.RegisterType((*FetchCommExtChannelFollowInfoResp)(nil), "channel_follow.FetchCommExtChannelFollowInfoResp")
	proto.RegisterType((*BatchGetTopNCntReq)(nil), "channel_follow.BatchGetTopNCntReq")
	proto.RegisterType((*BatchGetTopNCntResp)(nil), "channel_follow.BatchGetTopNCntResp")
	proto.RegisterType((*BatchGetTopNCntResp_TopN)(nil), "channel_follow.BatchGetTopNCntResp.TopN")
	proto.RegisterType((*FollowChannelAuthItem)(nil), "channel_follow.FollowChannelAuthItem")
	proto.RegisterType((*UpdateFollowChannelAuthReq)(nil), "channel_follow.UpdateFollowChannelAuthReq")
	proto.RegisterType((*UpdateFollowChannelAuthResp)(nil), "channel_follow.UpdateFollowChannelAuthResp")
	proto.RegisterType((*GetFollowChannelAuthReq)(nil), "channel_follow.GetFollowChannelAuthReq")
	proto.RegisterType((*GetFollowChannelAuthResp)(nil), "channel_follow.GetFollowChannelAuthResp")
	proto.RegisterType((*BatGetFollowChannelAuthReq)(nil), "channel_follow.BatGetFollowChannelAuthReq")
	proto.RegisterType((*BatGetFollowChannelAuthResp)(nil), "channel_follow.BatGetFollowChannelAuthResp")
	proto.RegisterType((*AnchorChannelFollowInfo)(nil), "channel_follow.AnchorChannelFollowInfo")
	proto.RegisterType((*ChannelMicInfo)(nil), "channel_follow.ChannelMicInfo")
	proto.RegisterType((*GetAnchorChannelFollowInfoReq)(nil), "channel_follow.GetAnchorChannelFollowInfoReq")
	proto.RegisterType((*GetAnchorChannelFollowInfoResp)(nil), "channel_follow.GetAnchorChannelFollowInfoResp")
	proto.RegisterType((*SetAnchorChannelFollowInfoReq)(nil), "channel_follow.SetAnchorChannelFollowInfoReq")
	proto.RegisterType((*SetAnchorChannelFollowInfoResp)(nil), "channel_follow.SetAnchorChannelFollowInfoResp")
	proto.RegisterType((*DelAnchorChannelFollowInfoReq)(nil), "channel_follow.DelAnchorChannelFollowInfoReq")
	proto.RegisterType((*DelAnchorChannelFollowInfoResp)(nil), "channel_follow.DelAnchorChannelFollowInfoResp")
	proto.RegisterType((*SetAnchorChannelFollowAbleReq)(nil), "channel_follow.SetAnchorChannelFollowAbleReq")
	proto.RegisterType((*SetAnchorChannelFollowAbleResp)(nil), "channel_follow.SetAnchorChannelFollowAbleResp")
	proto.RegisterType((*GetUserChannelFollowListReq)(nil), "channel_follow.GetUserChannelFollowListReq")
	proto.RegisterType((*GetUserChannelFollowListResp)(nil), "channel_follow.GetUserChannelFollowListResp")
	proto.RegisterType((*GetUserChannelFollowListResp_SChannelFollowItem)(nil), "channel_follow.GetUserChannelFollowListResp.SChannelFollowItem")
	proto.RegisterType((*BatchUpdateUserChannelFollowReq)(nil), "channel_follow.BatchUpdateUserChannelFollowReq")
	proto.RegisterType((*BatchUpdateUserChannelFollowResp)(nil), "channel_follow.BatchUpdateUserChannelFollowResp")
	proto.RegisterType((*NameplateDetailInfo)(nil), "channel_follow.NameplateDetailInfo")
	proto.RegisterType((*HandleOnlineFriendFollowPushReq)(nil), "channel_follow.HandleOnlineFriendFollowPushReq")
	proto.RegisterType((*HandleOnlineFriendFollowPushResp)(nil), "channel_follow.HandleOnlineFriendFollowPushResp")
	proto.RegisterEnum("channel_follow.E_ONLINE_STATUS", E_ONLINE_STATUS_name, E_ONLINE_STATUS_value)
	proto.RegisterEnum("channel_follow.NameplateType", NameplateType_name, NameplateType_value)
	proto.RegisterEnum("channel_follow.EOnlineEventType", EOnlineEventType_name, EOnlineEventType_value)
	proto.RegisterEnum("channel_follow.BatchGetTopNCntReq_TypeShip", BatchGetTopNCntReq_TypeShip_name, BatchGetTopNCntReq_TypeShip_value)
	proto.RegisterEnum("channel_follow.BatchUpdateUserChannelFollowReq_EUpdateType", BatchUpdateUserChannelFollowReq_EUpdateType_name, BatchUpdateUserChannelFollowReq_EUpdateType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelFollowClient is the client API for ChannelFollow service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelFollowClient interface {
	GetChannelFollowInfo(ctx context.Context, in *GetChannelFollowInfoReq, opts ...grpc.CallOption) (*GetChannelFollowInfoResp, error)
	BatchGetChannelFollowInfo(ctx context.Context, in *BatchGetChannelFollowInfoReq, opts ...grpc.CallOption) (*BatchGetChannelFollowInfoResp, error)
	// 通知跟随开关修改
	UpdateFollowChannelAuthSwitch(ctx context.Context, in *UpdateFollowChannelAuthSwitchReq, opts ...grpc.CallOption) (*UpdateFollowChannelAuthSwitchResp, error)
	GetChannelTabMap(ctx context.Context, in *GetChannelTabMapReq, opts ...grpc.CallOption) (*GetChannelTabMapResp, error)
	GetFollowCntList(ctx context.Context, in *GetFollowCntListReq, opts ...grpc.CallOption) (*GetFollowCntListResp, error)
	DelFollowCnt(ctx context.Context, in *DelFollowCntReq, opts ...grpc.CallOption) (*DelFollowCntResp, error)
	// 获取最大的跟随次数的人
	BatchGetTopNCnt(ctx context.Context, in *BatchGetTopNCntReq, opts ...grpc.CallOption) (*BatchGetTopNCntResp, error)
	// 用于收敛所有房间类型的跟随信息替换逻辑
	FetchCommExtChannelFollowInfo(ctx context.Context, in *FetchCommExtChannelFollowInfoReq, opts ...grpc.CallOption) (*FetchCommExtChannelFollowInfoResp, error)
	// 更新用户的跟随开关设置
	UpdateFollowChannelAuth(ctx context.Context, in *UpdateFollowChannelAuthReq, opts ...grpc.CallOption) (*UpdateFollowChannelAuthResp, error)
	// 获取指定用户的跟随开关设置
	GetFollowChannelAuth(ctx context.Context, in *GetFollowChannelAuthReq, opts ...grpc.CallOption) (*GetFollowChannelAuthResp, error)
	// 批量获取指定用户的跟随开关设置
	BatGetFollowChannelAuth(ctx context.Context, in *BatGetFollowChannelAuthReq, opts ...grpc.CallOption) (*BatGetFollowChannelAuthResp, error)
	// 主播房间跟随信息
	GetAnchorChannelFollowInfo(ctx context.Context, in *GetAnchorChannelFollowInfoReq, opts ...grpc.CallOption) (*GetAnchorChannelFollowInfoResp, error)
	SetAnchorChannelFollowInfo(ctx context.Context, in *SetAnchorChannelFollowInfoReq, opts ...grpc.CallOption) (*SetAnchorChannelFollowInfoResp, error)
	DelAnchorChannelFollowInfo(ctx context.Context, in *DelAnchorChannelFollowInfoReq, opts ...grpc.CallOption) (*DelAnchorChannelFollowInfoResp, error)
	SetAnchorChannelFollowAble(ctx context.Context, in *SetAnchorChannelFollowAbleReq, opts ...grpc.CallOption) (*SetAnchorChannelFollowAbleResp, error)
	// 拉取用户的跟随房间列表
	GetUserChannelFollowList(ctx context.Context, in *GetUserChannelFollowListReq, opts ...grpc.CallOption) (*GetUserChannelFollowListResp, error)
	// 批量更新用户的跟随列表信息到缓存
	BatchUpdateUserChannelFollow(ctx context.Context, in *BatchUpdateUserChannelFollowReq, opts ...grpc.CallOption) (*BatchUpdateUserChannelFollowResp, error)
	// friendOl 跟随推送
	HandleOnlineFriendFollowPush(ctx context.Context, in *HandleOnlineFriendFollowPushReq, opts ...grpc.CallOption) (*HandleOnlineFriendFollowPushResp, error)
	DelUserChannelFollowList(ctx context.Context, in *DelUserChannelFollowListReq, opts ...grpc.CallOption) (*DelUserChannelFollowListResp, error)
	// 更新 ugc 房玩法和发布状态缓存
	UpdateUgcChannelCacheTab(ctx context.Context, in *UpdateUgcChannelCacheTabReq, opts ...grpc.CallOption) (*UpdateUgcChannelCacheTabResp, error)
	UpdateUgcChannelCacheDisplay(ctx context.Context, in *UpdateUgcChannelCacheDisplayReq, opts ...grpc.CallOption) (*UpdateUgcChannelCacheDisplayResp, error)
	// 更新用户是否在房状态
	UpdateUserInChannelCache(ctx context.Context, in *UpdateUserInChannelCacheReq, opts ...grpc.CallOption) (*UpdateUserInChannelCacheResp, error)
}

type channelFollowClient struct {
	cc *grpc.ClientConn
}

func NewChannelFollowClient(cc *grpc.ClientConn) ChannelFollowClient {
	return &channelFollowClient{cc}
}

func (c *channelFollowClient) GetChannelFollowInfo(ctx context.Context, in *GetChannelFollowInfoReq, opts ...grpc.CallOption) (*GetChannelFollowInfoResp, error) {
	out := new(GetChannelFollowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/GetChannelFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) BatchGetChannelFollowInfo(ctx context.Context, in *BatchGetChannelFollowInfoReq, opts ...grpc.CallOption) (*BatchGetChannelFollowInfoResp, error) {
	out := new(BatchGetChannelFollowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/BatchGetChannelFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) UpdateFollowChannelAuthSwitch(ctx context.Context, in *UpdateFollowChannelAuthSwitchReq, opts ...grpc.CallOption) (*UpdateFollowChannelAuthSwitchResp, error) {
	out := new(UpdateFollowChannelAuthSwitchResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/UpdateFollowChannelAuthSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) GetChannelTabMap(ctx context.Context, in *GetChannelTabMapReq, opts ...grpc.CallOption) (*GetChannelTabMapResp, error) {
	out := new(GetChannelTabMapResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/GetChannelTabMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) GetFollowCntList(ctx context.Context, in *GetFollowCntListReq, opts ...grpc.CallOption) (*GetFollowCntListResp, error) {
	out := new(GetFollowCntListResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/GetFollowCntList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) DelFollowCnt(ctx context.Context, in *DelFollowCntReq, opts ...grpc.CallOption) (*DelFollowCntResp, error) {
	out := new(DelFollowCntResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/DelFollowCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) BatchGetTopNCnt(ctx context.Context, in *BatchGetTopNCntReq, opts ...grpc.CallOption) (*BatchGetTopNCntResp, error) {
	out := new(BatchGetTopNCntResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/BatchGetTopNCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) FetchCommExtChannelFollowInfo(ctx context.Context, in *FetchCommExtChannelFollowInfoReq, opts ...grpc.CallOption) (*FetchCommExtChannelFollowInfoResp, error) {
	out := new(FetchCommExtChannelFollowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/FetchCommExtChannelFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) UpdateFollowChannelAuth(ctx context.Context, in *UpdateFollowChannelAuthReq, opts ...grpc.CallOption) (*UpdateFollowChannelAuthResp, error) {
	out := new(UpdateFollowChannelAuthResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/UpdateFollowChannelAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) GetFollowChannelAuth(ctx context.Context, in *GetFollowChannelAuthReq, opts ...grpc.CallOption) (*GetFollowChannelAuthResp, error) {
	out := new(GetFollowChannelAuthResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/GetFollowChannelAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) BatGetFollowChannelAuth(ctx context.Context, in *BatGetFollowChannelAuthReq, opts ...grpc.CallOption) (*BatGetFollowChannelAuthResp, error) {
	out := new(BatGetFollowChannelAuthResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/BatGetFollowChannelAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) GetAnchorChannelFollowInfo(ctx context.Context, in *GetAnchorChannelFollowInfoReq, opts ...grpc.CallOption) (*GetAnchorChannelFollowInfoResp, error) {
	out := new(GetAnchorChannelFollowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/GetAnchorChannelFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) SetAnchorChannelFollowInfo(ctx context.Context, in *SetAnchorChannelFollowInfoReq, opts ...grpc.CallOption) (*SetAnchorChannelFollowInfoResp, error) {
	out := new(SetAnchorChannelFollowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/SetAnchorChannelFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) DelAnchorChannelFollowInfo(ctx context.Context, in *DelAnchorChannelFollowInfoReq, opts ...grpc.CallOption) (*DelAnchorChannelFollowInfoResp, error) {
	out := new(DelAnchorChannelFollowInfoResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/DelAnchorChannelFollowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) SetAnchorChannelFollowAble(ctx context.Context, in *SetAnchorChannelFollowAbleReq, opts ...grpc.CallOption) (*SetAnchorChannelFollowAbleResp, error) {
	out := new(SetAnchorChannelFollowAbleResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/SetAnchorChannelFollowAble", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) GetUserChannelFollowList(ctx context.Context, in *GetUserChannelFollowListReq, opts ...grpc.CallOption) (*GetUserChannelFollowListResp, error) {
	out := new(GetUserChannelFollowListResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/GetUserChannelFollowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) BatchUpdateUserChannelFollow(ctx context.Context, in *BatchUpdateUserChannelFollowReq, opts ...grpc.CallOption) (*BatchUpdateUserChannelFollowResp, error) {
	out := new(BatchUpdateUserChannelFollowResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/BatchUpdateUserChannelFollow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) HandleOnlineFriendFollowPush(ctx context.Context, in *HandleOnlineFriendFollowPushReq, opts ...grpc.CallOption) (*HandleOnlineFriendFollowPushResp, error) {
	out := new(HandleOnlineFriendFollowPushResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/HandleOnlineFriendFollowPush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) DelUserChannelFollowList(ctx context.Context, in *DelUserChannelFollowListReq, opts ...grpc.CallOption) (*DelUserChannelFollowListResp, error) {
	out := new(DelUserChannelFollowListResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/DelUserChannelFollowList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) UpdateUgcChannelCacheTab(ctx context.Context, in *UpdateUgcChannelCacheTabReq, opts ...grpc.CallOption) (*UpdateUgcChannelCacheTabResp, error) {
	out := new(UpdateUgcChannelCacheTabResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/UpdateUgcChannelCacheTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) UpdateUgcChannelCacheDisplay(ctx context.Context, in *UpdateUgcChannelCacheDisplayReq, opts ...grpc.CallOption) (*UpdateUgcChannelCacheDisplayResp, error) {
	out := new(UpdateUgcChannelCacheDisplayResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/UpdateUgcChannelCacheDisplay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelFollowClient) UpdateUserInChannelCache(ctx context.Context, in *UpdateUserInChannelCacheReq, opts ...grpc.CallOption) (*UpdateUserInChannelCacheResp, error) {
	out := new(UpdateUserInChannelCacheResp)
	err := c.cc.Invoke(ctx, "/channel_follow.ChannelFollow/UpdateUserInChannelCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelFollowServer is the server API for ChannelFollow service.
type ChannelFollowServer interface {
	GetChannelFollowInfo(context.Context, *GetChannelFollowInfoReq) (*GetChannelFollowInfoResp, error)
	BatchGetChannelFollowInfo(context.Context, *BatchGetChannelFollowInfoReq) (*BatchGetChannelFollowInfoResp, error)
	// 通知跟随开关修改
	UpdateFollowChannelAuthSwitch(context.Context, *UpdateFollowChannelAuthSwitchReq) (*UpdateFollowChannelAuthSwitchResp, error)
	GetChannelTabMap(context.Context, *GetChannelTabMapReq) (*GetChannelTabMapResp, error)
	GetFollowCntList(context.Context, *GetFollowCntListReq) (*GetFollowCntListResp, error)
	DelFollowCnt(context.Context, *DelFollowCntReq) (*DelFollowCntResp, error)
	// 获取最大的跟随次数的人
	BatchGetTopNCnt(context.Context, *BatchGetTopNCntReq) (*BatchGetTopNCntResp, error)
	// 用于收敛所有房间类型的跟随信息替换逻辑
	FetchCommExtChannelFollowInfo(context.Context, *FetchCommExtChannelFollowInfoReq) (*FetchCommExtChannelFollowInfoResp, error)
	// 更新用户的跟随开关设置
	UpdateFollowChannelAuth(context.Context, *UpdateFollowChannelAuthReq) (*UpdateFollowChannelAuthResp, error)
	// 获取指定用户的跟随开关设置
	GetFollowChannelAuth(context.Context, *GetFollowChannelAuthReq) (*GetFollowChannelAuthResp, error)
	// 批量获取指定用户的跟随开关设置
	BatGetFollowChannelAuth(context.Context, *BatGetFollowChannelAuthReq) (*BatGetFollowChannelAuthResp, error)
	// 主播房间跟随信息
	GetAnchorChannelFollowInfo(context.Context, *GetAnchorChannelFollowInfoReq) (*GetAnchorChannelFollowInfoResp, error)
	SetAnchorChannelFollowInfo(context.Context, *SetAnchorChannelFollowInfoReq) (*SetAnchorChannelFollowInfoResp, error)
	DelAnchorChannelFollowInfo(context.Context, *DelAnchorChannelFollowInfoReq) (*DelAnchorChannelFollowInfoResp, error)
	SetAnchorChannelFollowAble(context.Context, *SetAnchorChannelFollowAbleReq) (*SetAnchorChannelFollowAbleResp, error)
	// 拉取用户的跟随房间列表
	GetUserChannelFollowList(context.Context, *GetUserChannelFollowListReq) (*GetUserChannelFollowListResp, error)
	// 批量更新用户的跟随列表信息到缓存
	BatchUpdateUserChannelFollow(context.Context, *BatchUpdateUserChannelFollowReq) (*BatchUpdateUserChannelFollowResp, error)
	// friendOl 跟随推送
	HandleOnlineFriendFollowPush(context.Context, *HandleOnlineFriendFollowPushReq) (*HandleOnlineFriendFollowPushResp, error)
	DelUserChannelFollowList(context.Context, *DelUserChannelFollowListReq) (*DelUserChannelFollowListResp, error)
	// 更新 ugc 房玩法和发布状态缓存
	UpdateUgcChannelCacheTab(context.Context, *UpdateUgcChannelCacheTabReq) (*UpdateUgcChannelCacheTabResp, error)
	UpdateUgcChannelCacheDisplay(context.Context, *UpdateUgcChannelCacheDisplayReq) (*UpdateUgcChannelCacheDisplayResp, error)
	// 更新用户是否在房状态
	UpdateUserInChannelCache(context.Context, *UpdateUserInChannelCacheReq) (*UpdateUserInChannelCacheResp, error)
}

func RegisterChannelFollowServer(s *grpc.Server, srv ChannelFollowServer) {
	s.RegisterService(&_ChannelFollow_serviceDesc, srv)
}

func _ChannelFollow_GetChannelFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).GetChannelFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/GetChannelFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).GetChannelFollowInfo(ctx, req.(*GetChannelFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_BatchGetChannelFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).BatchGetChannelFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/BatchGetChannelFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).BatchGetChannelFollowInfo(ctx, req.(*BatchGetChannelFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_UpdateFollowChannelAuthSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFollowChannelAuthSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).UpdateFollowChannelAuthSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/UpdateFollowChannelAuthSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).UpdateFollowChannelAuthSwitch(ctx, req.(*UpdateFollowChannelAuthSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_GetChannelTabMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTabMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).GetChannelTabMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/GetChannelTabMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).GetChannelTabMap(ctx, req.(*GetChannelTabMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_GetFollowCntList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowCntListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).GetFollowCntList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/GetFollowCntList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).GetFollowCntList(ctx, req.(*GetFollowCntListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_DelFollowCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFollowCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).DelFollowCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/DelFollowCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).DelFollowCnt(ctx, req.(*DelFollowCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_BatchGetTopNCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTopNCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).BatchGetTopNCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/BatchGetTopNCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).BatchGetTopNCnt(ctx, req.(*BatchGetTopNCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_FetchCommExtChannelFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchCommExtChannelFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).FetchCommExtChannelFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/FetchCommExtChannelFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).FetchCommExtChannelFollowInfo(ctx, req.(*FetchCommExtChannelFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_UpdateFollowChannelAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFollowChannelAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).UpdateFollowChannelAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/UpdateFollowChannelAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).UpdateFollowChannelAuth(ctx, req.(*UpdateFollowChannelAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_GetFollowChannelAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFollowChannelAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).GetFollowChannelAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/GetFollowChannelAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).GetFollowChannelAuth(ctx, req.(*GetFollowChannelAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_BatGetFollowChannelAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetFollowChannelAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).BatGetFollowChannelAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/BatGetFollowChannelAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).BatGetFollowChannelAuth(ctx, req.(*BatGetFollowChannelAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_GetAnchorChannelFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorChannelFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).GetAnchorChannelFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/GetAnchorChannelFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).GetAnchorChannelFollowInfo(ctx, req.(*GetAnchorChannelFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_SetAnchorChannelFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAnchorChannelFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).SetAnchorChannelFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/SetAnchorChannelFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).SetAnchorChannelFollowInfo(ctx, req.(*SetAnchorChannelFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_DelAnchorChannelFollowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAnchorChannelFollowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).DelAnchorChannelFollowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/DelAnchorChannelFollowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).DelAnchorChannelFollowInfo(ctx, req.(*DelAnchorChannelFollowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_SetAnchorChannelFollowAble_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAnchorChannelFollowAbleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).SetAnchorChannelFollowAble(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/SetAnchorChannelFollowAble",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).SetAnchorChannelFollowAble(ctx, req.(*SetAnchorChannelFollowAbleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_GetUserChannelFollowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserChannelFollowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).GetUserChannelFollowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/GetUserChannelFollowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).GetUserChannelFollowList(ctx, req.(*GetUserChannelFollowListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_BatchUpdateUserChannelFollow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateUserChannelFollowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).BatchUpdateUserChannelFollow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/BatchUpdateUserChannelFollow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).BatchUpdateUserChannelFollow(ctx, req.(*BatchUpdateUserChannelFollowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_HandleOnlineFriendFollowPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleOnlineFriendFollowPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).HandleOnlineFriendFollowPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/HandleOnlineFriendFollowPush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).HandleOnlineFriendFollowPush(ctx, req.(*HandleOnlineFriendFollowPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_DelUserChannelFollowList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserChannelFollowListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).DelUserChannelFollowList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/DelUserChannelFollowList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).DelUserChannelFollowList(ctx, req.(*DelUserChannelFollowListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_UpdateUgcChannelCacheTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUgcChannelCacheTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).UpdateUgcChannelCacheTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/UpdateUgcChannelCacheTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).UpdateUgcChannelCacheTab(ctx, req.(*UpdateUgcChannelCacheTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_UpdateUgcChannelCacheDisplay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUgcChannelCacheDisplayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).UpdateUgcChannelCacheDisplay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/UpdateUgcChannelCacheDisplay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).UpdateUgcChannelCacheDisplay(ctx, req.(*UpdateUgcChannelCacheDisplayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelFollow_UpdateUserInChannelCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInChannelCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelFollowServer).UpdateUserInChannelCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_follow.ChannelFollow/UpdateUserInChannelCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelFollowServer).UpdateUserInChannelCache(ctx, req.(*UpdateUserInChannelCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelFollow_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_follow.ChannelFollow",
	HandlerType: (*ChannelFollowServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelFollowInfo",
			Handler:    _ChannelFollow_GetChannelFollowInfo_Handler,
		},
		{
			MethodName: "BatchGetChannelFollowInfo",
			Handler:    _ChannelFollow_BatchGetChannelFollowInfo_Handler,
		},
		{
			MethodName: "UpdateFollowChannelAuthSwitch",
			Handler:    _ChannelFollow_UpdateFollowChannelAuthSwitch_Handler,
		},
		{
			MethodName: "GetChannelTabMap",
			Handler:    _ChannelFollow_GetChannelTabMap_Handler,
		},
		{
			MethodName: "GetFollowCntList",
			Handler:    _ChannelFollow_GetFollowCntList_Handler,
		},
		{
			MethodName: "DelFollowCnt",
			Handler:    _ChannelFollow_DelFollowCnt_Handler,
		},
		{
			MethodName: "BatchGetTopNCnt",
			Handler:    _ChannelFollow_BatchGetTopNCnt_Handler,
		},
		{
			MethodName: "FetchCommExtChannelFollowInfo",
			Handler:    _ChannelFollow_FetchCommExtChannelFollowInfo_Handler,
		},
		{
			MethodName: "UpdateFollowChannelAuth",
			Handler:    _ChannelFollow_UpdateFollowChannelAuth_Handler,
		},
		{
			MethodName: "GetFollowChannelAuth",
			Handler:    _ChannelFollow_GetFollowChannelAuth_Handler,
		},
		{
			MethodName: "BatGetFollowChannelAuth",
			Handler:    _ChannelFollow_BatGetFollowChannelAuth_Handler,
		},
		{
			MethodName: "GetAnchorChannelFollowInfo",
			Handler:    _ChannelFollow_GetAnchorChannelFollowInfo_Handler,
		},
		{
			MethodName: "SetAnchorChannelFollowInfo",
			Handler:    _ChannelFollow_SetAnchorChannelFollowInfo_Handler,
		},
		{
			MethodName: "DelAnchorChannelFollowInfo",
			Handler:    _ChannelFollow_DelAnchorChannelFollowInfo_Handler,
		},
		{
			MethodName: "SetAnchorChannelFollowAble",
			Handler:    _ChannelFollow_SetAnchorChannelFollowAble_Handler,
		},
		{
			MethodName: "GetUserChannelFollowList",
			Handler:    _ChannelFollow_GetUserChannelFollowList_Handler,
		},
		{
			MethodName: "BatchUpdateUserChannelFollow",
			Handler:    _ChannelFollow_BatchUpdateUserChannelFollow_Handler,
		},
		{
			MethodName: "HandleOnlineFriendFollowPush",
			Handler:    _ChannelFollow_HandleOnlineFriendFollowPush_Handler,
		},
		{
			MethodName: "DelUserChannelFollowList",
			Handler:    _ChannelFollow_DelUserChannelFollowList_Handler,
		},
		{
			MethodName: "UpdateUgcChannelCacheTab",
			Handler:    _ChannelFollow_UpdateUgcChannelCacheTab_Handler,
		},
		{
			MethodName: "UpdateUgcChannelCacheDisplay",
			Handler:    _ChannelFollow_UpdateUgcChannelCacheDisplay_Handler,
		},
		{
			MethodName: "UpdateUserInChannelCache",
			Handler:    _ChannelFollow_UpdateUserInChannelCache_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-follow/channel-follow.proto",
}

func init() {
	proto.RegisterFile("channel-follow/channel-follow.proto", fileDescriptor_channel_follow_d6a68d30e217cad0)
}

var fileDescriptor_channel_follow_d6a68d30e217cad0 = []byte{
	// 2646 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x1a, 0xdb, 0x6e, 0xdb, 0xc8,
	0xd5, 0xf4, 0x55, 0x3a, 0x8e, 0x6d, 0x65, 0x62, 0x6f, 0x14, 0x39, 0x8e, 0x95, 0xc9, 0x6e, 0xd7,
	0xeb, 0x6c, 0xe4, 0xac, 0x17, 0xdd, 0x5d, 0xb4, 0xe8, 0x25, 0x6b, 0x2b, 0x89, 0xba, 0x8e, 0xec,
	0x52, 0xd2, 0x2e, 0xd2, 0x6e, 0x4b, 0x50, 0xe4, 0xd8, 0x9a, 0x9a, 0x22, 0x19, 0xcd, 0xc8, 0x97,
	0x02, 0x05, 0x0a, 0x14, 0x28, 0xd0, 0x0f, 0x68, 0xfb, 0xd0, 0x5f, 0xe8, 0x57, 0xb4, 0x6f, 0x7d,
	0xef, 0x5b, 0x81, 0xfe, 0x41, 0x7f, 0xa1, 0xc5, 0xcc, 0x90, 0x22, 0x25, 0x92, 0xba, 0xa0, 0x4f,
	0xe2, 0x9c, 0x39, 0xb7, 0x39, 0x67, 0xce, 0x99, 0x73, 0x46, 0x03, 0x4f, 0xac, 0x8e, 0xe9, 0xba,
	0xc4, 0x79, 0x76, 0xee, 0x39, 0x8e, 0x77, 0x7d, 0x30, 0x3c, 0xac, 0xf8, 0x3d, 0x8f, 0x7b, 0x68,
	0x3d, 0x80, 0x1a, 0x0a, 0x5a, 0xfa, 0xa8, 0x47, 0xae, 0x88, 0xdb, 0x27, 0xcf, 0x5c, 0xb3, 0x4b,
	0x7c, 0xc7, 0xe4, 0xe4, 0x20, 0x01, 0x51, 0xa4, 0xa5, 0xc7, 0xdc, 0xf3, 0xa9, 0x65, 0x04, 0x0c,
	0x0e, 0xe4, 0xe8, 0x59, 0x30, 0x52, 0x28, 0xf8, 0x1b, 0xd8, 0x3e, 0x26, 0x4e, 0x8b, 0x91, 0xde,
	0x91, 0x82, 0xbf, 0x94, 0x52, 0x4e, 0x28, 0xe3, 0x3a, 0x79, 0x87, 0x76, 0x00, 0x3a, 0xa6, 0x6b,
	0x3b, 0xc4, 0xe8, 0x53, 0xbb, 0xa8, 0x95, 0xb5, 0xbd, 0x35, 0x3d, 0xaf, 0x20, 0x2d, 0x6a, 0xa3,
	0x07, 0x90, 0xeb, 0x53, 0xdb, 0x70, 0x28, 0xe3, 0xc5, 0xf9, 0xf2, 0xc2, 0xde, 0x9a, 0xbe, 0xd2,
	0xa7, 0xb6, 0x20, 0xc6, 0x8f, 0xe0, 0x61, 0x36, 0x63, 0xe6, 0xe3, 0x3a, 0x6c, 0xb7, 0x7c, 0xdb,
	0xe4, 0x44, 0xa0, 0xd4, 0xdc, 0x00, 0xe9, 0xc8, 0xb4, 0x3a, 0x44, 0x08, 0x2e, 0xc0, 0x42, 0x24,
	0x51, 0x7c, 0x0a, 0x55, 0x42, 0x4b, 0x50, 0xbb, 0x38, 0xaf, 0x54, 0x09, 0x20, 0x35, 0x5b, 0xc8,
	0xcb, 0xe6, 0xc7, 0x7c, 0xac, 0x0f, 0xe4, 0x5d, 0x58, 0xf1, 0xc9, 0xa6, 0xd9, 0x0e, 0x16, 0x1a,
	0xe3, 0xae, 0x8d, 0x70, 0x47, 0x9b, 0xb0, 0xc4, 0xcd, 0x76, 0x2d, 0x94, 0xab, 0x06, 0x31, 0x99,
	0x29, 0x3c, 0x99, 0x8f, 0xff, 0xac, 0xc1, 0x6e, 0x2a, 0xc2, 0x31, 0x65, 0xbe, 0x63, 0xde, 0x4e,
	0x21, 0xb8, 0x05, 0x77, 0x6d, 0x85, 0x6c, 0xf0, 0x5b, 0x9f, 0x44, 0xa6, 0x5e, 0x3f, 0xfc, 0xa8,
	0x32, 0xe4, 0xde, 0x4a, 0xf8, 0x1b, 0x08, 0x0a, 0x64, 0x34, 0x6f, 0x7d, 0xa2, 0x6f, 0xd8, 0xd1,
	0x40, 0x7a, 0x07, 0x43, 0x79, 0xbc, 0x62, 0xcc, 0xc7, 0x3f, 0x81, 0xfb, 0xaf, 0x08, 0x1f, 0xf2,
	0x5e, 0xcd, 0x3d, 0xf7, 0x32, 0xbd, 0xc3, 0xcd, 0xde, 0x05, 0xe1, 0x72, 0xa3, 0x04, 0xde, 0x51,
	0x90, 0x16, 0xb5, 0xf1, 0x6b, 0x28, 0xa6, 0xf3, 0x62, 0x3e, 0xfa, 0x18, 0x16, 0xa9, 0x7b, 0xee,
	0x49, 0x6e, 0xab, 0x87, 0xc5, 0xca, 0xf0, 0x7e, 0xaf, 0xe8, 0x9e, 0xd7, 0x95, 0xb8, 0x12, 0x0b,
	0xf7, 0x20, 0x17, 0x42, 0xd0, 0x7d, 0x58, 0xe9, 0x79, 0x5e, 0x37, 0x32, 0xdc, 0xb2, 0x18, 0xd6,
	0x6c, 0xb4, 0x0d, 0x79, 0x39, 0x21, 0x4c, 0x16, 0x28, 0x93, 0x13, 0x00, 0xb1, 0x7e, 0x41, 0xd5,
	0xa6, 0xae, 0x2d, 0xa8, 0x16, 0x14, 0x95, 0x18, 0xd6, 0x6c, 0x31, 0x41, 0x99, 0xe1, 0x78, 0xd6,
	0x65, 0x71, 0xb1, 0xac, 0xed, 0xe5, 0xf4, 0x65, 0xca, 0x4e, 0x3c, 0xeb, 0x12, 0xff, 0x14, 0x1e,
	0x7e, 0x69, 0x72, 0xab, 0x33, 0xbd, 0x39, 0x76, 0x61, 0x35, 0x32, 0x07, 0x0b, 0x62, 0x03, 0x06,
	0xf6, 0x60, 0xf8, 0xef, 0x1a, 0xec, 0x8c, 0xe1, 0xc9, 0x7c, 0x54, 0x87, 0x25, 0xb1, 0x60, 0x56,
	0xd4, 0xca, 0x0b, 0x7b, 0xab, 0x87, 0x5f, 0x8c, 0xda, 0x65, 0x2c, 0x75, 0x45, 0x7c, 0xb0, 0xaa,
	0xcb, 0x7b, 0xb7, 0xba, 0x62, 0x53, 0xd2, 0x01, 0x22, 0xa0, 0x50, 0xf9, 0x92, 0xdc, 0x86, 0x2a,
	0x5f, 0x92, 0x5b, 0x54, 0x81, 0xa5, 0x2b, 0xd3, 0xe9, 0x2b, 0x7b, 0x8d, 0xf3, 0x83, 0x42, 0xfb,
	0xde, 0xfc, 0x17, 0x1a, 0xee, 0x86, 0xdb, 0x48, 0x49, 0x0f, 0x54, 0x79, 0xd1, 0xe7, 0x9d, 0xc6,
	0x35, 0xe5, 0x56, 0x27, 0xdd, 0x38, 0x9f, 0x43, 0x51, 0xf1, 0x0c, 0xb7, 0xae, 0x61, 0xf6, 0x79,
	0x27, 0xee, 0xac, 0xad, 0xf3, 0x51, 0x7e, 0xc2, 0x73, 0xf8, 0x09, 0x3c, 0x9e, 0x20, 0x8e, 0xf9,
	0xf8, 0x33, 0xb8, 0x17, 0x59, 0xa5, 0x69, 0xb6, 0xdf, 0x98, 0xbe, 0x50, 0x63, 0x17, 0x56, 0xa3,
	0x38, 0x53, 0x46, 0x5d, 0xd3, 0x61, 0x10, 0x68, 0x0c, 0xff, 0x53, 0x83, 0xcd, 0x24, 0x21, 0xf3,
	0x91, 0x01, 0x1b, 0x21, 0x25, 0x37, 0xdb, 0x46, 0xd7, 0xf4, 0x03, 0x97, 0x7c, 0x3e, 0x6a, 0xa2,
	0x34, 0xf2, 0xca, 0x10, 0x44, 0x79, 0x64, 0xcd, 0x8a, 0xc3, 0x4a, 0xdf, 0x02, 0x4a, 0x22, 0xa5,
	0x78, 0xe8, 0xf9, 0xb0, 0x87, 0x4a, 0xa3, 0xe2, 0x23, 0x26, 0x71, 0x1f, 0xfd, 0x4b, 0x03, 0x88,
	0x66, 0xd0, 0x3e, 0xdc, 0x0d, 0xc9, 0xa2, 0x10, 0x11, 0x42, 0xf2, 0x7a, 0xb8, 0x4c, 0x3d, 0x8c,
	0x94, 0x7d, 0xb8, 0x7b, 0x2e, 0x22, 0x45, 0xa4, 0x04, 0xea, 0x5e, 0x18, 0x9c, 0xdc, 0x70, 0x29,
	0x3c, 0xaf, 0x6f, 0x88, 0x89, 0x33, 0x05, 0x6f, 0x92, 0x1b, 0x8e, 0xf6, 0xa0, 0x30, 0x84, 0x4b,
	0xbb, 0x17, 0x32, 0xbc, 0xf2, 0xfa, 0x7a, 0x0c, 0xb5, 0xd6, 0xbd, 0x40, 0x27, 0x70, 0x27, 0x9e,
	0xd2, 0x8a, 0x8b, 0xb3, 0x66, 0xb3, 0xd5, 0x58, 0x36, 0xc3, 0x3f, 0x86, 0xb5, 0x60, 0x37, 0xb8,
	0x5c, 0x26, 0x85, 0xd4, 0xdc, 0x14, 0xee, 0x37, 0x97, 0x87, 0xb9, 0xe9, 0x3c, 0x24, 0xc2, 0x0d,
	0xb9, 0x61, 0x06, 0x4c, 0xc2, 0xa3, 0x6f, 0x1b, 0xf2, 0xde, 0xb5, 0x4b, 0x7a, 0xb1, 0x93, 0x2f,
	0x27, 0x01, 0x2d, 0x15, 0xdf, 0x01, 0xcb, 0x78, 0x7c, 0x2b, 0x90, 0x8c, 0xef, 0xaf, 0xe5, 0x66,
	0x1a, 0x61, 0xca, 0x7c, 0xf4, 0xc3, 0x01, 0xa1, 0x6d, 0x72, 0x33, 0xd8, 0x48, 0x3b, 0xa3, 0x9e,
	0x1c, 0x5a, 0x51, 0xc8, 0xf7, 0xd8, 0xe4, 0x26, 0x7e, 0x03, 0x1b, 0xc7, 0x61, 0xb0, 0x1f, 0xb9,
	0x93, 0x15, 0x8d, 0xd6, 0x1e, 0xcb, 0xcb, 0x03, 0x3d, 0x31, 0x82, 0xc2, 0x30, 0x3b, 0xe6, 0xe3,
	0xbf, 0x69, 0xf0, 0xa0, 0x71, 0xe4, 0x75, 0xbb, 0xd5, 0x9b, 0x91, 0xe4, 0xc2, 0x49, 0x17, 0x6d,
	0xc1, 0x32, 0xf7, 0x62, 0xa2, 0x96, 0xb8, 0xd7, 0x9a, 0x78, 0x3a, 0xa3, 0xc7, 0x70, 0x67, 0x10,
	0x43, 0xc2, 0xe7, 0x2a, 0xf1, 0x86, 0x11, 0x29, 0x37, 0xdb, 0xfb, 0x30, 0xa8, 0x74, 0x28, 0x33,
	0xfc, 0x6b, 0x3b, 0x48, 0xc2, 0x21, 0x61, 0x8d, 0x9d, 0x5d, 0xdb, 0xe8, 0x3b, 0xb0, 0xc1, 0x3d,
	0x51, 0x73, 0x50, 0x66, 0x9c, 0xf7, 0x28, 0x71, 0xed, 0xe2, 0x92, 0x44, 0x5b, 0x93, 0xe0, 0x1a,
	0x7b, 0x29, 0x81, 0xf8, 0x2f, 0x1a, 0x94, 0x5f, 0x12, 0x6e, 0x75, 0x52, 0x17, 0x92, 0x99, 0xb7,
	0x5f, 0x41, 0xfe, 0x5c, 0x50, 0x9d, 0xb6, 0x7f, 0xa5, 0xbc, 0xba, 0x7a, 0xf8, 0xd1, 0xa8, 0x73,
	0x32, 0x6d, 0xa3, 0x47, 0xb4, 0xe8, 0x21, 0xe4, 0xaf, 0x29, 0xef, 0xc8, 0x43, 0x55, 0xae, 0x36,
	0xa7, 0x47, 0x00, 0xfc, 0x9f, 0x65, 0xd8, 0x6a, 0xa4, 0x69, 0x35, 0x73, 0xdd, 0x33, 0x43, 0xdc,
	0xa5, 0x46, 0xf3, 0x62, 0x76, 0x34, 0xab, 0x6d, 0xe3, 0x98, 0x6d, 0x21, 0xb9, 0x7b, 0x21, 0xed,
	0x2c, 0xb8, 0xaa, 0x3a, 0x4e, 0x80, 0x43, 0xae, 0x71, 0x4c, 0xc9, 0x75, 0x39, 0xe0, 0x1a, 0xa1,
	0x4a, 0xae, 0x4f, 0x60, 0x4d, 0xb8, 0x96, 0xf4, 0xce, 0xbd, 0x5e, 0x97, 0xba, 0x17, 0xc5, 0x15,
	0xe5, 0x61, 0xca, 0xce, 0x06, 0x30, 0xb1, 0x0f, 0x84, 0x6f, 0x15, 0x4f, 0xb3, 0xed, 0x90, 0x62,
	0x2e, 0xc4, 0x52, 0x76, 0x7a, 0xd1, 0x76, 0x88, 0x50, 0x30, 0x9e, 0x94, 0x45, 0xe5, 0x5b, 0xcc,
	0x2b, 0x05, 0xa3, 0xe4, 0x5a, 0x37, 0xbb, 0x72, 0x5f, 0x39, 0x26, 0xe3, 0x86, 0x43, 0xaf, 0x88,
	0xc1, 0x69, 0x97, 0x14, 0x41, 0xda, 0xf0, 0x8e, 0x80, 0x9e, 0xd0, 0x2b, 0xd2, 0xa4, 0x5d, 0x22,
	0x54, 0x0b, 0xf9, 0x39, 0xe4, 0x8a, 0x38, 0xc5, 0x55, 0x85, 0x14, 0x00, 0x4f, 0x04, 0x0c, 0x7d,
	0x0a, 0xef, 0x51, 0x66, 0x74, 0xfb, 0x8c, 0x5a, 0x86, 0x4b, 0x6e, 0xb8, 0x61, 0xd3, 0x1e, 0xb1,
	0xb8, 0xd7, 0x2b, 0xde, 0x91, 0x2a, 0xde, 0xa3, 0xec, 0x8d, 0x98, 0xac, 0x93, 0x1b, 0x7e, 0x1c,
	0x4c, 0x89, 0xf0, 0xb4, 0x48, 0x8f, 0xab, 0x7d, 0xbf, 0xa6, 0xc2, 0x53, 0x00, 0xe4, 0xa6, 0x47,
	0xb0, 0x48, 0x2d, 0xcf, 0x2d, 0xae, 0x4b, 0xd5, 0xe5, 0xb7, 0x80, 0x49, 0x23, 0x6e, 0x28, 0x98,
	0xf8, 0x16, 0xf5, 0xa7, 0xe5, 0x39, 0x5e, 0xaf, 0x58, 0x28, 0x2f, 0xec, 0xe5, 0x75, 0x35, 0x10,
	0xb6, 0x17, 0xb3, 0x06, 0xeb, 0x98, 0xb6, 0xc8, 0x6e, 0x12, 0xe3, 0xae, 0xb2, 0xbd, 0x98, 0x68,
	0x48, 0xf8, 0x91, 0xc4, 0xdd, 0x01, 0xe8, 0x09, 0xaf, 0x2b, 0x24, 0x24, 0xd9, 0xe4, 0x05, 0x44,
	0x4d, 0xef, 0x41, 0x21, 0x9a, 0x36, 0xc8, 0x0d, 0xef, 0x99, 0xc5, 0x7b, 0x12, 0x69, 0x7d, 0x80,
	0x54, 0x15, 0xd0, 0x44, 0x28, 0x6f, 0x26, 0x43, 0xf9, 0x2d, 0xbc, 0x37, 0x68, 0x45, 0x0c, 0x9b,
	0x70, 0x93, 0x3a, 0x86, 0xaa, 0x65, 0xb6, 0x64, 0x48, 0x3d, 0x19, 0x0d, 0xa9, 0x7a, 0x88, 0x7d,
	0x2c, 0x91, 0x65, 0x7c, 0x6e, 0xba, 0x49, 0x20, 0x0b, 0xb7, 0x50, 0xbf, 0xed, 0x50, 0xd6, 0x11,
	0x5b, 0xe8, 0xbd, 0xc1, 0x16, 0x1a, 0xc0, 0x44, 0x10, 0x31, 0x72, 0x53, 0xbc, 0xaf, 0x82, 0x88,
	0x91, 0x1b, 0xfc, 0x5b, 0x0d, 0x1e, 0x4f, 0x48, 0x07, 0xcc, 0x47, 0x3f, 0x87, 0x2d, 0x92, 0x32,
	0x17, 0x96, 0x60, 0x1f, 0x24, 0x32, 0x41, 0x2a, 0xa7, 0x74, 0x1e, 0xf8, 0xaf, 0x1a, 0xa0, 0xb0,
	0x66, 0x6b, 0x7a, 0x7e, 0x3d, 0xc8, 0xde, 0x08, 0x16, 0xfb, 0x51, 0x41, 0x22, 0xbf, 0x85, 0xb7,
	0x1d, 0xda, 0xa5, 0xe1, 0x59, 0xa5, 0x06, 0xe8, 0x35, 0xe4, 0x65, 0x0b, 0xc0, 0x3a, 0xd4, 0x97,
	0x21, 0xbe, 0x7e, 0xf8, 0x34, 0xab, 0x28, 0x8c, 0x04, 0x54, 0x84, 0x47, 0x1a, 0x1d, 0xea, 0xeb,
	0x39, 0x1e, 0x7c, 0x61, 0x0c, 0xb9, 0x10, 0x8a, 0x00, 0x96, 0x55, 0x1e, 0x2d, 0xcc, 0xc9, 0x6f,
	0xc9, 0xa7, 0xa0, 0xe1, 0x3f, 0x6a, 0x70, 0x2f, 0xc1, 0x8d, 0xf9, 0xa8, 0x0a, 0x79, 0xee, 0xf9,
	0x75, 0xd5, 0x88, 0x28, 0xbb, 0xec, 0x4d, 0xd4, 0x82, 0xf9, 0x15, 0xf1, 0xad, 0xe7, 0x04, 0xa9,
	0x38, 0x0b, 0x4b, 0x9f, 0xc3, 0xa2, 0x80, 0x0c, 0x75, 0x90, 0xda, 0x50, 0x07, 0x29, 0xac, 0x20,
	0x8f, 0xb1, 0xd0, 0x0a, 0x72, 0x80, 0x2f, 0x61, 0x2b, 0x51, 0xfd, 0xc9, 0x83, 0xe9, 0x51, 0x78,
	0xd2, 0x09, 0x88, 0x4c, 0xa0, 0x39, 0x3d, 0x06, 0x41, 0x87, 0xb0, 0x19, 0x8d, 0x54, 0xbd, 0xd8,
	0x8c, 0x2a, 0xce, 0xd4, 0x39, 0xdc, 0x87, 0x52, 0x46, 0xc1, 0x99, 0x7e, 0x7c, 0x54, 0x01, 0xfa,
	0x12, 0x5f, 0xb8, 0x3c, 0x28, 0xd3, 0x3e, 0xc8, 0x38, 0xdc, 0x87, 0xd5, 0xd7, 0x63, 0x84, 0x78,
	0x27, 0xec, 0x55, 0x53, 0xc4, 0x32, 0x1f, 0x3f, 0x95, 0x8d, 0xd9, 0x74, 0x2a, 0xe1, 0x5f, 0xc8,
	0xce, 0x2b, 0x95, 0x11, 0x7a, 0x01, 0x39, 0x51, 0x79, 0xd7, 0xa2, 0xee, 0x6b, 0x4a, 0x65, 0x07,
	0x64, 0xf8, 0x33, 0x28, 0x7d, 0x69, 0xf2, 0x2c, 0x75, 0x8a, 0x10, 0x7a, 0x73, 0xc4, 0xb9, 0xb8,
	0x0d, 0xdb, 0x99, 0x74, 0xcc, 0x47, 0x47, 0x90, 0x0f, 0x45, 0x64, 0x46, 0x5f, 0xba, 0x6a, 0x11,
	0x1d, 0xfe, 0xd3, 0x3c, 0xdc, 0x7f, 0xe1, 0x5a, 0x1d, 0xaf, 0x37, 0xcd, 0x39, 0x9b, 0x3c, 0x77,
	0xe6, 0x53, 0xce, 0x9d, 0xe1, 0xd3, 0x78, 0x61, 0x52, 0x9d, 0xb3, 0x98, 0x4c, 0x8e, 0x4f, 0x01,
	0x51, 0x36, 0xe8, 0x7c, 0xe4, 0xa9, 0xe4, 0x5f, 0x06, 0x45, 0xcc, 0x06, 0x65, 0x81, 0xaa, 0xe2,
	0x60, 0x3a, 0xbb, 0x4c, 0x3d, 0x87, 0x97, 0xa7, 0x3f, 0x87, 0x57, 0x52, 0xcf, 0x61, 0xdc, 0x87,
	0xf5, 0x40, 0xcc, 0x1b, 0x6a, 0x49, 0x73, 0x4c, 0xb8, 0x85, 0xd8, 0x82, 0xe5, 0x2e, 0xb5, 0xa2,
	0xfa, 0x63, 0xa9, 0x4b, 0xad, 0x9a, 0x1d, 0x1a, 0x71, 0x21, 0x32, 0xe2, 0x36, 0xe4, 0x29, 0x33,
	0x4c, 0x69, 0xf4, 0xa0, 0x7e, 0xcb, 0x51, 0xa6, 0x9c, 0x80, 0x3f, 0x85, 0x9d, 0x57, 0x84, 0x67,
	0x78, 0x24, 0x23, 0x17, 0x62, 0x03, 0x1e, 0x8d, 0x23, 0x62, 0x3e, 0xfa, 0xc1, 0x70, 0xa3, 0xfc,
	0xe1, 0xe8, 0x3e, 0xc9, 0xa2, 0x55, 0x54, 0xf8, 0x97, 0xb0, 0xd3, 0x18, 0xab, 0xd5, 0xff, 0xc9,
	0xbf, 0x0c, 0x8f, 0x1a, 0x63, 0x17, 0x20, 0xec, 0x72, 0x4c, 0x9c, 0x19, 0xed, 0x52, 0x86, 0x47,
	0xe3, 0x88, 0x98, 0x8f, 0xdf, 0x66, 0x2d, 0x4c, 0x6c, 0xe4, 0xac, 0xa3, 0x67, 0xaa, 0x28, 0xc8,
	0x5e, 0x93, 0x62, 0xcd, 0x7c, 0x7c, 0x00, 0xdb, 0xaf, 0x08, 0xcf, 0xbc, 0x57, 0x4c, 0xe6, 0xa9,
	0x7f, 0x6b, 0xf0, 0x30, 0x9b, 0x82, 0xf9, 0xa8, 0x0f, 0x5b, 0xfd, 0xb4, 0xc9, 0xc0, 0x2d, 0x3f,
	0x4a, 0x69, 0xc6, 0x33, 0x99, 0x55, 0x1a, 0xc9, 0xe2, 0x3d, 0x9d, 0x7b, 0xe9, 0x35, 0xa0, 0x24,
	0x32, 0x2a, 0x87, 0x6d, 0x1c, 0xb1, 0x5b, 0x83, 0x75, 0xc4, 0x41, 0x62, 0x85, 0xd6, 0x20, 0x5e,
	0xc4, 0xa7, 0x48, 0x47, 0xbb, 0xf2, 0x64, 0x8c, 0xee, 0x29, 0x87, 0x18, 0x67, 0xb9, 0xa4, 0x05,
	0xc8, 0x4a, 0x96, 0x24, 0x19, 0x87, 0x4b, 0x7a, 0x49, 0x92, 0xc2, 0x00, 0xb5, 0x60, 0x85, 0x9c,
	0xfa, 0xcd, 0xb0, 0x1b, 0x5b, 0x3f, 0xfc, 0x7e, 0xea, 0x31, 0x9e, 0xad, 0x6c, 0xa5, 0xaa, 0xe6,
	0x64, 0x4f, 0x1e, 0xf2, 0xc2, 0x9f, 0xc0, 0x6a, 0x0c, 0x8e, 0xee, 0x40, 0xae, 0x6a, 0xb4, 0xdc,
	0xaf, 0xbc, 0x6b, 0xb7, 0x30, 0x87, 0xf2, 0xb0, 0x54, 0x35, 0x5e, 0xd8, 0x76, 0x41, 0x53, 0x9f,
	0xc7, 0xc4, 0x29, 0xcc, 0x63, 0x0c, 0xe5, 0xf1, 0xa2, 0x98, 0x8f, 0xff, 0xab, 0xc1, 0xbd, 0x94,
	0x2a, 0x11, 0xad, 0xc3, 0xfc, 0xc0, 0xfe, 0xf3, 0xd4, 0x16, 0x06, 0x94, 0xbd, 0x80, 0xba, 0xa5,
	0x90, 0xdf, 0xa2, 0xc6, 0x68, 0x9b, 0x8c, 0x18, 0xfd, 0x9e, 0x13, 0xb4, 0x46, 0x2b, 0x62, 0xdc,
	0xea, 0x39, 0xa2, 0x8f, 0xb7, 0x6f, 0x5d, 0x53, 0x24, 0x37, 0x31, 0xab, 0xba, 0x21, 0x08, 0x40,
	0x02, 0xe1, 0x13, 0x58, 0x94, 0x89, 0x7c, 0x49, 0x9a, 0x68, 0x27, 0xb3, 0x70, 0x95, 0x46, 0x90,
	0xa8, 0x22, 0x97, 0x32, 0x6e, 0x8a, 0x8a, 0x5f, 0x34, 0x1b, 0xcb, 0x2a, 0x97, 0x4a, 0x88, 0xec,
	0x34, 0x76, 0x61, 0xf5, 0x9c, 0xba, 0x94, 0x75, 0xd4, 0xfc, 0x8a, 0x9c, 0x07, 0x05, 0x92, 0x08,
	0x5b, 0xb0, 0x4c, 0x99, 0xd1, 0x67, 0x61, 0xe3, 0xb3, 0x44, 0x59, 0x8b, 0x11, 0xfc, 0x07, 0x0d,
	0x76, 0x5f, 0xcb, 0x9b, 0xf7, 0x53, 0xd7, 0xa1, 0x2e, 0x51, 0x8d, 0xae, 0x32, 0xd1, 0x59, 0x9f,
	0x65, 0x54, 0x24, 0xfb, 0x70, 0xd7, 0x93, 0xe8, 0x06, 0xb9, 0x22, 0x2e, 0x8f, 0x5f, 0xb2, 0x6d,
	0xa8, 0x89, 0xaa, 0x80, 0x4b, 0x5f, 0xed, 0x41, 0x41, 0x08, 0x96, 0x5e, 0x08, 0xca, 0xf6, 0xa0,
	0x75, 0x5d, 0xa7, 0x4c, 0x39, 0x47, 0x59, 0x1e, 0xd7, 0xa1, 0x3c, 0x5e, 0x15, 0xe6, 0xa7, 0x4b,
	0xd6, 0x52, 0x25, 0xef, 0x57, 0x60, 0xa3, 0x6a, 0x9c, 0xd6, 0x4f, 0x6a, 0xf5, 0xaa, 0xd1, 0x68,
	0xbe, 0x68, 0xb6, 0x1a, 0x68, 0x0d, 0xf2, 0x55, 0xe3, 0xf4, 0xe5, 0x4b, 0x01, 0x2b, 0xcc, 0xa9,
	0x7d, 0xa4, 0x30, 0x0a, 0xda, 0xfe, 0x73, 0x58, 0x1b, 0xb2, 0x3c, 0x5a, 0x07, 0x68, 0xbe, 0x3d,
	0xab, 0x1a, 0x55, 0x5d, 0x3f, 0xd5, 0x0b, 0x73, 0x68, 0x03, 0x56, 0xe5, 0xf8, 0xe4, 0xb4, 0xd9,
	0xac, 0xea, 0x05, 0x6d, 0xdf, 0x84, 0x42, 0xf5, 0x74, 0x64, 0xbd, 0x0f, 0x60, 0xab, 0x5a, 0x6f,
	0xbd, 0x31, 0x4e, 0x4f, 0x8c, 0xea, 0xd7, 0xd5, 0x7a, 0x33, 0x26, 0xae, 0x08, 0x9b, 0x23, 0x53,
	0x81, 0xe8, 0xe4, 0x4c, 0xab, 0xfe, 0x55, 0xfd, 0xf4, 0x9b, 0xc2, 0xfc, 0xe1, 0x3f, 0x36, 0x61,
	0x6d, 0x68, 0xe3, 0xa2, 0xcb, 0xf8, 0x8d, 0x62, 0xac, 0xf8, 0xf8, 0x30, 0xfb, 0xe2, 0x70, 0x28,
	0xf1, 0x97, 0xf6, 0xa6, 0x43, 0x64, 0x3e, 0x9e, 0x43, 0xbf, 0x86, 0x07, 0x99, 0x57, 0xc2, 0xe8,
	0xe3, 0x19, 0x6e, 0x8f, 0xdf, 0x95, 0x9e, 0xcd, 0x74, 0xd7, 0x8c, 0xe7, 0xd0, 0xef, 0x35, 0xd8,
	0x19, 0x7b, 0x33, 0x8b, 0x9e, 0x8f, 0xb2, 0x9c, 0x74, 0x6f, 0x5c, 0xfa, 0x64, 0x46, 0x0a, 0xa9,
	0x88, 0x01, 0x85, 0xd1, 0x4b, 0x58, 0xf4, 0x64, 0xf2, 0x35, 0xed, 0xbb, 0xd2, 0xfb, 0xd3, 0xdc,
	0xe5, 0x0e, 0x04, 0x0c, 0xdd, 0xeb, 0xa5, 0x0a, 0x18, 0xbd, 0x4e, 0x4c, 0x15, 0x90, 0xb8, 0x1e,
	0xc4, 0x73, 0xa8, 0x01, 0x77, 0xe2, 0x37, 0x72, 0x68, 0x77, 0x94, 0x6e, 0xe4, 0xfa, 0xaf, 0x54,
	0x1e, 0x8f, 0x20, 0x99, 0x7e, 0x0b, 0x1b, 0x23, 0x3d, 0x19, 0xc2, 0x93, 0x5b, 0xc7, 0xd2, 0x93,
	0x29, 0x1a, 0xbb, 0xc0, 0xfb, 0x63, 0x9b, 0xeb, 0xa4, 0xf7, 0x27, 0x5d, 0xcd, 0x25, 0xbd, 0x3f,
	0xb1, 0x7b, 0xc7, 0x73, 0x88, 0xc3, 0xfd, 0x8c, 0x4d, 0x82, 0xf6, 0xa7, 0xdc, 0x4d, 0x42, 0xf6,
	0xd3, 0xa9, 0x71, 0xa5, 0xd4, 0xcb, 0xf8, 0x55, 0x6f, 0x4c, 0xe4, 0x87, 0xd9, 0x1e, 0x1f, 0x96,
	0xb7, 0x37, 0x1d, 0x62, 0xb8, 0xc4, 0x8c, 0xbe, 0x29, 0xb9, 0xc4, 0xec, 0xc6, 0xac, 0xf4, 0x74,
	0x6a, 0x5c, 0x29, 0xf5, 0x37, 0x50, 0xca, 0x2e, 0xc2, 0xd1, 0xb3, 0x14, 0xfd, 0xb3, 0xab, 0xd9,
	0x52, 0x65, 0x16, 0xf4, 0x50, 0x7c, 0x63, 0x06, 0xf1, 0x8d, 0xd9, 0xc4, 0x37, 0xa6, 0x10, 0x9f,
	0x5d, 0x6a, 0x27, 0xc5, 0x8f, 0xad, 0xe5, 0x93, 0xe2, 0x27, 0x54, 0xf1, 0x63, 0x56, 0x2f, 0x1b,
	0xd2, 0x29, 0x57, 0x1f, 0xd4, 0xfc, 0xd3, 0xae, 0x7e, 0x50, 0xc7, 0xcf, 0xa1, 0x6b, 0x79, 0x81,
	0x90, 0x5a, 0x4a, 0xa3, 0xa7, 0xd3, 0x17, 0xdd, 0xef, 0x4a, 0x1f, 0xcf, 0x52, 0xa1, 0xe3, 0x39,
	0xf4, 0x3b, 0x2d, 0xf8, 0xdb, 0x35, 0xa3, 0x2e, 0x44, 0x07, 0x33, 0x16, 0xac, 0xa5, 0xe7, 0xb3,
	0x11, 0x0c, 0xb4, 0x18, 0x57, 0xeb, 0x24, 0xb5, 0x98, 0x50, 0xa4, 0x25, 0xb5, 0x98, 0x54, 0x4a,
	0x29, 0x27, 0x64, 0xbd, 0xa6, 0x48, 0x3a, 0x61, 0xcc, 0x83, 0x8e, 0xa4, 0x13, 0xc6, 0x3e, 0xd2,
	0x90, 0x82, 0xb3, 0x9e, 0x38, 0xa0, 0x8c, 0x3c, 0x99, 0xfa, 0xc0, 0x22, 0x29, 0x78, 0xec, 0xcb,
	0x09, 0x65, 0xf7, 0x71, 0x4f, 0x14, 0x92, 0x76, 0x9f, 0xf0, 0xd2, 0xa2, 0xf4, 0x7c, 0x36, 0x82,
	0x91, 0xe5, 0x27, 0x5e, 0x95, 0x64, 0x2e, 0x3f, 0xed, 0x3d, 0x4b, 0xe6, 0xf2, 0xd3, 0x1f, 0xab,
	0xcc, 0x7d, 0x79, 0xf8, 0xb3, 0xe7, 0x17, 0x9e, 0x63, 0xba, 0x17, 0x95, 0xef, 0x1e, 0x72, 0x5e,
	0xb1, 0xbc, 0xee, 0x81, 0x7c, 0xb0, 0x63, 0x79, 0xce, 0x01, 0x23, 0xbd, 0x2b, 0x6a, 0x11, 0x36,
	0xf2, 0x5e, 0xa8, 0xbd, 0x2c, 0x31, 0x3e, 0xfd, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xbc, 0xc8,
	0x12, 0x51, 0x57, 0x24, 0x00, 0x00,
}
