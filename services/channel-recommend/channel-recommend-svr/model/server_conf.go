package model

import (
	"fmt"
	"golang.52tt.com/pkg/config"
)

type ServerConfig struct {
	MysqlConf                *config.MysqlConfig
	MysqlLatinConf           *config.MysqlConfig
	RedisConf                *config.RedisConfig
	channelLotteryKfkConf    *config.KafkaConfig
	channelKfkConf           *config.KafkaConfig
	RecMysqlConf             *config.MysqlConfig
	RecRRedisConf            *config.RedisConfig
	RecMRedisConf            *config.RedisConfig
	RecRelationshipRedisConf *config.RedisConfig
	presentKfkConf           *config.KafkaConfig
	authKfkConf              *config.KafkaConfig
	deepLinkKfkConf          *config.KafkaConfig
	tagKfkConf               *config.KafkaConfig
	emperorKfkConf           *config.KafkaConfig
	UseSmallProcess          bool
	Env                      string
}

func (sc *ServerConfig) Parse(configer config.Configer) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	sc.MysqlConf = config.NewMysqlConfigWithSection(configer, "mysql")
	sc.RedisConf = config.NewRedisConfigWithSection(configer, "redis")
	sc.channelLotteryKfkConf = config.NewKafkaConfigWithSection(configer, "channel_lottery_kfk")
	sc.channelKfkConf = config.NewKafkaConfigWithSection(configer, "channel_kfk")
	sc.RecMysqlConf = config.NewMysqlConfigWithSection(configer, "rec_mysql")
	sc.RecRRedisConf = config.NewRedisConfigWithSection(configer, "rec_r_redis")
	sc.RecMRedisConf = config.NewRedisConfigWithSection(configer, "rec_m_redis")
	sc.RecRelationshipRedisConf = config.NewRedisConfigWithSection(configer, "rec_relationship_redis")
	sc.presentKfkConf = config.NewKafkaConfigWithSection(configer, "present_kfk")
	sc.MysqlLatinConf = config.NewMysqlConfigWithSection(configer, "mysql_latin")
	sc.authKfkConf = config.NewKafkaConfigWithSection(configer, "auth_kfk")
	sc.deepLinkKfkConf = config.NewKafkaConfigWithSection(configer, "deeplink_kfk")
	sc.tagKfkConf = config.NewKafkaConfigWithSection(configer, "tag_kfk")
	sc.UseSmallProcess, _ = configer.Bool("use_small_process")
	sc.emperorKfkConf = config.NewKafkaConfigWithSection(configer, "emperor_kfk")
	sc.Env = configer.String("env")
	return
}

func (sc *ServerConfig) GetChannelLotteryKfkConf() *config.KafkaConfig {
	return sc.channelLotteryKfkConf
}

func (sc *ServerConfig) GetChannelKfkConf() *config.KafkaConfig {
	return sc.channelKfkConf
}

func (sc *ServerConfig) GetPresentKfkConf() *config.KafkaConfig {
	return sc.presentKfkConf
}

func (sc *ServerConfig) GetEmperorKfkConf() *config.KafkaConfig {
	return sc.emperorKfkConf
}

func (sc *ServerConfig) GetAuthKfkConf() *config.KafkaConfig {
	return sc.authKfkConf
}

func (sc *ServerConfig) GetDeepLinkKfkConf() *config.KafkaConfig {
	return sc.deepLinkKfkConf
}

func (sc *ServerConfig) GetTagKfkConf() *config.KafkaConfig {
	return sc.tagKfkConf
}

func (sc *ServerConfig) GetEnv() string {
	return sc.Env
}
