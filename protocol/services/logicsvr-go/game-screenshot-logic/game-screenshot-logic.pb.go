// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/game-screenshot-logic/game-screenshot-logic.proto

package game_screenshot_logic // import "golang.52tt.com/protocol/services/logicsvr-go/game-screenshot-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import game_screenshot_logic "golang.52tt.com/protocol/app/game-screenshot-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameScreenshotLogicClient is the client API for GameScreenshotLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameScreenshotLogicClient interface {
	GetGameScreenshotSummary(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotSummaryReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotSummaryResp, error)
	GetGameScreenshotSetting(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotSettingReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotSettingResp, error)
	UpdateGameScreenshotSetting(ctx context.Context, in *game_screenshot_logic.UpdateGameScreenshotSettingReq, opts ...grpc.CallOption) (*game_screenshot_logic.UpdateGameScreenshotSettingResp, error)
	GetGameScreenshotHint(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotHintReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotHintResp, error)
	GetDiyGameScreenshot(ctx context.Context, in *game_screenshot_logic.GetDiyGameScreenshotReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetDiyGameScreenshotResp, error)
	SetDiyGameScreenshotFinish(ctx context.Context, in *game_screenshot_logic.SetDiyGameScreenshotFinishReq, opts ...grpc.CallOption) (*game_screenshot_logic.SetDiyGameScreenshotFinishResp, error)
	GetGameScreenshotGuide(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotGuideReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotGuideResp, error)
}

type gameScreenshotLogicClient struct {
	cc *grpc.ClientConn
}

func NewGameScreenshotLogicClient(cc *grpc.ClientConn) GameScreenshotLogicClient {
	return &gameScreenshotLogicClient{cc}
}

func (c *gameScreenshotLogicClient) GetGameScreenshotSummary(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotSummaryReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotSummaryResp, error) {
	out := new(game_screenshot_logic.GetGameScreenshotSummaryResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/GetGameScreenshotSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameScreenshotLogicClient) GetGameScreenshotSetting(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotSettingReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotSettingResp, error) {
	out := new(game_screenshot_logic.GetGameScreenshotSettingResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/GetGameScreenshotSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameScreenshotLogicClient) UpdateGameScreenshotSetting(ctx context.Context, in *game_screenshot_logic.UpdateGameScreenshotSettingReq, opts ...grpc.CallOption) (*game_screenshot_logic.UpdateGameScreenshotSettingResp, error) {
	out := new(game_screenshot_logic.UpdateGameScreenshotSettingResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/UpdateGameScreenshotSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameScreenshotLogicClient) GetGameScreenshotHint(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotHintReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotHintResp, error) {
	out := new(game_screenshot_logic.GetGameScreenshotHintResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/GetGameScreenshotHint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameScreenshotLogicClient) GetDiyGameScreenshot(ctx context.Context, in *game_screenshot_logic.GetDiyGameScreenshotReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetDiyGameScreenshotResp, error) {
	out := new(game_screenshot_logic.GetDiyGameScreenshotResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/GetDiyGameScreenshot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameScreenshotLogicClient) SetDiyGameScreenshotFinish(ctx context.Context, in *game_screenshot_logic.SetDiyGameScreenshotFinishReq, opts ...grpc.CallOption) (*game_screenshot_logic.SetDiyGameScreenshotFinishResp, error) {
	out := new(game_screenshot_logic.SetDiyGameScreenshotFinishResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/SetDiyGameScreenshotFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameScreenshotLogicClient) GetGameScreenshotGuide(ctx context.Context, in *game_screenshot_logic.GetGameScreenshotGuideReq, opts ...grpc.CallOption) (*game_screenshot_logic.GetGameScreenshotGuideResp, error) {
	out := new(game_screenshot_logic.GetGameScreenshotGuideResp)
	err := c.cc.Invoke(ctx, "/logic.GameScreenshotLogic/GetGameScreenshotGuide", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameScreenshotLogicServer is the server API for GameScreenshotLogic service.
type GameScreenshotLogicServer interface {
	GetGameScreenshotSummary(context.Context, *game_screenshot_logic.GetGameScreenshotSummaryReq) (*game_screenshot_logic.GetGameScreenshotSummaryResp, error)
	GetGameScreenshotSetting(context.Context, *game_screenshot_logic.GetGameScreenshotSettingReq) (*game_screenshot_logic.GetGameScreenshotSettingResp, error)
	UpdateGameScreenshotSetting(context.Context, *game_screenshot_logic.UpdateGameScreenshotSettingReq) (*game_screenshot_logic.UpdateGameScreenshotSettingResp, error)
	GetGameScreenshotHint(context.Context, *game_screenshot_logic.GetGameScreenshotHintReq) (*game_screenshot_logic.GetGameScreenshotHintResp, error)
	GetDiyGameScreenshot(context.Context, *game_screenshot_logic.GetDiyGameScreenshotReq) (*game_screenshot_logic.GetDiyGameScreenshotResp, error)
	SetDiyGameScreenshotFinish(context.Context, *game_screenshot_logic.SetDiyGameScreenshotFinishReq) (*game_screenshot_logic.SetDiyGameScreenshotFinishResp, error)
	GetGameScreenshotGuide(context.Context, *game_screenshot_logic.GetGameScreenshotGuideReq) (*game_screenshot_logic.GetGameScreenshotGuideResp, error)
}

func RegisterGameScreenshotLogicServer(s *grpc.Server, srv GameScreenshotLogicServer) {
	s.RegisterService(&_GameScreenshotLogic_serviceDesc, srv)
}

func _GameScreenshotLogic_GetGameScreenshotSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.GetGameScreenshotSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/GetGameScreenshotSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotSummary(ctx, req.(*game_screenshot_logic.GetGameScreenshotSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameScreenshotLogic_GetGameScreenshotSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.GetGameScreenshotSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/GetGameScreenshotSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotSetting(ctx, req.(*game_screenshot_logic.GetGameScreenshotSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameScreenshotLogic_UpdateGameScreenshotSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.UpdateGameScreenshotSettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).UpdateGameScreenshotSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/UpdateGameScreenshotSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).UpdateGameScreenshotSetting(ctx, req.(*game_screenshot_logic.UpdateGameScreenshotSettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameScreenshotLogic_GetGameScreenshotHint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.GetGameScreenshotHintReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotHint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/GetGameScreenshotHint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotHint(ctx, req.(*game_screenshot_logic.GetGameScreenshotHintReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameScreenshotLogic_GetDiyGameScreenshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.GetDiyGameScreenshotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).GetDiyGameScreenshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/GetDiyGameScreenshot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).GetDiyGameScreenshot(ctx, req.(*game_screenshot_logic.GetDiyGameScreenshotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameScreenshotLogic_SetDiyGameScreenshotFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.SetDiyGameScreenshotFinishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).SetDiyGameScreenshotFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/SetDiyGameScreenshotFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).SetDiyGameScreenshotFinish(ctx, req.(*game_screenshot_logic.SetDiyGameScreenshotFinishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameScreenshotLogic_GetGameScreenshotGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_screenshot_logic.GetGameScreenshotGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameScreenshotLogic/GetGameScreenshotGuide",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameScreenshotLogicServer).GetGameScreenshotGuide(ctx, req.(*game_screenshot_logic.GetGameScreenshotGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameScreenshotLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.GameScreenshotLogic",
	HandlerType: (*GameScreenshotLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameScreenshotSummary",
			Handler:    _GameScreenshotLogic_GetGameScreenshotSummary_Handler,
		},
		{
			MethodName: "GetGameScreenshotSetting",
			Handler:    _GameScreenshotLogic_GetGameScreenshotSetting_Handler,
		},
		{
			MethodName: "UpdateGameScreenshotSetting",
			Handler:    _GameScreenshotLogic_UpdateGameScreenshotSetting_Handler,
		},
		{
			MethodName: "GetGameScreenshotHint",
			Handler:    _GameScreenshotLogic_GetGameScreenshotHint_Handler,
		},
		{
			MethodName: "GetDiyGameScreenshot",
			Handler:    _GameScreenshotLogic_GetDiyGameScreenshot_Handler,
		},
		{
			MethodName: "SetDiyGameScreenshotFinish",
			Handler:    _GameScreenshotLogic_SetDiyGameScreenshotFinish_Handler,
		},
		{
			MethodName: "GetGameScreenshotGuide",
			Handler:    _GameScreenshotLogic_GetGameScreenshotGuide_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/game-screenshot-logic/game-screenshot-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/game-screenshot-logic/game-screenshot-logic.proto", fileDescriptor_game_screenshot_logic_a4969b4a8cc658d1)
}

var fileDescriptor_game_screenshot_logic_a4969b4a8cc658d1 = []byte{
	// 393 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x94, 0xbb, 0x4e, 0xf3, 0x30,
	0x18, 0x86, 0x95, 0xe8, 0xff, 0x51, 0xf1, 0x68, 0x28, 0xa0, 0xc0, 0x80, 0xd8, 0x18, 0x9a, 0x88,
	0x94, 0x42, 0x19, 0x58, 0x38, 0x85, 0x81, 0x89, 0x8a, 0x85, 0xa5, 0x32, 0xa9, 0x95, 0x5a, 0x6a,
	0xe2, 0x10, 0xbb, 0x45, 0xbd, 0x03, 0x24, 0x24, 0xa6, 0x72, 0x50, 0x91, 0x2f, 0xa4, 0x77, 0xc0,
	0xd6, 0x7b, 0x61, 0x64, 0x42, 0x4e, 0x54, 0xac, 0xaa, 0x49, 0x91, 0x3b, 0x45, 0xc9, 0xf7, 0x3e,
	0x6f, 0x1e, 0x27, 0xd2, 0x07, 0x8e, 0x3b, 0x34, 0x20, 0x3e, 0xeb, 0x25, 0x95, 0x80, 0x3a, 0x01,
	0x0a, 0x71, 0x85, 0xf9, 0x09, 0xc6, 0x11, 0x6b, 0x53, 0x5e, 0x49, 0x67, 0xf9, 0x4f, 0xed, 0x38,
	0xa1, 0x9c, 0xc2, 0xff, 0xe9, 0x8d, 0xb5, 0x3b, 0xdd, 0xc2, 0xf1, 0x03, 0xea, 0x3b, 0x34, 0xe6,
	0x84, 0x46, 0x6c, 0x72, 0xcd, 0x08, 0x6b, 0x1b, 0xc5, 0x71, 0x7e, 0x65, 0x33, 0x4b, 0xb8, 0x9f,
	0x25, 0xb0, 0xe2, 0xa1, 0x10, 0x37, 0x7e, 0xe7, 0x57, 0x72, 0x0c, 0x07, 0x06, 0xd8, 0xf0, 0x30,
	0x9f, 0x1e, 0x35, 0xba, 0x61, 0x88, 0x92, 0x3e, 0xac, 0xd9, 0x01, 0xb2, 0x65, 0x6d, 0x53, 0xd5,
	0x36, 0x33, 0xd3, 0x22, 0xe6, 0x1a, 0xdf, 0x5b, 0x07, 0x8b, 0x60, 0x2c, 0xde, 0x59, 0xfe, 0x1a,
	0x8f, 0xec, 0x7f, 0xa5, 0x81, 0x30, 0x0b, 0xb4, 0x30, 0xe7, 0x24, 0x0a, 0xf4, 0xb4, 0x32, 0x46,
	0x5b, 0x6b, 0x82, 0x29, 0xad, 0x17, 0x61, 0x42, 0x61, 0x80, 0xcd, 0x9b, 0xb8, 0x85, 0x38, 0xce,
	0x37, 0xab, 0x17, 0xbf, 0x62, 0x0e, 0x26, 0xe5, 0x8e, 0x16, 0x24, 0x95, 0xdf, 0xab, 0x30, 0xe1,
	0x93, 0x01, 0xca, 0x33, 0x67, 0xb9, 0x24, 0x11, 0x87, 0xae, 0xc6, 0xe1, 0x25, 0x20, 0x9d, 0xaa,
	0xda, 0x8c, 0xb2, 0x79, 0x13, 0x26, 0x7c, 0x34, 0xc0, 0xaa, 0x87, 0xf9, 0x19, 0xe9, 0x4f, 0x67,
	0xe1, 0xde, 0xdc, 0xe2, 0x99, 0xbc, 0x74, 0x71, 0x75, 0x11, 0xa5, 0xf2, 0x2e, 0x4c, 0x38, 0x34,
	0x80, 0xd5, 0xc8, 0xc9, 0x5d, 0x90, 0x88, 0xb0, 0x36, 0x3c, 0x2c, 0x6e, 0x2f, 0xa6, 0xa4, 0x56,
	0x7d, 0x31, 0x50, 0xc9, 0x0d, 0x85, 0x09, 0x9f, 0x0d, 0xb0, 0x36, 0xf3, 0x41, 0xbd, 0x2e, 0x69,
	0x61, 0xa8, 0xf3, 0x0b, 0x52, 0x42, 0x4a, 0xed, 0xeb, 0x43, 0x4a, 0xe8, 0x43, 0x98, 0xd6, 0xd6,
	0xf7, 0x78, 0x64, 0xaf, 0x83, 0x72, 0xee, 0x46, 0x39, 0x39, 0xbf, 0x3d, 0x0d, 0x68, 0x07, 0x45,
	0x81, 0x5d, 0x73, 0x39, 0xb7, 0x7d, 0x1a, 0x3a, 0xe9, 0x8e, 0xf1, 0x69, 0xc7, 0x61, 0x38, 0xe9,
	0x11, 0x1f, 0x33, 0xe7, 0xcf, 0x0d, 0x78, 0xb7, 0x94, 0x42, 0xd5, 0x9f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x27, 0x11, 0xfc, 0x4b, 0x2d, 0x05, 0x00, 0x00,
}
