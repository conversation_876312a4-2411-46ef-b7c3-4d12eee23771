@echo off
echo Generating mock for IAnchorLevelMgr...
mockgen.exe -destination=mocks/mock_mgr.go -package=mocks golang.52tt.com/services/anchor-level/manager IAnchorLevelMgr
if %errorlevel% neq 0 (
    echo Error generating mock_mgr.go
    goto :eof
)

echo Generating mock for IStore...
mockgen.exe -destination=mocks/mock_store.go -package=mocks golang.52tt.com/services/anchor-level/store IStore
if %errorlevel% neq 0 (
    echo Error generating mock_store.go
    goto :eof
)

echo Generating mock for IFeishuReporterV2...
mockgen.exe -destination=mocks/mock_report.go -package=mocks golang.52tt.com/services/anchor-level/report IFeishuReporterV2
if %errorlevel% neq 0 (
    echo Error generating mock_report.go
    goto :eof
)

echo Generating mock for ISDyConfigHandler...
mockgen.exe -destination=mocks/mock_conf.go -package=mocks golang.52tt.com/services/anchor-level/conf ISDyConfigHandler
if %errorlevel% neq 0 (
    echo Error generating mock_conf.go
    goto :eof
)

echo All mocks generated successfully.
:eof