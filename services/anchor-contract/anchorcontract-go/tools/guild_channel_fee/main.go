package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"time"

	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	channelguild_go "golang.52tt.com/clients/channelguild-go"
	"golang.52tt.com/clients/guild"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/common/status"
	channelguildpb "golang.52tt.com/protocol/services/channelguild-go"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/mysql"
)

type Config struct {
	ContractDB  *config.MysqlConfig
	GiftSlaveDB *config.MysqlConfig
}

var (
	confile = flag.String("confile", "", "confile")
	month   = flag.Int("month", 0, "month")
)

func main() {
	flag.Parse()
	fmt.Printf("month=%d\n", *month)
	if *month == 0 {
		return
	}

	bin, err := ioutil.ReadFile(*confile)
	if err != nil {
		log.Errorln(err)
		return
	}

	sc := &Config{}
	err = json.Unmarshal(bin, sc)
	if err != nil {
		log.Errorf("json.Unmarshal fail %v", err)
		return
	}

	contractDB, err := getMysql(sc.ContractDB)
	if err != nil {
		return
	}
	giftSlaveDB, err := getMysql(sc.GiftSlaveDB)
	if err != nil {
		return
	}

	guildCli := guild.NewClient()
	channelGuildCli, _ := channelguild_go.NewClient()
	time.Sleep(time.Second * 2)

	guildList := []*mysql.ContractInfo{}
	err = contractDB.Table("tbl_contract").Select("distinct(guild_id)as guild_id").Find(&guildList).Error
	if err != nil {
		log.Errorf("get guild list fail %v", err)
		return
	}

	err = contractDB.Exec(tblGuildFeeCreateSQL).Error
	if err != nil {
		log.Errorf("create tbl fail %v", err)
		return
	}
	store := mysql.NewMysql(contractDB, contractDB)

	for _, info := range guildList {
		guildId := info.GuildId
		log.Infof("got guildId=%d", guildId)

		_, serr := guildCli.GetGuild(context.Background(), guildId)
		if serr != nil && serr.Code() == status.ErrGuildNotExist {
			log.Errorf("ErrGuildNotExist guildId=%d", guildId)
			err = store.SetGuildChannelFee(guildId, 1, uint32(*month), uint32(0))
			if err != nil {
				log.Errorf("SetGuildChannelFee fail %v, guildId=%d", err, guildId)
				return
			}
			continue
		}

		resp, serr := channelGuildCli.ListGuildPubChannel(context.Background(), &channelguildpb.ListGuildPubChannelReq{
			GuildIdList: []uint32{guildId},
			PageNum:     100000,
		})
		if serr != nil {
			log.Errorf("ListGuildPubChannel fail %v gid=%d", serr, guildId)
			return
		}
		log.Infof("ListGuildPubChannel guildid=%d total=%d", guildId, resp.GetTotal())

		if resp.GetTotal() == 0 {
			err = store.SetGuildChannelFee(guildId, 1, uint32(*month), uint32(0))
			if err != nil {
				log.Errorf("SetGuildChannelFee fail %v, guildId=%d", err, guildId)
				return
			}
			continue
		}

		for _, channelInfo := range resp.GetInfoList() {
			// 202311,202312
			cid := channelInfo.GetChannelId()

			fee, err := getChannelFee(giftSlaveDB, cid, uint32(*month))
			if err != nil {
				return
			}

			err = store.SetGuildChannelFee(guildId, cid, uint32(*month), uint32(fee))
			if err != nil {
				log.Errorf("SetGuildChannelFee fail %v, cid=%d", err, cid)
				return
			}

			log.Infof("SetGuildChannelFee guildId=%d cid=%d fee=%d", guildId, cid, fee)
			//time.Sleep(time.Millisecond * 30)
		}
	}

}

func getChannelFee(giftSlaveDB *gorm.DB, cid uint32, month uint32) (int64, error) {
	feeInfo := FeeInfo{}
	tbl := fmt.Sprintf("user_present_monthly_history_%d", month)
	err := giftSlaveDB.Table(tbl).Select("sum(total_price) as total_price").
		Where("channel_id=?", cid).Scan(&feeInfo).Error
	if err != nil && !gorm.IsRecordNotFoundError(err) {
		log.Errorf("getChannelFee fail %v, cid=%d", err, cid)
		return 0, err
	}
	return feeInfo.TotalPrice, nil
}

type FeeInfo struct {
	TotalPrice int64 `db:"total_price"`
}

func getMysql(mysqlCfg *config.MysqlConfig) (db *gorm.DB, err error) {
	mysqlDb, err := gorm.Open("mysql", mysqlCfg.ConnectionString())
	if err != nil {
		log.Errorf("Failed to create mysql %v, mysqlCfg=%+v", err, mysqlCfg)
		return nil, err
	}
	mysqlDb = mysqlDb.Debug()
	mysqlDb.DB().SetMaxOpenConns(5)
	return mysqlDb, nil
}

var (
	tblGuildFeeCreateSQL = `CREATE TABLE IF NOT EXISTS tbl_guild_channel_fee_record(
		id int unsigned NOT NULL AUTO_INCREMENT,
		guild_id int(10) unsigned NOT NULL COMMENT '公会id',
		channel_id int(10) unsigned NOT NULL COMMENT '房间id',
		month int(10) unsigned NOT NULL COMMENT '月份',
		fee bigint unsigned NOT NULL COMMENT '总流水',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (id),
		UNIQUE KEY idx_channelid_month (channel_id,month),
		KEY idx_guild_id (guild_id),
		KEY idx_month (month)
	  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '公会房月流水记录表';`
)
