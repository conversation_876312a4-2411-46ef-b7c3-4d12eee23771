# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

50951:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50951 --source api/treasure_house/grpc_treasure_house.proto --lang go --method /ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivityList
50952:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50952 --source api/treasure_house/grpc_treasure_house.proto --lang go --method /ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivity
50953:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50953 --source api/treasure_house/grpc_treasure_house.proto --lang go --method /ga.api.treasure_house.TreasureHouseLogic/ClaimPresentPermission
50954:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50954 --source api/treasure_house/grpc_treasure_house.proto --lang go --method /ga.api.treasure_house.TreasureHouseLogic/BuyPresentPermission
50955:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50955 --source api/treasure_house/grpc_treasure_house.proto --lang go --method /ga.api.treasure_house.TreasureHouseLogic/GetTreasureActivityUpdateInfo
