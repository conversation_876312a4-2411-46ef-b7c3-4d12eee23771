package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	pb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia/conf"
	"testing"
	"time"
)

// 正常的点本
func TestOrderDramaService_OrderDrama(t *testing.T) {
	t.Skip()
	controller := gomock.NewController(t)
	defer controller.Finish()
	idService := NewMockIDService(controller)
	lockService := NewMockLockService(controller)
	orderDramaListRepo := NewMockOrderDramaListRepo(controller)
	timeService := NewMockTimeService(controller)
	orderDramaService := NewOrderDramaService(lockService, idService, orderDramaListRepo, conf.NewDefaultOrderDramaList(), timeService)
	// 构造参数
	req := &pb.OrderDramaReq{
		DramaId:   123,
		ChannelId: 123,
		UserId:    123,
	}
	// 构造行为
	lockService.EXPECT().TryLock(gomock.Any(), gomock.Any()).Return(true).Times(1)
	lockService.EXPECT().Unlock(gomock.Any()).Times(1)
	idService.EXPECT().GenerateID().Times(1).Return(int64(123))
	orderDramaListRepo.EXPECT().Save(gomock.Any(), gomock.Any()).Times(1)
	orderDramaList := &OrderDramaList{
		ChannelID: 123,
	}
	orderDramaListRepo.EXPECT().FetchAndCreateIfNotPresent(gomock.Any(), gomock.Any(), gomock.Any()).Return(orderDramaList, nil).Times(1)
	timeService.EXPECT().CurrentTime(gomock.Any()).Return(time.Now(), nil).AnyTimes()

	err := orderDramaService.OrderDrama(context.Background(), req)
	assert.Nil(t, err)
}

func TestOrderDramaService_BatchDeleteOrderDrama(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()
	idService := NewMockIDService(controller)
	lockService := NewMockLockService(controller)
	orderDramaListRepo := NewMockOrderDramaListRepo(controller)
	timeService := NewMockTimeService(controller)
	orderDramaService := NewOrderDramaService(lockService, idService, orderDramaListRepo, conf.NewDefaultOrderDramaList(), timeService)
	lockService.EXPECT().TryLock(gomock.Any(), gomock.Any()).Return(true).Times(1)
	lockService.EXPECT().Unlock(gomock.Any()).Times(1)
	orderDramaListRepo.EXPECT().Save(gomock.Any(), gomock.Any()).Times(1)
	orderDramaListRepo.EXPECT().Fetch(gomock.Any(), gomock.Any(), gomock.Any()).Return(&OrderDramaList{
		ChannelID: 123,
		List:      make([]*OrderDrama, 0),
	}, nil).Times(1)
	timeService.EXPECT().CurrentTime(gomock.Any()).Return(time.Now(), nil).AnyTimes()

	err := orderDramaService.BatchDeleteOrderDrama(context.Background(), &BatchDeleteOrderDramaReq{
		indexID:   []int64{1, 2, 3},
		channelID: 123,
		userID:    123,
		userAuth:  UserAuthAdmin,
	})
	assert.Nil(t, err)
}

func TestNewBatchDeleteOrderDramaReq(t *testing.T) {

	data := []struct {
		name    string
		req     *pb.DeleteOrderDramaReq
		wantErr assert.ErrorAssertionFunc
		want    *BatchDeleteOrderDramaReq
	}{
		{
			name: "正常",
			req: &pb.DeleteOrderDramaReq{
				IndexIdList:  []int64{1, 2, 3},
				ChannelId:    123,
				UserId:       123,
				UserAuthType: pb.PiaDramaOrderUserAuthType_NORMAL,
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				assert.NoError(t, err)
				return false
			},
			want: &BatchDeleteOrderDramaReq{},
		},
	}
	for _, item := range data {
		t.Run(item.name, func(t *testing.T) {
			got, err := NewBatchDeleteOrderDramaReq(item.req)
			if !item.wantErr(t, err) {
				return
			}
			assert.Equal(t, item.want, got)
		})
	}
}

func TestNewOrderDramaFromPb(t *testing.T) {

	controller := gomock.NewController(t)
	idService := NewMockIDService(controller)

	testData := []struct {
		name     string
		pb       *pb.OrderDramaReq
		want     *OrderDrama
		wantErr  error
		initFunc func()
	}{
		{
			name: "正常情况",
			pb: &pb.OrderDramaReq{
				DramaId:   123,
				ChannelId: 123,
				UserId:    123,
			},
			want: &OrderDrama{
				DramaID: 123,
				UserID:  123,
				IndexID: 123,
			},
			wantErr: nil,
			initFunc: func() {
				idService.EXPECT().GenerateID().Return(int64(123)).AnyTimes()
			},
		},
		{
			name: "缺少剧本ID",
			pb: &pb.OrderDramaReq{
				ChannelId: 123,
				UserId:    123,
			},
			want:    nil,
			wantErr: ErrMissingDramaID,
			initFunc: func() {
				idService.EXPECT().GenerateID().Return(int64(123)).AnyTimes()
			},
		},
		{
			name: "缺少房间ID",
			pb: &pb.OrderDramaReq{
				DramaId: 123,
				UserId:  123,
			},
			want:    nil,
			wantErr: ErrMissingChannelID,
			initFunc: func() {
				idService.EXPECT().GenerateID().Return(int64(123)).AnyTimes()
			},
		},
		{
			name: "缺少用户ID",
			pb: &pb.OrderDramaReq{
				DramaId:   123,
				ChannelId: 123,
			},
			wantErr: ErrMissingUserID,
			initFunc: func() {
				idService.EXPECT().GenerateID().Return(int64(123)).AnyTimes()
			},
		},
	}

	for _, item := range testData {
		t.Run(item.name, func(t *testing.T) {
			if item.initFunc != nil {
				item.initFunc()
			}
			orderDrama, err := NewOrderDramaFromPb(item.pb, idService)
			assert.Equal(t, item.wantErr, err)
			if item.wantErr == nil {
				assert.Equal(t, item.want.DramaID, orderDrama.DramaID)
				assert.Equal(t, item.want.UserID, orderDrama.UserID)
				assert.Equal(t, item.want.IndexID, orderDrama.IndexID)
			}
		})
	}
}

func TestOrderDramaList_AddDrama(t *testing.T) {
	t.Skip()
	testData := []struct {
		name    string
		wantErr error
		list    *OrderDramaList
		drama   *OrderDrama
		config  *conf.OrderDramaList
	}{
		{
			name:    "正常添加",
			wantErr: nil,
			list:    &OrderDramaList{},
			drama:   &OrderDrama{},
			config:  conf.NewDefaultOrderDramaList(),
		},
		{
			name:    "超过列表限制",
			wantErr: ErrOrderDramaListReachLimit,
			list: &OrderDramaList{
				List: make([]*OrderDrama, 1000),
			},
			drama:  &OrderDrama{},
			config: conf.NewDefaultOrderDramaList(),
		},
		{
			name:    "超过列表限制",
			wantErr: ErrOrderDramaUserLimit,
			list: &OrderDramaList{
				List: []*OrderDrama{
					{
						DramaID: 123,
						UserID:  123,
					},
					{
						DramaID: 1233,
						UserID:  123,
					},
					{
						DramaID: 12333,
						UserID:  123,
					},
				},
			},
			drama: &OrderDrama{
				DramaID: 211,
				UserID:  123,
			},
			config: conf.NewDefaultOrderDramaList(),
		},
		{
			name:    "重复添加",
			wantErr: ErrOrderDramaAlreadyExist,
			list: &OrderDramaList{
				List: []*OrderDrama{
					{
						DramaID: 123,
						UserID:  123,
					},
					{
						DramaID: 1233,
						UserID:  123,
					},
					{
						DramaID: 12333,
						UserID:  1234,
					},
				},
			},
			drama: &OrderDrama{
				DramaID: 1233,
				UserID:  123,
			},
			config: conf.NewDefaultOrderDramaList(),
		},
	}
	for _, item := range testData {
		t.Run(item.name, func(t *testing.T) {
			orgLen := len(item.list.List)
			err := item.list.AddDrama(item.drama, item.config)
			assert.Equal(t, item.wantErr, err)
			if item.wantErr == nil {
				assert.Equal(t, orgLen+1, len(item.list.List))
			}
		})
	}
}

func TestOrderDramaList_BatchDeleteDramaByIndexID(t *testing.T) {
	testData := []struct {
		name        string
		list        *OrderDramaList
		indexIDList []int64
		left        int
	}{
		{
			name: "正常删除",
			list: &OrderDramaList{
				List: []*OrderDrama{
					{
						IndexID: 1,
					},
					{
						IndexID: 2,
					},
					{
						IndexID: 3,
					},
					{
						IndexID: 4,
					},
				},
			},
			indexIDList: []int64{1, 2, 3},
			left:        1,
		},
		{
			name: "部分删除",
			list: &OrderDramaList{
				List: []*OrderDrama{
					{
						IndexID: 1,
					},
					{
						IndexID: 2,
					},
					{
						IndexID: 3,
					},
					{
						IndexID: 4,
					},
				},
			},
			indexIDList: []int64{1, 2, 30},
			left:        2,
		},
	}
	for _, item := range testData {
		t.Run(item.name, func(t *testing.T) {
			item.list.BatchDeleteDramaByIndexID(item.indexIDList...)
			assert.Equal(t, item.left, len(item.list.List))
		})
	}
}
func TestOrderDramaList_BatchDeleteDramaByDramaID(t *testing.T) {
	testData := []struct {
		name        string
		list        *OrderDramaList
		dramaIDList []uint32
		left        int
	}{
		{
			name: "正常删除",
			list: &OrderDramaList{
				List: []*OrderDrama{
					{
						DramaID: 1,
					},
					{
						DramaID: 2,
					},
					{
						DramaID: 3,
					},
					{
						DramaID: 4,
					},
				},
			},
			dramaIDList: []uint32{1, 2, 3},
			left:        1,
		},
		{
			name: "部分删除",
			list: &OrderDramaList{
				List: []*OrderDrama{
					{
						DramaID: 1,
					},
					{
						DramaID: 2,
					},
					{
						DramaID: 3,
					},
					{
						DramaID: 4,
					},
				},
			},
			dramaIDList: []uint32{1, 2, 30},
			left:        2,
		},
	}
	for _, item := range testData {
		t.Run(item.name, func(t *testing.T) {
			item.list.BatchDeleteDramaByDramaID(item.dramaIDList...)
			assert.Equal(t, item.left, len(item.list.List))
		})
	}
}

func TestOrderDramaList_GetAllDramaIDs(t *testing.T) {
	testData := &OrderDramaList{
		List: []*OrderDrama{
			{
				DramaID: 1,
			},
			{
				DramaID: 2,
			},
			{
				DramaID: 3,
			},
			{
				DramaID: 4,
			},
			{
				DramaID: 5,
			},
			{
				DramaID: 6,
			},
		},
	}
	want := []uint32{1, 2, 3, 4, 5, 6}
	ds := testData.GetAllDramaIDs()
	dsMap := make(map[uint32]bool)
	for _, v := range ds {
		dsMap[v] = true
	}
	assert.Equal(t, len(want), len(ds))
	for _, item := range want {
		b, ok := dsMap[item]
		assert.True(t, ok)
		assert.True(t, b)
	}
}

func TestOrderDramaList_GetAllUserIDs(t *testing.T) {
	testData := &OrderDramaList{
		List: []*OrderDrama{
			{
				UserID: 1,
			},
			{
				UserID: 2,
			},
			{
				UserID: 3,
			},
			{
				UserID: 4,
			},
			{
				UserID: 5,
			},
			{
				UserID: 6,
			},
		},
	}
	want := []uint32{1, 2, 3, 4, 5, 6}
	ds := testData.GetAllUserIDs()
	dsMap := make(map[uint32]bool)
	for _, v := range ds {
		dsMap[v] = true
	}
	assert.Equal(t, len(want), len(ds))
	for _, item := range want {
		b, ok := dsMap[item]
		assert.True(t, ok)
		assert.True(t, b)
	}
}

func TestOrderDramaService_ClearChannelOrderDrama(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()
	idService := NewMockIDService(controller)
	lockService := NewMockLockService(controller)
	orderDramaListRepo := NewMockOrderDramaListRepo(controller)
	timeService := NewMockTimeService(controller)
	orderDramaService := NewOrderDramaService(lockService, idService, orderDramaListRepo, conf.NewDefaultOrderDramaList(), timeService)

	testData := []struct {
		name      string
		channelID uint32
		initFunc  func()
		wantErr   assert.ErrorAssertionFunc
	}{
		{
			name: "正常清除",
			initFunc: func() {
				orderDramaListRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil)
			},
			channelID: 123,
			wantErr:   assert.NoError,
		},
	}

	for _, item := range testData {
		t.Run(item.name, func(t *testing.T) {
			if item.initFunc != nil {
				item.initFunc()
			}
			err := orderDramaService.ClearChannelOrderDrama(context.Background(), item.channelID)
			item.wantErr(t, err)
		})
	}

}
