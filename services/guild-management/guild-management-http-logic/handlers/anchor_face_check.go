package hanlders

import (
	"context"
	"encoding/json"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/http"
	"golang.52tt.com/pkg/web"
	guildmanagementsvr "golang.52tt.com/protocol/services/guild-management-svr"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
	"time"
)

const MaxQueryDays = 31 * 24 * 60 * 60 // 最大查询31天

// BatchGetDailyFaceCheckInfo 获取公会主播人脸核验日明细列表
func BatchGetDailyFaceCheckInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchGetDailyFaceCheckInfoReq{}
	resp := &api.BatchGetDailyFaceCheckInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetDailyFaceCheckInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "BatchGetDailyFaceCheckInfo begin %s req=%+v", string(authInfo.Body), req)
	guildId := req.GetGuildId()

	// 校验参数
	if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的时间范围错误", nil)
		return
	}
	if guildId == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的公会ID错误", nil)
		return
	}
	if req.GetPage() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的页码错误", nil)
	}
	if req.GetPageSize() == 0 {
		req.PageSize = 10 // 默认10条
	}

	if req.GetEndTs()-req.GetBeginTs() > MaxQueryDays || req.GetEndTs()-req.GetBeginTs() < 0 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询天数大于31", nil)
		return
	}
	if len(req.GetTtidList()) > 20 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询的id数量大于20", nil)
		return
	}

	// 校验权限
	var perType string
	switch api.SIGN_ANCHOR_IDENTITY(req.GetIdentity()) {
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER:
		perType = api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail.String()
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE:
		perType = api.GuildLiveDataMenuType_AnchorFaceCheckDetail.String()
	}
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, perType)
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	// 查询数据
	svrResp, serverError := models.GetModelServer().GuildManagementCli.BatchGetDailyFaceCheckInfo(ctx, &guildmanagementsvr.BatchGetDailyFaceCheckInfoReq{
		GuildId:  guildId,
		Identity: req.GetIdentity(),
		BeginTs:  req.GetBeginTs(),
		EndTs:    req.GetEndTs(),
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		TtidList: req.GetTtidList(),
	})
	if serverError != nil {
		log.ErrorWithCtx(ctx, "BatchGetDailyFaceCheckInfo failed uid:%d req:%v err:%v", uid, req, serverError)
		_ = web.ServeAPICodeJson(w, int32(serverError.Code()), serverError.Message(), nil)
		return
	}

	resp.Total = svrResp.GetTotal()
	resp.InfoList = make([]*api.DailyFaceCheckInfo, 0)
	for _, info := range svrResp.GetInfoList() {
		resp.InfoList = append(resp.InfoList, &api.DailyFaceCheckInfo{
			Date:              info.GetDate(),
			Account:           info.GetAccount(),
			Nickname:          info.GetNickname(),
			Ttid:              info.GetTtid(),
			SignStartTs:       info.GetSignStartTs(),
			SignEndTs:         info.GetSignEndTs(),
			AgentNickname:     info.GetAgentNickname(),
			RuleType:          info.GetRuleType(),
			DisplayId:         info.GetChannelDisplayId(),
			GuildShortId:      info.GetGuildShortId(),
			RoomGiftAmt:       info.GetRoomGiftAmt(),
			GuildGiftAmt:      info.GetGuildGiftAmt(),
			LessCommissionAmt: info.GetLessCommissionAmt(),
		})
	}

	_ = web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "BatchGetDailyFaceCheckInfo uid:%d req:%v resp:%v", uid, req, resp)
}

// BatchGetWeeklyFaceCheckInfo 获取公会主播人脸核验周明细列表
func BatchGetWeeklyFaceCheckInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchGetWeeklyFaceCheckInfoReq{}
	resp := &api.BatchGetWeeklyFaceCheckInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetWeeklyFaceCheckInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "BatchGetWeeklyFaceCheckInfo begin %s req=%+v", string(authInfo.Body), req)
	guildId := req.GetGuildId()

	// 校验参数
	if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的时间范围错误", nil)
		return
	}
	if guildId == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的公会ID错误", nil)
		return
	}
	if req.GetPage() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的页码错误", nil)
	}
	if req.GetPageSize() == 0 {
		req.PageSize = 10 // 默认10条
	}

	if len(req.GetTtidList()) > 20 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询的id数量大于20", nil)
		return
	}

	// 校验权限
	var perType string
	switch api.SIGN_ANCHOR_IDENTITY(req.GetIdentity()) {
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER:
		perType = api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail.String()
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE:
		perType = api.GuildLiveDataMenuType_AnchorFaceCheckDetail.String()
	}
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, perType)
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	// 查询数据
	svrResp, serverError := models.GetModelServer().GuildManagementCli.BatchGetWeeklyFaceCheckInfo(ctx, &guildmanagementsvr.BatchGetWeeklyFaceCheckInfoReq{
		GuildId:  guildId,
		Identity: req.GetIdentity(),
		BeginTs:  req.GetBeginTs(),
		EndTs:    req.GetEndTs(),
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
		TtidList: req.GetTtidList(),
	})
	if serverError != nil {
		log.ErrorWithCtx(ctx, "BatchGetWeeklyFaceCheckInfo failed uid:%d req:%v err:%v", uid, req, serverError)
		_ = web.ServeAPICodeJson(w, int32(serverError.Code()), serverError.Message(), nil)
		return
	}

	resp.Total = svrResp.GetTotal()
	resp.InfoList = make([]*api.WeeklyFaceCheckInfo, 0)
	for _, info := range svrResp.GetInfoList() {
		resp.InfoList = append(resp.InfoList, &api.WeeklyFaceCheckInfo{
			Date:              info.GetDate(),
			Account:           info.GetAccount(),
			Nickname:          info.GetNickname(),
			Ttid:              info.GetTtid(),
			SignStartTs:       info.GetSignStartTs(),
			SignEndTs:         info.GetSignEndTs(),
			AgentNickname:     info.GetAgentNickname(),
			RuleType:          info.GetRuleType(),
			DisplayId:         info.GetChannelDisplayId(),
			GuildShortId:      info.GetGuildShortId(),
			RoomGiftAmt:       info.GetRoomGiftAmt(),
			GuildGiftAmt:      info.GetGuildGiftAmt(),
			LessCommissionAmt: info.GetLessCommissionAmt(),
		})
	}

	_ = web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "BatchGetWeeklyFaceCheckInfo uid:%d req:%v resp:%v", uid, req, resp)
}

// BatchGetGuildWeeklySumFaceCheckInfo 获取公会主播人脸核验公会每周汇总
func BatchGetGuildWeeklySumFaceCheckInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchGetGuildWeeklySumFaceCheckInfoReq{}
	resp := &api.BatchGetGuildWeeklySumFaceCheckInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGuildWeeklySumFaceCheckInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "BatchGetGuildWeeklySumFaceCheckInfo begin %s req=%+v", string(authInfo.Body), req)
	guildId := req.GetGuildId()

	// 校验参数
	if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的时间范围错误", nil)
		return
	}
	if guildId == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的公会ID错误", nil)
		return
	}
	if req.GetPage() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的页码错误", nil)
	}
	if req.GetPageSize() == 0 {
		req.PageSize = 10 // 默认10条
	}

	if req.GetEndTs()-req.GetBeginTs() > MaxQueryDays || req.GetEndTs()-req.GetBeginTs() < 0 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询天数大于31", nil)
		return
	}

	// 校验权限
	var perType string
	switch api.SIGN_ANCHOR_IDENTITY(req.GetIdentity()) {
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER:
		perType = api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail.String()
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE:
		perType = api.GuildLiveDataMenuType_AnchorFaceCheckDetail.String()
	}
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, perType)
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	// 查询数据
	svrResp, serverError := models.GetModelServer().GuildManagementCli.BatchGetWeeklySumFaceCheckInfo(ctx, &guildmanagementsvr.BatchGetWeeklySumFaceCheckInfoReq{
		GuildId:  guildId,
		Identity: req.GetIdentity(),
		BeginTs:  req.GetBeginTs(),
		EndTs:    req.GetEndTs(),
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
	})
	if serverError != nil {
		log.ErrorWithCtx(ctx, "BatchGetWeeklySumFaceCheckInfo failed uid:%d req:%v err:%v", uid, req, serverError)
		_ = web.ServeAPICodeJson(w, int32(serverError.Code()), serverError.Message(), nil)
		return
	}

	resp.Total = svrResp.GetTotal()
	resp.InfoList = make([]*api.GuildWeeklySumFaceCheckInfo, 0)
	for _, info := range svrResp.GetInfoList() {
		resp.InfoList = append(resp.InfoList, &api.GuildWeeklySumFaceCheckInfo{
			Date:                   info.GetDate(),
			RuleType:               info.GetRuleType(),
			ActionUserCnt:          info.GetActionUserCnt(),
			FaceUserCnt:            info.GetFaceUserCnt(),
			NotHimselfUserCnt:      info.GetNotHimselfUserCnt(),
			FaceRatio:              info.GetFaceRatio(),
			HimselfRatio:           info.GetHimselfRatio(),
			FaceHimselfRatio:       info.GetFaceHimselfRatio(),
			NotHimselfGuildGiftAmt: info.GetNotHimselfGuildGiftAmt(),
			LessCommissionAmt:      info.GetLessCommissionAmt(),
		})
	}

	_ = web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "BatchGetWeeklySumFaceCheckInfo uid:%d req:%v resp:%v", uid, req, resp)
}

// BatchGetGuildDailySumFaceCheckInfo 获取公会主播人脸核验公会每日汇总列表
func BatchGetGuildDailySumFaceCheckInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.BatchGetGuildDailySumFaceCheckInfoReq{}
	resp := &api.BatchGetGuildDailySumFaceCheckInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGuildDailySumFaceCheckInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "BatchGetGuildDailySumFaceCheckInfo begin %s req=%+v", string(authInfo.Body), req)
	guildId := req.GetGuildId()

	// 校验参数
	if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的时间范围错误", nil)
		return
	}
	if guildId == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的公会ID错误", nil)
		return
	}
	if req.GetPage() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的页码错误", nil)
	}
	if req.GetPageSize() == 0 {
		req.PageSize = 10 // 默认10条
	}

	if req.GetEndTs()-req.GetBeginTs() > MaxQueryDays || req.GetEndTs()-req.GetBeginTs() < 0 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询天数大于31", nil)
		return
	}

	// 校验权限
	var perType string
	switch api.SIGN_ANCHOR_IDENTITY(req.GetIdentity()) {
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER:
		perType = api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail.String()
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE:
		perType = api.GuildLiveDataMenuType_AnchorFaceCheckDetail.String()
	}
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, perType)
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	// 查询数据
	svrResp, serverError := models.GetModelServer().GuildManagementCli.BatchGetDailySumFaceCheckInfo(ctx, &guildmanagementsvr.BatchGetDailySumFaceCheckInfoReq{
		GuildId:  guildId,
		Identity: req.GetIdentity(),
		BeginTs:  req.GetBeginTs(),
		EndTs:    req.GetEndTs(),
		Page:     req.GetPage(),
		PageSize: req.GetPageSize(),
	})
	if serverError != nil {
		log.ErrorWithCtx(ctx, "BatchGetGuildDailySumFaceCheckInfo failed uid:%d req:%v err:%v", uid, req, serverError)
		_ = web.ServeAPICodeJson(w, int32(serverError.Code()), serverError.Message(), nil)
		return
	}

	resp.Total = svrResp.GetTotal()
	resp.InfoList = make([]*api.GuildDailySumFaceCheckInfo, 0)
	for _, info := range svrResp.GetInfoList() {
		resp.InfoList = append(resp.InfoList, &api.GuildDailySumFaceCheckInfo{
			Date:                   info.GetDate(),
			RuleType:               info.GetRuleType(),
			ActionUserCnt:          info.GetActionUserCnt(),
			FaceUserCnt:            info.GetFaceUserCnt(),
			NotHimselfUserCnt:      info.GetNotHimselfUserCnt(),
			FaceRatio:              info.GetFaceRatio(),
			HimselfRatio:           info.GetHimselfRatio(),
			FaceHimselfRatio:       info.GetFaceHimselfRatio(),
			NotHimselfGuildGiftAmt: info.GetNotHimselfGuildGiftAmt(),
			LessCommissionAmt:      info.GetLessCommissionAmt(),
		})
	}

	_ = web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "BatchGetGuildDailySumFaceCheckInfo uid:%d req:%v resp:%v", uid, req, resp)
}

func ExportFaceCheckInfo(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID
	req := &api.ExportFaceCheckInfoReq{}
	resp := &api.ExportFaceCheckInfoResp{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "ExportFaceCheckInfo Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "ExportFaceCheckInfo begin %s req=%+v", string(authInfo.Body), req)
	guildId := req.GetGuildId()

	// 校验参数
	if req.GetBeginTs() == 0 || req.GetEndTs() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的时间范围错误", nil)
		return
	}
	if guildId == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的公会ID错误", nil)
		return
	}
	if req.GetExportType() == 0 {
		_ = web.ServeAPICodeJson(w, -403, "请求的导出类型错误", nil)
		return
	}
	if req.GetEndTs()-req.GetBeginTs() > MaxQueryDays || req.GetEndTs()-req.GetBeginTs() < 0 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询天数大于31", nil)
		return
	}
	if len(req.GetTtidList()) > 20 {
		_ = web.ServeAPICodeJson(w, -403, "不支持设置查询的id数量大于20", nil)
		return
	}

	// 校验权限
	var perType string
	switch api.SIGN_ANCHOR_IDENTITY(req.GetIdentity()) {
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_MULTIPLAYER:
		perType = api.GuildMultiPlayDataType_MultiAnchorFaceCheckDetail.String()
	case api.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE:
		perType = api.GuildLiveDataMenuType_AnchorFaceCheckDetail.String()
	}
	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, perType)
	if !hasPer {
		_ = web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	// 查询数据
	svrResp, serverError := models.GetModelServer().GuildManagementCli.ExportFaceCheckInfo(ctx, &guildmanagementsvr.ExportFaceCheckInfoReq{
		Uid:        uid,
		GuildId:    guildId,
		BeginTs:    req.GetBeginTs(),
		EndTs:      req.GetEndTs(),
		TtidList:   req.GetTtidList(),
		ExportType: guildmanagementsvr.FaceCheckExportType(req.GetExportType()),
		Identity:   req.GetIdentity(),
	})
	if serverError != nil {
		log.ErrorWithCtx(ctx, "ExportFaceCheckInfo failed uid:%d req:%v err:%v", uid, req, serverError)
		_ = web.ServeAPICodeJson(w, int32(serverError.Code()), serverError.Message(), nil)
		return
	}

	resp.DownloadUrl = svrResp.GetDownloadUrl()
	_ = web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "ExportFaceCheckInfo uid:%d req:%v resp:%v", uid, req, resp)
}
