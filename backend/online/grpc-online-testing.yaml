apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-online-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.online.OnlineLogic/
    rewrite:
      uri: /logic.OnlineLogic/
    delegate:
       name: online-logic-delegator-80
       namespace: quicksilver


