package manager

import (
	"context"
	"reflect"
	"sync"
	"testing"
)

func TestNewPopUpConfManager(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    *PopUpConfManager
		wantErr bool
	}{
		//{
		//	name:    "",
		//	args:    args{ctx: context.Background()},
		//	want:    nil,
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewPopUpConfManager(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewPopUpConfManager() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("NewPopUpConfManager() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPopUpConfManager_GetPresentPopUpIdList(t *testing.T) {
	type fields struct {
		PresentPopIdList []uint32
		stop             chan interface{}
		sw               sync.WaitGroup
		rwLock           sync.RWMutex
	}
	tests := []struct {
		name   string
		fields fields
		want   []uint32
	}{
		{
			name: "",
			fields: fields{
				PresentPopIdList: nil,
				stop:             nil,
				sw:               sync.WaitGroup{},
				rwLock:           sync.RWMutex{},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PopUpConfManager{
				PresentPopIdList: tt.fields.PresentPopIdList,
				stop:             tt.fields.stop,
				sw:               tt.fields.sw,
				rwLock:           tt.fields.rwLock,
			}
			if got := p.GetPresentPopUpIdList(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPresentPopUpIdList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPopUpConfManager_ReadConfFromFile(t *testing.T) {
	type fields struct {
		PresentPopIdList []uint32
		stop             chan interface{}
		sw               sync.WaitGroup
		rwLock           sync.RWMutex
	}
	type args struct {
		ctx      context.Context
		filePath string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PopUpConfManager{
				PresentPopIdList: tt.fields.PresentPopIdList,
				stop:             tt.fields.stop,
				sw:               tt.fields.sw,
				rwLock:           tt.fields.rwLock,
			}
			if err := p.ReadConfFromFile(tt.args.ctx, tt.args.filePath); (err != nil) != tt.wantErr {
				t.Errorf("ReadConfFromFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPopUpConfManager_ShutDown(t *testing.T) {
	type fields struct {
		PresentPopIdList []uint32
		stop             chan interface{}
		sw               sync.WaitGroup
		rwLock           sync.RWMutex
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PopUpConfManager{
				PresentPopIdList: tt.fields.PresentPopIdList,
				stop:             tt.fields.stop,
				sw:               tt.fields.sw,
				rwLock:           tt.fields.rwLock,
			}
			p.ShutDown()
		})
	}
}

func TestPopUpConfManager_Timer(t *testing.T) {
	type fields struct {
		PresentPopIdList []uint32
		stop             chan interface{}
		sw               sync.WaitGroup
		rwLock           sync.RWMutex
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &PopUpConfManager{
				PresentPopIdList: tt.fields.PresentPopIdList,
				stop:             tt.fields.stop,
				sw:               tt.fields.sw,
				rwLock:           tt.fields.rwLock,
			}
			p.Timer()
		})
	}
}
