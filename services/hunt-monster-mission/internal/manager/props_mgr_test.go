package manager

import (
	"context"
	"errors"
	apicenter "golang.52tt.com/clients/mocks/apicenter/apiserver"
	"reflect"
	"testing"
	"time"

	gomock "github.com/golang/mock/gomock"
	appconfigCli "golang.52tt.com/clients/mocks/appconfig"
	darkCli "golang.52tt.com/clients/mocks/darkserver"
	enterCli "golang.52tt.com/clients/mocks/entertainmentrecommendback"
	huntmonsterCli "golang.52tt.com/clients/mocks/hunt-monster"
	pushCli "golang.52tt.com/clients/mocks/push-notification/v2"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	appconfigPb "golang.52tt.com/protocol/services/appconfig"
	entertainmentRecommendBack "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	pb "golang.52tt.com/protocol/services/huntmonstermission"
)

func GeneralMgr(t *testing.T) *PropsMgr {
	controller := gomock.NewController(t)
	defer controller.Finish()
	cacheCli := NewMockIPropsCache(controller)
	mysqlStore := NewMockIStore(controller)
	enterCli := enterCli.NewMockIClient(controller)
	appconfigCli := appconfigCli.NewMockIClient(controller)
	huntmonsterCli := huntmonsterCli.NewMockIClient(controller)
	pushCli := pushCli.NewMockIClient(controller)
	darkCli := darkCli.NewMockIClient(controller)
	apicenterCli := apicenter.NewMockIClient(controller)
	apicenterCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mgr, _ := NewPropsMgr(context.Background(), cacheCli, mysqlStore, enterCli, appconfigCli, huntmonsterCli, pushCli, darkCli, nil, apicenterCli)
	return mgr
}

func TestPropsMgr_CheckIsWhiteUser(t *testing.T) {
	mgr := GeneralMgr(t)

	type args struct {
		uid uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "非白名单",
			args: args{uid: 2465920},
			want: false,
		},
		{
			name: "白名单",
			args: args{uid: 147269299},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := mgr.CheckIsWhiteUser(tt.args.uid); got != tt.want {
				t.Errorf("PropsMgr.CheckIsWhiteUser() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPropsMgr_TimerRefreshChannelRecInfo(t *testing.T) {
	controller := gomock.NewController(t)
	defer controller.Finish()
	mgr := GeneralMgr(t)
	enterCli := enterCli.NewMockIClient(controller)
	cid := uint32(2013095)
	tagId := uint32(2022)
	newStartTime := uint32(1614182400)
	oldStartTime := uint32(1614182400)

	enterCli.EXPECT().GetPrepareChannelListV2(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, in *entertainmentRecommendBack.GetPrepareChannelListV2Req) (*entertainmentRecommendBack.GetPrepareChannelListV2Resp, protocol.ServerError) {
		offset := uint32(1)
		if *in.Offset > offset {
			return &entertainmentRecommendBack.GetPrepareChannelListV2Resp{}, nil
		} else {
			return &entertainmentRecommendBack.GetPrepareChannelListV2Resp{
				PrepareChannelList: []*entertainmentRecommendBack.PrepareChannelInfo{
					{
						ChannelId:    &cid,
						TagId:        &tagId,
						NewStartTime: &newStartTime,
						OldStartTime: &oldStartTime,
					},
				},
			}, nil

		}
	}).AnyTimes()

	mgr.enterCli = enterCli
	tests := []struct {
		name string
		want bool
	}{
		{
			name: "正常",
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := mgr.TimerRefreshChannelRecInfo(); got != tt.want {
				t.Errorf("PropsMgr.TimerRefreshChannelRecInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTools(t *testing.T) {
	mgr := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()
	pushCli := pushCli.NewMockIClient(controller)
	pushCli.EXPECT().PushToUsers(context.Background(), []uint32{2465920}, gomock.Any()).Return(nil)
	mgr.pushCli = pushCli
	type args struct {
		uid uint32
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "试试看",
			args: args{
				uid: 2465920,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr.TestTools(tt.args.uid)
		})
	}
}

func TestPropsMgr_RemoveUserStayTime(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()
	redisCli := NewMockIPropsCache(controller)
	redisCli.EXPECT().RemoveUserStayTime(context.Background(), uint32(2465920), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, uid, index uint32, mType pb.EnumMissionType) error {
		if pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE == mType {
			return nil
		} else {
			return errors.New("mType出错")
		}
	}).AnyTimes()
	p.cacheCli = redisCli
	type args struct {
		ctx   context.Context
		uid   uint32
		mType pb.EnumMissionType
	}

	tests := []struct {
		name string
		args args
	}{
		{
			name: "RemoveUserStayTime 出错",
			args: args{
				ctx:   context.Background(),
				uid:   uint32(2465920),
				mType: pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE,
			},
		},
		{
			name: "RemoveUserStayTime 通过",
			args: args{
				ctx:   context.Background(),
				uid:   uint32(2465920),
				mType: pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p.RemoveUserStayTime(tt.args.ctx, tt.args.uid, tt.args.mType)
		})
	}
}

func TestPropsMgr_TimerRefreshHuntMonsterConfig(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()
	appconfigCli := appconfigCli.NewMockIClient(controller)
	appconfigCli.EXPECT().GetHuntMonsterConfig(context.Background(), uint32(0)).Return(
		[]appconfigPb.FloatLayerEntry{
			{
				Url:       "baidu.com",
				BeginTime: 1660557600,
				EndTime:   1663236000,
			},
		},
		nil,
	)
	p.appconfigCli = appconfigCli
	tests := []struct {
		name string
		want bool
	}{
		{
			name: "打龙活动开启",
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := p.TimerRefreshHuntMonsterConfig(); got != tt.want {
				t.Errorf("PropsMgr.TimerRefreshHuntMonsterConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPropsMgr_TimerProcUserChannelStayMissionFive(t *testing.T) {
	log.SetLevel(log.DebugLevel)
	controller := gomock.NewController(t)
	defer controller.Finish()
	ctx := context.Background()
	cacheCli := NewMockIPropsCache(controller)
	cacheCli.EXPECT().GetLock(ctx, "proc_mission_five_2", 60*time.Second).Return(true).Times(1)
	cacheCli.EXPECT().GetStayTimeListByScore(ctx, int64(0), time.Now().Unix()-int64(fiveMinute), int64(0), int64(50), uint32(2), pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE).Return(
		[]UserStayTime{
			{
				Uid: 2400464,
				Ts:  uint32(time.Now().Add(-6 * time.Minute).Unix()),
			},
			{
				Uid: 2227220,
				Ts:  uint32(time.Now().Add(-6 * time.Minute).Unix()),
			},
			{
				Uid: 2465920,
				Ts:  uint32(time.Now().Add(-5 * time.Minute).Unix()),
			},
			{
				Uid: 2405443,
				Ts:  uint32(time.Now().Add(-3 * time.Minute).Unix()),
			},
		}, nil,
	).Times(1)

	cacheCli.EXPECT().BatchGetGetUserMissionStatus(ctx, []uint32{2400464, 2227220, 2465920}, pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE).Return(
		map[uint32]uint32{
			2405443: 0,
			2465920: 0,
			2400464: 1,
		}, nil,
	).Times(1)
	cacheCli.EXPECT().ReleaseLock(gomock.Any(), gomock.Any()).Times(1)
	// 两个用户在线五分钟的任务已经完成

	// 只有一名用户任务没有完成，且时间大于等于5分钟
	cacheCli.EXPECT().AddUserDailyMissionValue(gomock.Any(), gomock.Any(), uint32(1), pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE).Return(uint32(1), nil).Times(2)
	cacheCli.EXPECT().UpdateUserStayTimeV2(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mysqlStore := NewMockIStore(controller)
	mysqlStore.EXPECT().RecordUserMissionAwardInfo(gomock.Any(), gomock.Any(), uint32(pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	enterCli := enterCli.NewMockIClient(controller)
	appconfigCli := appconfigCli.NewMockIClient(controller)
	huntmonsterCli := huntmonsterCli.NewMockIClient(controller)
	huntmonsterCli.EXPECT().AddHuntMonsterItem(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	pushCli := pushCli.NewMockIClient(controller)
	pushCli.EXPECT().PushToUsers(context.Background(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	darkCli := darkCli.NewMockIClient(controller)
	darkCli.EXPECT().UserBehaviorCheck(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, toUid uint32) (uint32, protocol.ServerError) {
			if uint32(2227220) == toUid {
				return uint32(2227220), nil
			}
			return uint32(0), nil

		},
	).AnyTimes()
	cacheCli.EXPECT().BatchRemoveUserStayTime(gomock.Any(), []uint32{2405443, 2400464}, uint32(2), pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE).Return(nil)
	ts := time.Now().Unix()
	cacheCli.EXPECT().BatchUpdateUserStayTimeV2(gomock.Any(), []uint32{2227220, 2465920}, uint32(ts), uint32(2), pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE)
	apicenterCli := apicenter.NewMockIClient(controller)
	apicenterCli.EXPECT().SendImMsg(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	p, _ := NewPropsMgr(context.Background(), cacheCli, mysqlStore, enterCli, appconfigCli, huntmonsterCli, pushCli, darkCli, nil, apicenterCli)
	p.HuntMonsterConfigIsValid = true
	p.huntMonsterJumpUrl = "baidu.com"
	type args struct {
		index uint32
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "TimerProcUserChannelStayMissionFive",
			args: args{
				index: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p.TimerProcUserChannelStayMissionFive(tt.args.index)
		})
	}
}

func TestPropsMgr_AddUserDailyMissionValue(t *testing.T) {
	p := GeneralMgr(t)
	ctx := context.Background()
	controller := gomock.NewController(t)
	defer controller.Finish()
	cacheCli := NewMockIPropsCache(controller)
	cacheCli.EXPECT().AddUserDailyMissionValue(ctx, gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, uid, value uint32, missionType pb.EnumMissionType) (uint32, error) {
		if uid == 2405443 {
			if missionType == pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT {
				return uint32(51), nil
			} else if missionType == pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE {
				return uint32(13), nil
			}
		}
		return uint32(1), nil
	}).AnyTimes()
	cacheCli.EXPECT().SetUserMissionStatus(ctx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	p.cacheCli = cacheCli
	darkCli := darkCli.NewMockIClient(controller)
	darkCli.EXPECT().UserBehaviorCheck(gomock.Any(), gomock.Any()).Return(uint32(0), nil).AnyTimes()
	p.darkCli = darkCli
	huntMonsterCli := huntmonsterCli.NewMockIClient(controller)
	huntMonsterCli.EXPECT().AddHuntMonsterItem(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	p.huntMonsterCli = huntMonsterCli
	p.huntMonsterCli = huntMonsterCli
	mysqlStore := NewMockIStore(controller)
	mysqlStore.EXPECT().RecordUserMissionAwardInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	p.store = mysqlStore

	type args struct {
		ctx         context.Context
		uid         uint32
		value       uint32
		missionType pb.EnumMissionType
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "粉丝团",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_ADD_FANS_GROUP,
			},
			want: false,
		},
		{
			name: "未知",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_UNKOWN,
			},
			want: true, // 任务值为0
		},
		{
			name: "进房一分钟",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE,
			},
			want: true,
		},
		{
			name: "进房五分钟",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE,
			},
			want: false,
		},
		{
			name: "进房五分钟 完成",
			args: args{
				ctx:         context.Background(),
				uid:         2405443,
				value:       2,
				missionType: pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE,
			},
			want: true,
		},
		{
			name: "送t豆礼物",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_SEND_PRESENT,
			},
			want: true,
		},
		{
			name: "送一千t豆礼物",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT,
			},
			want: false,
		},
		{
			name: "送一千t豆礼物 完成",
			args: args{
				ctx:         context.Background(),
				uid:         2405443,
				value:       2,
				missionType: pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT,
			},
			want: true,
		},
		{
			name: "第一次送t豆礼物",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT,
			},
			want: true,
		},
		{
			name: "第一次送t豆礼物",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_ENTER_CHANNEL_HOUR_RANK,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := p.AddUserDailyMissionValue(tt.args.ctx, tt.args.uid, tt.args.value, tt.args.missionType); got != tt.want {
				t.Errorf("PropsMgr.AddUserDailyMissionValue() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPropsMgr_AddUserMissionValue(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()

	ctx := context.Background()
	cacheCli := NewMockIPropsCache(controller)
	cacheCli.EXPECT().AddUserMissionValue(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil).Times(3)
	p.cacheCli = cacheCli
	darkCli := darkCli.NewMockIClient(controller)
	darkCli.EXPECT().UserBehaviorCheck(gomock.Any(), gomock.Any()).Return(uint32(0), nil).AnyTimes()
	p.darkCli = darkCli
	huntmonsterCli := huntmonsterCli.NewMockIClient(controller)
	huntmonsterCli.EXPECT().AddHuntMonsterItem(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	p.huntMonsterCli = huntmonsterCli
	mysqlStore := NewMockIStore(controller)
	mysqlStore.EXPECT().RecordUserMissionAwardInfo(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	p.store = mysqlStore
	type args struct {
		ctx         context.Context
		uid         uint32
		value       uint32
		missionType pb.EnumMissionType
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "第一次送t豆礼物",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT,
			},
		},
		{
			name: "加粉丝团",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       6,
				missionType: pb.EnumMissionType_E_ADD_FANS_GROUP,
			},
		},
		{
			name: "错误类型",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				value:       1,
				missionType: pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p.AddUserMissionValue(tt.args.ctx, tt.args.uid, tt.args.value, tt.args.missionType)
		})
	}
}

func TestPropsMgr_UpdateUserStayTime(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()

	ctx := context.Background()
	cacheCli := NewMockIPropsCache(controller)
	cacheCli.EXPECT().UpdateUserStayTime(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
	p.cacheCli = cacheCli
	type args struct {
		ctx      context.Context
		uid      uint32
		updateTs uint32
		mType    pb.EnumMissionType
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "进房五分钟",
			args: args{
				ctx:      context.Background(),
				uid:      2465920,
				updateTs: uint32(time.Now().Unix()),
				mType:    pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE,
			},
		},
		{
			name: "进房1分钟",
			args: args{
				ctx:      context.Background(),
				uid:      2465920,
				updateTs: uint32(time.Now().Unix()),
				mType:    pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p.UpdateUserStayTime(tt.args.ctx, tt.args.uid, tt.args.updateTs, tt.args.mType)
		})
	}
}

func TestPropsMgr_GetUserHuntMonsterMission(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()
	cacheCli := NewMockIPropsCache(controller)

	ctx := context.Background()
	cacheCli.EXPECT().GetUserMissionValue(ctx, gomock.Any()).Return(
		map[pb.EnumMissionType]uint32{}, nil,
	)
	cacheCli.EXPECT().GetUserDailyMissionValue(ctx, gomock.Any()).Return(
		map[pb.EnumMissionType]uint32{}, nil,
	)
	p.cacheCli = cacheCli
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.GetUserHuntMonsterMissionResp
		wantErr bool
	}{
		{
			name: "GetUserHuntMonsterMission",
			args: args{
				ctx: context.Background(),
				uid: 2465920,
			},
			want:    &pb.GetUserHuntMonsterMissionResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := p.GetUserHuntMonsterMission(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("PropsMgr.GetUserHuntMonsterMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PropsMgr.GetUserHuntMonsterMission() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPropsMgr_GetUserHuntMissionInfo(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()
	p.HuntMonsterConfigIsValid = true
	p.mapId2IsRecommend[2254093] = true
	ctx := context.Background()
	type args struct {
		ctx         context.Context
		uid         uint32
		channelId   uint32
		missionType pb.EnumMissionType
	}
	tests := []struct {
		name     string
		args     args
		want     *pb.GetUserHuntMissionInfoResp
		wantErr  bool
		initFunc func()
	}{
		{
			name: "GetUserHuntMissionInfo每日任务",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				channelId:   2254093,
				missionType: pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE,
			},
			want: &pb.GetUserHuntMissionInfoResp{
				IsFinish: false,
				PopMsg:   "打龙-停留5分钟任务即将完成，可获得打野刀x2喔，是否现在退出",
				UpdateMs: time.Now().UnixNano() / 1e6,
			},
			wantErr: false,
			initFunc: func() {
				cacheCli := NewMockIPropsCache(controller)
				cacheCli.EXPECT().GetUserDailyMissionValue(ctx, gomock.Any()).Return(
					map[pb.EnumMissionType]uint32{}, nil,
				)
				p.cacheCli = cacheCli
			},
		},
		{
			name: "GetUserHuntMissionInfo限定任务",
			args: args{
				ctx:         context.Background(),
				uid:         2465920,
				channelId:   2254093,
				missionType: pb.EnumMissionType_E_ADD_FANS_GROUP,
			},
			want: &pb.GetUserHuntMissionInfoResp{
				IsFinish: false,
				PopMsg:   "打龙-停留5分钟任务即将完成，可获得打野刀x2喔，是否现在退出",
				UpdateMs: time.Now().UnixNano() / 1e6,
			},
			wantErr: false,
			initFunc: func() {
				cacheCli := NewMockIPropsCache(controller)
				cacheCli.EXPECT().GetUserMissionValue(ctx, gomock.Any()).Return(
					map[pb.EnumMissionType]uint32{}, nil,
				)
				p.cacheCli = cacheCli
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.initFunc()
			got, err := p.GetUserHuntMissionInfo(tt.args.ctx, tt.args.uid, tt.args.channelId, tt.args.missionType)
			if (err != nil) != tt.wantErr {
				t.Errorf("PropsMgr.GetUserHuntMissionInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				log.Infof("PropsMgr.GetUserHuntMissionInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPropsMgr_GetUserHuntMonsterDailyMission(t *testing.T) {
	p := GeneralMgr(t)
	controller := gomock.NewController(t)
	defer controller.Finish()
	cacheCli := NewMockIPropsCache(controller)

	ctx := context.Background()
	cacheCli.EXPECT().GetUserDailyMissionValue(ctx, gomock.Any()).Return(
		map[pb.EnumMissionType]uint32{}, nil,
	)
	p.cacheCli = cacheCli
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		args    args
		want    map[pb.EnumMissionType]uint32
		wantErr bool
	}{
		{
			name: "GetUserHuntMonsterDailyMission",
			args: args{
				ctx: context.Background(),
				uid: 2465920,
			},
			want:    map[pb.EnumMissionType]uint32{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := p.GetUserHuntMonsterDailyMission(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("PropsMgr.GetUserHuntMonsterDailyMission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PropsMgr.GetUserHuntMonsterDailyMission() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPropsMgr_GetMissionLimit(t *testing.T) {
	p := GeneralMgr(t)
	tests := []struct {
		name string
		want map[pb.EnumMissionType]uint32
	}{
		{
			name: "GetMissionLimit",
			want: map[pb.EnumMissionType]uint32{
				pb.EnumMissionType_E_STAY_CHANNEL_ONE_MINUTE:   1,
				pb.EnumMissionType_E_STAY_CHANNEL_CONTINUE:     12,
				pb.EnumMissionType_E_ENTER_CHANNEL_HOUR_RANK:   1,
				pb.EnumMissionType_E_SEND_PRESENT:              1,
				pb.EnumMissionType_E_SEND_ONE_THOUSAND_PRESENT: 50,
				pb.EnumMissionType_E_FIRST_SEND_TBEAN_PRESENT:  1,
				pb.EnumMissionType_E_ADD_FANS_GROUP:            5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := p.GetMissionLimit(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PropsMgr.GetMissionLimit() = %v, want %v", got, tt.want)
			}
		})
	}
}
