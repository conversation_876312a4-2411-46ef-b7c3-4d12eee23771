package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"reflect"
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/pia"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
)

func TestAddDramaV2(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	data, err := ioutil.ReadFile("../tools/dramaV2.json")
	if err != nil {
		log.ErrorWithCtx(ctx, "err:%v", err)
	}

	type DramaList struct {
		Dramas []*DramaEntityV2 `json:"dramas"`
	}
	drama := new(DramaList)
	err = json.Unmarshal(data, &drama)
	if err != nil {
		log.ErrorWithCtx(ctx, "err:%v", err)
	}
	serverConfig := &config.ServerConfig{}
	err = serverConfig.InitWithPath("json", "../service-config.json")
	if err != nil {
		log.ErrorWithCtx(ctx, "InitWithPath err:%v", err)
	}
	mongoConfig := config.NewMongoConfigWithSection(serverConfig.Configer, "mongo")
	client, err := mongo.NewClient(mongoConfig.OptionsForReplicaSet())
	if err != nil {
		log.Errorf("Failed to newMongoStore err: (%v)", err)
	}
	log.Infof("newMongoStore OK")
	if err := client.Connect(ctx); err != nil {
		log.Errorf("newMongoStore Failed client.Connect err: (%v)", err)
	}
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		log.Errorf("newMongoStore Failed  client.Ping err: (%v)", err)
	}

	type TDrama struct {
		*mongo.Collection
	}
	s := &TDrama{
		client.Database("pia").Collection("drama_v2"),
	}
	opts := options.Update()
	opts.SetUpsert(true)
	for _, d := range drama.Dramas {
		d.SearchID = "35"
		d.ID = 35
		roleMap := make(map[string]string)
		for _, role := range d.RoleList {
			roleMap[role.RoleID] = role.RoleName
			fmt.Println(role.RoleID, role.RoleName)
		}
		for _, content := range d.ContentList {
			content.RoleName = roleMap[content.RoleID]
		}
		_, err = s.UpdateOne(ctx, bson.M{"_id": 35}, bson.M{"$set": d}, opts)
		if err != nil {
			log.ErrorWithCtx(ctx, "InsertOne err:%v", err)
		}
	}
	log.InfoWithCtx(ctx, "%+v, %+v", drama.Dramas[0])
}

func Test_GetDramaListV2(t *testing.T) {
	t.Skip()
	ctx := context.Background()
	data, err := ioutil.ReadFile("../tools/dramaV2.json")
	if err != nil {
		log.ErrorWithCtx(ctx, "err:%v", err)
	}

	type DramaList struct {
		Dramas []*DramaEntityV2 `json:"dramas"`
	}
	drama := new(DramaList)
	err = json.Unmarshal(data, &drama)
	if err != nil {
		log.ErrorWithCtx(ctx, "err:%v", err)
	}
	serverConfig := &config.ServerConfig{}
	err = serverConfig.InitWithPath("json", "../service-config.json")
	if err != nil {
		log.ErrorWithCtx(ctx, "InitWithPath err:%v", err)
	}
	mongoConfig := config.NewMongoConfigWithSection(serverConfig.Configer, "mongo")
	client, err := mongo.NewClient(mongoConfig.OptionsForReplicaSet())
	if err != nil {
		log.Errorf("Failed to newMongoStore err: (%v)", err)
	}
	client.Connect(ctx)
	opts := &options.FindOptions{}
	opts.SetSort(bson.M{"_id": -1})
	opts.SetLimit(10)

	result := make([]*DramaEntityV2, 0)

	coll := client.Database("pia").Collection("drama_v2")

	cur, err := coll.Find(ctx, bson.M{}, opts)
	fmt.Println(err)
	err = cur.All(ctx, &result)
	fmt.Println(err)
	fmt.Println(result[0])
}

// func Test_post(t *testing.T) {
// 	t.Skip()
// 	type data struct {
// 		PiaList []*DataV2 `json:"list"`
// 		Total   uint32    `json:"total"`
// 	}
// 	type pia struct {
// 		Code    int    `json:"code"`
// 		Message string `json:"msg"`
// 		Data    *data  `json:"data"`
// 	}
// 	type syncReq struct {
// 		Size       uint32 `json:"size"`
// 		Page       uint32 `json:"page"`
// 		CreateTime int64  `json:"updateTime"`
// 	}
// 	req := &syncReq{
// 		Size:       50,
// 		Page:       1,
// 		CreateTime: 0,
// 	}
// 	cnt := 1
// 	for { //http请求
// 		url := "https://node-hw.52tt.com/app-production/miao-pia/dramaOp.DramaOp/sync"
// 		reqByte, err := json.Marshal(req)
// 		if err != nil {
// 			fmt.Println(err)
// 			return
// 		}
// 		ctx := context.Background()
// 		request, err := http.NewRequest("POST", url, strings.NewReader(string(reqByte)))
// 		request = request.WithContext(ctx)
// 		request.Header.Set("Content-Type", "application/json")
// 		request.Header.Set("Authorization", "eyJhbGciOiJIUzI1NiIsInRhcCI6IkpXVCJ9qb7J1aWQiOiI2MDZkNjk0NGRjMGNjMjAwMG")
// 		resp, err := http.DefaultClient.Do(request)
// 		if nil != err {
// 			log.ErrorWithCtx(ctx, "[调用网站接口获取剧本信息]GetDramaDataV2 failed to http.Post, err:%v", err)
// 			return
// 		}
// 		defer func() {
// 			if err2 := resp.Body.Close(); err2 != nil {
// 				log.ErrorWithCtx(ctx, "[调用网站接口获取剧本信息]GetDramaDataV2 failed to resp.Body.Close, err:%v", err2)
// 			}
// 		}()
// 		if nil != err {
// 			fmt.Println(err)
// 		}
// 		defer resp.Body.Close()

// 		bodyStr, err := ioutil.ReadAll(resp.Body)
// 		if err != nil {
// 			fmt.Println(err)
// 		}
// 		if len(bodyStr) == 0 {
// 			err = errors.New("response not correct")
// 			fmt.Println(err)
// 		}
// 		p := new(pia)
// 		err = json.Unmarshal(bodyStr, p)
// 		if err != nil {
// 			fmt.Println(err)
// 			return
// 		}
// 		if p.Code != 0 {
// 			fmt.Println(p.Code, p.Message)
// 			return
// 		}

// 		for _, data := range p.Data.PiaList {
// 			fmt.Printf("%+v\n", data.Id)
// 			cnt++
// 			// for _, r := range data.Roles {
// 			// 	fmt.Printf("%+v\n", r)
// 			// }
// 			// for _, content := range data.Content {
// 			// 	fmt.Printf("%+v\n", content)
// 			// }
// 			// for _, bgm := range data.Bgm {
// 			// 	fmt.Printf("%+v\n", bgm)
// 			// }

// 		}
// 		cnt := len(p.Data.PiaList)
// 		if cnt < int(req.Size) {
// 			break
// 		} else {
// 			req.Page++
// 		}
// 	}

// }

//
//func TestDrama_GetDramaList(t *testing.T) {
//	mgr, f, err2 := createDramaMgr(context.TODO())
//	if err2 != nil {
//		t.Fatal(err2)
//	}
//	defer f()
//	rs, err := mgr.GetDramaList([]*pb.SearchOption{
//		{
//			SearchType: uint32(pb.SearchType_MALE_NUM),
//			Label:      "3",
//		},
//		{
//			SearchType: uint32(pb.SearchType_FEMALE_NUM),
//			Label:      "2",
//		},
//	}, 0, 20)
//	if err != nil {
//		t.Error(err)
//	}
//
//	for _, item := range rs {
//		t.Logf("%+v", item)
//	}
//}
//
//func TestDrama_GetSearchOptionGroup(t *testing.T) {
//	mgr, f, err2 := createDramaMgr(context.TODO())
//	if err2 != nil {
//		t.Fatal(err2)
//	}
//	defer f()
//	rs, err := mgr.GetSearchOptionGroup()
//	if err != nil {
//		t.Error(err)
//	}
//
//	for _, item := range rs {
//		t.Logf("%+v", item)
//	}
//}
//
//func TestDrama_GetDramaByID(t *testing.T) {
//	mgr, f, err2 := createDramaMgr(context.TODO())
//	if err2 != nil {
//		t.Fatal(err2)
//	}
//	defer f()
//	data, err := mgr.GetDramaByID(context.TODO(), 1)
//	if err != nil {
//		t.Error(err)
//	}
//
//	t.Logf(" %+v", data)
//}
//
//func Test_DeleteDramaById(t *testing.T) {
//	mgr, f, err2 := createDramaMgr(context.TODO())
//	if err2 != nil {
//		t.Fatal(err2)
//	}
//	defer f()
//	err := mgr.DeleteDramaById(context.TODO(), 180157)
//	fmt.Println(err)
//}
//
//func Test_RecoverDramaById(t *testing.T) {
//	mgr, f, err2 := createDramaMgr(context.TODO())
//	if err2 != nil {
//		t.Fatal(err2)
//	}
//	defer f()
//	err := mgr.RecoverDramaById(context.TODO(), 180157)
//	fmt.Println(err)
//}
//
//func Test_UpdateDramaById(t *testing.T) {
//	mgr, f, err2 := createDramaMgr(context.TODO())
//	if err2 != nil {
//		t.Fatal(err2)
//	}
//	defer f()
//	err := mgr.UpdateDramaById(context.TODO(), 180157)
//	fmt.Println(err)
//}

//func TestDrama_GetDramaListV2(t *testing.T) {
//	controller := gomock.NewController(t)
//	dramaV2 := NewMockIDramaV2(controller)
//	fmt.Println(bson.M{"$and": []bson.M{{"tags": "历史"}}, "deleted": false, "is_freeze": false, "is_private": false, "status": int(3)})
//	dramaV2.EXPECT().GetDramaList(gomock.Any(), bson.M{"$and": []bson.M{{"tags": "历史"}}, "deleted": false, "is_freeze": false, "is_private": false, "status": int(3)}, uint32(0), uint32(10)).Return([]*DramaEntityV2{
//		{
//			ID:    1,
//			Title: "测试",
//			Tags:  []string{"历史"},
//		},
//	}, nil).Times(1)
//	mgr := NewDramaMgr(nil, dramaV2, nil, nil)
//	type args struct {
//		searchOption []*pb.SearchOption
//		pageToken    uint32
//		pageSize     uint32
//	}
//	tests := []struct {
//		name    string
//		m       *Drama
//		args    args
//		want    []*pb.DramaV2
//		wantErr bool
//	}{
//		{
//			name: "获取剧本",
//			args: args{
//				searchOption: []*pb.SearchOption{
//					{
//						SearchType: uint32(pb.SearchType_DRAMA_TAG),
//						Label:      "历史",
//					},
//				},
//				pageToken: 0,
//				pageSize:  10,
//			},
//			want: []*pb.DramaV2{
//				{
//					DramaSubInfo: &pb.DramaSubInfo{
//						Id:      1,
//						Title:   "测试",
//						TagList: []string{"历史"},
//					},
//				},
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, err := mgr.GetDramaListV2(tt.args.searchOption, tt.args.pageToken, tt.args.pageSize)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("Drama.GetDramaListV2() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//
//			if !reflect.DeepEqual(got, tt.want) {
//				fmt.Printf("Drama.GetDramaListV2() = %v, want %v", got, tt.want)
//			}
//		})
//	}
//}

func TestDrama_GetDramaList(t *testing.T) {
	type args struct {
		searchOption []*pb.SearchOption
		pageToken    uint32
		pageSize     uint32
	}
	tests := []struct {
		name    string
		m       *Drama
		args    args
		want    []*pb.Drama
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.m.GetDramaList(tt.args.searchOption, tt.args.pageToken, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("Drama.GetDramaList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Drama.GetDramaList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getDramaListQueryBuilder(t *testing.T) {
	option := []*pb.SearchOption{
		{
			SearchType: 0,
			Label:      "36",
		},
	}

	list := getDramaListQueryBuilder(option)
	list.SetIsDeleted(true)

}
