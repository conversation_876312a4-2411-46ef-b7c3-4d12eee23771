package delayqueue

/*
import (
	"fmt"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
)

var redisCli *redis.Client

func init() {
	log.SetLevel(log.DebugLevel)

	redisCli = redis.NewClient(&redis.Options{
		Addr:         "**************:6379",
		DialTimeout:  10 * time.Second,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		PoolSize:     10,
		PoolTimeout:  30 * time.Second,
	})

	_, err := redisCli.Ping().Result()
	if err != nil {
		log.Panicln(err)
	}
}

func pushList(key string, cnt int) {
	pipe := redisCli.Pipeline()
	for i := 0; i < cnt; i++ {
		pipe.LPush(key, i+1)
	}
	pipe.Exec()
}

func scriptPop(script string, key string, limit int, gorountineSize int) {
	now := time.Now()
	popSize := uint32(0)
	wg := &sync.WaitGroup{}
	for i := 0; i < gorountineSize; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for {
				resp, _ := redisCli.Eval(script, []string{key}, limit).Result()
				res := resp.([]interface{})
				if len(res) == 0 {
					break
				}
				atomic.AddUint32(&popSize, uint32(len(res)))
			}
		}()
	}
	wg.Wait()
	fmt.Printf("key %s, pop size %v, gorountine cnt %d, cost %s\n", key, popSize, gorountineSize, time.Since(now))
}

// go test -timeout 30s -run ^TestBatchPop$ golang.52tt.com/services/anchor-contract/anchorcontract-go/cache/delayqueue -v -count=1
/*
=== RUN   TestBatchPop
key l1, pop size 1000, gorountine cnt 5, cost 1.044551ms
key l2, pop size 1000, gorountine cnt 5, cost 1.007521ms

key l1, pop size 10000, gorountine cnt 5, cost 7.288719ms
key l2, pop size 10000, gorountine cnt 5, cost 6.282281ms

key l1, pop size 100000, gorountine cnt 5, cost 74.411312ms
key l2, pop size 100000, gorountine cnt 5, cost 51.073454ms

key l1, pop size 1000000, gorountine cnt 5, cost 747.73009ms
key l2, pop size 1000000, gorountine cnt 5, cost 529.825376ms

--- PASS: TestBatchPop (3.05s)
PASS
ok  	golang.52tt.com/services/anchor-contract/anchorcontract-go/cache/delayqueue	3.060s
=== RUN   TestBatchPop
key l1, pop size 1000, gorountine cnt 10, cost 1.454156ms
key l2, pop size 1000, gorountine cnt 10, cost 603.36µs

key l1, pop size 10000, gorountine cnt 10, cost 7.7868ms
key l2, pop size 10000, gorountine cnt 10, cost 4.562781ms

key l1, pop size 100000, gorountine cnt 10, cost 69.565285ms
key l2, pop size 100000, gorountine cnt 10, cost 45.529404ms

key l1, pop size 1000000, gorountine cnt 10, cost 718.37216ms
key l2, pop size 1000000, gorountine cnt 10, cost 475.456171ms

--- PASS: TestBatchPop (3.02s)
PASS
ok  	golang.52tt.com/services/anchor-contract/anchorcontract-go/cache/delayqueue	3.025s
*/
/*
func TestBatchPop(t *testing.T) {
	/*
		k1 := "l1"
		k2 := "l2"

		cnt := 1000
		limit := 100
		gorountineSize := 10
		for i := 0; i < 4; i++ {
			pushList(k1, cnt)
			pushList(k2, cnt)

			scriptPop(ready2UnackScript, k1, limit, gorountineSize)
			scriptPop(ready2UnackScript2, k2, limit, gorountineSize)

			cnt *= 10
			fmt.Println()
		}
	*/
//}


/*
// go test -timeout 30s -run ^TestLuaSha$ golang.52tt.com/services/anchor-contract/anchorcontract-go/cache/delayqueue -v -count=1
func TestLuaSha(t *testing.T) {
	_, err := redisCli.EvalSha("aaa", []string{"key1"}).Result()
	t.Log(err)
	t.Logf("%T\n", err)
}

/*
// go test -timeout 30s -run ^TestNotify$ golang.52tt.com/services/anchor-contract/anchorcontract-go/cache/delayqueue -v -count=1
func TestNotify(t *testing.T) {

	q, err := NewDelayQueue(redisCli)
	t.Log(err)

	delayKey := "dq:test:pending"
	readyKey := "dq:test:ready"

	now := time.Now().Unix()
	taskCnt := int64(22)
	workerCnt := 3

	for i := int64(0); i < taskCnt; i++ {
		q.PushToPendingQueue(delayKey, []redis.Z{{
			Score:  float64(now + 30 + i),
			Member: fmt.Sprintf("m:%d", i)},
		})
	}

	t.Logf("push ok\n")

	go func() {
		for {
			cnt, _ := q.PopPendingQueue(delayKey, readyKey, uint32(time.Now().Unix()+3600), 5)
			fmt.Printf("master pop %d\n", cnt)
			time.Sleep(time.Second)
		}
	}()

	for i := 0; i < workerCnt; i++ {
		go func(id int) {
			for {
				list, _ := q.PopReadyQueue(readyKey, 10)

				fmt.Printf("g:%d, pop size %d, %+v\n", id, len(list), list)
				time.Sleep(time.Second)
			}
		}(i)
	}

	select {}
}
*/

/*
// go test -timeout 30s -run ^TestRemPendingQueue$ golang.52tt.com/services/anchor-contract/anchorcontract-go/cache/delayqueue -v -count=1
func TestRemPendingQueue(t *testing.T) {
	q, err := NewDelayQueue(redisCli)
	t.Log(err)

	delayKey := "dq:test:pending"
	now := time.Now().Unix()
	member := "m"

	err = q.PushToPendingQueue(delayKey, []redis.Z{{
		Score:  float64(now + 30),
		Member: member},
	})
	t.Logf("push err %v\n", err)

	err = q.RemPendingQueue(delayKey, []string{member})
	t.Logf("rem err %v\n", err)
}

*/