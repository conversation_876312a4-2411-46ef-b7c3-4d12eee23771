# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

210:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 210 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ThirdpartyAuth
211:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 211 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ThirdPartyReg
212:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 212 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChinaMobileAuth
213:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 213 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChinaMobileReg
214:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 214 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ThirdpartyVerifyCheck
215:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 215 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChinaUnicomAuth
216:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 216 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChinaUnicomReg
30089:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30089 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChuangLanThirdPartyBindPhone
30090:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30090 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChuangLanAccountBind
30091:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 30091 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/ChuangLanUidBind
404:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 404 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/BindPhoneBeforeAuth
405:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 405 --source api/thirdparty_auth/grpc_thirdparty_auth_cpp.proto --lang cpp --method /ga.api.auth.ThirdpartyAuthLogic/BindPhoneAfterAuth
