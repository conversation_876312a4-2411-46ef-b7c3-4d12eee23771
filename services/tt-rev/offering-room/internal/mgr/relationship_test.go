package mgr

import (
	"context"
	im_api "golang.52tt.com/clients/mocks/im-api"
	conf2 "golang.52tt.com/services/tt-rev/offering-room/internal/conf"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	offering_room "golang.52tt.com/protocol/services/offer-room"
)

type relationshipMgrHelperForTest struct {
	*releationshipMgr
}

func newRelationshipMgrHelperForTest(t *testing.T) *relationshipMgrHelperForTest {
	controller := gomock.NewController(t)
	return &relationshipMgrHelperForTest{
		&releationshipMgr{
			convertor:                  NewConvertor(),
			relaitonStore:              NewMockRelationshipStore(controller),
			relationshipNotExistMarker: NewMockRelationshipNotExistMarkerCache(controller),
			config:                     conf2.NewMockIConfig(controller),
			imCli:                      im_api.NewMockIClient(controller),
		},
	}
}

func (receiver *relationshipMgrHelperForTest) getImCli() *im_api.MockIClient {
	return receiver.imCli.(*im_api.MockIClient)
}

func (receiver *relationshipMgrHelperForTest) getStore() *MockRelationshipStore {
	return receiver.relaitonStore.(*MockRelationshipStore)
}

func (receiver *relationshipMgrHelperForTest) getConfig() *conf2.MockIConfig {
	return receiver.config.(*conf2.MockIConfig)
}

func (receiver *relationshipMgrHelperForTest) getRelationshipNotExistMarker() *MockRelationshipNotExistMarkerCache {
	return receiver.relationshipNotExistMarker.(*MockRelationshipNotExistMarkerCache)
}

func Test_releationshipMgr_CreateRelationship(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.CreateRelationshipRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.CreateRelationshipResponse
		wantErr  bool
		initFunc func(s *relationshipMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: context.Background(),
				request: &offering_room.CreateRelationshipRequest{
					FromUid:    123,
					FromIsFake: true,
					ToUid:      456,
				},
			},
			want: &offering_room.CreateRelationshipResponse{
				LevelUpMap: map[uint32]uint32{
					123: 1,
				},
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().AddRelationship(context.Background(), gomock.Any()).Return(
					nil,
				)
				s.getRelationshipNotExistMarker().EXPECT().Unmark(context.Background(), uint32(123), uint32(456)).Return(nil)
				s.getStore().EXPECT().UpdateNameplateInfo(context.Background(), []uint32{123, 456}, int32(1)).Return(nil)
				s.getStore().EXPECT().BatchGetUserNameplateInfo(context.Background(), []uint32{123, 456}).Return(
					[]*NameplateInfo{
						{
							Uid:     123,
							StarCnt: 1,
						},
						{
							Uid:     456,
							StarCnt: 2,
						},
					}, nil,
				)
				s.getConfig().EXPECT().GetNameplateConfig().Return(
					&conf2.NameplateConfig{
						NameplateListField: []*conf2.Nameplate{
							{
								LevelField:          1,
								StarsField:          5,
								LevelThresholdField: 1,
							},
							{
								LevelField:          2,
								StarsField:          6,
								LevelThresholdField: 6,
							},
						},
						NameplateCfgVersionField: 1,
					},
				).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newRelationshipMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.CreateRelationship(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("releationshipMgr.CreateRelationship() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("releationshipMgr.CreateRelationship() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_releationshipMgr_DeleteRelationship(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.DeleteRelationshipRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.DeleteRelationshipResponse
		wantErr  bool
		initFunc func(s *relationshipMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: context.Background(),
				request: &offering_room.DeleteRelationshipRequest{
					Uid:          123,
					RecordIdList: []uint32{1},
				},
			},
			want:    &offering_room.DeleteRelationshipResponse{},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().DeleteRelationship(context.Background(), uint32(123), uint32(1)).Return(nil)
				s.getStore().EXPECT().GetRelationshipByRecordId(context.Background(), uint32(1)).Return(
					&RelationInfo{
						RecordId: 1,
						FromUid:  123,
						ToUid:    456,
					}, nil)
				s.getStore().EXPECT().UpdateNameplateInfo(context.Background(), []uint32{123, 456}, int32(-1)).Return(nil)
				s.getStore().EXPECT().BatchGetUserNameplateInfo(context.Background(), []uint32{123, 456}).Return(
					[]*NameplateInfo{
						{
							Uid:     123,
							StarCnt: 5,
						},
						{
							Uid:     456,
							StarCnt: 6,
						},
					}, nil)
				s.getConfig().EXPECT().GetNameplateConfig().Return(
					&conf2.NameplateConfig{
						NameplateListField: []*conf2.Nameplate{
							{
								LevelField:          1,
								StarsField:          5,
								LevelThresholdField: 1,
							},
							{
								LevelField:          2,
								StarsField:          6,
								LevelThresholdField: 6,
							},
						},
						NameplateCfgVersionField: 1,
						NeedPushExpiredMsgField:  true,
					},
				).AnyTimes()
				s.getImCli().EXPECT().SimpleSendTTAssistantText(context.Background(), uint32(123), "您的拍拍铭牌等级降至LV.1，快去拍关系升级吧，点击去拍拍房>>", highlightText, jumpUrl).Return(nil, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newRelationshipMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.DeleteRelationship(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("releationshipMgr.DeleteRelationship() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("releationshipMgr.DeleteRelationship() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_releationshipMgr_UnfreezeRelationshipRecord(t *testing.T) {
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(s *relationshipMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				ctx: context.Background(),
				uid: 2465920,
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().UnfreezeRelationshipRecord(gomock.Any(), gomock.Any())
				s.getStore().EXPECT().GetFreezePartnerUidList(gomock.Any(), gomock.Any()).Return([]uint32{123}, nil)
				s.getRelationshipNotExistMarker().EXPECT().Unmark(gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newRelationshipMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			if err := o.UnfreezeRelationshipRecord(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("releationshipMgr.UnfreezeRelationshipRecord() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_releationshipMgr_OfferingRelationships(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.OfferingRelationshipsRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.OfferingRelationshipsResponse
		wantErr  bool
		initFunc func(s *relationshipMgrHelperForTest)
	}{
		{
			name: "正常",
			args: args{
				c: context.Background(),
				request: &offering_room.OfferingRelationshipsRequest{
					Uid:       123,
					TargetUid: 456,
					PageToken: "123",
					PageSize:  10,
				},
			},
			want: &offering_room.OfferingRelationshipsResponse{
				RelationshipList: []*offering_room.OfferingRelationshipInfo{
					{
						RecordId:   1,
						PartnerUid: 123,
						IsBuyer:    false,
						GiftId:     1,
						GiftCnt:    2,
						ExpireTs:   1,
					},
				},
				RelationshipName: "哈哈",
				RelationshipCnt:  1,
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetRelationshipByPage(context.Background(), uint32(456), uint32(10), "123").Return(
					"", []*RelationInfo{
						{
							RecordId:    1,
							FromUid:     123,
							ToUid:       456,
							GiftId:      1,
							GiftCnt:     2,
							ExpiredTime: time.Now().Add(time.Second * 2),
						},
					}, nil,
				).AnyTimes()
				s.getStore().EXPECT().GetLatestRelationshipName(context.Background(), uint32(123), uint32(456)).Return(
					"哈哈", nil,
				).AnyTimes()
				s.getRelationshipNotExistMarker().EXPECT().Check(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				s.getStore().EXPECT().GetRelationshipCnt(context.Background(), uint32(456)).Return(uint32(1), nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newRelationshipMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.OfferingRelationships(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("releationshipMgr.OfferingRelationships() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("releationshipMgr.OfferingRelationships() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_releationshipMgr_OfferRoomCardInfo(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.OfferRoomCardInfoRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.OfferRoomCardInfoResponse
		wantErr  bool
		initFunc func(s *relationshipMgrHelperForTest)
	}{
		{
			name: "关系数量为0",
			args: args{
				c: context.Background(),
				request: &offering_room.OfferRoomCardInfoRequest{
					FromUid:               123,
					TargetUid:             456,
					NeedTop3Relationships: false,
				},
			},
			want: &offering_room.OfferRoomCardInfoResponse{
				TotalCnt:         0,
				RelationshipName: "",
			},
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getConfig().EXPECT().GetNameplateConfig().Return(&conf2.NameplateConfig{
					NameplateCfgVersionField: 0,
				})
				s.getRelationshipNotExistMarker().EXPECT().Check(context.Background(), uint32(456)).Return(false, nil)
				s.getStore().EXPECT().GetRelationshipCnt(context.Background(), uint32(456)).Return(uint32(0), nil)
				s.getRelationshipNotExistMarker().EXPECT().Mark(context.Background(), uint32(456)).Return(nil)
			},
		},
		{
			name: "正常",
			args: args{
				c: context.Background(),
				request: &offering_room.OfferRoomCardInfoRequest{
					FromUid:   123,
					TargetUid: 456,
				},
			},
			want: &offering_room.OfferRoomCardInfoResponse{
				TotalCnt: 2,
				StarCnt:  2,
				Rank:     1,
			},
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetRelationshipCnt(context.Background(), uint32(456)).Return(uint32(2), nil)
				s.getStore().EXPECT().GetLatestRelationshipName(context.Background(), uint32(123), uint32(456)).Return("", nil)
				s.getRelationshipNotExistMarker().EXPECT().Check(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				s.getConfig().EXPECT().GetNameplateConfig().Return(
					&conf2.NameplateConfig{
						NameplateListField: []*conf2.Nameplate{
							{
								LevelField:          1,
								StarsField:          5,
								LevelThresholdField: 1,
							},
							{
								LevelField:          2,
								StarsField:          6,
								LevelThresholdField: 6,
							},
						},
					}).AnyTimes()
				s.getStore().EXPECT().BatchGetUserNameplateInfo(context.Background(), []uint32{456}).Return(
					[]*NameplateInfo{
						{
							Uid:     456,
							StarCnt: 2,
						},
					}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newRelationshipMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.OfferRoomCardInfo(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("releationshipMgr.OfferRoomCardInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("releationshipMgr.OfferRoomCardInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_releationshipMgr_CheckRelationNameAvailability(t *testing.T) {
	type args struct {
		c       context.Context
		request *offering_room.CheckRelationNameAvailabilityRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *offering_room.CheckRelationNameAvailabilityResponse
		wantErr  bool
		initFunc func(s *relationshipMgrHelperForTest)
	}{
		{
			name: "Limited relationship does not exist",
			args: args{
				c: context.Background(),
				request: &offering_room.CheckRelationNameAvailabilityRequest{
					RelationName: "Test",
				},
			},
			want: &offering_room.CheckRelationNameAvailabilityResponse{
				IsAvailable: true,
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetLimitedRelationshipByName(context.Background(), "Test").Return(nil, ErrNotFound)
			},
		},
		{
			name: "Limited relationship exists and is active",
			args: args{
				c: context.Background(),
				request: &offering_room.CheckRelationNameAvailabilityRequest{
					RelationName: "Test",
				},
			},
			want: &offering_room.CheckRelationNameAvailabilityResponse{
				IsAvailable: true,
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetLimitedRelationshipByName(context.Background(), "Test").Return(&LimitedRelationshipsConfig{
					StartTime: time.Now().Add(-time.Hour),
					EndTime:   time.Now().Add(time.Hour),
				}, nil)
			},
		},
		{
			name: "Limited relationship exists, is not active and the switch is off",
			args: args{
				c: context.Background(),
				request: &offering_room.CheckRelationNameAvailabilityRequest{
					RelationName: "Test",
					Uid:          123,
				},
			},
			want: &offering_room.CheckRelationNameAvailabilityResponse{
				IsAvailable: false,
				Reason:      "Unavailable reason",
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetLimitedRelationshipByName(context.Background(), "Test").Return(&LimitedRelationshipsConfig{
					StartTime:                    time.Now().Add(time.Hour),
					EndTime:                      time.Now().Add(2 * time.Hour),
					IsAvailableForRegisteredUser: false,
				}, nil)
				s.getConfig().EXPECT().GetLimitRelation().Return(&conf2.LimitRelation{
					OnlineTimeField:        time.Now().Add(-2 * time.Hour).Unix(),
					UnavailableReasonField: "Unavailable reason",
				}).AnyTimes()
			},
		},
		{
			name: "Limited relationship exists, is not active and user has not had this relationship",
			args: args{
				c: context.Background(),
				request: &offering_room.CheckRelationNameAvailabilityRequest{
					RelationName: "Test",
					Uid:          123,
				},
			},
			want: &offering_room.CheckRelationNameAvailabilityResponse{
				IsAvailable: false,
				Reason:      "Unavailable reason",
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetLimitedRelationshipByName(context.Background(), "Test").Return(&LimitedRelationshipsConfig{
					StartTime:                    time.Now().Add(time.Hour),
					EndTime:                      time.Now().Add(2 * time.Hour),
					IsAvailableForRegisteredUser: true,
				}, nil)
				s.getConfig().EXPECT().GetLimitRelation().Return(&conf2.LimitRelation{
					OnlineTimeField:        time.Now().Add(-2 * time.Hour).Unix(),
					UnavailableReasonField: "Unavailable reason",
				}).AnyTimes()
				s.getStore().EXPECT().GetRelationshipRecordByUidAndNameSince(context.Background(), uint32(123), "Test", gomock.Any()).Return([]*RelationInfo{}, nil)
			},
		},
		{
			name: "Limited relationship exists, is not active and user has had this relationship",
			args: args{
				c: context.Background(),
				request: &offering_room.CheckRelationNameAvailabilityRequest{
					RelationName: "Test",
					Uid:          123,
				},
			},
			want: &offering_room.CheckRelationNameAvailabilityResponse{
				IsAvailable: true,
			},
			wantErr: false,
			initFunc: func(s *relationshipMgrHelperForTest) {
				s.getStore().EXPECT().GetLimitedRelationshipByName(context.Background(), "Test").Return(&LimitedRelationshipsConfig{
					StartTime:                    time.Now().Add(time.Hour),
					EndTime:                      time.Now().Add(2 * time.Hour),
					IsAvailableForRegisteredUser: true,
				}, nil)
				s.getConfig().EXPECT().GetLimitRelation().Return(&conf2.LimitRelation{
					OnlineTimeField: time.Now().Add(-2 * time.Hour).Unix(),
				})
				s.getStore().EXPECT().GetRelationshipRecordByUidAndNameSince(context.Background(), uint32(123), "Test", gomock.Any()).Return([]*RelationInfo{
					{
						RelationShipName: "Test",
					},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := newRelationshipMgrHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(o)
			}
			got, err := o.CheckRelationNameAvailability(tt.args.c, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("releationshipMgr.CheckRelationNameAvailability() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("releationshipMgr.CheckRelationNameAvailability() = %v, want %v", got, tt.want)
			}
		})
	}
}
