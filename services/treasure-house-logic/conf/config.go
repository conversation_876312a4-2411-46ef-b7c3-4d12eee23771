package conf

import (
	"encoding/json"
	"fmt"
	_ "golang.52tt.com/pkg/config"
	"io/ioutil"
)

type ServiceConfigT struct {
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	if err = Setup(); err != nil {
		return err
	}

	return
}

func (sc *ServiceConfigT) GetEntranceSwitch() bool {
	if TreasureHouseDyConfig == nil {
		return false
	}
	return TreasureHouseDyConfig.Get().EntranceSwitch
}
