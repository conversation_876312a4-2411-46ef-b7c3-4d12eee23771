package control

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/services/golddiamond-logic/models"
	api "golang.52tt.com/services/golddiamond-logic/models/gen-go"
)

// GetYuyinIncomeDetailHandler 获取会长服务号语音直播收益数据详情（会长服务号首页加载接口）
func GetYuyinIncomeDetailHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	// 解析请求参数
	var req api.GetYuyinIncomeDetailReq
	err := json.Unmarshal(authInfo.Body, &req)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to parse request body [%s], err [%+v]", string(authInfo.Body), err)
		web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_UNMARSHAL), "unmarshal err", &api.GetYuyinIncomeDetailRsp{
			DateDimenIncome: &api.GetDateDimenIncomeRsp{}, MonthDimenIncome: &api.GetMonthDimenIncomeRsp{},
			WeekDimenIncome: &api.GetWeekDimenIncomeRsp{}})
		return
	}
	log.DebugfWithCtx(ctx, "GetYuyinIncomeDetail body:[%+v] req:[%+v]", string(authInfo.Body), req)

	// 校验参数合法性
	if req.GetGuildid() == 0 || req.GetUid() == "" {
		log.ErrorWithCtx(ctx, "GetYuyinIncomeDetail param err: guildId:[%+v]， uid:[%+v]", req.GetGuildid(), req.GetUid())
		web.ServeAPICodeJson(w, int32(api.ErrorCode_ERROR_CODE_PARR), "param err", &api.GetYuyinIncomeDetailRsp{
			DateDimenIncome: &api.GetDateDimenIncomeRsp{}, MonthDimenIncome: &api.GetMonthDimenIncomeRsp{},
			WeekDimenIncome: &api.GetWeekDimenIncomeRsp{}})
		return
	}

	// RPC调用下游服务
	// 校验公会ID是否合法
	valid, guildUid := models.GetModelServer().CheckGuildId(ctx, req.GetGuildid(), authInfo.UserID)
	if !valid {
		log.ErrorWithCtx(ctx, "CheckGuildId param err:  guildId:[%+v], req_uid:[%+v], real_uid:[%+v]", req.GetGuildid(), authInfo.UserID, guildUid)
		web.ServeBadReq(w)
		return
	}
	// 请求下游接口
	detailRsp, err := models.GetModelServer().GoldCommissionClient.GetYuyinIncomeDetail(ctx, authInfo.UserID, req.GetGuildid())
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinIncomeDetail failed，err:[%+v]", err.Error())
		web.ServeAPIError(w)
		return
	}

	// 组装返回体
	rsp := &api.GetYuyinIncomeDetailRsp{}
	if daily := detailRsp.GetDailyIncome(); daily != nil {
		rsp.DateDimenIncome = &api.GetDateDimenIncomeRsp{
			TodayIncome:     daily.GetTodayIncome(),
			YesterdayIncome: daily.GetYesterdayIncome(),
			LastdayQoq:      daily.GetDailyQoq(),
		}
	}
	if weekly := detailRsp.GetWeeklyIncome(); weekly != nil {
		rsp.WeekDimenIncome = &api.GetWeekDimenIncomeRsp{
			ThisWeekIncome: weekly.GetThisWeekIncome(),
			LastWeekIncome: weekly.GetLastWeekIncome(),
			CurrentTime:    weekly.GetCurrentTime(),
			WeekStartTime:  weekly.GetWeekStartTime(),
			WeekEndTime:    weekly.GetWeekEndTime(),
		}
	}
	if monthly := detailRsp.GetMonthlyIncome(); monthly != nil {
		rsp.MonthDimenIncome = &api.GetMonthDimenIncomeRsp{
			ThisMonthIncome:         monthly.GetThisMonthIncome(),
			LastMonthIncome:         monthly.GetLastMonthIncome(),
			MonthQoq:                monthly.GetMonthlyQoq(),
			SameLastMonthIncome:     monthly.GetSameLastMonthIncome(),
			LastMonthValidIncomeNum: monthly.GetLastMonthValidIncome(),
			MonthSixIncome:          monthly.GetLastSixMonthIncome(),
		}
	}
	log.DebugfWithCtx(ctx, "GetYuyinIncomeDetail rsp [%+v]", rsp)
	web.ServeAPIJson(w, rsp)
}
