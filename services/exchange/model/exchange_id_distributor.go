package model

import (
    "github.com/jinzhu/gorm"
    "time"
)

type ExchangeIdDistributor struct {
    IdNumber        string `gorm:"primary_key;type:varchar(20);auto_increment:false"`
    DistributorName string `gorm:"not null;type:varchar(200)"`
    CreateTime      uint64 `gorm:"not null;default:0"`
}

func (e *ExchangeIdDistributor) TableName() string {
    return "exchange_id_distributor"
}

type ExchangeIdDistributorDB struct {
    ExchangeIdDistributor
    Db *gorm.DB
}

func (xdb *ExchangeIdDistributorDB)AutoMigrate() error {
    return xdb.Db.AutoMigrate(&xdb.ExchangeIdDistributor).Error
}

func (xdb *ExchangeIdDistributorDB) LoadWithIdNumber(idNumber string) {
    xdb.Db.Model(&xdb.ExchangeIdDistributor).Where("id_number=?", idNumber).First(&xdb.ExchangeIdDistributor)
}

func (xdb *ExchangeIdDistributorDB) SaveData() error {
    xdb.CreateTime = uint64(time.Now().Unix())
    return xdb.Db.Model(&xdb.ExchangeIdDistributor).Save(&xdb.ExchangeIdDistributor).Error
}

func (xdb *ExchangeIdDistributorDB) GetAll() (list []ExchangeIdDistributor) {
    xdb.Db.Model(&xdb.ExchangeIdDistributor).Find(&list)
    return
}