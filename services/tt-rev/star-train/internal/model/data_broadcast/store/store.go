//go:generate quicksilver-cli test interface ../store
//go:generate mockgen -destination=../mocks/store.go -package=mocks golang.52tt.com/services/tt-rev/star-train/internal/model/data_broadcast/store IStore

package store

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/protocol/services/userpresent"
	processStore "golang.52tt.com/services/tt-rev/star-train/internal/model/game_process/store"
	settleStore "golang.52tt.com/services/tt-rev/star-train/internal/model/game_settle/store"
	"strings"
	"time"
)

type Store struct {
	readonlyDb mysql.DBx
}

func NewStore(readonlyDb mysql.DBx) *Store {
	s := &Store{
		readonlyDb: readonlyDb,
	}
	return s
}

func genParamJoinStr(list []uint32) string {
	strList := make([]string, 0, len(list))
	for _, i := range list {
		strList = append(strList, fmt.Sprint(i))
	}

	return strings.Join(strList, ",")
}

func (s *Store) Close() {}

// GetRoundInfoByEndTime 获取近一段时间结束的轮次信息
func (s *Store) GetRoundInfoByEndTime(ctx context.Context, lastTime time.Time) ([]*processStore.RoundRecord, error) {
	now := time.Now()
	monthTime := time.Date(lastTime.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	list := make([]*processStore.RoundRecord, 0)

	sqlFormat := "select id,activity_id,round_id,train_id,total_distance,curr_distance,begin_time,end_time,ctime,mtime from %s where end_time>=?"
	query := fmt.Sprintf(sqlFormat, processStore.GenTrainRoundTblName(monthTime))

	err := s.readonlyDb.SelectContext(ctx, &list, query, lastTime)
	if err != nil {
		// 非表不存在错误
		if !mysql.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetRoundInfoByEndTime fail. now:%v, err %v", now, err)
			return list, err
		}
	}

	if lastTime.Month() == now.Month() {
		return list, nil
	}

	// 查上个月的表
	lastMonth := monthTime.AddDate(0, -1, 0)
	query = fmt.Sprintf(sqlFormat, processStore.GenTrainRoundTblName(lastMonth))
	err = s.readonlyDb.SelectContext(ctx, &list, query, lastTime)
	if err != nil {
		// 非表不存在错误
		if !mysql.IsMySQLError(err, 1146) {
			log.ErrorWithCtx(ctx, "GetRoundInfoByEndTime fail. now:%v, err %v", now, err)
			return list, err
		}
	}

	return list, nil
}

// GetStationListByRound 获取轮次站点列表
func (s *Store) GetStationListByRound(ctx context.Context, actId, roundId uint32, roundTime time.Time) ([]*processStore.StationRecord, error) {
	list := make([]*processStore.StationRecord, 0)
	sql := fmt.Sprintf("select id,activity_id,round_id,train_id,station_id,bg_id,prize_type,begin_distance,end_distance,settle_status,ctime,mtime"+
		" from %s where activity_id=? and round_id=? order by end_distance", processStore.GenTrainStationTblName(roundTime))

	err := s.readonlyDb.SelectContext(ctx, &list, sql, actId, roundId)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		log.ErrorWithCtx(ctx, "GetStationListByRound fail. actId:%d, roundId:%d, err %v", actId, roundId, err)
		return list, err
	}

	return list, nil
}

// GetAwardListByStation 获取站点奖励列表
func (s *Store) GetAwardListByStation(ctx context.Context, actId, roundId, stationDistance, prizeTy uint32, roundTime time.Time) ([]*settleStore.AwardRecord, error) {
	list := make([]*settleStore.AwardRecord, 0)
	sql := fmt.Sprintf("select order_id,activity_id,round_id,station_distance,uid,gift_id,gift_type,gift_name,gift_icon,gift_worth,price_type,"+
		"amount,status,prize_type,award_time,round_time from %s "+
		"where activity_id=? and round_id=? and station_distance=? and prize_type=?",
		settleStore.GenAwardTblName(roundTime))

	err := s.readonlyDb.SelectContext(ctx, &list, sql, actId, roundId, stationDistance, prizeTy)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		log.ErrorWithCtx(ctx, "GetAwardListByStation fail. actId:%d, roundId:%d, stationDistance:%d, err %v", actId, roundId, stationDistance, err)
		return list, err
	}

	return list, nil
}

type SummaryInfo struct {
	Id         uint32 `db:"id"`
	TotalWorth int64  `db:"total_worth"` // t豆总数
	UserCnt    uint32 `db:"user_cnt"`    // 用户数
}

// GetAwardSummaryByPrizeTy 获取奖励汇总信息
func (s *Store) GetAwardSummaryByPrizeTy(ctx context.Context, actId, roundId, stationDistance, prizeTy uint32, roundTime time.Time) (*SummaryInfo, error) {
	var summary SummaryInfo
	sql := fmt.Sprintf("select prize_type as id, sum(if(price_type=?,gift_worth*amount,0)) as total_worth, count(distinct uid) as user_cnt from %s "+
		"where activity_id=? and round_id=? and station_distance=? and prize_type=? group by prize_type",
		settleStore.GenAwardTblName(roundTime))

	err := s.readonlyDb.GetContext(ctx, &summary, sql, uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN), actId, roundId, stationDistance, prizeTy)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		if mysql.IsNoRowsError(err) {
			return &summary, nil
		}

		log.ErrorWithCtx(ctx, "GetAwardSummaryByPrizeTy fail. actId:%d, roundId:%d, stationDistance:%d, prizeTy:%d, err %v", actId, roundId, stationDistance, prizeTy, err)
		return &summary, err
	}

	return &summary, nil
}

// GetAwardSummaryByAwardTime 根据时间获取奖励汇总信息
func (s *Store) GetAwardSummaryByAwardTime(ctx context.Context, beginTime, endTime time.Time) ([]*SummaryInfo, error) {
	list := make([]*SummaryInfo, 0)
	sql := fmt.Sprintf("select prize_type as id, sum(if(price_type=?,gift_worth*amount,0)) as total_worth, count(distinct uid) as user_cnt from %s "+
		"where award_time>=? and award_time<? group by prize_type",
		settleStore.GenAwardTblName(beginTime))

	err := s.readonlyDb.SelectContext(ctx, &list, sql, uint32(userpresent.PresentPriceType_PRESENT_PRICE_TBEAN), beginTime, endTime)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		if mysql.IsNoRowsError(err) {
			return list, nil
		}

		log.ErrorWithCtx(ctx, "GetAwardSummaryByAwardTime fail. err %v", err)
		return list, err
	}

	return list, nil
}

// GetUserJoinDistance 获取用户在区间内的助力流水
func (s *Store) GetUserJoinDistance(ctx context.Context, actId, roundId, uid, beginDistance, endDistance uint32, roundTime time.Time) (uint32, error) {
	var val uint32
	query := fmt.Sprintf("select IFNULL(SUM(LEAST(final_distance, ?) - GREATEST(final_distance-incr_distance, ?)),0) as distance from %s "+
		"where uid=? and activity_id=? and round_id=? and final_distance > ? and final_distance-incr_distance < ? group by uid",
		processStore.GenUserJoinTblName(roundTime))
	err := s.readonlyDb.GetContext(ctx, &val, query, endDistance, beginDistance, uid, actId, roundId, beginDistance, endDistance)
	if err != nil {
		// 表不存在
		if mysql.IsMySQLError(err, 1146) {
			return val, nil
		}

		if mysql.IsNoRowsError(err) {
			return val, nil
		}

		log.ErrorWithCtx(ctx, "GetUserJoinDistance fail. uid:%d, activityID:%d, roundID:%d, beginDistance:%d, endDistance:%d, err %v",
			uid, actId, roundId, beginDistance, endDistance, err)
		return val, err
	}

	return val, nil
}

// GetJoinDistanceSummaryByRound 根据轮次获取助力汇总信息
func (s *Store) GetJoinDistanceSummaryByRound(ctx context.Context, actId, roundId uint32, roundTime time.Time) (*SummaryInfo, error) {
	var summary SummaryInfo
	sql := fmt.Sprintf("select round_id as id, sum(incr_distance) as total_worth, count(distinct uid) as user_cnt from %s "+
		"where activity_id=? and round_id=? group by round_id",
		processStore.GenUserJoinTblName(roundTime))

	err := s.readonlyDb.GetContext(ctx, &summary, sql, actId, roundId)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		if mysql.IsNoRowsError(err) {
			return &summary, nil
		}

		log.ErrorWithCtx(ctx, "GetJoinDistanceSummaryByRound fail. actId:%d, roundId:%d, err %v", actId, roundId, err)
		return &summary, err
	}

	return &summary, nil
}

// GetJoinDistanceSummaryByCTime 根据时间获取助力汇总信息
func (s *Store) GetJoinDistanceSummaryByCTime(ctx context.Context, beginTime, endTime time.Time) (*SummaryInfo, error) {
	var summary SummaryInfo
	sql := fmt.Sprintf("select ifnull(sum(incr_distance),0) as total_worth, ifnull(count(distinct uid),0) as user_cnt from %s "+
		"where ctime>=? and ctime<?",
		processStore.GenUserJoinTblName(beginTime))

	err := s.readonlyDb.GetContext(ctx, &summary, sql, beginTime, endTime)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		if mysql.IsNoRowsError(err) {
			return &summary, nil
		}

		log.ErrorWithCtx(ctx, "GetJoinDistanceSummaryByCTime fail. err %v", err)
		return &summary, err
	}

	return &summary, nil
}

// GetPresentTotalWorth 获取平台总送礼流水
func (s *Store) GetPresentTotalWorth(ctx context.Context, beginTime, endTime time.Time) (int64, error) {
	var totalWorth int64
	sql := fmt.Sprintf("select ifnull(sum(total_price),0) as total_worth from %s "+
		"where outside_time>=? and outside_time<?",
		processStore.GenPresentLogTableName(beginTime))

	err := s.readonlyDb.GetContext(ctx, &totalWorth, sql, beginTime, endTime)
	if err != nil && !mysql.IsMySQLError(err, 1146) {
		if mysql.IsNoRowsError(err) {
			return totalWorth, nil
		}

		log.ErrorWithCtx(ctx, "GetPresentTotalWorth fail. err %v", err)
		return totalWorth, err
	}

	return totalWorth, nil
}
