package mgr

import (
    "context"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/timer"
    "time"
)

func (mgr *Mgr) setupTimer() error {
    // 创建定时器
    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    // 大小进程的定时器，同一个任务只会在一个节点上执行
    timerD, err := timer.NewTimerD(ctx, "glory-lottery", timer.WithV8RedisCmdable(mgr.cache.GetRedisClient()), timer.WithTTL(10*time.Second))
    if err != nil {
        log.Errorf("setupTimer fail to NewTimerD. err:%v", err)
        return err
    }

    err = timerD.AddTask("@every 1s", "InitRandomMinute", timer.BuildFromLambda(func(ctx context.Context) {
        // 生成库存更新随机值
        mgr.InitRandomMinute()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "AutoUpdateCurrentStockHourly", timer.BuildFromLambda(func(ctx context.Context) {
        //每小时更新固定库存
        mgr.AutoUpdateCurrentStockHourly()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "UpdateCurrentStockManually", timer.BuildFromLambda(func(ctx context.Context) {
        //每小时更新手动库存
        mgr.UpdateCurrentStockManually()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "HandleMonthStock", timer.BuildFromLambda(func(ctx context.Context) {
        //每月更新库存
        mgr.HandleMonthStock()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "HandleHourReport", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟检查一次小时报告
        mgr.HandleHourReport()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "HandleDayReport", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟检查一次每天报告
        mgr.HandleDayReport()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1h", "HandleMonthReport", timer.BuildFromLambda(func(ctx context.Context) {
        // 每小时检查一次每月报告
        mgr.HandleMonthReport()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 3m", "StockWarningOnly", timer.BuildFromLambda(func(ctx context.Context) {
        // 每3min检查一次库存
        mgr.StockWarningOnly()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "ResetStockCyclely", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟检测一次库存重置
        mgr.ResetStockCyclely()
    }))
    if err != nil {
        return err
    }

    err = timerD.AddTask("@every 1m", "AutoReplaceOrder", timer.BuildFromLambda(func(ctx context.Context) {
        // 每分钟执行一次自动补单的, 处理订单区间为 [当前时间-6min, 当前时间-1min], 最大重试五次
        mgr.AutoReplaceOrder()
    }))
    if err != nil {
        return err
    }

    timerD.Start()

    mgr.timerD = timerD
    return nil
}

/*










*/
