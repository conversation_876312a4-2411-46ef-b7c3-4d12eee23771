// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/user-profile-api (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	app "golang.52tt.com/protocol/app"
	account_go "golang.52tt.com/protocol/services/account-go"
	Account "golang.52tt.com/protocol/services/accountsvr"
	youknowwho "golang.52tt.com/protocol/services/youknowwho"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserProfile mocks base method.
func (m *MockIClient) BatchGetUserProfile(arg0 context.Context, arg1 []uint32) (map[uint32]*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserProfile", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserProfile indicates an expected call of BatchGetUserProfile.
func (mr *MockIClientMockRecorder) BatchGetUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserProfile", reflect.TypeOf((*MockIClient)(nil).BatchGetUserProfile), arg0, arg1)
}

// BatchGetUserProfileAndUKWPersonInfo mocks base method.
func (m *MockIClient) BatchGetUserProfileAndUKWPersonInfo(arg0 context.Context, arg1 []uint32) (map[uint32]*app.UserProfile, map[uint32]*youknowwho.UKWPersonInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserProfileAndUKWPersonInfo", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(map[uint32]*youknowwho.UKWPersonInfo)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// BatchGetUserProfileAndUKWPersonInfo indicates an expected call of BatchGetUserProfileAndUKWPersonInfo.
func (mr *MockIClientMockRecorder) BatchGetUserProfileAndUKWPersonInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserProfileAndUKWPersonInfo", reflect.TypeOf((*MockIClient)(nil).BatchGetUserProfileAndUKWPersonInfo), arg0, arg1)
}

// BatchGetUserProfileV2 mocks base method.
func (m *MockIClient) BatchGetUserProfileV2(arg0 context.Context, arg1 []uint32, arg2 bool) (map[uint32]*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserProfileV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchGetUserProfileV2 indicates an expected call of BatchGetUserProfileV2.
func (mr *MockIClientMockRecorder) BatchGetUserProfileV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserProfileV2", reflect.TypeOf((*MockIClient)(nil).BatchGetUserProfileV2), arg0, arg1, arg2)
}

// BatchGetUserYNWProfile mocks base method.
func (m *MockIClient) BatchGetUserYNWProfile(arg0 context.Context, arg1 []uint32) (map[uint32]*app.UserProfile, map[uint32]*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserYNWProfile", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(map[uint32]*app.UserProfile)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// BatchGetUserYNWProfile indicates an expected call of BatchGetUserYNWProfile.
func (mr *MockIClientMockRecorder) BatchGetUserYNWProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserYNWProfile", reflect.TypeOf((*MockIClient)(nil).BatchGetUserYNWProfile), arg0, arg1)
}

// BatchUserGoInfoToProfile mocks base method.
func (m *MockIClient) BatchUserGoInfoToProfile(arg0 context.Context, arg1 map[uint32]*account_go.UserResp, arg2 bool) (map[uint32]*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUserGoInfoToProfile", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchUserGoInfoToProfile indicates an expected call of BatchUserGoInfoToProfile.
func (mr *MockIClientMockRecorder) BatchUserGoInfoToProfile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUserGoInfoToProfile", reflect.TypeOf((*MockIClient)(nil).BatchUserGoInfoToProfile), arg0, arg1, arg2)
}

// BatchUserInfoToProfile mocks base method.
func (m *MockIClient) BatchUserInfoToProfile(arg0 context.Context, arg1 map[uint32]*Account.UserResp, arg2 bool) (map[uint32]*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUserInfoToProfile", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint32]*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchUserInfoToProfile indicates an expected call of BatchUserInfoToProfile.
func (mr *MockIClientMockRecorder) BatchUserInfoToProfile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUserInfoToProfile", reflect.TypeOf((*MockIClient)(nil).BatchUserInfoToProfile), arg0, arg1, arg2)
}

// GetUserProfile mocks base method.
func (m *MockIClient) GetUserProfile(arg0 context.Context, arg1 uint32) (*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", arg0, arg1)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockIClientMockRecorder) GetUserProfile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockIClient)(nil).GetUserProfile), arg0, arg1)
}

// GetUserProfileAndAccount mocks base method.
func (m *MockIClient) GetUserProfileAndAccount(arg0 context.Context, arg1 uint32) (*app.UserProfile, *Account.UserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfileAndAccount", arg0, arg1)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(*Account.UserResp)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetUserProfileAndAccount indicates an expected call of GetUserProfileAndAccount.
func (mr *MockIClientMockRecorder) GetUserProfileAndAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfileAndAccount", reflect.TypeOf((*MockIClient)(nil).GetUserProfileAndAccount), arg0, arg1)
}

// GetUserProfileV2 mocks base method.
func (m *MockIClient) GetUserProfileV2(arg0 context.Context, arg1 uint32, arg2 bool) (*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfileV2", arg0, arg1, arg2)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUserProfileV2 indicates an expected call of GetUserProfileV2.
func (mr *MockIClientMockRecorder) GetUserProfileV2(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfileV2", reflect.TypeOf((*MockIClient)(nil).GetUserProfileV2), arg0, arg1, arg2)
}

// UkwAndUserInfoToProfile mocks base method.
func (m *MockIClient) UkwAndUserInfoToProfile(arg0 context.Context, arg1 *youknowwho.UKWPersonInfo, arg2 *Account.UserResp, arg3 bool) (*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UkwAndUserInfoToProfile", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UkwAndUserInfoToProfile indicates an expected call of UkwAndUserInfoToProfile.
func (mr *MockIClientMockRecorder) UkwAndUserInfoToProfile(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UkwAndUserInfoToProfile", reflect.TypeOf((*MockIClient)(nil).UkwAndUserInfoToProfile), arg0, arg1, arg2, arg3)
}

// UserInfoToProfile mocks base method.
func (m *MockIClient) UserInfoToProfile(arg0 context.Context, arg1 *Account.UserResp, arg2 bool) (*app.UserProfile, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserInfoToProfile", arg0, arg1, arg2)
	ret0, _ := ret[0].(*app.UserProfile)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UserInfoToProfile indicates an expected call of UserInfoToProfile.
func (mr *MockIClientMockRecorder) UserInfoToProfile(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserInfoToProfile", reflect.TypeOf((*MockIClient)(nil).UserInfoToProfile), arg0, arg1, arg2)
}
