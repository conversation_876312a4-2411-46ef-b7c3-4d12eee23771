// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/tel-call-logic/iclient.go

// Package telcalllogic is a generated GoMock package.
package telcalllogic

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	telcalllogic "golang.52tt.com/protocol/app/telcalllogic"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchRemoveGroupUser mocks base method.
func (m *MockIClient) BatchRemoveGroupUser(ctx context.Context, req telcalllogic.BatchRemoveGroupUserReq) (*telcalllogic.BatchRemoveGroupUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchRemoveGroupUser", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.BatchRemoveGroupUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BatchRemoveGroupUser indicates an expected call of BatchRemoveGroupUser.
func (mr *MockIClientMockRecorder) BatchRemoveGroupUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchRemoveGroupUser", reflect.TypeOf((*MockIClient)(nil).BatchRemoveGroupUser), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CallStart mocks base method.
func (m *MockIClient) CallStart(ctx context.Context, req telcalllogic.CallStartReq) (*telcalllogic.CallStartResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CallStart", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.CallStartResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CallStart indicates an expected call of CallStart.
func (mr *MockIClientMockRecorder) CallStart(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CallStart", reflect.TypeOf((*MockIClient)(nil).CallStart), ctx, req)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCallGroup mocks base method.
func (m *MockIClient) GetCallGroup(ctx context.Context, req telcalllogic.GetCallGroupReq) (*telcalllogic.GetCallGroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallGroup", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.GetCallGroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCallGroup indicates an expected call of GetCallGroup.
func (mr *MockIClientMockRecorder) GetCallGroup(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallGroup", reflect.TypeOf((*MockIClient)(nil).GetCallGroup), ctx, req)
}

// GetCallGroupAllUsers mocks base method.
func (m *MockIClient) GetCallGroupAllUsers(ctx context.Context, req telcalllogic.GetCallGroupAllUsersReq) (*telcalllogic.GetCallGroupAllUsersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallGroupAllUsers", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.GetCallGroupAllUsersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCallGroupAllUsers indicates an expected call of GetCallGroupAllUsers.
func (mr *MockIClientMockRecorder) GetCallGroupAllUsers(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallGroupAllUsers", reflect.TypeOf((*MockIClient)(nil).GetCallGroupAllUsers), ctx, req)
}

// GetCallGroupUser mocks base method.
func (m *MockIClient) GetCallGroupUser(ctx context.Context, req telcalllogic.GetCallGroupUserReq) (*telcalllogic.GetCallGroupUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallGroupUser", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.GetCallGroupUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCallGroupUser indicates an expected call of GetCallGroupUser.
func (mr *MockIClientMockRecorder) GetCallGroupUser(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallGroupUser", reflect.TypeOf((*MockIClient)(nil).GetCallGroupUser), ctx, req)
}

// GetCallGroupUsers mocks base method.
func (m *MockIClient) GetCallGroupUsers(ctx context.Context, req telcalllogic.GetCallGroupUsersReq) (*telcalllogic.GetCallGroupUsersResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallGroupUsers", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.GetCallGroupUsersResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCallGroupUsers indicates an expected call of GetCallGroupUsers.
func (mr *MockIClientMockRecorder) GetCallGroupUsers(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallGroupUsers", reflect.TypeOf((*MockIClient)(nil).GetCallGroupUsers), ctx, req)
}

// GetCallUserStatus mocks base method.
func (m *MockIClient) GetCallUserStatus(ctx context.Context, req telcalllogic.GetCallUserStatusReq) (*telcalllogic.GetCallUserStatusResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallUserStatus", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.GetCallUserStatusResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCallUserStatus indicates an expected call of GetCallUserStatus.
func (mr *MockIClientMockRecorder) GetCallUserStatus(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallUserStatus", reflect.TypeOf((*MockIClient)(nil).GetCallUserStatus), ctx, req)
}

// GetSmsVerifyCode mocks base method.
func (m *MockIClient) GetSmsVerifyCode(ctx context.Context, req telcalllogic.GetSmsVerifyCodeReq) (*telcalllogic.GetSmsVerifyCodeResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSmsVerifyCode", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.GetSmsVerifyCodeResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetSmsVerifyCode indicates an expected call of GetSmsVerifyCode.
func (mr *MockIClientMockRecorder) GetSmsVerifyCode(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSmsVerifyCode", reflect.TypeOf((*MockIClient)(nil).GetSmsVerifyCode), ctx, req)
}

// HandleCallGroup mocks base method.
func (m *MockIClient) HandleCallGroup(ctx context.Context, req telcalllogic.HandleCallGroupReq) (*telcalllogic.HandleCallGroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleCallGroup", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.HandleCallGroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// HandleCallGroup indicates an expected call of HandleCallGroup.
func (mr *MockIClientMockRecorder) HandleCallGroup(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleCallGroup", reflect.TypeOf((*MockIClient)(nil).HandleCallGroup), ctx, req)
}

// JoinCallGroup mocks base method.
func (m *MockIClient) JoinCallGroup(ctx context.Context, req telcalllogic.JoinCallGroupReq) (*telcalllogic.JoinCallGroupResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinCallGroup", ctx, req)
	ret0, _ := ret[0].(*telcalllogic.JoinCallGroupResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// JoinCallGroup indicates an expected call of JoinCallGroup.
func (mr *MockIClientMockRecorder) JoinCallGroup(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinCallGroup", reflect.TypeOf((*MockIClient)(nil).JoinCallGroup), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
