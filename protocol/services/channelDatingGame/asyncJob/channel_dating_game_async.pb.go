// Code generated by protoc-gen-gogo.
// source: services/channelDatingGame/asyncJob/channel_dating_game_async.proto
// DO NOT EDIT!

/*
	Package channel_dating_game_async is a generated protocol buffer package.

	It is generated from these files:
		services/channelDatingGame/asyncJob/channel_dating_game_async.proto

	It has these top-level messages:
		ChannelDatingGameAsyncJobHatNotify
		ChannelDatingGameAsyncJobVipNotify
		ChannelDatingMicUserLikeBeatValNotify
		ChannelDatingSelectStatusNotify
		SelectStatusInfo
		ChannelDatingLikeBeatObjNotify
		ChannelDatingApplyMicNotify
*/
package channel_dating_game_async

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"

import "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 帽子变化
type ChannelDatingGameAsyncJobHatNotify struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	NewHatUid uint32 `protobuf:"varint,2,req,name=new_hat_uid,json=newHatUid" json:"new_hat_uid"`
	PreHatUid uint32 `protobuf:"varint,3,req,name=pre_hat_uid,json=preHatUid" json:"pre_hat_uid"`
	HatCfg    []byte `protobuf:"bytes,4,opt,name=hat_cfg,json=hatCfg" json:"hat_cfg"`
	OpUid     uint32 `protobuf:"varint,5,req,name=op_uid,json=opUid" json:"op_uid"`
	IsMale    bool   `protobuf:"varint,6,opt,name=is_male,json=isMale" json:"is_male"`
}

func (m *ChannelDatingGameAsyncJobHatNotify) Reset()         { *m = ChannelDatingGameAsyncJobHatNotify{} }
func (m *ChannelDatingGameAsyncJobHatNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelDatingGameAsyncJobHatNotify) ProtoMessage()    {}
func (*ChannelDatingGameAsyncJobHatNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{0}
}

func (m *ChannelDatingGameAsyncJobHatNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobHatNotify) GetNewHatUid() uint32 {
	if m != nil {
		return m.NewHatUid
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobHatNotify) GetPreHatUid() uint32 {
	if m != nil {
		return m.PreHatUid
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobHatNotify) GetHatCfg() []byte {
	if m != nil {
		return m.HatCfg
	}
	return nil
}

func (m *ChannelDatingGameAsyncJobHatNotify) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobHatNotify) GetIsMale() bool {
	if m != nil {
		return m.IsMale
	}
	return false
}

// vip用户变化
type ChannelDatingGameAsyncJobVipNotify struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	NewVipUid uint32 `protobuf:"varint,2,req,name=new_vip_uid,json=newVipUid" json:"new_vip_uid"`
	PreVipUid uint32 `protobuf:"varint,3,req,name=pre_vip_uid,json=preVipUid" json:"pre_vip_uid"`
	OpUid     uint32 `protobuf:"varint,4,req,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *ChannelDatingGameAsyncJobVipNotify) Reset()         { *m = ChannelDatingGameAsyncJobVipNotify{} }
func (m *ChannelDatingGameAsyncJobVipNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelDatingGameAsyncJobVipNotify) ProtoMessage()    {}
func (*ChannelDatingGameAsyncJobVipNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{1}
}

func (m *ChannelDatingGameAsyncJobVipNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobVipNotify) GetNewVipUid() uint32 {
	if m != nil {
		return m.NewVipUid
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobVipNotify) GetPreVipUid() uint32 {
	if m != nil {
		return m.PreVipUid
	}
	return 0
}

func (m *ChannelDatingGameAsyncJobVipNotify) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

// 麦上用户心动值
type ChannelDatingMicUserLikeBeatValNotify struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid         uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	LikeBeatVal uint32 `protobuf:"varint,3,req,name=like_beat_val,json=likeBeatVal" json:"like_beat_val"`
}

func (m *ChannelDatingMicUserLikeBeatValNotify) Reset()         { *m = ChannelDatingMicUserLikeBeatValNotify{} }
func (m *ChannelDatingMicUserLikeBeatValNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelDatingMicUserLikeBeatValNotify) ProtoMessage()    {}
func (*ChannelDatingMicUserLikeBeatValNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{2}
}

func (m *ChannelDatingMicUserLikeBeatValNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDatingMicUserLikeBeatValNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelDatingMicUserLikeBeatValNotify) GetLikeBeatVal() uint32 {
	if m != nil {
		return m.LikeBeatVal
	}
	return 0
}

// 麦上用户选择状态
type ChannelDatingSelectStatusNotify struct {
	ChannelId      uint32              `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StatusInfoList []*SelectStatusInfo `protobuf:"bytes,2,rep,name=status_info_list,json=statusInfoList" json:"status_info_list,omitempty"`
	PresideUid     uint32              `protobuf:"varint,3,req,name=preside_uid,json=presideUid" json:"preside_uid"`
}

func (m *ChannelDatingSelectStatusNotify) Reset()         { *m = ChannelDatingSelectStatusNotify{} }
func (m *ChannelDatingSelectStatusNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelDatingSelectStatusNotify) ProtoMessage()    {}
func (*ChannelDatingSelectStatusNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{3}
}

func (m *ChannelDatingSelectStatusNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDatingSelectStatusNotify) GetStatusInfoList() []*SelectStatusInfo {
	if m != nil {
		return m.StatusInfoList
	}
	return nil
}

func (m *ChannelDatingSelectStatusNotify) GetPresideUid() uint32 {
	if m != nil {
		return m.PresideUid
	}
	return 0
}

type SelectStatusInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	SelectStatus bool   `protobuf:"varint,2,req,name=select_status,json=selectStatus" json:"select_status"`
}

func (m *SelectStatusInfo) Reset()         { *m = SelectStatusInfo{} }
func (m *SelectStatusInfo) String() string { return proto.CompactTextString(m) }
func (*SelectStatusInfo) ProtoMessage()    {}
func (*SelectStatusInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{4}
}

func (m *SelectStatusInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SelectStatusInfo) GetSelectStatus() bool {
	if m != nil {
		return m.SelectStatus
	}
	return false
}

// 心动对象
type ChannelDatingLikeBeatObjNotify struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpenUid   uint32 `protobuf:"varint,2,req,name=open_uid,json=openUid" json:"open_uid"`
	LikeUid   uint32 `protobuf:"varint,3,req,name=like_uid,json=likeUid" json:"like_uid"`
	AddFriend bool   `protobuf:"varint,4,req,name=add_friend,json=addFriend" json:"add_friend"`
}

func (m *ChannelDatingLikeBeatObjNotify) Reset()         { *m = ChannelDatingLikeBeatObjNotify{} }
func (m *ChannelDatingLikeBeatObjNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelDatingLikeBeatObjNotify) ProtoMessage()    {}
func (*ChannelDatingLikeBeatObjNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{5}
}

func (m *ChannelDatingLikeBeatObjNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDatingLikeBeatObjNotify) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

func (m *ChannelDatingLikeBeatObjNotify) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

func (m *ChannelDatingLikeBeatObjNotify) GetAddFriend() bool {
	if m != nil {
		return m.AddFriend
	}
	return false
}

type ChannelDatingApplyMicNotify struct {
	ChannelId      uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	PushUid        uint32 `protobuf:"varint,2,req,name=push_uid,json=pushUid" json:"push_uid"`
	ApplyUid       uint32 `protobuf:"varint,3,req,name=apply_uid,json=applyUid" json:"apply_uid"`
	EntranceUid    uint32 `protobuf:"varint,4,req,name=entrance_uid,json=entranceUid" json:"entrance_uid"`
	ApplyUserCount uint32 `protobuf:"varint,5,req,name=apply_user_count,json=applyUserCount" json:"apply_user_count"`
	IsCancel       uint32 `protobuf:"varint,6,req,name=is_cancel,json=isCancel" json:"is_cancel"`
}

func (m *ChannelDatingApplyMicNotify) Reset()         { *m = ChannelDatingApplyMicNotify{} }
func (m *ChannelDatingApplyMicNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelDatingApplyMicNotify) ProtoMessage()    {}
func (*ChannelDatingApplyMicNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGameAsync, []int{6}
}

func (m *ChannelDatingApplyMicNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDatingApplyMicNotify) GetPushUid() uint32 {
	if m != nil {
		return m.PushUid
	}
	return 0
}

func (m *ChannelDatingApplyMicNotify) GetApplyUid() uint32 {
	if m != nil {
		return m.ApplyUid
	}
	return 0
}

func (m *ChannelDatingApplyMicNotify) GetEntranceUid() uint32 {
	if m != nil {
		return m.EntranceUid
	}
	return 0
}

func (m *ChannelDatingApplyMicNotify) GetApplyUserCount() uint32 {
	if m != nil {
		return m.ApplyUserCount
	}
	return 0
}

func (m *ChannelDatingApplyMicNotify) GetIsCancel() uint32 {
	if m != nil {
		return m.IsCancel
	}
	return 0
}

func init() {
	proto.RegisterType((*ChannelDatingGameAsyncJobHatNotify)(nil), "channel_dating_game.async.ChannelDatingGameAsyncJobHatNotify")
	proto.RegisterType((*ChannelDatingGameAsyncJobVipNotify)(nil), "channel_dating_game.async.ChannelDatingGameAsyncJobVipNotify")
	proto.RegisterType((*ChannelDatingMicUserLikeBeatValNotify)(nil), "channel_dating_game.async.ChannelDatingMicUserLikeBeatValNotify")
	proto.RegisterType((*ChannelDatingSelectStatusNotify)(nil), "channel_dating_game.async.ChannelDatingSelectStatusNotify")
	proto.RegisterType((*SelectStatusInfo)(nil), "channel_dating_game.async.SelectStatusInfo")
	proto.RegisterType((*ChannelDatingLikeBeatObjNotify)(nil), "channel_dating_game.async.ChannelDatingLikeBeatObjNotify")
	proto.RegisterType((*ChannelDatingApplyMicNotify)(nil), "channel_dating_game.async.ChannelDatingApplyMicNotify")
}
func (m *ChannelDatingGameAsyncJobHatNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDatingGameAsyncJobHatNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.NewHatUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.PreHatUid))
	if m.HatCfg != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(len(m.HatCfg)))
		i += copy(dAtA[i:], m.HatCfg)
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x30
	i++
	if m.IsMale {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelDatingGameAsyncJobVipNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDatingGameAsyncJobVipNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.NewVipUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.PreVipUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *ChannelDatingMicUserLikeBeatValNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDatingMicUserLikeBeatValNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.LikeBeatVal))
	return i, nil
}

func (m *ChannelDatingSelectStatusNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDatingSelectStatusNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ChannelId))
	if len(m.StatusInfoList) > 0 {
		for _, msg := range m.StatusInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.PresideUid))
	return i, nil
}

func (m *SelectStatusInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SelectStatusInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.SelectStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelDatingLikeBeatObjNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDatingLikeBeatObjNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.OpenUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.LikeUid))
	dAtA[i] = 0x20
	i++
	if m.AddFriend {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelDatingApplyMicNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelDatingApplyMicNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.PushUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ApplyUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.EntranceUid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.ApplyUserCount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelDatingGameAsync(dAtA, i, uint64(m.IsCancel))
	return i, nil
}

func encodeFixed64ChannelDatingGameAsync(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32ChannelDatingGameAsync(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelDatingGameAsync(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ChannelDatingGameAsyncJobHatNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGameAsync(uint64(m.NewHatUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.PreHatUid))
	if m.HatCfg != nil {
		l = len(m.HatCfg)
		n += 1 + l + sovChannelDatingGameAsync(uint64(l))
	}
	n += 1 + sovChannelDatingGameAsync(uint64(m.OpUid))
	n += 2
	return n
}

func (m *ChannelDatingGameAsyncJobVipNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGameAsync(uint64(m.NewVipUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.PreVipUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.OpUid))
	return n
}

func (m *ChannelDatingMicUserLikeBeatValNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGameAsync(uint64(m.Uid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.LikeBeatVal))
	return n
}

func (m *ChannelDatingSelectStatusNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.ChannelId))
	if len(m.StatusInfoList) > 0 {
		for _, e := range m.StatusInfoList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGameAsync(uint64(l))
		}
	}
	n += 1 + sovChannelDatingGameAsync(uint64(m.PresideUid))
	return n
}

func (m *SelectStatusInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.Uid))
	n += 2
	return n
}

func (m *ChannelDatingLikeBeatObjNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGameAsync(uint64(m.OpenUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.LikeUid))
	n += 2
	return n
}

func (m *ChannelDatingApplyMicNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGameAsync(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGameAsync(uint64(m.PushUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.ApplyUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.EntranceUid))
	n += 1 + sovChannelDatingGameAsync(uint64(m.ApplyUserCount))
	n += 1 + sovChannelDatingGameAsync(uint64(m.IsCancel))
	return n
}

func sovChannelDatingGameAsync(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelDatingGameAsync(x uint64) (n int) {
	return sovChannelDatingGameAsync(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ChannelDatingGameAsyncJobHatNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDatingGameAsyncJobHatNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDatingGameAsyncJobHatNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field NewHatUid", wireType)
			}
			m.NewHatUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewHatUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PreHatUid", wireType)
			}
			m.PreHatUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PreHatUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field HatCfg", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HatCfg = append(m.HatCfg[:0], dAtA[iNdEx:postIndex]...)
			if m.HatCfg == nil {
				m.HatCfg = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsMale", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMale = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("new_hat_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("pre_hat_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelDatingGameAsyncJobVipNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDatingGameAsyncJobVipNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDatingGameAsyncJobVipNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field NewVipUid", wireType)
			}
			m.NewVipUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewVipUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PreVipUid", wireType)
			}
			m.PreVipUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PreVipUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("new_vip_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("pre_vip_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelDatingMicUserLikeBeatValNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDatingMicUserLikeBeatValNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDatingMicUserLikeBeatValNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LikeBeatVal", wireType)
			}
			m.LikeBeatVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeBeatVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("like_beat_val")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelDatingSelectStatusNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDatingSelectStatusNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDatingSelectStatusNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field StatusInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StatusInfoList = append(m.StatusInfoList, &SelectStatusInfo{})
			if err := m.StatusInfoList[len(m.StatusInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PresideUid", wireType)
			}
			m.PresideUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PresideUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("preside_uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SelectStatusInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SelectStatusInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SelectStatusInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SelectStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SelectStatus = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("select_status")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelDatingLikeBeatObjNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDatingLikeBeatObjNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDatingLikeBeatObjNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OpenUid", wireType)
			}
			m.OpenUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LikeUid", wireType)
			}
			m.LikeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AddFriend", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AddFriend = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("open_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("like_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("add_friend")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelDatingApplyMicNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ChannelDatingApplyMicNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ChannelDatingApplyMicNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PushUid", wireType)
			}
			m.PushUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PushUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ApplyUid", wireType)
			}
			m.ApplyUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EntranceUid", wireType)
			}
			m.EntranceUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EntranceUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ApplyUserCount", wireType)
			}
			m.ApplyUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsCancel", wireType)
			}
			m.IsCancel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsCancel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGameAsync(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGameAsync
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("push_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("apply_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("entrance_uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("apply_user_count")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("is_cancel")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelDatingGameAsync(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelDatingGameAsync
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelDatingGameAsync
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelDatingGameAsync
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelDatingGameAsync
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelDatingGameAsync(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelDatingGameAsync = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelDatingGameAsync   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/channelDatingGame/asyncJob/channel_dating_game_async.proto", fileDescriptorChannelDatingGameAsync)
}

var fileDescriptorChannelDatingGameAsync = []byte{
	// 587 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x54, 0x5d, 0x6e, 0xd3, 0x4c,
	0x14, 0xad, 0xd3, 0x36, 0x4d, 0x6f, 0xda, 0x2a, 0xf2, 0xc3, 0x27, 0x7f, 0xaa, 0x48, 0x82, 0x4b,
	0x45, 0x10, 0x52, 0x2a, 0xb1, 0x83, 0x36, 0x08, 0x5a, 0xd4, 0x82, 0xd4, 0x2a, 0x79, 0x1d, 0x4d,
	0xec, 0xeb, 0x64, 0xa8, 0x33, 0x1e, 0x79, 0x26, 0xa9, 0xf2, 0xca, 0x02, 0x10, 0x0b, 0xe1, 0x89,
	0x35, 0xf0, 0xd0, 0x47, 0x56, 0x80, 0x50, 0xd8, 0x00, 0x4b, 0x40, 0x33, 0xb1, 0x5b, 0xff, 0x14,
	0xa4, 0xf0, 0x16, 0xdd, 0x7b, 0xee, 0xb9, 0xe7, 0x9c, 0xb9, 0x31, 0xf4, 0x24, 0xc6, 0x33, 0xe6,
	0xa1, 0x3c, 0xf2, 0xc6, 0x94, 0x73, 0x0c, 0x5f, 0x52, 0xc5, 0xf8, 0xe8, 0x35, 0x9d, 0xe0, 0x11,
	0x95, 0x73, 0xee, 0xbd, 0x89, 0x86, 0x69, 0x8b, 0xf8, 0xa6, 0x47, 0x46, 0x74, 0x82, 0xc4, 0x34,
	0xbb, 0x22, 0x8e, 0x54, 0x64, 0xff, 0xff, 0x00, 0xa0, 0x6b, 0x00, 0xee, 0x2f, 0x0b, 0xdc, 0x5e,
	0x91, 0xf9, 0x38, 0x21, 0x3e, 0xa5, 0xea, 0x6d, 0xa4, 0x58, 0x30, 0xb7, 0x0f, 0x00, 0x52, 0x0e,
	0xe6, 0x3b, 0x56, 0xbb, 0xd2, 0xd9, 0x3d, 0xd9, 0xb8, 0xfd, 0xde, 0x5a, 0xbb, 0xdc, 0x4e, 0xea,
	0x67, 0xbe, 0xfd, 0x04, 0xea, 0x1c, 0x6f, 0xc8, 0x98, 0x2a, 0x32, 0x65, 0xbe, 0x53, 0xc9, 0xa2,
	0x38, 0xde, 0x9c, 0x52, 0xd5, 0x67, 0x06, 0x25, 0x62, 0xbc, 0x43, 0xad, 0x67, 0x51, 0x22, 0xc6,
	0x04, 0xf5, 0x08, 0xb6, 0x34, 0xc2, 0x0b, 0x46, 0xce, 0x46, 0xdb, 0xea, 0xec, 0x24, 0x88, 0xea,
	0x98, 0xaa, 0x5e, 0x30, 0xb2, 0xf7, 0xa1, 0x1a, 0x09, 0x33, 0xbf, 0x99, 0x99, 0xdf, 0x8c, 0x44,
	0x32, 0xcb, 0x24, 0x99, 0xd0, 0x10, 0x9d, 0x6a, 0xdb, 0xea, 0xd4, 0xd2, 0x59, 0x26, 0x2f, 0x68,
	0x88, 0xee, 0x97, 0xbf, 0x59, 0x1e, 0x30, 0xf1, 0x0f, 0x96, 0x67, 0x4c, 0x3c, 0x68, 0x79, 0xc0,
	0x44, 0xc6, 0x72, 0x8a, 0x2a, 0x5a, 0x4e, 0x50, 0xf7, 0x9e, 0x36, 0x4a, 0x9e, 0xdc, 0x8f, 0x16,
	0x1c, 0xe6, 0x44, 0x5f, 0x30, 0xaf, 0x2f, 0x31, 0x3e, 0x67, 0xd7, 0x78, 0x82, 0x54, 0x0d, 0x68,
	0xb8, 0x8a, 0xee, 0xff, 0x60, 0xbd, 0xa8, 0x57, 0x17, 0xec, 0x0e, 0xec, 0x86, 0xec, 0x1a, 0xc9,
	0x10, 0xa9, 0x22, 0x33, 0x1a, 0xe6, 0xb4, 0xd6, 0xc3, 0xfb, 0x65, 0xee, 0x57, 0x0b, 0x5a, 0x39,
	0x41, 0x57, 0x18, 0xa2, 0xa7, 0xae, 0x14, 0x55, 0x53, 0xb9, 0x8a, 0x94, 0x3e, 0x34, 0xa4, 0x19,
	0x22, 0x8c, 0x07, 0x11, 0x09, 0x99, 0x54, 0x4e, 0xa5, 0xbd, 0xde, 0xa9, 0xbf, 0x78, 0xde, 0xfd,
	0xe3, 0xdd, 0x76, 0xb3, 0xdb, 0xce, 0x78, 0x10, 0x5d, 0xee, 0xc9, 0xbb, 0xdf, 0xe7, 0x4c, 0x2a,
	0xfb, 0xd0, 0x64, 0x2e, 0x99, 0x8f, 0xa5, 0xcc, 0x21, 0x69, 0xe8, 0x5c, 0xfb, 0xd0, 0x28, 0x52,
	0xa5, 0xe1, 0x58, 0xc5, 0x70, 0x9e, 0xc1, 0xae, 0x34, 0x58, 0xb2, 0xdc, 0x65, 0xe2, 0x4b, 0xaf,
	0x6b, 0x47, 0x66, 0x68, 0xdc, 0xcf, 0x16, 0x34, 0x73, 0xe9, 0xa4, 0xef, 0xf4, 0x6e, 0xf8, 0x7e,
	0x95, 0x70, 0x5a, 0x50, 0x8b, 0x04, 0xf2, 0xd2, 0x71, 0x6d, 0xe9, 0xaa, 0x3e, 0x9a, 0x16, 0xd4,
	0xcc, 0x83, 0x15, 0x3d, 0x6e, 0xe9, 0xaa, 0x06, 0x1c, 0x00, 0x50, 0xdf, 0x27, 0x41, 0xcc, 0x90,
	0x2f, 0x2f, 0x2b, 0x55, 0xbc, 0x4d, 0x7d, 0xff, 0x95, 0x29, 0xbb, 0x1f, 0x2a, 0xb0, 0x9f, 0x93,
	0x7b, 0x2c, 0x44, 0x38, 0xbf, 0x60, 0xde, 0x8a, 0x5a, 0xc5, 0x54, 0x8e, 0xcb, 0x5a, 0x75, 0x55,
	0x4b, 0x79, 0x0c, 0xdb, 0x54, 0xf3, 0x96, 0xc4, 0xd6, 0x4c, 0x59, 0x43, 0x9e, 0xc2, 0x0e, 0x72,
	0x15, 0x53, 0xee, 0x61, 0xe9, 0x9f, 0x50, 0x4f, 0x3b, 0x1a, 0xd8, 0x85, 0x46, 0xc2, 0x25, 0x31,
	0x26, 0x5e, 0x34, 0xe5, 0x2a, 0xf7, 0x29, 0xd8, 0x5b, 0x52, 0x4a, 0x8c, 0x7b, 0xba, 0xa7, 0x77,
	0x33, 0x49, 0x3c, 0x3d, 0x1e, 0x3a, 0xd5, 0xec, 0x6e, 0x26, 0x7b, 0xa6, 0x7a, 0xd2, 0xb8, 0x5d,
	0x34, 0xad, 0x6f, 0x8b, 0xa6, 0xf5, 0x63, 0xd1, 0xb4, 0x3e, 0xfd, 0x6c, 0xae, 0xfd, 0x0e, 0x00,
	0x00, 0xff, 0xff, 0xc5, 0xef, 0x7c, 0x63, 0x7d, 0x05, 0x00, 0x00,
}
