syntax = "proto3";
package ga.api.smash_egg_notify;



import "smash_egg_notify/smash-egg-notify-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/smash_egg_notify;smash_egg_notify";

service SmashEggNotifyLogic {
    option (ga.api.extension.logic_service_name) = "smash-egg-notify-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/smash_egg_notify_logic.SmashEggNotifyLogic/";
    rpc GetNotify(ga.smash_egg_notify.GetNotifyReq) returns (ga.smash_egg_notify.GetNotifyResp) {
        option (ga.api.extension.command) = {
             id: 30037
        };
    }
}

