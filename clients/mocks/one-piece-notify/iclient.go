// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/one-piece-notify/iclient.go

// Package one_piece_notify is a generated GoMock package.
package one_piece_notify

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	one_piece_notify "golang.52tt.com/protocol/services/one-piece-notify"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCurrentNotify mocks base method.
func (m *MockIClient) GetCurrentNotify(ctx context.Context, opUid uint32, req *one_piece_notify.GetCurrentNotifyReq) (*one_piece_notify.GetCurrentNotifyResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentNotify", ctx, opUid, req)
	ret0, _ := ret[0].(*one_piece_notify.GetCurrentNotifyResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCurrentNotify indicates an expected call of GetCurrentNotify.
func (mr *MockIClientMockRecorder) GetCurrentNotify(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentNotify", reflect.TypeOf((*MockIClient)(nil).GetCurrentNotify), ctx, opUid, req)
}

// GetNotifyInfo mocks base method.
func (m *MockIClient) GetNotifyInfo(ctx context.Context, opUid uint32, req *one_piece_notify.GetNotifyInfoReq) (*one_piece_notify.GetNotifyInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotifyInfo", ctx, opUid, req)
	ret0, _ := ret[0].(*one_piece_notify.GetNotifyInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetNotifyInfo indicates an expected call of GetNotifyInfo.
func (mr *MockIClientMockRecorder) GetNotifyInfo(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotifyInfo", reflect.TypeOf((*MockIClient)(nil).GetNotifyInfo), ctx, opUid, req)
}

// SetNotifyInfo mocks base method.
func (m *MockIClient) SetNotifyInfo(ctx context.Context, opUid uint32, req *one_piece_notify.SetNotifyInfoReq) (*one_piece_notify.SetNotifyInfoResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNotifyInfo", ctx, opUid, req)
	ret0, _ := ret[0].(*one_piece_notify.SetNotifyInfoResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SetNotifyInfo indicates an expected call of SetNotifyInfo.
func (mr *MockIClientMockRecorder) SetNotifyInfo(ctx, opUid, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNotifyInfo", reflect.TypeOf((*MockIClient)(nil).SetNotifyInfo), ctx, opUid, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
