package event

import (
    "context"
    "golang.52tt.com/services/tt-rev/offer-room-common/mic"
    "time"

    "gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/protocol/app/channel"
    channel_scheme_conf_mgr "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"
    "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkasimplemic"
)

func (u *processor) TtSimpleMicEvForGameHold(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {

    event, err := parseSimpleMicEvent(msg)
    if err != nil {
        log.Errorf("[拍卖位嘉宾上麦监听] unmarshal 数据出错: %v，msg: %v", err, msg)
        return err, false
    }

    log.DebugWithCtx(ctx, "[拍卖位嘉宾上麦监听] cid: %d, uid: %d, 收到上下麦事件: %+v", event.GetChId(), event.GetMicUserId(), event)

    // 判断是否是拍卖位
    if event.GetMicTargetId() != mic.OfferMicId && event.GetMicOriginId() != mic.OfferMicId {
        log.DebugWithCtx(ctx, "[拍卖位嘉宾上麦监听] 不是拍卖位，不处理，cid: %d, target_mic_id: %d, origin_mic_id: %d", event.GetChId(), event.GetMicTargetId(), event.GetMicOriginId())
        return nil, false
    }

    // 判断房间类型
    if channel.ChannelType(event.GetChannelType()) != channel.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE {
        log.DebugWithCtx(ctx, "[拍卖位嘉宾上麦监听] 不是拍卖位房，不处理，cid: %d, channel_type: %s", event.GetChId(), channel.ChannelType(event.GetChannelType()))
        return nil, false
    }

    // 判断房间当前的玩法
    timeoutCtx, cancelFunc := context.WithTimeout(context.Background(), time.Second*2)
    defer cancelFunc()
    info, serverError := u.channelSchemeCli.GetCurChannelSchemeInfo(timeoutCtx, event.GetChId(), event.GetChannelType())
    if err != nil {
        log.Errorf("[拍卖位嘉宾上麦监听] 获取房间信息失败: %v，cid: %d", serverError, event.GetChId())
        return serverError, false
    }
    if channel_scheme_conf_mgr.SchemeSvrDetailType(info.GetSchemeInfo().GetSchemeSvrDetailType()) !=
        channel_scheme_conf_mgr.SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_OFFER_CHANNEL {
        log.DebugWithCtx(ctx, "[拍卖位嘉宾上麦监听] 不是拍卖位房间，不处理，cid: %d", event.GetChId())
        return nil, false
    }

    // 判断是否是上麦
    if mic.IsHoldMicEvent(event, mic.OfferMicId) {
        return u.handleHoldMicEvent(timeoutCtx, event)
    } else {
        log.ErrorWithCtx(timeoutCtx, "[拍卖位嘉宾上麦监听] 不是上麦，不处理，event: %+v", event)
        return nil, false
    }
}

// handleHoldMicEvent 处理上麦事件
func (u *processor) handleHoldMicEvent(ctx context.Context, event *kafkasimplemic.SimpleMicEvent) (error, bool) {
    // 初始化游戏
    err := u.gameMgr.InitNewGame(ctx, event.GetChId(), event.GetMicUserId())
    if err != nil {
        log.Errorf("[拍卖位嘉宾上麦监听] 初始化游戏失败: %v，cid: %d，uid: %d", err, event.GetChId(), event.GetMicUserId())
    }

    // 埋点上报嘉宾上麦时在队伍的位置
    u.appliedListMgr.ReportHoldMicByLink(event.GetChId(), event.GetMicUserId())

    // 把嘉宾从报名列表中移除
    _, err = u.appliedListMgr.UserCancelApply(ctx, event.GetChId(), event.GetMicUserId())
    if err != nil {
        log.ErrorWithCtx(ctx, "[拍卖位嘉宾上麦监听] 用户取消报名失败: %v，cid: %d，uid: %d", err, event.GetChId(), event.GetMicUserId())
    }

    // 推送最新的列表
    err = u.appliedListMgr.PushLatestList(ctx, event.GetChId())
    if err != nil {
        log.ErrorWithCtx(ctx, "[拍卖位嘉宾上麦监听] 推送最新的列表失败: %v，cid: %d，uid: %d", err, event.GetChId(), event.GetMicUserId())
    }
    return nil, false
}
