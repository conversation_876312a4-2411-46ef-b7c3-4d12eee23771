package report

import (
    "github.com/jarcoal/httpmock"
    "net/http"
    "testing"
)

func TestAlarmSender_SendError(t *testing.T) {
    httpmock.Activate()

    env := "dev"
    url := "http://abc"
    method := "POST"
    response, err := httpmock.NewJsonResponder(http.StatusOK, nil)
    if err != nil {
        panic(err)
    }
    httpmock.RegisterResponder(method, url, response)

    type fields struct {
        Env string
        Url string
    }
    type args struct {
        text string
    }
    tests := []struct {
        name   string
        fields fields
        args   args
    }{
        // Add test cases.
        {
            name:   "TestAlarmSender_SendError",
            fields: fields{
                Env: env,
                Url: url,
            },
            args:   args{
                text: "1",
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            sender := &FeishuReporterV2{
                env: tt.fields.Env,
                url: tt.fields.Url,
            }
            sender.SendError(tt.args.text)
        })
    }
}

func TestAlarmSender_SendInfo(t *testing.T) {
    httpmock.Activate()

    env := "dev"
    url := "http://abc"
    method := "POST"
    response, err := httpmock.NewJsonResponder(http.StatusOK, nil)
    if err != nil {
        panic(err)
    }
    httpmock.RegisterResponder(method, url, response)

    type fields struct {
        Env string
        Url string
    }
    type args struct {
        text string
    }
    tests := []struct {
        name   string
        fields fields
        args   args
    }{
        // Add test cases.
        {
            name:   "TestAlarmSender_SendInfo",
            fields: fields{
                Env: env,
                Url: url,
            },
            args:   args{
                text: "1",
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            sender := &FeishuReporterV2{
                env: tt.fields.Env,
                url: tt.fields.Url,
            }
            sender.SendInfo(tt.args.text)
        })
    }
}

func TestAlarmSender_SendWarning(t *testing.T) {
    httpmock.Activate()

    env := "dev"
    url := "http://abc"
    method := "POST"
    response, err := httpmock.NewJsonResponder(http.StatusOK, nil)
    if err != nil {
        panic(err)
    }
    httpmock.RegisterResponder(method, url, response)

    type fields struct {
        Env string
        Url string
    }
    type args struct {
        text string
    }
    tests := []struct {
        name   string
        fields fields
        args   args
    }{
        // Add test cases.
        {
            name:   "TestAlarmSender_SendWarning",
            fields: fields{
                Env: env,
                Url: url,
            },
            args:   args{
                text: "1",
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            sender := &FeishuReporterV2{
                env: tt.fields.Env,
                url: tt.fields.Url,
            }
            sender.SendWarning(tt.args.text)
        })
    }
}

func TestAlarmSender_sendFeishu(t *testing.T) {
    httpmock.Activate()

    env := "dev"
    url := "http://abc"
    method := "POST"
    response, err := httpmock.NewJsonResponder(http.StatusOK, nil)
    if err != nil {
        panic(err)
    }
    httpmock.RegisterResponder(method, url, response)

    type fields struct {
        Env string
        Url string
    }
    type args struct {
        text string
    }
    tests := []struct {
        name    string
        fields  fields
        args    args
        wantErr bool
    }{
        // Add test cases.
        {
            name:    "TestAlarmSender_sendFeishu",
            fields:  fields{
                Env: env,
                Url: url,
            },
            args:    args{
                text: "abc",
            },
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            sender := &FeishuReporterV2{
                env: tt.fields.Env,
                url: tt.fields.Url,
            }
            if err := sender.send(tt.args.text); (err != nil) != tt.wantErr {
                t.Errorf("sendFeishu() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}

