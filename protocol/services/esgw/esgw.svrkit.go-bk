package esgw

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/esgw/esgw.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for ESGw service
const ESGwMagic = uint16(14955)

// Client API for ESGw service

type ESGwClientInterface interface {
	Search(ctx context.Context, uin uint32, in *SearchReq, opts ...svrkit.CallOption) (*SearchResp, error)
	Create(ctx context.Context, uin uint32, in *CreateReq, opts ...svrkit.CallOption) (*CreateResp, error)
	Delete(ctx context.Context, uin uint32, in *DeleteReq, opts ...svrkit.CallOption) (*DeleteResp, error)
	Index(ctx context.Context, uin uint32, in *IndexReq, opts ...svrkit.CallOption) (*IndexResp, error)
	Count(ctx context.Context, uin uint32, in *CountReq, opts ...svrkit.CallOption) (*CountResp, error)
	GetById(ctx context.Context, uin uint32, in *GetByIdReq, opts ...svrkit.CallOption) (*GetByIdResp, error)
	Bulk(ctx context.Context, uin uint32, in *BulkReq, opts ...svrkit.CallOption) (*BulkResp, error)
	DeleteByQuery(ctx context.Context, uin uint32, in *DeleteByQueryReq, opts ...svrkit.CallOption) (*DeleteByQueryResp, error)
}

type ESGwClient struct {
	cc *svrkit.ClientConn
}

func NewESGwClient(cc *svrkit.ClientConn) ESGwClientInterface {
	return &ESGwClient{cc}
}

const (
	commandESGwGetSelfSvnInfo = 9995
	commandESGwEcho           = 9999
	commandESGwSearch         = 1
	commandESGwCreate         = 2
	commandESGwDelete         = 3
	commandESGwIndex          = 4
	commandESGwCount          = 5
	commandESGwGetById        = 6
	commandESGwBulk           = 7
	commandESGwDeleteByQuery  = 8
)

func (c *ESGwClient) Search(ctx context.Context, uin uint32, in *SearchReq, opts ...svrkit.CallOption) (*SearchResp, error) {
	out := new(SearchResp)
	err := c.cc.Invoke(ctx, uin, commandESGwSearch, "/esgw.ESGw/Search", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) Create(ctx context.Context, uin uint32, in *CreateReq, opts ...svrkit.CallOption) (*CreateResp, error) {
	out := new(CreateResp)
	err := c.cc.Invoke(ctx, uin, commandESGwCreate, "/esgw.ESGw/Create", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) Delete(ctx context.Context, uin uint32, in *DeleteReq, opts ...svrkit.CallOption) (*DeleteResp, error) {
	out := new(DeleteResp)
	err := c.cc.Invoke(ctx, uin, commandESGwDelete, "/esgw.ESGw/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) Index(ctx context.Context, uin uint32, in *IndexReq, opts ...svrkit.CallOption) (*IndexResp, error) {
	out := new(IndexResp)
	err := c.cc.Invoke(ctx, uin, commandESGwIndex, "/esgw.ESGw/Index", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) Count(ctx context.Context, uin uint32, in *CountReq, opts ...svrkit.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, uin, commandESGwCount, "/esgw.ESGw/Count", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) GetById(ctx context.Context, uin uint32, in *GetByIdReq, opts ...svrkit.CallOption) (*GetByIdResp, error) {
	out := new(GetByIdResp)
	err := c.cc.Invoke(ctx, uin, commandESGwGetById, "/esgw.ESGw/GetById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) Bulk(ctx context.Context, uin uint32, in *BulkReq, opts ...svrkit.CallOption) (*BulkResp, error) {
	out := new(BulkResp)
	err := c.cc.Invoke(ctx, uin, commandESGwBulk, "/esgw.ESGw/Bulk", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ESGwClient) DeleteByQuery(ctx context.Context, uin uint32, in *DeleteByQueryReq, opts ...svrkit.CallOption) (*DeleteByQueryResp, error) {
	out := new(DeleteByQueryResp)
	err := c.cc.Invoke(ctx, uin, commandESGwDeleteByQuery, "/esgw.ESGw/DeleteByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
