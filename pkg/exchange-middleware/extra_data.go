package exchange_middleware

type BackpackSenderExtraData struct {
    BusinessId         uint32 `json:"business_id"`
    BackpackSenderSign string `json:"backpack_sender_sign"`
    SourceAppId        string `json:"source_app_id"`
}

type TBeanTransferExtraData struct {
    AppId string `json:"app_id"`
    From  uint32 `json:"from"`
    Desc  string `json:"desc"`
}

type ScoreExtraData struct {
    Reason string `json:"reason"`
}

type UseFragmentExtraData struct {
    UseReason      uint32 `json:"reason"`
    RollbackReason uint32 `json:"rollback_reason"`
}

type TBeanPayExtraData struct {
    AppId    string `json:"app_id"`
    Reason   string `json:"reason"`
    ItemName string `json:"item_name"`
    Platform string `json:"platform"`
    Notes    string `json:"notes"`
}