syntax="proto3";

package unified_pay;

service UnifiedPay {
    rpc Freeze( FreezeReq ) returns( FreezeResp ) {}
    rpc Confirm( ConfirmReq ) returns( ConfirmResp ) {}

    rpc DirectPay( DirectPayReq ) returns( DirectPayResp ) {}
    rpc DirectTrans( DirectTransReq ) returns( DirectTransResp ) {}

    // T豆冻结接口 V2 支持长时间冻结
    rpc PresetFreeze( PresetFreezeReq ) returns( PresetFreezeResp ) {}
    // T豆解冻并回退接口
    rpc UnFreezeAndRefund( UnFreezeAndRefundReq ) returns( UnFreezeAndRefundResp ) {}
    // T豆解冻并消费
    rpc UnfreezeAndConsume( UnfreezeAndConsumeReq ) returns( UnfreezeAndConsumeResp ) {}
}

enum FeeType {
    UNKNOWN = 0;
    RED_DIAMOND = 1;
    TBEAN = 2;
}

message ClientInfo {
    string  client_ip = 1;
    bytes   client_device_id = 2;
    string  client_device_info = 3;
    uint32  terminal_type = 4;
}

enum PayOrderStatus {
    INIT = 0;
    FREEZED = 1;
    COMMITTED = 2;
    ROLLBACK = 3;

    ERROR = 99;
}

message Callback {
    enum Type {
        UNDEFINED = 0;
        GRPC = 1;
    }

    // Type, only GRPC is supported by now.
    Type    type = 25;

    // A url which should be registered to name server
    // e.g.: pay_callback.userpresent.52tt.local
    string  url = 26;

    // Duration after pay order is created (in seconds).
    // default: 5 minutes
    // max: 1 hour
    uint32  first_time = 27;
}

message PayOrder {
    string          app_id = 1;
    string          out_trade_no = 2;
    uint32          uid = 3;
    string          user_name = 4;
    FeeType         fee_type = 5;
    uint32          total_fee = 6;
    string          body = 7;
    string          detail = 8;
    uint32          create_at = 9;
    PayOrderStatus  status = 10;
    bool            confirmed = 11;

    string item_id = 12;

    ClientInfo      client_info = 20;
    Callback        callback = 21;
}

message FreezeReq {
    PayOrder pay_order = 1;
}

message FreezeResp {
    string pay_order_id = 1;
    int64 balance = 2;
    string tbean_order_time =3;
    string deal_token = 4; //链路检查
}

message ConfirmReq {
    enum OP {
        COMMIT = 0;
        ROLLBACK = 1;
    }
    string app_id = 1;
    string pay_order_id = 2;
    string out_trade_no = 3;
    OP op = 4;
    string oper = 5;
    bool synchronized = 6;  // 是否同步处理, 默认为异步
}

message ConfirmResp {

}

message DirectPayReq {
    PayOrder pay_order = 1;
}

message DirectPayResp {
    int64 balance = 1;
}

//
message TransOrder {
    string          app_id = 1;
    string          out_trade_no = 2;
    uint32          from_uid = 3;
    string          from_user_name = 4;
    uint32          target_uid = 5;
    string          target_user_name = 6;
    FeeType         fee_type = 7;
    uint32          total_fee = 8;
    uint32          create_at = 9;

    PayOrderStatus  status = 10; // 转帐交易的状态 默认为0

    ClientInfo      client_info = 20;
    Callback        callback = 21;
}

message DirectTransReq {
    TransOrder trans_order = 1;
}

message DirectTransResp {
}

// PresetFreezeReq
message PresetFreezeReq {
    uint32          uid = 1;            // UID
    uint32          freeze_balance = 2; // 冻结T豆数
    string          out_trade_no = 3;    // 外部唯一订单号，不能为空
    string          freeze_reason = 4;  // 申请冻结说明
    string          out_trade_time = 5;  // 发起冻结T豆时间 (yyyy-MM-dd hh:mm:ss)
}

message PresetFreezeResp {
    uint32          code = 1;            //1成功  其他错误
    string          message = 2;         //错误信息
}

message UnFreezeAndRefundReq{
     uint32          uid = 1;            // UID
     uint32          freeze_balance = 2; // 冻结T豆数
     string          out_trade_no = 3;    // 外部唯一订单号，不能为空
}

message UnFreezeAndRefundResp{
    int32           code = 1;            // 1成功  其他错误
    string          message = 2;         //错误信息
}

message UnfreezeAndConsumeReq{
    uint32          uid = 1;            // UID
    uint32          user_name = 2;
    uint32          item_id = 3;
    string          item_name = 4;				//名称
    uint32          item_num = 5;
    uint32          item_price = 6;
    uint32          total_price = 7;
    string          platform = 8;
    string          out_trade_no = 9;
    string          notes = 10;
}

message UnfreezeAndConsumeResp{
    uint32          code = 1;            //1成功  其他错误
    string          message = 2;         //错误信息
}