syntax="proto2";

package channel.async;

// 麦位信息
message MicrSpace
{
	required uint32 mic_id = 1;                       // 麦位ID 1 - 9
	optional uint32 mic_state = 2;                    // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	optional uint32 mic_uid = 3;                      // 麦上用户 如果麦上有人的话
}

// 异步任务
message Channel2UserRelation
{
	required uint32 channel_id = 1;
	repeated uint32 uid_list = 2;         // 正常UID列表
	optional uint32 appid = 3;
	
	optional uint32 channel_type = 4;     // 房间类型
	optional uint32 remain_member = 5;    // 房间剩余人数 仅在房间类型为个人房和公会公开房时 该字段有效
	optional uint32 remain_admin_cnt = 6; // 房间剩余管理员人数 仅在房间类型为个人房和公会公开房时 该字段有效
	
	repeated uint32 admin_uid_list = 7;   // uid_list 中是管理员的那些人
	repeated uint32 member_online_second_list = 8; // 对应uid_list中每个人在线时间
}

message ChannelAsyncJobUserExpireNotify
{
	required uint32 type = 1;
	repeated Channel2UserRelation channel2uid_list = 2;
}

message ChannelAsyncJobChannelDissmissNotify
{
	required uint32 type = 1; // 
	required uint32 opuid = 2; // 
	required uint32 channelID = 3; // 
	repeated uint32 uid_list = 4;
	optional uint32 appid = 5;
	optional uint32 channel_type = 6; // 房间类型
}

message ChannelAsyncJobUserFaultDetectNotify
{
	required uint32 faulttype = 1; // 
	repeated Channel2UserRelation channel2uid_list = 2;
}

message ChannelAsyncJobCommonTask
{
	required uint32 task_type = 1;
	required bytes task_bin_msg = 2;
}

message ChannelAsyncJobChannelCreateNotify
{
	required uint32 uid = 1;
	required uint32 chid = 2;
	optional uint32 appid = 3;
}

// 房间有人进入
message ChannelAsyncJobChannelUserEnterNotify
{
	required uint32 uid = 1;
	required uint32 chid = 2;
	optional uint32 appid = 3;

	optional uint32 channel_type = 4;     // 房间类型
	optional uint32 remain_member = 5;    // 房间剩余人数             仅在房间类型为个人房和公会公开房时 该字段有效
	optional uint32 remain_admin_cnt = 6; // 房间剩余管理员人数 仅在房间类型为个人房和公会公开房时 该字段有效
	optional bool is_admin = 7;           // 进房间的这个人是否是管理员
	
}

// 房间有人退出
message ChannelAsyncJobChannelUserQuitNotify
{
	required uint32 uid = 1;
	required uint32 chid = 2;
	optional uint32 appid = 3;
	
	optional uint32 channel_type = 4;     // 房间类型
	optional uint32 remain_member = 5;    // 房间剩余人数             仅在房间类型为个人房和公会公开房时 该字段有效
	optional uint32 remain_admin_cnt = 6; // 房间剩余管理员人数 仅在房间类型为个人房和公会公开房时 该字段有效
	optional bool is_admin = 7;           // 退房间的这个人是否是管理员
	optional uint32 online_second = 8;    // 退出房间时 本次该用户在房间的在线时长
}

// 房间有人下麦
message ChannelAsyncJobChannelUserOffMicNotify
{
	required uint32 uid = 1;
	required uint32 chid = 2;
	optional uint32 appid = 3;
	
	optional uint32 mic_id = 4;
	optional uint32 mic_mod = 5;
    optional uint32 at = 6;
}

// 房间成员列表的排名有变化
message ChannelAsyncJobMemberRankChangeNotify
{
	required uint32 chid = 1;
	optional uint32 appid = 2;
	repeated uint32 notify_uid_list = 3;
}

// 指定的麦位状态发生了变化
message ChannelAsyncJobMicStatChangeNotify
{
	required uint32 chid = 1;
	optional uint32 appid = 2;
	
	optional uint32 uid = 3;
	optional uint32 mic_id = 4;
	optional uint32 new_state  = 5;
	
	repeated MicrSpace all_mic_list = 6;          // 全体麦位信息 包括各个麦位状态
	optional uint32 micr_mode = 7;                // 麦模式
	optional uint64 server_time_ms  = 8;          // 64bit 毫秒级 服务器时间
}

// 直播连麦申请超时事件
message ChannelAsyncJobLiveApplyExpireNotify
{
	required uint32 channel_id =1;
	required uint32 remain_apply_cnt =2;
	repeated uint32 expire_uid_list =3;
	optional uint64 server_time_ms  = 4;          // 64bit 毫秒级 服务器时间
}

// 直播结束事件
message ChannelAsyncJobLiveFinishedNotify
{
	required uint32 channel_id =1;
	required uint32 uid =2;
	
	repeated MicrSpace kick_mic_list = 3;         // 结束直播 导致的下麦列表
	repeated MicrSpace all_mic_list = 4;          // 全体麦位信息 包括各个麦位状态
	optional uint32 micr_mode = 5;                // 麦模式
	optional uint64 server_time_ms  = 6;          // 64bit 毫秒级 服务器时间
}
