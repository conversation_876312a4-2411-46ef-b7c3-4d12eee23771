# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2051:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2051 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetGfitHistory
2071:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2071 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetRecommend
2073:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2073 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetHotList
2074:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2074 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/channelGetShowSwitch
2075:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2075 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetTagList
2076:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2076 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetCardList
2077:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2077 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetChannelByTagId
2078:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2078 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelSetTagId
2079:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2079 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetTagId
2080:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2080 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelRefreshTime
2081:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2081 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetAdvList
2082:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2082 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/GetChannelRefreshCD
2083:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2083 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetHomeDetail
2086:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2086 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetGameMatchOptions
2087:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2087 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetGameMatchListHomePage
2088:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2088 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetGameMatchListByTagId
2089:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2089 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelStartGameMatch
2520:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2520 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetUserDecorationList
2521:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2521 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelActivateUserDecoration
2720:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 2720 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelGetNoviceRecommendStatus
3502:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 3502 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/CChannelGetMicroUserGameTagService
458:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 458 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/channelConsumeTopN
620:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 620 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/GetCharmAndRichLableInfo
621:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 621 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelWeekConsumeTopN
622:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 622 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelHourRankTopN
623:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 623 --source api/channel_ext/grpc_channel_ext.proto --lang cpp --method /ga.api.channel_ext.ChannelExtLogic/ChannelHourRankById
