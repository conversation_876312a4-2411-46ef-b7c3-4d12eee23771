// Code generated by protoc-gen-go-quicksilver. DO NOT EDIT.

package revenue_api_go

import (
	client "golang.52tt.com/pkg/client"
	client1 "golang.52tt.com/pkg/foundation/grpc/client"
	protocol "golang.52tt.com/pkg/protocol"
	revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

const (
	serviceName = "revenue-api-go"
)

// Client is the wrapper-client for RevenueApiGo client.
type Client struct {
	client.BaseClient
}

// newClient creates a new wrapper-Client.
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return revenue_api_go.NewRevenueApiGoClient(cc)
			},
			dopts...,
		),
	}, nil
}

// typedStub returns the stub of RevenueApiGoClient.
func (c *Client) typedStub() revenue_api_go.RevenueApiGoClient {
	return c.Stub().(revenue_api_go.RevenueApiGoClient)
}

// NewClient creates a new wrapper-Client.
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(client1.UnaryClientInterceptor))
	return newClient(dopts...)
}

// NewClientOrg creates a new Client with wrapper-client instance.
func NewClientOrg(dopts ...grpc.DialOption) (revenue_api_go.RevenueApiGoClient, error) {
	c, err := NewClient(dopts...)
	if err != nil {
		return nil, err
	}
	return c, nil
}

// GetRevenueMicInfo
func (c *Client) GetRevenueMicInfo(ctx context.Context, req *revenue_api_go.GetRevenueMicInfoReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueMicInfoResp, error) {
	resp, err := c.typedStub().GetRevenueMicInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

// BatchGetRevenueMicInfo
func (c *Client) BatchGetRevenueMicInfo(ctx context.Context, req *revenue_api_go.BatchGetRevenueMicInfoReq, opts ...grpc.CallOption) (*revenue_api_go.BatchGetRevenueMicInfoResp, error) {
	resp, err := c.typedStub().BatchGetRevenueMicInfo(ctx, req, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRevenueEnterChannelInfo(ctx context.Context, in *revenue_api_go.GetRevenueEnterChannelInfoReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueEnterChannelInfoResp, error) {
	resp, err := c.typedStub().GetRevenueEnterChannelInfo(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRevenueUserVisitorRecord(ctx context.Context, in *revenue_api_go.GetRevenueUserVisitorRecordReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueUserVisitorRecordResp, error) {
	resp, err := c.typedStub().GetRevenueUserVisitorRecord(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetRevenueAwardInfosByType(ctx context.Context, in *revenue_api_go.GetRevenueAwardInfosByTypeReq, opts ...grpc.CallOption) (*revenue_api_go.GetRevenueAwardInfosByTypeResp, error) {
	resp, err := c.typedStub().GetRevenueAwardInfosByType(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetRevenueAwardInfosByIds(ctx context.Context, in *revenue_api_go.BatchGetRevenueAwardInfosByIdsReq, opts ...grpc.CallOption) (*revenue_api_go.BatchGetRevenueAwardInfosByIdsResp, error) {
	resp, err := c.typedStub().BatchGetRevenueAwardInfosByIds(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}

func (c *Client) CheckCanModifySex(ctx context.Context, in *revenue_api_go.CheckCanModifySexReq, opts ...grpc.CallOption) (*revenue_api_go.CheckCanModifySexResp, error) {
	resp, err := c.typedStub().CheckCanModifySex(ctx, in, opts...)
	return resp, protocol.ToServerError(err)
}
