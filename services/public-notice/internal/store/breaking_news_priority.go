package store

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	public_notice "golang.52tt.com/protocol/services/public-notice"
	"google.golang.org/grpc/codes"
	"strings"
	"time"
)

type BreakingNewsPriorityRecord struct {
	Id           uint32    `db:"id"`            // 主键
	NewsID       uint32    `db:"news_id"`       // 公告ID
	Rank         float32   `db:"news_rank"`     // 排序
	NewsName     string    `db:"news_name"`     // 公告名称
	AnimeZip     string    `db:"anime_zip"`     // 动画zip
	AnimeType    uint32    `db:"anime_type"`    // 动画类型
	AnimePreview string    `db:"anime_preview"` // 动画预览
	BeginTime    time.Time `db:"begin_time"`    // 开始时间
	EndTime      time.Time `db:"end_time"`      // 结束时间
	CreateTime   time.Time `db:"create_time"`   // 创建时间
	UpdateTime   time.Time `db:"update_time"`   // 更新时间
	IsPermanent  bool      `db:"is_permanent"`  // 是否永久置顶
	IsDeleted    uint32    `db:"is_deleted"`    // 是否删除
}

type BreakingNewsSimpleItem struct {
	RecordId    uint32    `db:"id"`      // 主键
	NewsID      uint32    `db:"news_id"` // 公告ID
	BeginTime   time.Time `db:"begin_time"`
	EndTime     time.Time `db:"end_time"`
	IsPermanent bool      `db:"is_permanent"`
}

const (
	// BreakingNewsPriorityRecordTableName 表名
	breakingNewsPriorityRecordTableName = "breaking_news_priority_record"

	// 建表语句
	_ = `
CREATE TABLE IF NOT EXISTS breaking_news_priority_record (
 id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
 news_id INT UNSIGNED NOT NULL COMMENT '公告ID',
 news_rank DECIMAL(10, 3) NOT NULL DEFAULT 0 COMMENT '排序',
 begin_time datetime NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '开始时间',
 end_time datetime NOT NULL DEFAULT '2038-01-19 03:14:00' COMMENT '结束时间',
 create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 is_permanent TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否永久置顶',
 is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
 INDEX idx_begin_time (begin_time),
 INDEX idx_end_time (end_time),
 INDEX idx_news_id (news_id),
 INDEX idx_update_time (update_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告优先级记录表';
`
)

func (st *Store) AddBreakingNewsPriorityRecord(ctx context.Context, newId uint32, rank float32, beginTime, endTime int64) error {
	if rank == 0 {
		log.ErrorWithCtx(ctx, "AddBreakingNewsPriorityRecord failed, rank is 0")
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服优先级不能为0")
	}
	var sql string
	if beginTime == 0 && endTime == 0 {
		sql = fmt.Sprintf("INSERT INTO %s (news_id, news_rank, is_permanent) VALUES (?, ?, 1)", breakingNewsPriorityRecordTableName)
		_, err := st.ExecContext(ctx, sql, newId, rank)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddBreakingNewsPriorityRecord failed, err:%v", err)
			return err
		}
	} else {
		beginTimeStr := time.Unix(beginTime, 0).Format("2006-01-02 15:04:05")
		endTimeStr := time.Unix(endTime, 0).Format("2006-01-02 15:04:05")
		sql = fmt.Sprintf("INSERT INTO %s (news_id, news_rank, begin_time, end_time) VALUES (?, ?, ?, ?)", breakingNewsPriorityRecordTableName)
		_, err := st.ExecContext(ctx, sql, newId, rank, beginTimeStr, endTimeStr)
		if err != nil {
			log.ErrorWithCtx(ctx, "AddBreakingNewsPriorityRecord failed, err:%v", err)
			return err
		}
	}
	log.InfoWithCtx(ctx, "AddBreakingNewsPriorityRecord success, newsId:%d, rank:%f, beginTime:%d, endTime:%d", newId, rank, beginTime, endTime)
	return nil
}

func (st *Store) UpdateBreakingNewsPriorityRecord(ctx context.Context, recordId uint32, rank float32, beginTime, endTime int64) error {
	if rank == 0 {
		log.ErrorWithCtx(ctx, "UpdateBreakingNewsPriorityRecord failed, rank is 0")
		return protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服优先级不能为0")
	}
	var sql string
	if beginTime == 0 && endTime == 0 {
		sql = fmt.Sprintf("UPDATE %s SET news_rank = ?, is_permanent = 1, begin_time = '1970-01-01 08:00:00', end_time = '2038-01-19 03:14:00' WHERE id = ?", breakingNewsPriorityRecordTableName)
		_, err := st.ExecContext(ctx, sql, rank, recordId)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateBreakingNewsPriorityRecord failed, err:%v", err)
			return err
		}
	} else {
		sql = fmt.Sprintf("UPDATE %s SET news_rank = ?, begin_time = ?, end_time = ?, is_permanent = 0 WHERE id = ?", breakingNewsPriorityRecordTableName)
		beginTimeStr := time.Unix(beginTime, 0).Format("2006-01-02 15:04:05")
		endTimeStr := time.Unix(endTime, 0).Format("2006-01-02 15:04:05")
		_, err := st.ExecContext(ctx, sql, rank, beginTimeStr, endTimeStr, recordId)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateBreakingNewsPriorityRecord failed, err:%v", err)
			return err
		}
	}
	log.InfoWithCtx(ctx, "UpdateBreakingNewsPriorityRecord success, recordId:%d, rank:%f, beginTime:%d, endTime:%d", recordId, rank, beginTime, endTime)
	return nil
}

func (st *Store) DeleteBreakingNewsPriorityRecord(ctx context.Context, recordId uint32) error {
	sql := fmt.Sprintf("UPDATE %s SET is_deleted = 1 WHERE id = ?", breakingNewsPriorityRecordTableName)
	_, err := st.ExecContext(ctx, sql, recordId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteBreakingNewsPriorityRecord failed, err:%v", err)
		return err
	}
	log.InfoWithCtx(ctx, "DeleteBreakingNewsPriorityRecord success, recordId:%d", recordId)
	return nil
}

func (st *Store) GetAllStickBreakingNews(ctx context.Context) (*public_notice.GetAllStickBreakingNewsResp, error) {
	sql := fmt.Sprintf(`SELECT stickTbl.id as id,
        stickTbl.news_id as news_id,
        stickTbl.news_rank as news_rank,
		newTbl.news_name as news_name,
		newTbl.anime_zip as anime_zip,
		newTbl.anime_type as anime_type,
		newTbl.anime_preview as anime_preview,
        stickTbl.begin_time as begin_time,
        stickTbl.end_time as end_time,
        stickTbl.create_time as create_time,
        stickTbl.update_time as update_time,
        stickTbl.is_permanent as is_permanent
FROM %s AS stickTbl INNER JOIN %s AS newTbl ON stickTbl.news_id = newTbl.id WHERE stickTbl.is_deleted = 0 ORDER BY stickTbl.update_time DESC`,
		breakingNewsPriorityRecordTableName, "breaking_news_config")
	var records []*BreakingNewsPriorityRecord
	resp := &public_notice.GetAllStickBreakingNewsResp{}
	err := st.SelectContext(ctx, &records, sql)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllStickBreakingNews failed, err:%v", err)
		return resp, err
	}
	now := time.Now()
	for _, record := range records {
		item := &public_notice.StickBreakingNews{
			RecordId:     record.Id,
			NewsId:       record.NewsID,
			Rank:         fmt.Sprintf("%.2f", record.Rank),
			BeginTime:    record.BeginTime.Unix(),
			EndTime:      record.EndTime.Unix(),
			IsPermanent:  record.IsPermanent,
			NewsName:     record.NewsName,
			AnimePreview: record.AnimePreview,
			AnimeType:    record.AnimeType,
		}
		// https://ga-album-cdnqn.52tt.com/channel/publicNotice/notice_qfgg20240520.zip -> notice_qfgg20240520
		if record.AnimeZip != "" {
			item.AnimeZip = record.AnimeZip
			item.AnimeName = strings.TrimSuffix(strings.TrimPrefix(record.AnimeZip, "https://ga-album-cdnqn.52tt.com/channel/publicNotice/"), ".zip")
		}
		if record.IsPermanent == true {
			item.Status = public_notice.StickStatus_STICK_STATUS_EFFECTIVE
			item.IsPermanent = true
		} else {
			if record.BeginTime.Before(now) && record.EndTime.After(now) {
				item.Status = public_notice.StickStatus_STICK_STATUS_EFFECTIVE
			} else if record.BeginTime.After(now) {
				item.Status = public_notice.StickStatus_STICK_STATUS_WAITING
			} else {
				item.Status = public_notice.StickStatus_STICK_STATUS_EXPIRED
			}
		}
		resp.StickBreakingNewsList = append(resp.StickBreakingNewsList, item)

	}
	return resp, nil
}

func (st *Store) GetStickRecordsByNewsId(ctx context.Context, newsId uint32) ([]*BreakingNewsSimpleItem, error) {
	ts := time.Now()
	sql := fmt.Sprintf(`SELECT id, news_id, begin_time, end_time, is_permanent FROM %s WHERE news_id = ? and end_time > ? AND is_deleted = 0`,
		breakingNewsPriorityRecordTableName)
	var records []*BreakingNewsSimpleItem
	err := st.SelectContext(ctx, &records, sql, newsId, ts)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetStickRecordsByNewsId failed, err:%v", err)
		return nil, err
	}
	return records, nil
}

func (st *Store) GetAvailableStickRecord(ctx context.Context, newsId uint32) (float32, error) {
	ts := time.Now()
	var rank float32
	sql := fmt.Sprintf(`SELECT news_rank
FROM %s WHERE news_id = ? and begin_time < ? and end_time > ? AND is_deleted = 0
union
SELECT news_rank
FROM %s WHERE news_id = ? and is_permanent = 1 AND is_deleted = 0 limit 1`,
		breakingNewsPriorityRecordTableName, breakingNewsPriorityRecordTableName)
	err := st.GetContext(ctx, &rank, sql, newsId, ts, ts, newsId)
	if err != nil {
		// no rows in result set
		if strings.Contains(err.Error(), "no rows in result set") {
			log.DebugWithCtx(ctx, "GetAvailableStickRecord failed, newsId:%d not found", newsId)
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetAvailableStickRecord failed, err:%v, newsId:%d", err, newsId)
		return 0, err
	}
	return rank, nil
}

func (st *Store) GetStickRecordByRecordId(ctx context.Context, recordId uint32) (*BreakingNewsSimpleItem, error) {
	sql := fmt.Sprintf(`SELECT id, news_id, begin_time, end_time, is_permanent FROM %s WHERE id = ? AND is_deleted = 0`,
		breakingNewsPriorityRecordTableName)
	var record BreakingNewsSimpleItem
	err := st.GetContext(ctx, &record, sql, recordId)
	if err != nil {
		// no rows in result set
		if strings.Contains(err.Error(), "no rows in result set") {
			log.ErrorWithCtx(ctx, "GetStickRecordByRecordId failed, recordId:%d not found", recordId)
			return nil, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid, "全服公告优先级记录Id不存在")
		}
		log.ErrorWithCtx(ctx, "GetStickRecordByRecordId failed, err:%v", err)
		return nil, err
	}
	return &record, nil
}
