package metrics

import (
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

var (
	clientConnectionsGauge = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "logic_grpc_gateway_client_connections",
		Help: "Number of client connections",
	}, []string{"proxy_ip"})

	requestsPendingCounter = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "logic_grpc_gateway_pending_req_total",
		Help: "Number of pending req by `logic-grpc-gateway`.",
	}, []string{"destination_service_name", "request_path"})

	requestsCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "logic_grpc_gateway_requests_total",
		Help: "Total requests handled by `logic-grpc-gateway`.",
	}, []string{"cmd_id", "cmd_name", "proxy_ip", "response_code", "destination_service", "destination_service_name"})

	requestDurationHistogram = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name: "logic_grpc_gateway_request_duration",
		Help: "Duration in seconds that `logic-grpc-gateway` handles requests",
	}, []string{"cmd", "cmd_name", "proxy_ip", "response_code", "destination_service", "destination_service_name"})
)

func init() {
	prometheus.MustRegister(clientConnectionsGauge, requestsCounter, requestDurationHistogram, requestsPendingCounter)
}

// IncClientConnections -
func IncClientConnections(proxyIP string) {
	clientConnectionsGauge.WithLabelValues(proxyIP).Inc()
}

// DecClientConnections -
func DecClientConnections(proxyIP string) {
	clientConnectionsGauge.WithLabelValues(proxyIP).Dec()
}

func IncSvcRequestPath(svc, path string) {
	requestsPendingCounter.WithLabelValues(svc, path).Inc()
}

func DecSvcRequestPath(svc, path string) {
	requestsPendingCounter.WithLabelValues(svc, path).Dec()
}

// ObserveRequestDuration -
func ObserveRequestDuration(cmd int, cmdName, proxyIP string, responseCode int, destionationService, destionationServiceName string, dur time.Duration) {
	labels := []string{strconv.Itoa(cmd), cmdName, proxyIP, strconv.Itoa(responseCode), destionationService, destionationServiceName}
	requestsCounter.WithLabelValues(labels...).Add(1)
	requestDurationHistogram.WithLabelValues(labels...).Observe(float64(dur) / float64(time.Second))
}
