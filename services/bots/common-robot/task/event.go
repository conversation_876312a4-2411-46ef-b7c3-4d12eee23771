package task

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"golang.52tt.com/services/bots/common-robot/storage/mongo/model"

	"golang.52tt.com/protocol/services/commonrobot"

	"golang.52tt.com/pkg/log"

	"github.com/larksuite/oapi-sdk-go/core"
	"github.com/larksuite/oapi-sdk-go/core/tools"
	eventhttpserver "github.com/larksuite/oapi-sdk-go/event/http/native"
	im "github.com/larksuite/oapi-sdk-go/service/im/v1"
	globalconf "golang.52tt.com/services/bots/common-robot/conf"
)

const (
	helpDesc = `使用帮助
[1] whoami: 查看用户userId和openId
[2] convert <八进制>: 将八进制转化成utf-8，例如 \\346\\236\\234\\345\\255\\220 -> 果子`
	nextSN = 3 // 下一个序号
)

func (s *TaskService) handleMsgEvent() {
	im.SetMessageReceiveEventHandler(globalconf.DefaultConf, func(ctx *core.Context, event *im.MessageReceiveEvent) error {
		// 打印请求的Request ID，方便 oncall 排查问题
		fmt.Println(ctx.GetRequestID())
		// 打印事件
		fmt.Println(tools.Prettify(event))

		content := make(map[string]string)
		err := json.Unmarshal([]byte(event.Event.Message.Content), &content)
		if err != nil {
			log.Errorf("json.Unmarshal err:%v", err)
			return err
		}
		if text, ok := content["text"]; ok {
			text = strings.ReplaceAll(text, "@_user_1", "")
			params := strings.Fields(strings.TrimSpace(text))
			if len(params) > 0 {
				cmd := params[0]
				params = params[1:]
				userId := event.Event.Sender.SenderId.UserId
				openId := event.Event.Sender.SenderId.OpenId
				msgId := event.Event.Message.MessageId
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
				defer cancel()

				switch cmd {
				case "whoami":
					err = s.handleWhoAmICmd(ctx, msgId, userId, openId)
					if err != nil {
						log.Errorf("handleWhoAmICmd err:%v", err)
						return err
					}
				case "convert":
					err = s.handleConvertCmd(ctx, msgId, params)
					if err != nil {
						log.Errorf("handleConvertCmd err:%v", err)
						return err
					}
				case "help":
					err = s.handleHelpCmd(ctx, msgId, userId, openId)
					if err != nil {
						log.Errorf("handleHelpCmd err:%v", err)
						return err
					}
				default:
					err = s.handleDynamicCmd(ctx, msgId, cmd, params, userId, openId)
					if err != nil {
						log.Errorf("handleDynamicCmd err:%v", err)
						return err
					}
				}
			}
		}
		return nil
	})

	// 设置 "开发者后台" -> "事件订阅" 请求网址 URL：https://domain/webhook/event
	eventhttpserver.Register("/lark_robot/webhook/event", globalconf.DefaultConf)
	err := http.ListenAndServe(":8089", nil)
	if err != nil {
		panic(err)
	}
}

func (s *TaskService) handleWhoAmICmd(ctx context.Context, msgId, userId, openId string) error {
	text := fmt.Sprintf("userId:%s\nopenId:%s", userId, openId)
	err := s.robotService.ReplyMsg(ctx, msgId, text)
	if err != nil {
		return err
	}
	return nil
}

func (s *TaskService) handleConvertCmd(ctx context.Context, msgId string, params []string) error {
	var text string
	for _, p := range params {
		text += fmt.Sprintf("%s -> %s\n", p, convertOctonaryToUtf8(p))
	}
	err := s.robotService.ReplyMsg(ctx, msgId, text)
	if err != nil {
		return err
	}
	return nil
}

func (s *TaskService) handleHelpCmd(ctx context.Context, msgId, userId, openId string) error {
	tasks := make([]*model.Task, 0)
	for _, task := range s.getTaskIdToTask() {
		if !task.GetEnable() || task.GetCommand() == nil {
			continue
		}
		noPermission := true
		if len(task.GetCommand().GetValidUserIds()) == 0 && len(task.GetCommand().GetValidOpenIds()) == 0 {
			noPermission = false
		} else {
			for _, validOpenId := range task.GetCommand().GetValidOpenIds() {
				if openId == validOpenId {
					noPermission = false
					break
				}
			}
			if noPermission {
				for _, validUserId := range task.GetCommand().GetValidUserIds() {
					if userId == validUserId {
						noPermission = false
						break
					}
				}
			}
		}
		if noPermission {
			continue
		}

		tasks = append(tasks, task)
	}
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].GetCommand().GetCmd() < tasks[j].GetCommand().GetCmd()
	})

	text := helpDesc
	for i, task := range tasks {
		text = text + fmt.Sprintf("\n[%d] %s", i+nextSN, task.GetCommand().GetHelp())
	}

	err := s.robotService.ReplyMsg(ctx, msgId, text)
	if err != nil {
		log.Errorf("ReplyMsg err:%v", err)
		return err
	}
	return nil
}

func (s *TaskService) handleDynamicCmd(ctx context.Context, msgId string, cmd string, params []string, userId, openId string) error {
	isHandle := false
	for _, task := range s.getTaskIdToTask() {
		if !task.GetEnable() || task.GetCommand() == nil || cmd != task.GetCommand().GetCmd() {
			continue
		}
		noPermission := true
		if len(task.GetCommand().GetValidUserIds()) == 0 && len(task.GetCommand().GetValidOpenIds()) == 0 {
			noPermission = false
		} else {
			for _, validOpenId := range task.GetCommand().GetValidOpenIds() {
				if openId == validOpenId {
					noPermission = false
					break
				}
			}
			if noPermission {
				for _, validUserId := range task.GetCommand().GetValidUserIds() {
					if userId == validUserId {
						noPermission = false
						break
					}
				}
			}
		}
		if noPermission {
			continue
		}

		client, ok := s.clientMap[task.GetService()]
		if ok {
			in, out := new(commonrobot.HelloReq), new(commonrobot.HelloResp)
			in.Params = strings.Join(params, " ")
			err := client.cc().Invoke(ctx, task.GetMethod(), in, out)
			if err != nil {
				log.Errorf("Invoke err:%v", err)
				return err
			}

			text := fmt.Sprintf(warningMsgTemplate, s.env, task.GetDesc(), out.GetMsg())
			err = s.robotService.ReplyMsg(ctx, msgId, text)
			if err != nil {
				log.Errorf("ReplyMsg err:%v", err)
				return err
			}
		} else {
			err := s.robotService.ReplyMsg(ctx, msgId, "client not found!!")
			if err != nil {
				log.Errorf("ReplyMsg err:%v", err)
				return err
			}
		}
		isHandle = true
		break
	}
	if !isHandle {
		err := s.robotService.ReplyMsg(ctx, msgId, "无效的命令，请回复help以获取更多帮助信息！")
		if err != nil {
			log.Errorf("ReplyMsg err:%v", err)
			return err
		}
	}
	return nil
}

func convertOctonaryToUtf8(in string) string {
	s := []byte(in)
	reg := regexp.MustCompile(`\\\\[0-7]{3}`)

	out := reg.ReplaceAllFunc(s,
		func(b []byte) []byte {
			i, _ := strconv.ParseInt(string(b[2:]), 8, 0)
			return []byte{byte(i)}
		})
	return string(out)
}
