package main

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/core/log"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
    "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server" // server startup

    pb "golang.52tt.com/protocol/services/channel-wedding-minigame"

    "golang.52tt.com/services/channel-wedding-minigame/internal"

    _ "golang.52tt.com/pkg/hub/tyr/compatible/server"
    "golang.52tt.com/services/channel-wedding-minigame/internal/conf"
    unified_pay_callback "golang.52tt.com/protocol/services/unified_pay/cb"
)

func main() {
    var (
        svr *internal.Server
        cfg = &conf.StartConfig{}
        err error
    )

    // config file support yaml & json, default channel-wedding-minigame.json/yaml
    if err := server.NewServer("channel-wedding-minigame", cfg).
        AddGrpcServer(server.NewBuildOption().
            WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
                if svr, err = internal.NewServer(ctx, cfg); err != nil {
                    return err
                }

                // register custom grpc server
                pb.RegisterChannelWeddingMinigameServer(s, svr)

                unified_pay_callback.RegisterPayCallbackServer(s, svr)
                // 注册旧路径（其实是新的兼容路径）
                unified_pay_callback.RegisterPayCallbackServerCompat(s, svr)
                return nil
            }),
        ).
        WithCloseFunc(func(ctx context.Context) {
            // do something when server terminating
            svr.ShutDown()
        }).
        Start(); err != nil {
        log.Errorf("server start fail, err: %v", err)
    }
}
