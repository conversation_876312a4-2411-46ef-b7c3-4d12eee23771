package manager

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/pgc-channel/pgc-glory-title/internal/model"
	"time"
)

// CheckInvertorRankReconcile 检查金主榜值是否一致
func (mgr *PgcGloryTitleManager) CheckInvertorRankReconcile(ctx context.Context, uid, cid, anchorUid uint32, now time.Time, isNeedFix bool) (uint64, uint64, error) {
	sumPrice, err := mgr.store.GetSumPriceByMonth(ctx, uid, anchorUid, cid, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckInvertorRankReconcile GetSumPriceByTimeRange uid:%d, cid:%d, anchorUid:%d, err:%v", uid, cid, anchorUid, err)
		return 0, 0, err
	}
	if sumPrice == 0 {
		return sumPrice, 0, nil
	}
	rankVal, err := mgr.rc.GetInvertorRankVal(ctx, uid, cid, anchorUid, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckInvertorRankReconcile GetInvertorRankVal uid:%d, cid:%d, anchorUid:%d, err:%v", uid, cid, anchorUid, err)
		return sumPrice, rankVal, err
	}
	if sumPrice > rankVal {
		log.ErrorWithCtx(ctx, "CheckInvertorRankReconcile failed: now:%v, sumPrice:%d > rankVal:%d, uid:%d, cid:%d, anchorUid:%d", now, sumPrice, rankVal, uid, cid, anchorUid)
		if isNeedFix { // 修正榜值
			err = mgr.rc.FixInvertorRankVal(ctx, uid, cid, anchorUid, now.Unix(), sumPrice)
			if err != nil {
				log.ErrorWithCtx(ctx, "CheckInvertorRankReconcile FixInvertorRankVal uid:%d, cid:%d, anchorUid:%d, err:%v", uid, cid, anchorUid, err)
				return sumPrice, rankVal, err
			}
			_ = mgr.CalcAnchorRankBaseInfo(ctx, cid, anchorUid, now.Unix(), 0)
		}
		return sumPrice, rankVal, nil
	}
	return sumPrice, rankVal, nil
}

// CheckAnchorRankReconcile 检查成员榜值是否一致
func (mgr *PgcGloryTitleManager) CheckAnchorRankReconcile(ctx context.Context, anchorUid, cid uint32, now time.Time, isNeedFix bool) (uint64, uint64, error) {
	beginTs := mgr.dyConfig.GetCurWeekEndTs(now.Unix()) + 1 - 7*24*3600
	sumPrice, err := mgr.store.GetSumPriceByTimeRange(ctx, anchorUid, cid, beginTs, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorRankReconcile GetSumPriceByTimeRange anchorUid:%d, cid:%d, err:%v", anchorUid, cid, err)
		return 0, 0, err
	}
	if sumPrice == 0 {
		return sumPrice, 0, nil
	}
	weekNo := mgr.dyConfig.GetWeekNo(now.Unix())
	rankVal, err := mgr.rc.GetAnchorRankVal(ctx, anchorUid, cid, weekNo)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorRankReconcile GetAnchorRankVal anchorUid:%d, cid:%d, err:%v", anchorUid, cid, err)
		return sumPrice, rankVal, err
	}
	if sumPrice > rankVal {
		log.ErrorWithCtx(ctx, "CheckAnchorRankReconcile failed: now:%v, sumPrice:%d > rankVal:%d, anchorUid:%d, cid:%d", now, sumPrice, rankVal, anchorUid, cid)
		if isNeedFix {
			err = mgr.rc.FixAnchorRankVal(ctx, anchorUid, cid, weekNo, sumPrice)
			if err != nil {
				log.ErrorWithCtx(ctx, "CheckAnchorRankReconcile FixAnchorRankVal anchorUid:%d, cid:%d, err:%v", anchorUid, cid, err)
				return sumPrice, rankVal, err
			}
		}
		return sumPrice, rankVal, nil
	}

	return sumPrice, rankVal, nil
}

// TimerCheckRankReconcile 定时检查榜值是否一致
func (mgr *PgcGloryTitleManager) TimerCheckRankReconcile() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()
	now := time.Now().Add(-time.Minute * 5)
	defer func() {
		log.InfoWithCtx(ctx, "TimerCheckRankReconcile done, ts:%v cost:%s", now, time.Since(now))
	}()
	lastCheckTs, err := mgr.rc.GetRankLastCheckTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerCheckRankReconcile GetRankLastCheckTime err=%v", err)
		return
	}
	if lastCheckTs == 0 {
		lastCheckTs = now.Add(-time.Hour * 24).Unix()
	}
	checkList, err := mgr.store.GetCheckOrdersByTimeRange(ctx, lastCheckTs, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerCheckRankReconcile GetCheckOrdersByTimeRange lastCheckTs:%d, now:%d, err=%v", lastCheckTs, now.Unix(), err)
		return
	}
	isNeedFix := mgr.dyConfig.GetCheckRankIsNeedFix()
	checkUidCidAnchors := make(map[string]*model.CheckOrder)
	checkAnchorCids := make(map[string]*model.CheckOrder)
	for _, order := range checkList {
		key := fmt.Sprintf("%d_%d_%d", order.ChannelId, order.FromeUid, order.ToUid)
		checkUidCidAnchors[key] = order
		key = fmt.Sprintf("%d_%d", order.ChannelId, order.ToUid)
		checkAnchorCids[key] = order
	}
	for _, order := range checkUidCidAnchors {
		_, _, err = mgr.CheckInvertorRankReconcile(ctx, order.FromeUid, order.ChannelId, order.ToUid, now, isNeedFix)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerCheckRankReconcile CheckInvertorRankReconcile err=%v, order:%+v", err, order)
			return
		}
	}

	for _, order := range checkAnchorCids {
		_, _, err = mgr.CheckAnchorRankReconcile(ctx, order.ToUid, order.ChannelId, now, isNeedFix)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerCheckRankReconcile CheckAnchorRankReconcile err=%v, order:%+v", err, order)
			return
		}
	}

	err = mgr.rc.SetRankLastCheckTime(ctx, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerCheckRankReconcile SetRankLastCheckTime err=%v", err)
		return
	}
}
