syntax = "proto3";

package ga.glory_world;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/glory_world;glory_world";

// NoticeType 红点提醒类型
enum NoticeType {
  NOTICE_TYPE_UNSPECIFIED = 0; // 错误类型
  NOTICE_TYPE_CHANNEL = 1; // 房间右下角更多入口红点
  NOTICE_TYPE_HEADER = 2; // 荣耀世界我的头像
  NOTICE_TYPE_CHALLENGE = 3; // 荣耀世界可领取
}

// GloryFragmentType 星钻类型
enum GloryFragmentType {
  GLORY_FRAGMENT_TYPE_UNSPECIFIED = 0; // 无效类型
  GLORY_FRAGMENT_TYPE_FAME = 1; // 声望星钻
  GLORY_FRAGMENT_TYPE_GLORY = 2; // 荣耀星钻
}

// GetGloryEnterInfoReq 获取荣耀世界入口信息
message GetGloryEnterInfoRequest {
  ga.BaseReq base_req = 1;
  NoticeType type = 2; // 红点提示位置
}
message GetGloryEnterInfoResponse {
  ga.BaseResp base_resp = 1;
  bool is_open = 2; // 是否开启入口
  string url = 3; // 跳转链接
  bool is_notice = 4; // 是否进行提醒
  bool need_first_notice = 5; // 需要进行首次点击红点提醒
  string first_notice_version = 6; // 首次提醒配置版本
}

// ReceiveRewardReq 领取任务奖励
message ReceiveRewardRequest {
  ga.BaseReq base_req = 1;
  repeated uint32 task_id = 2; // 领取任务id列表
}
message ReceiveRewardResponse {
  ga.BaseResp base_resp = 1;
}

// GloryRewardChannelTaskMsg 星钻获取任务提示房间推送
message GloryRewardChannelTaskMsg {
  uint32 uid = 1; // Uid
  GloryFragmentType type = 2; // 星钻类型
  string url = 3; // 弹窗的URL
  string md5 = 4; // 资源md5
  string special_effect_url = 5; // 特效URL
  string special_effect_md5 = 6; // 特效MD5
  string banner = 7; // 弹窗标题资源
  uint32 num = 8; // 获取的星钻数量
  uint32 display_time = 9; // 展示时间
  repeated uint32 task_list = 10; // 完成的任务id列表
  string notice = 11; // 公屏展示文案
  string enter_url = 12; // 公屏展示跳转链接
  string shine_url = 13; // 特效闪烁资源
  string circle_light_url = 14; // 特效旋转资源
}

// 荣耀世界-名流殿堂IM消息
message GloryWorldHallOfFameImOpt {
  string content = 1; // 文案 "和你分享我的荣耀"
  string url = 2; // 跳转短链
  string title = 3; // 称号文案 "成就者"
  uint32 rank = 4; // 排名 No.26的26
  string image_url = 5; // 背景图片 "https://obs-cdn.52tt.com/tt/fe-moss/web/20230801101545_23641350.png"
  string account = 6; // 用户头像
  string nickname = 7; // 用户昵称
}

// GetFloatingLayerInfoRequest 获取浮层信息
message GetFloatingLayerInfoRequest {
  ga.BaseReq base_req = 1;
}
message GetFloatingLayerInfoResponse {
  ga.BaseResp base_resp = 1;
  bool is_notice = 2; // 是否进行提醒
  string notice_content = 3; // 浮层内容
  uint32 notice_show_time = 4; // 浮层显示时间
}