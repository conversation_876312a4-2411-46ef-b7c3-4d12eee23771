// nolint:golint
package esport

import (
    "context"
    "golang.52tt.com/clients/account"
    esport_role "golang.52tt.com/clients/esport-role"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    "golang.52tt.com/pkg/protocol/grpc"
    tyr_http "gitlab.ttyuyin.com/avengers/tyr/core/service/http"
    "golang.52tt.com/protocol/services/esport-skill"
    esport_role_pb "golang.52tt.com/protocol/services/esport_role"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/common"
    "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/conf"
    esport_http "golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/esport"
    "strconv"
)

type ReportMgr struct {
    ESportSkillCli *esport_skill.Client
    ESportRoleCli  esport_role.IClient
    AccountCli     account.IClient
}

func NewReportMgr(RoleCli esport_role.IClient, SkillCli *esport_skill.Client, AccountCli account.IClient) *ReportMgr {
    return &ReportMgr{
        ESportSkillCli: SkillCli,
        ESportRoleCli:  RoleCli,
        AccountCli:     AccountCli,
    }
}

//nolint:all
func (r *ReportMgr) Register(cfg *conf.ServiceConfig, router *tyr_http.Router) {
    child := router.Child("/esport/report")
    child.POST("/check_can_report", common.HandleWrapper(cfg,
        func() interface{} {
            return &esport_http.CheckCanReportCoachRequest{}
        }, func(ctx context.Context, req interface{}) (interface{}, error) {
            return r.CheckCanReportCoach(ctx, req.(*esport_http.CheckCanReportCoachRequest))
        })) //nolint:all
}

// CheckCanReportCoach 检查是否可以举报该陪玩
// 电竞指导总开关打开 && 对方是陪玩身份才可以举报
func (r *ReportMgr) CheckCanReportCoach(c context.Context, req *esport_http.CheckCanReportCoachRequest) (*esport_http.CheckCanReportCoachResponse, error) {
    out := &esport_http.CheckCanReportCoachResponse{}
    log.Debugf("CheckCanReportCoach req:%+v", req)

    serviceInfo, _ := grpc.ServiceInfoFromContext(c)
    serviceInfo.DeviceID = []byte(req.GetDeviceId())

    version, _ := strconv.Atoi(req.GetVersion())
    serviceInfo.ClientVersion = uint32(version)

    marketId, _ := strconv.Atoi(req.GetMarketId())
    serviceInfo.MarketID = uint32(marketId)

    platform, _ := strconv.Atoi(req.GetPlatform())
    os, _ := strconv.Atoi(req.GetOsType())
    app, _ := strconv.Atoi(req.GetApp())
    serviceInfo.TerminalType = protocol.PackTerminalType(protocol.Platform(platform), protocol.OS(os), protocol.AppID(app))

    switch protocol.OS(os) {
    case protocol.ANDROID:
        serviceInfo.ClientType = protocol.ClientTypeANDROID
    case protocol.IOS:
        serviceInfo.ClientType = protocol.ClientTypeIOS
    case protocol.WINDOWS:
        serviceInfo.ClientType = protocol.ClientTypePcTT
    }

    ctx := grpc.WithServiceInfo(c, serviceInfo)

    targetUid := req.GetTargetUid()
    if targetUid == 0 {
        var err error
        targetUid, _, err = r.AccountCli.GetUidByName(ctx, req.GetTargetAccount())
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckCanReportCoach fail to GetUidByName req:%+v, err:%+v", req, err)
            return out, err
        }
    }

    if targetUid == 0 {
        return out, nil
    }

    switchResp, err := r.ESportSkillCli.GetSwitch(ctx, &esport_skill.GetSwitchRequest{
        Uid: targetUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckCanReportCoach fail to GetSwitch req:%+v, err:%+v", req, err)
        return out, err
    }

    if switchResp.GetSwitchStatus().GetMainSwitchStatus() != esport_skill.EsportSwitchStatus_SWITCH_STATUS_ON {
        out.CanReport = false
        return out, nil
    }

    roleResp, err := r.ESportRoleCli.GetUserESportRole(ctx, &esport_role_pb.GetUserESportRoleReq{
        Uid: targetUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckCanReportCoach fail to GetUserESportRole req:%+v, err:%+v", req, err)
        return out, err
    }

    out.CanReport = roleResp.GetEsportRole() != uint32(esport_role_pb.ESportErType_ESPORT_TYPE_UNSPECIFIED)
    return out, nil
}
