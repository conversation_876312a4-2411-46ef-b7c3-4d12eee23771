// Code generated by protoc-gen-go. DO NOT EDIT.
// source: apicenter-go/topic-channel-tab-api.proto

package apicentergo // import "golang.52tt.com/protocol/services/apicentergo"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MinorityGameInfo struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameIcon             string   `protobuf:"bytes,2,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	GameScore            uint32   `protobuf:"varint,3,opt,name=game_score,json=gameScore,proto3" json:"game_score,omitempty"`
	GameName             string   `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	TabId                uint32   `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MinorityGameInfo) Reset()         { *m = MinorityGameInfo{} }
func (m *MinorityGameInfo) String() string { return proto.CompactTextString(m) }
func (*MinorityGameInfo) ProtoMessage()    {}
func (*MinorityGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{0}
}
func (m *MinorityGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MinorityGameInfo.Unmarshal(m, b)
}
func (m *MinorityGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MinorityGameInfo.Marshal(b, m, deterministic)
}
func (dst *MinorityGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MinorityGameInfo.Merge(dst, src)
}
func (m *MinorityGameInfo) XXX_Size() int {
	return xxx_messageInfo_MinorityGameInfo.Size(m)
}
func (m *MinorityGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MinorityGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MinorityGameInfo proto.InternalMessageInfo

func (m *MinorityGameInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *MinorityGameInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *MinorityGameInfo) GetGameScore() uint32 {
	if m != nil {
		return m.GameScore
	}
	return 0
}

func (m *MinorityGameInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *MinorityGameInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetMinorityGameReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMinorityGameReq) Reset()         { *m = GetMinorityGameReq{} }
func (m *GetMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*GetMinorityGameReq) ProtoMessage()    {}
func (*GetMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{1}
}
func (m *GetMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinorityGameReq.Unmarshal(m, b)
}
func (m *GetMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *GetMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinorityGameReq.Merge(dst, src)
}
func (m *GetMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_GetMinorityGameReq.Size(m)
}
func (m *GetMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinorityGameReq proto.InternalMessageInfo

type GetMinorityGameResp struct {
	MinorityGameList     []*MinorityGameInfo `protobuf:"bytes,1,rep,name=minority_game_list,json=minorityGameList,proto3" json:"minority_game_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetMinorityGameResp) Reset()         { *m = GetMinorityGameResp{} }
func (m *GetMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*GetMinorityGameResp) ProtoMessage()    {}
func (*GetMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{2}
}
func (m *GetMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinorityGameResp.Unmarshal(m, b)
}
func (m *GetMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *GetMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinorityGameResp.Merge(dst, src)
}
func (m *GetMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_GetMinorityGameResp.Size(m)
}
func (m *GetMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinorityGameResp proto.InternalMessageInfo

func (m *GetMinorityGameResp) GetMinorityGameList() []*MinorityGameInfo {
	if m != nil {
		return m.MinorityGameList
	}
	return nil
}

type AddMinorityGameReq struct {
	MinorityGameInfos    []*MinorityGameInfo `protobuf:"bytes,1,rep,name=minority_game_infos,json=minorityGameInfos,proto3" json:"minority_game_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddMinorityGameReq) Reset()         { *m = AddMinorityGameReq{} }
func (m *AddMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*AddMinorityGameReq) ProtoMessage()    {}
func (*AddMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{3}
}
func (m *AddMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMinorityGameReq.Unmarshal(m, b)
}
func (m *AddMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *AddMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMinorityGameReq.Merge(dst, src)
}
func (m *AddMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_AddMinorityGameReq.Size(m)
}
func (m *AddMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddMinorityGameReq proto.InternalMessageInfo

func (m *AddMinorityGameReq) GetMinorityGameInfos() []*MinorityGameInfo {
	if m != nil {
		return m.MinorityGameInfos
	}
	return nil
}

type AddMinorityGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddMinorityGameResp) Reset()         { *m = AddMinorityGameResp{} }
func (m *AddMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*AddMinorityGameResp) ProtoMessage()    {}
func (*AddMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{4}
}
func (m *AddMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddMinorityGameResp.Unmarshal(m, b)
}
func (m *AddMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *AddMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddMinorityGameResp.Merge(dst, src)
}
func (m *AddMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_AddMinorityGameResp.Size(m)
}
func (m *AddMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddMinorityGameResp proto.InternalMessageInfo

type RemoveMinorityGameReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveMinorityGameReq) Reset()         { *m = RemoveMinorityGameReq{} }
func (m *RemoveMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*RemoveMinorityGameReq) ProtoMessage()    {}
func (*RemoveMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{5}
}
func (m *RemoveMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveMinorityGameReq.Unmarshal(m, b)
}
func (m *RemoveMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *RemoveMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveMinorityGameReq.Merge(dst, src)
}
func (m *RemoveMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_RemoveMinorityGameReq.Size(m)
}
func (m *RemoveMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveMinorityGameReq proto.InternalMessageInfo

func (m *RemoveMinorityGameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

type RemoveMinorityGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveMinorityGameResp) Reset()         { *m = RemoveMinorityGameResp{} }
func (m *RemoveMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*RemoveMinorityGameResp) ProtoMessage()    {}
func (*RemoveMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{6}
}
func (m *RemoveMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveMinorityGameResp.Unmarshal(m, b)
}
func (m *RemoveMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *RemoveMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveMinorityGameResp.Merge(dst, src)
}
func (m *RemoveMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_RemoveMinorityGameResp.Size(m)
}
func (m *RemoveMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveMinorityGameResp proto.InternalMessageInfo

type ChangeMinorityGameReq struct {
	UGameId              uint32   `protobuf:"varint,1,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	GameIcon             string   `protobuf:"bytes,2,opt,name=game_icon,json=gameIcon,proto3" json:"game_icon,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMinorityGameReq) Reset()         { *m = ChangeMinorityGameReq{} }
func (m *ChangeMinorityGameReq) String() string { return proto.CompactTextString(m) }
func (*ChangeMinorityGameReq) ProtoMessage()    {}
func (*ChangeMinorityGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{7}
}
func (m *ChangeMinorityGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMinorityGameReq.Unmarshal(m, b)
}
func (m *ChangeMinorityGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMinorityGameReq.Marshal(b, m, deterministic)
}
func (dst *ChangeMinorityGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMinorityGameReq.Merge(dst, src)
}
func (m *ChangeMinorityGameReq) XXX_Size() int {
	return xxx_messageInfo_ChangeMinorityGameReq.Size(m)
}
func (m *ChangeMinorityGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMinorityGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMinorityGameReq proto.InternalMessageInfo

func (m *ChangeMinorityGameReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *ChangeMinorityGameReq) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

func (m *ChangeMinorityGameReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ChangeMinorityGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeMinorityGameResp) Reset()         { *m = ChangeMinorityGameResp{} }
func (m *ChangeMinorityGameResp) String() string { return proto.CompactTextString(m) }
func (*ChangeMinorityGameResp) ProtoMessage()    {}
func (*ChangeMinorityGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4, []int{8}
}
func (m *ChangeMinorityGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeMinorityGameResp.Unmarshal(m, b)
}
func (m *ChangeMinorityGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeMinorityGameResp.Marshal(b, m, deterministic)
}
func (dst *ChangeMinorityGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeMinorityGameResp.Merge(dst, src)
}
func (m *ChangeMinorityGameResp) XXX_Size() int {
	return xxx_messageInfo_ChangeMinorityGameResp.Size(m)
}
func (m *ChangeMinorityGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeMinorityGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeMinorityGameResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MinorityGameInfo)(nil), "apicentergo.MinorityGameInfo")
	proto.RegisterType((*GetMinorityGameReq)(nil), "apicentergo.GetMinorityGameReq")
	proto.RegisterType((*GetMinorityGameResp)(nil), "apicentergo.GetMinorityGameResp")
	proto.RegisterType((*AddMinorityGameReq)(nil), "apicentergo.AddMinorityGameReq")
	proto.RegisterType((*AddMinorityGameResp)(nil), "apicentergo.AddMinorityGameResp")
	proto.RegisterType((*RemoveMinorityGameReq)(nil), "apicentergo.RemoveMinorityGameReq")
	proto.RegisterType((*RemoveMinorityGameResp)(nil), "apicentergo.RemoveMinorityGameResp")
	proto.RegisterType((*ChangeMinorityGameReq)(nil), "apicentergo.ChangeMinorityGameReq")
	proto.RegisterType((*ChangeMinorityGameResp)(nil), "apicentergo.ChangeMinorityGameResp")
}

func init() {
	proto.RegisterFile("apicenter-go/topic-channel-tab-api.proto", fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4)
}

var fileDescriptor_topic_channel_tab_api_afb2b371c8887fe4 = []byte{
	// 350 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0xcd, 0x4e, 0xc2, 0x40,
	0x14, 0x85, 0x53, 0x11, 0x94, 0x4b, 0x4c, 0x70, 0xb0, 0xa4, 0xd1, 0x90, 0x90, 0x59, 0x75, 0xd3,
	0x36, 0x81, 0xf8, 0x00, 0xea, 0x82, 0x10, 0xc5, 0x45, 0xdd, 0xb9, 0x21, 0xd3, 0xe9, 0x50, 0x26,
	0xe9, 0xfc, 0xd8, 0x19, 0x48, 0x7c, 0x19, 0x9f, 0xd5, 0x50, 0x0c, 0xd4, 0x42, 0x88, 0x6e, 0xcf,
	0xb9, 0xa7, 0xdf, 0xbd, 0x3d, 0x03, 0x3e, 0xd1, 0x9c, 0x32, 0x69, 0x59, 0x11, 0x64, 0x2a, 0xb2,
	0x4a, 0x73, 0x1a, 0xd0, 0x25, 0x91, 0x92, 0xe5, 0x81, 0x25, 0x49, 0x40, 0x34, 0x0f, 0x75, 0xa1,
	0xac, 0x42, 0x9d, 0xdd, 0x64, 0xa6, 0xf0, 0x97, 0x03, 0xdd, 0x19, 0x97, 0xaa, 0xe0, 0xf6, 0x73,
	0x42, 0x04, 0x9b, 0xca, 0x85, 0x42, 0xb7, 0xd0, 0x5e, 0xcd, 0x33, 0x22, 0xd8, 0x9c, 0xa7, 0x9e,
	0x33, 0x74, 0xfc, 0xab, 0xf8, 0x62, 0x55, 0xba, 0x29, 0xba, 0x83, 0xf6, 0xd6, 0xa1, 0x4a, 0x7a,
	0x67, 0x43, 0xc7, 0x6f, 0xc7, 0x97, 0x1b, 0x61, 0x4a, 0x95, 0x44, 0x03, 0x80, 0xd2, 0x34, 0x54,
	0x15, 0xcc, 0x6b, 0x94, 0xc9, 0x72, 0xfc, 0x6d, 0x23, 0xec, 0xb2, 0x92, 0x08, 0xe6, 0x9d, 0xef,
	0xb3, 0xaf, 0x44, 0x30, 0xe4, 0x42, 0xcb, 0x92, 0x64, 0x43, 0x6c, 0x96, 0xb9, 0xa6, 0x25, 0xc9,
	0x34, 0xc5, 0x37, 0x80, 0x26, 0xcc, 0x56, 0x57, 0x8c, 0xd9, 0x07, 0x4e, 0xa0, 0x77, 0xa0, 0x1a,
	0x8d, 0x9e, 0x01, 0x89, 0x1f, 0x6d, 0xbb, 0x7f, 0xce, 0x8d, 0xf5, 0x9c, 0x61, 0xc3, 0xef, 0x8c,
	0x06, 0x61, 0xe5, 0xee, 0xb0, 0x7e, 0x73, 0xdc, 0x15, 0x15, 0xe5, 0x85, 0x1b, 0x8b, 0x29, 0xa0,
	0x87, 0x34, 0xad, 0x91, 0xd1, 0x0c, 0x7a, 0xbf, 0x11, 0x5c, 0x2e, 0x94, 0xf9, 0x1b, 0xe3, 0x5a,
	0xd4, 0x14, 0x83, 0x5d, 0xe8, 0x1d, 0x40, 0x8c, 0xc6, 0x63, 0x70, 0x63, 0x26, 0xd4, 0x9a, 0xd5,
	0xf1, 0x27, 0xaa, 0xc1, 0x1e, 0xf4, 0x8f, 0x85, 0x8c, 0xc6, 0x19, 0xb8, 0x4f, 0x4b, 0x22, 0xb3,
	0xff, 0x7c, 0xee, 0x74, 0xd3, 0xfb, 0xb6, 0x1a, 0xd5, 0xb6, 0x3c, 0xe8, 0x1f, 0x03, 0x19, 0xfd,
	0x18, 0xbd, 0x07, 0x99, 0xca, 0x89, 0xcc, 0xc2, 0xfb, 0x91, 0xb5, 0x21, 0x55, 0x22, 0x2a, 0x9f,
	0x23, 0x55, 0x79, 0x64, 0x58, 0xb1, 0xe6, 0x94, 0x99, 0xa8, 0xf2, 0xf7, 0x92, 0x56, 0x69, 0x8f,
	0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xe5, 0xf6, 0xd0, 0x2d, 0xd9, 0x02, 0x00, 0x00,
}
