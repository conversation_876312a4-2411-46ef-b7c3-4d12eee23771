package mgr

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/tt-rev/present-week-card/internal/store"
	"time"
)

// 判断用户是否已购买周卡,并返回周卡id
func (m *Mgr) checkIfUserBought(ctx context.Context, uid uint32) ([]uint32, error) {
	cardIdList := make([]uint32, 0)
	// 查用户已购买的周卡信息
	info, err := m.GetUserCardInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "checkIfUserBought fail to GetUserCardInfo uid:%v, err: %v", uid, err)
		return cardIdList, err
	}

	for _, v := range info.CardList {
		if time.Now().Before(v.ExpireTime) {
			cardIdList = append(cardIdList, v.CardId)
			break
		}
	}

	return cardIdList, nil
}

// 向下取整5分的时间
func getRound10MinTime(t time.Time, cycleCnt int) time.Time {
	return t.Truncate(time.Duration(cycleCnt) * time.Minute)
}

// 计算cycleDuration
func getMinuteCycleDuration(beginTime, endTime time.Time, cycleCnt int) uint32 {
	// 计算时间差值
	duration := getRound10MinTime(endTime, cycleCnt).Sub(getRound10MinTime(beginTime, cycleCnt))
	minutes := int(duration.Minutes())

	log.DebugWithCtx(context.TODO(), "getMinuteCycleDuration beginTime:%v, endTime:%v minutes:%d", beginTime, endTime, minutes)
	return uint32(minutes / cycleCnt)
}

// 获取当天0点时间
func getZeroDayTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
}

func getDayDuration(beginTime, endTime time.Time) uint32 {

	// 计算时间差值
	duration := getZeroDayTime(endTime).Sub(getZeroDayTime(beginTime))
	day := int(duration.Hours() / 24)

	return uint32(day)
}

// 计算dayDuration
// 计算eventTime和buyTime 的dayDuration
// 计算lastLoginTime和buyTime 的dayDuration
// return eventDuration - lastLoginDuration
func getLastLoginDuration(useMinuteMode bool, buyTime, eventTime, lastLoginTime time.Time, cycleCnt int) uint32 {
	var eventDuration, lastLoginDuration uint32

	if useMinuteMode {
		// 测试模式
		eventDuration = getMinuteCycleDuration(buyTime, eventTime, cycleCnt)
		lastLoginDuration = getMinuteCycleDuration(buyTime, lastLoginTime, cycleCnt)
	} else {
		eventDuration = getDayDuration(buyTime, eventTime)
		lastLoginDuration = getDayDuration(buyTime, lastLoginTime)
	}

	log.InfoWithCtx(context.TODO(), "getLastLoginDuration eventDuration:%d, lastLoginDuration:%d", eventDuration, lastLoginDuration)
	return eventDuration - lastLoginDuration
}

/*
origin log_type: login
事件类型-login:  无补加
事件类型-logout: 有补加逻辑

origin log_type: logout
事件类型-login:  无补加
事件类型-logout: 无补加
*/
func eventCalculateAwardCnt(info *store.WeekCardUserLoginAwardCnt, eventTime, lastLoginTime time.Time, eventType uint32) uint32 {
	var duration uint32
	testMode := info.CycleCnt > 0
	duration = getLastLoginDuration(testMode, info.CreateTime, eventTime, lastLoginTime, int(info.CycleCnt))

	if info.LastLogType == store.LoginEventTypeLogin &&
		eventType == store.LoginEventTypeLogout {
		// 补加n个奖励
		return info.AwardCnt + duration

	} else {
		if duration > 0 {
			return info.AwardCnt + 1
		}
		return info.AwardCnt
	}
}

// HandleUserOnlineEvent 用户上线事件handle
func (m *Mgr) HandleUserOnlineEvent(ctx context.Context, uid uint32, eventTime time.Time, eventType uint32) error {

	cardIds, err := m.checkIfUserBought(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleUserOnlineEventLogin fail to checkIfUserBought uid:%v,eventType:%d err: %v", uid, eventType, err)
		return err
	}

	if len(cardIds) == 0 {
		log.DebugWithCtx(ctx, "HandleUserOnlineEventLogin Done len(cardIds) == 0 uid:%v,eventType:%d", uid, eventType)
		return nil
	}

	// 仅保留至秒级，防止其四舍五入
	eventTime = time.Unix(eventTime.Unix(), 0)

	infoList, err := m.store.GetUserLoginAwardCntRecord(ctx, uid, eventTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleUserOnlineEventLogin fail to GetUserLoginAwardCntRecordForUpdate uid:%v,eventType:%d err: %v", uid, eventType, err)
		return err
	}

	if len(infoList) == 0 {
		log.InfoWithCtx(ctx, "HandleUserOnlineEventLogin Done len(infoList) == 0 uid:%v,eventType:%d", uid, eventType)
		return nil
	}

	// 上锁
	ok, err := m.cache.LockOnlineEventUpdate(ctx, uid, 3*time.Second)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleUserOnlineEventLogin fail to LockOnlineEventUpdate uid:%v,eventType:%d err: %v", uid, eventType, err)
		return err
	}

	if !ok {
		log.WarnWithCtx(ctx, "HandleUserOnlineEventLogin LockOnlineEventUpdate fail,try again later. uid:%v,eventType:%d", uid, eventType)
		return fmt.Errorf("LockOnlineEventUpdate fail")
	}

	// 退出函数时解锁
	defer func() {
		_ = m.cache.UnlockOnlineEventUpdate(ctx, uid)
	}()

	for _, info := range infoList {
		// 更新最新上线时间，判断是否需要增加可领取数量
		info.AwardCnt = eventCalculateAwardCnt(info, eventTime, info.LatestLoginTime, eventType)
		info.LastLogType = eventType
		info.EventSource = store.EventSourceOnlineKfk
		info.LatestLoginTime = eventTime

		err = m.store.UpdateUserLoginAwardCntRecord(ctx, info)
		if err != nil {
			log.ErrorWithCtx(ctx, "HandleUserOnlineEventLogin fail to UpdateUserLoginAwardCntRecord uid:%v,eventType:%d err: %v", uid, eventType, err)
			return err
		}
	}

	// 清除缓存
	_ = m.cache.DelUserCardInfo(ctx, uid)

	log.InfoWithCtx(ctx, "HandleUserOnlineEventLogin Done uid:%v,eventType:%d", uid, eventType)
	return nil
}

// 获取指定时间所在月份的总天数
func getMonthMaxDay(t time.Time) uint32 {
	// 构建指定月份的第一天时间
	firstDay := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.Local)

	// 获取下个月第一天时间
	nextMonth := firstDay.AddDate(0, 1, 0)

	// 计算两个时间之间的天数差，即当前月份的天数
	days := nextMonth.Sub(firstDay).Hours() / 24

	return uint32(days)
}
