package medalgo

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/medalgo"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserMedalList(ctx context.Context, req pb.GetUserMedalListReq) (*pb.GetUserMedalListResp, protocol.ServerError)
	AwardMedalToUser(ctx context.Context, req pb.AwardMedalToUserReq) (*pb.AwardMedalToUserResp, protocol.ServerError)
	AwardMedalToUserEx(ctx context.Context, uid, medalID, exTime uint32, isExt bool) (*pb.AwardMedalToUserResp, error)
	ReduceUserMedal(ctx context.Context, req pb.ReduceUserMedalReq) (*pb.ReduceUserMedalResp, protocol.ServerError)
	ReduceUserMedalEx(ctx context.Context, uid, medalID, exTime uint32) (*pb.ReduceUserMedalResp, error)
	GetMedalConfigUpdateTime(ctx context.Context, req pb.GetMedalConfigUpdateTimeReq) (*pb.GetMedalConfigUpdateTimeResp, protocol.ServerError)
	GetMedalConfigList(ctx context.Context, req pb.GetMedalConfigListReq) (*pb.GetMedalConfigListResp, protocol.ServerError)
	BatGetUserMedalList(ctx context.Context, req pb.BatGetUserMedalListReq) (*pb.BatGetUserMedalListResp, protocol.ServerError)
	GetUserMedalListWhitTaillight(ctx context.Context, req pb.GetUserMedalListWhitTaillightReq) (*pb.GetUserMedalListWhitTaillightResp, protocol.ServerError)
	BatGetUserMedalListWhitTaillight(ctx context.Context, req pb.BatGetUserMedalListWhitTaillightReq) (*pb.BatGetUserMedalListWhitTaillightResp, protocol.ServerError)
	BatGetUserMedalListWhitTaillightEx(ctx context.Context, uin uint32, uidList []uint32) (map[uint32][]*pb.UserMedal, map[uint32][]uint32, map[uint32][]uint32, error)
	SetUserMedalTaillight(ctx context.Context, req pb.SetUserMedalTaillightReq) (*pb.SetUserMedalTaillightResp, protocol.ServerError)
	AddMedal(ctx context.Context, req pb.AddMedalReq) (*pb.AddMedalResp, protocol.ServerError)
	UpdateMedal(ctx context.Context, req pb.UpdateMedalReq) (*pb.UpdateMedalResp, protocol.ServerError)
	DeleteMedal(ctx context.Context, req pb.DeleteMedalReq) (*pb.DeleteMedalResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
