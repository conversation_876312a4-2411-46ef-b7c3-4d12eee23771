package cache

import (
    "context"
    "fmt"
    "testing"
    "time"

    "golang.52tt.com/pkg/log"
    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
)

var testCache *Cache

func init() {
    var err error
    testCache, err = NewCache(context.Background(), &redisConnect.RedisConfig{
        Host: "*************",
        Port: 6379,
    })

    if err != nil {
        fmt.Println(err)
    }
}

func Test_IncrCancelMatchCount(t *testing.T) {
    count, err := testCache.IncrCancelMatchCount(context.Background(), 1, time.Now())
    fmt.Println(count, err)

    getCount, err := testCache.GetCancelMatchCount(context.Background(), 1, time.Now())
    fmt.Println(getCount, err)
}

func Test_SetMatchInfo(t *testing.T) {
    err := testCache.SetMatchInfo(context.Background(), &MatchInfo{
        Uid:                1,
        Phrase:             1,
        Sex:                1,
        MatchStarted:       time.Now(),
        ChannelId:          2,
        MicId:              1,
        MatchId:            "",
        MinWait:            1,
        MaxWait:            10,
        RefreshQuestionCnt: 0,
        OrderId:            "",
        Questions:          nil,
    })

    match, err := testCache.GetMatchInfo(context.Background(), 1)
    fmt.Println(match, err)

    testCache.BatchDelMatchInfo(context.Background(), []uint32{1})
    //testCache.DelHeartBeatTimeoutUserMathInfo(context.Background(), []uint32{1})
    match, err = testCache.GetMatchInfo(context.Background(), 1)
    fmt.Println(match, err)
}

//func Test_AddMatchChannel(t *testing.T) {
//	testCache.AddMatchChannel(context.Background(), 1, 10)
//	testCache.AddMatchChannel(context.Background(), 2, 10)
//	testCache.AddMatchChannel(context.Background(), 3, 10)
//
//	count, err := testCache.GetMatchChannelSize(context.Background(), )
//	fmt.Println(count, err)
//
//}

func TestCache_GetMatchingUid(t *testing.T) {
    female, male, err := testCache.GetMatchingUid(context.Background())
    if err != nil {
        t.Error(err)
    }
    t.Logf("%+v, %+v", female, male)
}

func TestCache_DelMatchingUid(t *testing.T) {
    err := testCache.DelMatchingUid(context.Background(), []uint32{2465835}, []uint32{2465835})
    if err != nil {
        t.Error(err)
    }
}

func TestCache_AtLeastOneMatchedTeam(t *testing.T) {
    flag := testCache.AtLeastOneMatchedTeam(context.Background())
    t.Logf("%v", flag)
}

func TestGetMatchInfo(t *testing.T) {
    matchInfo, _ := testCache.GetMatchInfo(context.Background(), 2202002)
    t.Logf("%+v", matchInfo.GetSelectQuestion())
}

func TestCache_BatchGetMatchInfo(t *testing.T) {
    matchInfoList, _ := testCache.BatchGetMatchInfo(context.Background(), []uint32{2465835, 2513421})
    t.Logf("%+v", matchInfoList)
}

func TestBatchLockUserMatchOp(t *testing.T) {
    log.Infof("%+v", testCache.BatchLockUserMatchOp(context.Background(), 3600*time.Second, 2465835, 2, 3))
    log.Infof("%+v", testCache.BatchLockUserMatchOp(context.Background(), 3600*time.Second, 3, 4, 2465835))
}

func TestBatchUnlockUserMatchOp(t *testing.T) {
    testCache.BatchUnlockUserMatchOp(context.Background(), 1, 2, 3, 4, 5)
}
