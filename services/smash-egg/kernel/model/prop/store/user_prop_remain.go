package store

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/smash-egg/kernel/entity"
	"time"
)

const UserRemainTblNum = 100

func GenUserRemainTbl(id uint32) string {
	return fmt.Sprintf("smash_egg_prop_remain_%02d", id%UserRemainTblNum)
}

const CreateUserRemainTbl = `
CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    uid int(10) unsigned NOT NULL DEFAULT 0,
    expire_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '过期时间',
    prop_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '道具id',
    num int(10) unsigned NOT NULL DEFAULT 0 COMMENT '数量',
    ctime timestamp NOT NULL default CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime timestamp NOT NULL default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    primary key (id),
    unique key idx_unique(uid, prop_id, expire_time),
    index idx_expire_time(expire_time)
)engine=InnoDB default charset=utf8mb4 COMMENT "用户道具余额表";`

func (s *Store) CreateUserPropRemainTbl(ctx context.Context, id uint32) error {
	_, err := s.db.ExecContext(ctx, fmt.Sprintf(CreateUserRemainTbl, GenUserRemainTbl(id)))
	if nil != err {
		log.ErrorWithCtx(ctx, "CreateUserPropRemainTbl db err:%s", err.Error())
		return err
	}

	return nil
}

type UserRemain struct {
	Id         uint32    `db:"id"`
	Uid        uint32    `db:"uid"`
	PropId     uint32    `db:"prop_id"`
	Num        uint32    `db:"num"`
	ExpireTime time.Time `db:"expire_time"`
	CTime      time.Time `db:"ctime"`
	MTime      time.Time `db:"mtime"`
}

func (s *Store) InitUserRemain(ctx context.Context, r *UserRemain) (bool, error) {
	if r == nil {
		return false, errors.New("input is nil")
	}

	query := fmt.Sprintf("INSERT INTO %s(uid, prop_id, expire_time) VALUES(?,?,?)",
		GenUserRemainTbl(r.Uid))
	_, err := s.db.ExecContext(ctx, query, r.Uid, r.PropId, r.ExpireTime)
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok {
			if mysqlErr.Number == 1062 {
				// 记录已存在
				return true, nil
			}
			if mysqlErr.Number == 1146 {
				// 表不存在，建表
				err = s.CreateUserPropRemainTbl(ctx, r.Uid)
				if err != nil {
					log.ErrorWithCtx(ctx, "InitUserRemain fail to CreateUserPropRemainTbl. %+v, err:%v", r, err)
					return false, err
				}

				_, err = s.db.ExecContext(ctx, query, r.Uid, r.PropId, r.ExpireTime)
				if err == nil {
					return false, nil
				}
			}
		}
		log.ErrorWithCtx(ctx, "InitUserRemain fail. r:%+v, err:%v", r, err)
		return false, err
	}

	return false, nil
}

func (s *Store) AddUserRemainNum(ctx context.Context, tx *sqlx.Tx, uid, propId uint32, expireTime time.Time, changeNum int32) (ok bool, err error) {
	query := fmt.Sprintf("UPDATE %s SET num=num+? WHERE uid=? AND prop_id=? AND expire_time=?", GenUserRemainTbl(uid))
	if changeNum < 0 {
		query += fmt.Sprintf(" AND num>=%d", -changeNum)
	}

	var res sql.Result
	if tx != nil {
		// 事务操作
		res, err = tx.ExecContext(ctx, query, changeNum, uid, propId, expireTime)
	} else {
		res, err = s.db.ExecContext(ctx, query, changeNum, uid, propId, expireTime)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserRemainNum fail. uid:%d, propId:%d, changeNum:%d, err:%v", uid, propId, changeNum, err)
		return false, err
	}

	ra, _ := res.RowsAffected()
	log.DebugWithCtx(ctx, "AddUserRemainNum uid:%d, propId:%d, changeNum:%d, expireTime:%v", uid, propId, changeNum, expireTime)
	return ra > 0, nil
}

func (s *Store) GetUserRemain(ctx context.Context, tx *sqlx.Tx, uid, propId uint32, expireTime time.Time) (*UserRemain, bool, error) {
	r := &UserRemain{}
	query := fmt.Sprintf("SELECT id, uid, prop_id, expire_time, num, ctime, mtime FROM %s "+
		"WHERE uid=? AND prop_id=? AND expire_time=?",
		GenUserRemainTbl(uid))

	var err error
	if tx != nil {
		// 事务操作
		err = tx.GetContext(ctx, r, query, uid, propId, expireTime)
	} else {
		err = s.db.GetContext(ctx, r, query, uid, propId, expireTime)
	}

	if err != nil {
		if err == sql.ErrNoRows {
			return r, false, nil
		}
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在
			return r, false, nil
		}

		log.ErrorWithCtx(ctx, "GetUserRemain fail. uid:%d, propId:%d, err:%v", uid, propId, err)
		return r, false, err
	}

	return r, true, nil
}

func (s *Store) GetUserRemainByPropId(ctx context.Context, tx *sqlx.Tx, uid, propId uint32) ([]*UserRemain, error) {
	var rs []*UserRemain
	now := time.Now()
	query := fmt.Sprintf("SELECT id, uid, prop_id, expire_time, num, ctime, mtime FROM %s "+
		"WHERE uid=? AND prop_id=? AND expire_time>? AND num>0 ORDER BY expire_time",
		GenUserRemainTbl(uid))

	var err error
	if tx != nil {
		// 事务操作
		err = tx.SelectContext(ctx, &rs, query, uid, propId, now)
	} else {
		err = s.db.SelectContext(ctx, &rs, query, uid, propId, now)
	}

	if err != nil {
		if err == sql.ErrNoRows {
			return rs, nil
		}
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在
			return rs, nil
		}

		log.ErrorWithCtx(ctx, "GetUserRemainByPropId fail. uid:%d, propId:%d, err:%v", uid, propId, err)
		return rs, err
	}

	if len(rs) == 0 {
		return rs, nil
	}

	return rs, nil
}

func (s *Store) GetUserRemainByUid(ctx context.Context, tx *sqlx.Tx, uid uint32) ([]*UserRemain, error) {
	list := make([]*UserRemain, 0)
	now := time.Now()
	query := fmt.Sprintf("SELECT id, uid, prop_id, expire_time, num, ctime, mtime FROM %s WHERE uid=? AND expire_time>? AND num>0",
		GenUserRemainTbl(uid))

	var err error
	if tx != nil {
		// 事务操作
		err = tx.SelectContext(ctx, &list, query, uid, now)
	} else {
		err = s.db.SelectContext(ctx, &list, query, uid, now)
	}

	if err != nil {
		if err == sql.ErrNoRows {
			return list, nil
		}
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}

		log.ErrorWithCtx(ctx, "GetUserRemainByUid fail. uid:%d, err:%v", uid, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetUserRemainByExpireTime(ctx context.Context, tblId, propId, offset, limit uint32, expireTime time.Time) ([]*UserRemain, error) {
	list := make([]*UserRemain, 0)
	if limit == 0 {
		return list, nil
	}

	query := fmt.Sprintf("SELECT id, uid, prop_id, expire_time, num, ctime, mtime FROM %s WHERE expire_time=? AND prop_id=? LIMIT ?,?", GenUserRemainTbl(tblId))
	err := s.db.SelectContext(ctx, &list, query, expireTime, propId, offset, limit)
	if err != nil {
		if err == sql.ErrNoRows {
			return list, nil
		}
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetUserRemainByExpireTime fail. expireTime:%d, err:%v", expireTime, err)
		return list, err
	}

	return list, nil
}

func (s *Store) GetUserRemainStat(ctx context.Context, tableIdx uint32, offlineTime time.Time) ([]*entity.PropRemainStat, error) {
	list := make([]*entity.PropRemainStat, 0)
	query := fmt.Sprintf("SELECT uid, sum(num) as total_remain FROM %s WHERE expire_time>? and num>0 GROUP BY uid", GenUserRemainTbl(tableIdx))
	err := s.db.SelectContext(ctx, &list, query, offlineTime)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return list, nil
		}
		var mysqlErr *mysql.MySQLError
		if errors.As(err, &mysqlErr) && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}
		log.ErrorWithCtx(ctx, "GetUserRemainStat fail. offlineTime:%v, err:%v", offlineTime, err)
		return list, err
	}

	return list, nil
}

// GetUserRemainByExpireTimeRange 根据expireTime时间获取用户道具明细
func (s *Store) GetUserRemainByExpireTimeRange(ctx context.Context, uid, propId, offset, limit uint32, useEndTime bool, beginTime,
	endTime time.Time) ([]*UserRemain, error) {

	var err error
	list := make([]*UserRemain, 0)

	if useEndTime {
		query := fmt.Sprintf("SELECT id, uid, prop_id, num, expire_time From %s WHERE uid=? and prop_id=? and expire_time>? and expire_time<=? and num>0"+
			" order by expire_time desc limit ?,?", GenUserRemainTbl(uid))
		err = s.db.SelectContext(ctx, &list, query, uid, propId, beginTime, endTime, offset, limit)

	} else {

		query := fmt.Sprintf("SELECT id, uid, prop_id, num, expire_time From %s WHERE uid=? and prop_id=? and expire_time>? and num>0"+
			" order by expire_time desc limit ?,?", GenUserRemainTbl(uid))
		err = s.db.SelectContext(ctx, &list, query, uid, propId, beginTime, offset, limit)

	}

	if err != nil {
		var mysqlErr *mysql.MySQLError
		if errors.As(err, &mysqlErr) && mysqlErr.Number == 1146 {
			// 表不存在
			return list, nil
		}

		log.ErrorWithCtx(ctx, "GetUserRemainByExpireTimeRange fail,uid:%d,err:%v", uid, err)
		return list, err
	}

	return list, nil
}

// GetUserRemainRecordCntByExpireTimeRange 获取指定过期时间范围内用户道具总数量
func (s *Store) GetUserRemainRecordCntByExpireTimeRange(ctx context.Context, uid, propId uint32, useEndTime bool, beginTime, endTime time.Time) (uint32, error) {
	var cnt uint32
	var err error

	if useEndTime {
		query := fmt.Sprintf("SELECT count(1) FROM %s WHERE uid=? and prop_id=? and expire_time> ? and expire_time <= ? and num>0 ", GenUserRemainTbl(uid))
		err = s.db.GetContext(ctx, &cnt, query, uid, propId, beginTime, endTime)

	} else {
		query := fmt.Sprintf("SELECT count(1) FROM %s WHERE uid=? and prop_id=? and expire_time> ? and num>0 ", GenUserRemainTbl(uid))
		err = s.db.GetContext(ctx, &cnt, query, uid, propId, beginTime)
	}

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, nil
		}

		var mysqlErr *mysql.MySQLError
		if errors.As(err, &mysqlErr) && mysqlErr.Number == 1146 {
			// 表不存在
			return 0, nil
		}
		log.ErrorWithCtx(ctx, "GetUserRemainRecordCntByExpireTimeRange fail. uid:%d expireBegin:%v,expireEnd:%v err:%v", uid, beginTime, endTime, err)
		return 0, err
	}

	return cnt, nil
}
