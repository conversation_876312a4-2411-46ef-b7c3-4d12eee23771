// Code generated by protoc-gen-go. DO NOT EDIT.
// source: favourite.proto

/*
Package favourite is a generated protocol buffer package.

It is generated from these files:
	favourite.proto

It has these top-level messages:
	FavouriteInfo
	AddFavouriteReq
	AddFavouriteResp
	CancelFavouriteReq
	CancelFavouriteResp
	ListFavouriteReq
	ListFavouriteResp
*/
package favourite

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Status int32

const (
	Status_DELETED Status = 0
	Status_NORMAL  Status = 1
)

var Status_name = map[int32]string{
	0: "DELETED",
	1: "NORMAL",
}
var Status_value = map[string]int32{
	"DELETED": 0,
	"NORMAL":  1,
}

func (x Status) String() string {
	return proto.EnumName(Status_name, int32(x))
}
func (Status) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

type FavouriteInfo struct {
	XId      string `protobuf:"bytes,1,opt,name=_id,json=Id" json:"_id,omitempty"`
	UserId   uint32 `protobuf:"varint,2,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	CreateAt uint64 `protobuf:"varint,3,opt,name=create_at,json=createAt" json:"create_at,omitempty"`
	ObjectId string `protobuf:"bytes,4,opt,name=object_id,json=objectId" json:"object_id,omitempty"`
	Category uint32 `protobuf:"varint,5,opt,name=category" json:"category,omitempty"`
	Remark   string `protobuf:"bytes,6,opt,name=remark" json:"remark,omitempty"`
	Status   Status `protobuf:"varint,7,opt,name=status,enum=ugc.Status" json:"status,omitempty"`
}

func (m *FavouriteInfo) Reset()                    { *m = FavouriteInfo{} }
func (m *FavouriteInfo) String() string            { return proto.CompactTextString(m) }
func (*FavouriteInfo) ProtoMessage()               {}
func (*FavouriteInfo) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *FavouriteInfo) GetXId() string {
	if m != nil {
		return m.XId
	}
	return ""
}

func (m *FavouriteInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *FavouriteInfo) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *FavouriteInfo) GetObjectId() string {
	if m != nil {
		return m.ObjectId
	}
	return ""
}

func (m *FavouriteInfo) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *FavouriteInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *FavouriteInfo) GetStatus() Status {
	if m != nil {
		return m.Status
	}
	return Status_DELETED
}

type AddFavouriteReq struct {
	Info *FavouriteInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
}

func (m *AddFavouriteReq) Reset()                    { *m = AddFavouriteReq{} }
func (m *AddFavouriteReq) String() string            { return proto.CompactTextString(m) }
func (*AddFavouriteReq) ProtoMessage()               {}
func (*AddFavouriteReq) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

func (m *AddFavouriteReq) GetInfo() *FavouriteInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AddFavouriteResp struct {
	Info        *FavouriteInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
	IsFirstTime bool           `protobuf:"varint,2,opt,name=is_first_time,json=isFirstTime" json:"is_first_time,omitempty"`
}

func (m *AddFavouriteResp) Reset()                    { *m = AddFavouriteResp{} }
func (m *AddFavouriteResp) String() string            { return proto.CompactTextString(m) }
func (*AddFavouriteResp) ProtoMessage()               {}
func (*AddFavouriteResp) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{2} }

func (m *AddFavouriteResp) GetInfo() *FavouriteInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AddFavouriteResp) GetIsFirstTime() bool {
	if m != nil {
		return m.IsFirstTime
	}
	return false
}

type CancelFavouriteReq struct {
	Ids []string `protobuf:"bytes,1,rep,name=ids" json:"ids,omitempty"`
}

func (m *CancelFavouriteReq) Reset()                    { *m = CancelFavouriteReq{} }
func (m *CancelFavouriteReq) String() string            { return proto.CompactTextString(m) }
func (*CancelFavouriteReq) ProtoMessage()               {}
func (*CancelFavouriteReq) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{3} }

func (m *CancelFavouriteReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type CancelFavouriteResp struct {
}

func (m *CancelFavouriteResp) Reset()                    { *m = CancelFavouriteResp{} }
func (m *CancelFavouriteResp) String() string            { return proto.CompactTextString(m) }
func (*CancelFavouriteResp) ProtoMessage()               {}
func (*CancelFavouriteResp) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{4} }

type ListFavouriteReq struct {
	UserId uint32 `protobuf:"varint,1,opt,name=user_id,json=userId" json:"user_id,omitempty"`
}

func (m *ListFavouriteReq) Reset()                    { *m = ListFavouriteReq{} }
func (m *ListFavouriteReq) String() string            { return proto.CompactTextString(m) }
func (*ListFavouriteReq) ProtoMessage()               {}
func (*ListFavouriteReq) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{5} }

func (m *ListFavouriteReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type ListFavouriteResp struct {
	Info []*FavouriteInfo `protobuf:"bytes,1,rep,name=info" json:"info,omitempty"`
}

func (m *ListFavouriteResp) Reset()                    { *m = ListFavouriteResp{} }
func (m *ListFavouriteResp) String() string            { return proto.CompactTextString(m) }
func (*ListFavouriteResp) ProtoMessage()               {}
func (*ListFavouriteResp) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{6} }

func (m *ListFavouriteResp) GetInfo() []*FavouriteInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func init() {
	proto.RegisterType((*FavouriteInfo)(nil), "ugc.FavouriteInfo")
	proto.RegisterType((*AddFavouriteReq)(nil), "ugc.AddFavouriteReq")
	proto.RegisterType((*AddFavouriteResp)(nil), "ugc.AddFavouriteResp")
	proto.RegisterType((*CancelFavouriteReq)(nil), "ugc.CancelFavouriteReq")
	proto.RegisterType((*CancelFavouriteResp)(nil), "ugc.CancelFavouriteResp")
	proto.RegisterType((*ListFavouriteReq)(nil), "ugc.ListFavouriteReq")
	proto.RegisterType((*ListFavouriteResp)(nil), "ugc.ListFavouriteResp")
	proto.RegisterEnum("ugc.Status", Status_name, Status_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Favourite service

type FavouriteClient interface {
	// 添加收藏
	AddFavourite(ctx context.Context, in *AddFavouriteReq, opts ...grpc.CallOption) (*AddFavouriteResp, error)
	// 取消收藏
	CancelFavourite(ctx context.Context, in *CancelFavouriteReq, opts ...grpc.CallOption) (*CancelFavouriteResp, error)
	//
	ListFavourite(ctx context.Context, in *ListFavouriteReq, opts ...grpc.CallOption) (*ListFavouriteResp, error)
}

type favouriteClient struct {
	cc *grpc.ClientConn
}

func NewFavouriteClient(cc *grpc.ClientConn) FavouriteClient {
	return &favouriteClient{cc}
}

func (c *favouriteClient) AddFavourite(ctx context.Context, in *AddFavouriteReq, opts ...grpc.CallOption) (*AddFavouriteResp, error) {
	out := new(AddFavouriteResp)
	err := grpc.Invoke(ctx, "/ugc.Favourite/AddFavourite", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *favouriteClient) CancelFavourite(ctx context.Context, in *CancelFavouriteReq, opts ...grpc.CallOption) (*CancelFavouriteResp, error) {
	out := new(CancelFavouriteResp)
	err := grpc.Invoke(ctx, "/ugc.Favourite/CancelFavourite", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *favouriteClient) ListFavourite(ctx context.Context, in *ListFavouriteReq, opts ...grpc.CallOption) (*ListFavouriteResp, error) {
	out := new(ListFavouriteResp)
	err := grpc.Invoke(ctx, "/ugc.Favourite/ListFavourite", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Favourite service

type FavouriteServer interface {
	// 添加收藏
	AddFavourite(context.Context, *AddFavouriteReq) (*AddFavouriteResp, error)
	// 取消收藏
	CancelFavourite(context.Context, *CancelFavouriteReq) (*CancelFavouriteResp, error)
	//
	ListFavourite(context.Context, *ListFavouriteReq) (*ListFavouriteResp, error)
}

func RegisterFavouriteServer(s *grpc.Server, srv FavouriteServer) {
	s.RegisterService(&_Favourite_serviceDesc, srv)
}

func _Favourite_AddFavourite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFavouriteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FavouriteServer).AddFavourite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.Favourite/AddFavourite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FavouriteServer).AddFavourite(ctx, req.(*AddFavouriteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Favourite_CancelFavourite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelFavouriteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FavouriteServer).CancelFavourite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.Favourite/CancelFavourite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FavouriteServer).CancelFavourite(ctx, req.(*CancelFavouriteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Favourite_ListFavourite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFavouriteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FavouriteServer).ListFavourite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.Favourite/ListFavourite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FavouriteServer).ListFavourite(ctx, req.(*ListFavouriteReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Favourite_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.Favourite",
	HandlerType: (*FavouriteServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddFavourite",
			Handler:    _Favourite_AddFavourite_Handler,
		},
		{
			MethodName: "CancelFavourite",
			Handler:    _Favourite_CancelFavourite_Handler,
		},
		{
			MethodName: "ListFavourite",
			Handler:    _Favourite_ListFavourite_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "favourite.proto",
}

func init() { proto.RegisterFile("favourite.proto", fileDescriptor0) }

var fileDescriptor0 = []byte{
	// 447 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x53, 0x51, 0x8b, 0xd3, 0x40,
	0x10, 0x76, 0x2f, 0x35, 0x6d, 0xa7, 0xd6, 0xc6, 0xd1, 0xbb, 0x0b, 0xf5, 0x25, 0x46, 0x38, 0x82,
	0x42, 0x82, 0x15, 0x1f, 0xe4, 0x40, 0xa8, 0xb6, 0x07, 0x85, 0xaa, 0xb0, 0xde, 0x93, 0x0f, 0x96,
	0xdc, 0x66, 0x1b, 0x56, 0xdb, 0x6e, 0xdc, 0xdd, 0x1c, 0xf8, 0x1f, 0xc5, 0xdf, 0x24, 0xd9, 0x9e,
	0xa5, 0xc9, 0x15, 0xb9, 0xb7, 0xcc, 0xf7, 0xcd, 0x37, 0xd9, 0xef, 0x1b, 0x06, 0x06, 0xcb, 0xf4,
	0x5a, 0x96, 0x4a, 0x18, 0x1e, 0x17, 0x4a, 0x1a, 0x89, 0x4e, 0x99, 0xb3, 0xf0, 0x37, 0x81, 0xfe,
	0xc5, 0x3f, 0x62, 0xb6, 0x59, 0x4a, 0x1c, 0x80, 0xb3, 0x10, 0x99, 0x4f, 0x02, 0x12, 0x75, 0xe9,
	0xd1, 0x2c, 0xc3, 0x53, 0x68, 0x97, 0x9a, 0xab, 0x0a, 0x3c, 0x0a, 0x48, 0xd4, 0xa7, 0x6e, 0x55,
	0xce, 0x32, 0x7c, 0x0a, 0x5d, 0xa6, 0x78, 0x6a, 0xf8, 0x22, 0x35, 0xbe, 0x13, 0x90, 0xa8, 0x45,
	0x3b, 0x5b, 0x60, 0x6c, 0x2a, 0x52, 0x5e, 0x7d, 0xe7, 0xcc, 0x54, 0xba, 0x96, 0x1d, 0xd6, 0xd9,
	0x02, 0xb3, 0x0c, 0x87, 0xd0, 0x61, 0xa9, 0xe1, 0xb9, 0x54, 0xbf, 0xfc, 0xfb, 0x76, 0xe6, 0xae,
	0xc6, 0x13, 0x70, 0x15, 0x5f, 0xa7, 0xea, 0x87, 0xef, 0x5a, 0xd5, 0x4d, 0x85, 0xcf, 0xc1, 0xd5,
	0x26, 0x35, 0xa5, 0xf6, 0xdb, 0x01, 0x89, 0x1e, 0x8e, 0x7a, 0x71, 0x99, 0xb3, 0xf8, 0x8b, 0x85,
	0xe8, 0x0d, 0x15, 0xbe, 0x85, 0xc1, 0x38, 0xcb, 0x76, 0x86, 0x28, 0xff, 0x89, 0x67, 0xd0, 0x12,
	0x9b, 0xa5, 0xb4, 0x86, 0x7a, 0x23, 0xb4, 0xaa, 0x9a, 0x63, 0x6a, 0xf9, 0xf0, 0x1b, 0x78, 0x75,
	0xa9, 0x2e, 0xee, 0xaa, 0xc5, 0x10, 0xfa, 0x42, 0x2f, 0x96, 0x42, 0x69, 0xb3, 0x30, 0x62, 0xcd,
	0x6d, 0x50, 0x1d, 0xda, 0x13, 0xfa, 0xa2, 0xc2, 0x2e, 0xc5, 0x9a, 0x87, 0x67, 0x80, 0x1f, 0xd2,
	0x0d, 0xe3, 0xab, 0xda, 0xeb, 0x3c, 0x70, 0x44, 0xa6, 0x7d, 0x12, 0x38, 0x51, 0x97, 0x56, 0x9f,
	0xe1, 0x31, 0x3c, 0xbe, 0xd5, 0xa7, 0x8b, 0xf0, 0x25, 0x78, 0x73, 0xa1, 0x4d, 0x4d, 0xbc, 0xb7,
	0x19, 0xb2, 0xbf, 0x99, 0xf0, 0x1c, 0x1e, 0x35, 0x9a, 0x6b, 0x66, 0x9c, 0xff, 0x99, 0x79, 0xf1,
	0x0c, 0xdc, 0x6d, 0xaa, 0xd8, 0x83, 0xf6, 0x64, 0x3a, 0x9f, 0x5e, 0x4e, 0x27, 0xde, 0x3d, 0x04,
	0x70, 0x3f, 0x7d, 0xa6, 0x1f, 0xc7, 0x73, 0x8f, 0x8c, 0xfe, 0x10, 0xe8, 0xee, 0xa4, 0x78, 0x0e,
	0x0f, 0xf6, 0x93, 0xc3, 0x27, 0x76, 0x74, 0x63, 0x0f, 0xc3, 0xe3, 0x03, 0xa8, 0x2e, 0x70, 0x02,
	0x83, 0x86, 0x5d, 0x3c, 0xb5, 0x9d, 0xb7, 0xc3, 0x1a, 0xfa, 0x87, 0x09, 0x5d, 0xe0, 0x3b, 0xe8,
	0xd7, 0x0c, 0xe3, 0xf6, 0x6f, 0xcd, 0xc4, 0x86, 0x27, 0x87, 0x60, 0x5d, 0xbc, 0x7f, 0xf5, 0x35,
	0xc9, 0xe5, 0x2a, 0xdd, 0xe4, 0xf1, 0x9b, 0x91, 0x31, 0x31, 0x93, 0xeb, 0xc4, 0x1e, 0x09, 0x93,
	0xab, 0x44, 0x73, 0x75, 0x2d, 0x18, 0xd7, 0x49, 0x99, 0xb3, 0x64, 0x77, 0x44, 0x57, 0xae, 0x6d,
	0x78, 0xfd, 0x37, 0x00, 0x00, 0xff, 0xff, 0x3e, 0xc3, 0xc7, 0xfe, 0x58, 0x03, 0x00, 0x00,
}
