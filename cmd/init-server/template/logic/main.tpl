package main

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/pkg/tracing/jaeger"
	"golang.52tt.com/services/runtime"
	"golang.52tt.com/services/{{.Servicename}}/server"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
	"os"
	pb "golang.52tt.com/protocol/services/logicsvr-go/{{.Servicename}}"
)

func main() {
	flags := grpcEx.ParseServerFlags(os.Args)

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	var (
		svr *server.{{.ServiceName}}
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.New{{.ServiceName}}(sc.Configer)
		if err != nil {
			return err
		}

		pb.Register{{.ServiceName}}Server(s, svr)
		return nil
	}

	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	unaryInt := grpc_middleware.ChainUnaryServer(
		runtime.LogicServerUnaryInterceptor(flags.LogRequests, flags.LogResponses),
	)

	s := grpcEx.NewServer(
		flags,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("config.json", grpcEx.AdapterJSON),
	)
	s.Serve()
}
