package app_config

import (
	"context"
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"net/http"
	"strings"
	"sync"
	"time"
)

var (
	OnePageSize = 50
	mu          sync.RWMutex
)
var AppToOwner AppConfig

type AppConfig struct {
	OwnerToApp map[string][]string `json:"app_config"`
}

func Init() error {

	go func() {
		for {
			GetAppConfig()
			time.Sleep(time.Hour)
		}
	}()
	return nil
}

func GetProjectOwner(app string) []string {
	mu.RLock()
	defer mu.RUnlock()
	return AppToOwner.OwnerToApp[app]
}

type AppConfigData struct {
	Owner   string `json:"owner"`
	AppName string `json:"service"`
}

type AppConfigBaseInfo struct {
	BaseInfo *AppConfigData `json:"basic_info"`
}

type AppConfigResp struct {
	Code uint32               `json:"code"`
	Data []*AppConfigBaseInfo `json:"data"`
}

func GetAppConfig() error {
	baseUrl := "https://yw-cicd.ttyuyin.com/api/v2/app/team/units/?team=quicksilver&page=%d&limit=%d"

	if len(AppToOwner.OwnerToApp) == 0 {
		AppToOwner.OwnerToApp = make(map[string][]string)
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	for page := 1; page <= 100; page++ {
		url := fmt.Sprintf(baseUrl, page, OnePageSize)
		client := &http.Client{}
		httpReq, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
		if err != nil {
			log.ErrorWithCtx(ctx, "NewRequestWithContext new http request err: %v", err)
			fmt.Println("request err ", err)
			return err
		}

		httpReq.Header.Set("Authorization", "Token 0bdc32d6e2aa362f24d1db94606f3b27d8f22bbd")
		httpReq.Header.Set("Content-Type", "application/json;utf-8")

		resp, err := client.Do(httpReq)
		if err != nil {
			fmt.Println("request Do err ", err)
			return err
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			log.Errorf("http.Get fail. %s %v", url, resp.StatusCode)
			return err
		}

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return err
		}

		var appConfigResp AppConfigResp
		err = json.Unmarshal(body, &appConfigResp)
		if err != nil {
			return err
		}

		fmt.Printf("get app config %d:%d \n", page, len(appConfigResp.Data))

		if appConfigResp.Code != 200 {
			continue
		}

		if appConfigResp.Data == nil {
			continue
		}

		for _, data := range appConfigResp.Data {
			appName := data.BaseInfo.AppName
			owners := strings.Split(data.BaseInfo.Owner, ",")
			mu.Lock()
			AppToOwner.OwnerToApp[appName] = owners
			mu.Unlock()
		}

		if len(appConfigResp.Data) < OnePageSize {
			fmt.Println("read all app ", page)
			break
		}
	}
	return nil
}
