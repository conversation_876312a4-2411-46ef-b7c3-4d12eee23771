# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

2053:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 2053 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/ChannelSetMicStatus
2068:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 2068 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/ChannelChangeMic
2711:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 2711 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/ChannelNormalQueueUpMicApply
2712:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 2712 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/ChannelNormalQueueUpMicList
432:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 432 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/ChannelGetMicList
440:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 440 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/KickoutChannelMic
50852:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50852 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/ChannelMicTakeChange
50853:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50853 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/GetChannelMicName
50854:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50854 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/SetChannelMicName
50855:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50855 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/SetChannelMicSwitch
50857:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 50857 --source api/channel_mic/grpc_channel_mic_go.proto --lang go --method /ga.api.channel_mic.ChannelMicLogicGo/SetChannelMicNameSwitch
