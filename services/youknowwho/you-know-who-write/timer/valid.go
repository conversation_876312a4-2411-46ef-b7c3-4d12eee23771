package timer

import (
	"time"

	"golang.52tt.com/services/youknowwho/common/dyconf"
)

// validNumberPoolStart 判断是否执行刷新号码池定时任务
// 在 00:00 - 02:00 进行
func validNumberPoolStart() bool {
	now := time.Now()
	if now.Hour() < 2 {
		return true
	}
	if dyconf.NumberPoolConf.Get().UpdateDirect {
		return true
	}
	return false
}

// isUpdateNicknameTime 判断是否是昵称刷新时间
// 在03：00 - 03：30进行刷新，每一分钟执行一次函数
func isUpdateNicknameTime(timeNow time.Time) bool {
	if timeNow.Hour() == 3 && timeNow.Minute() < 30 {
		return true
	}

	if dyconf.RefreshConf.Get().NeedRefreshFakeUid && timeNow.Hour() > 3 {
		return true
	}

	return false
}

// isUpdateFakeUidPoolTime 判断是否是神秘人假uid池刷新时间
// 在01：00 - 01：30进行刷新，每一分钟执行一次函数
// 测试环境开关控制是否刷新
func isUpdateFakeUidPoolTime() bool {
	timeNow := time.Now()
	if timeNow.Hour() == 1 && timeNow.Minute() < 30 {
		return true
	}

	if dyconf.RefreshConf.Get().NeedRefreshFakePool {
		return true
	}

	return false
}
