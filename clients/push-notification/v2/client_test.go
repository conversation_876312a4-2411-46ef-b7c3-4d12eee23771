package PushNotification

import (
	"context"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/push-notification/v2"
)

var notify *pb.CompositiveNotification

func init() {
	notify = &pb.CompositiveNotification{
		Sequence: 1,
		ProxyNotification: &pb.ProxyNotification{
			Type:       6,
			ExpireTime: uint32(time.Now().Add(time.Hour).Unix()),
			PushType:   1,
			TaskId:     "test-task-id",
		},
		ClientTimestamp: uint32(time.Now().Unix()),
	}
}

func TestClient_UnaryPushToUsersWithReqId(t *testing.T) {
	Convey("TestClient_UnaryPushToUsersWithReqId", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		requestId, err := client.UnaryPushToUsersWithReqId(context.Background(), &pb.PushToUsersReq{
			UidList:      []uint32{1},
			Notification: notify,
		})
		So(err, ShouldBeNil)
		t.Log(requestId)
	})
}

func TestClient_UnaryPushToUserMapWithReqId(t *testing.T) {
	Convey("TestClient_UnaryPushToUserMapWithReqId", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		requestId, err := client.UnaryPushToUserMapWithReqId(context.Background(), &pb.PushToUserMapReq{
			UidMap: map[uint32]*pb.CompositiveNotification{
				1: notify,
			},
		})
		So(err, ShouldBeNil)
		t.Log(requestId)
	})
}

func TestClient_UnaryPushToPresenceListWithReqId(t *testing.T) {
	Convey("TestClient_UnaryPushToPresenceListWithReqId", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		requestId, err := client.UnaryPushMulticastWithReqId(context.Background(), &pb.PushMulticastReq{
			MulticastAccount: &pb.MulticastAccount{
				Id:      1,
				Account: "channel",
			},
			Notification: notify,
		})
		So(err, ShouldBeNil)
		t.Log(requestId)
	})
}

func TestClient_UnaryPushMulticastWithReqId(t *testing.T) {
	Convey("TestClient_UnaryPushMulticastWithReqId", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)

		requestId, err := client.UnaryPushToPresenceListWithReqId(context.Background(), &pb.PushToPresenceListReq{
			PresenceList: []*pb.Presence{
				{
					ProxyIp:      1,
					ProxyPort:    1,
					Uid:          1,
					ClientId:     1,
					TerminalType: 1,
				},
			},
			Notification: notify,
		})
		So(err, ShouldBeNil)
		t.Log(requestId)
	})
}
