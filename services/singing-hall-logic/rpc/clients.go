package rpc

import (
	"github.com/google/wire"
	singing_hall "golang.52tt.com/clients/singing-hall"
	"google.golang.org/grpc"
)

type Client struct {
	SingingHallSvrCli *singing_hall.Client
}

var ProviderSetForDownStreamClient = wire.NewSet(
	NewGrpcOption,
	NewClient,
)

func NewClient() *Client {
	singingHallSvrCli, _ := singing_hall.NewClient()
	return &Client{
		SingingHallSvrCli: singingHallSvrCli,
	}
}

func NewGrpcOption() []grpc.DialOption {
	return []grpc.DialOption{
		grpc.WithBlock(),
	}
}
