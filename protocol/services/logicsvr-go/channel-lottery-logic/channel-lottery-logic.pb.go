// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-lottery-logic/channel-lottery-logic.proto

package channel_lottery_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-lottery-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_lottery "golang.52tt.com/protocol/app/channel-lottery"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLotteryLogicClient is the client API for ChannelLotteryLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLotteryLogicClient interface {
	// -------------------- 官频-抽奖 --------------------
	// 获取是否有抽奖功能
	ShowChannelLotterySetting(ctx context.Context, in *channel_lottery.ShowChannelLotterySettingReq, opts ...grpc.CallOption) (*channel_lottery.ShowChannelLotterySettingResp, error)
	// 获取抽奖设置信息
	GetChannelLotterySetting(ctx context.Context, in *channel_lottery.GetChannelLotterySettingReq, opts ...grpc.CallOption) (*channel_lottery.GetChannelLotterySettingResp, error)
	// 设置抽奖信息
	SetChannelLotteryInfo(ctx context.Context, in *channel_lottery.SetChannelLotteryInfoReq, opts ...grpc.CallOption) (*channel_lottery.SetChannelLotteryInfoResp, error)
	// 点击参与抽奖
	JoinChannelLottery(ctx context.Context, in *channel_lottery.JoinChannelLotteryReq, opts ...grpc.CallOption) (*channel_lottery.JoinChannelLotteryResp, error)
	// 获取本次排班所有抽奖配置和抽奖结果
	GetChannelLotteryInfoList(ctx context.Context, in *channel_lottery.GetChannelLotteryInfoListReq, opts ...grpc.CallOption) (*channel_lottery.GetChannelLotteryInfoListResp, error)
	// 确定并开始抽奖
	BeginChannelLottery(ctx context.Context, in *channel_lottery.BeginChannelLotteryReq, opts ...grpc.CallOption) (*channel_lottery.BeginChannelLotteryResp, error)
	// 确认送礼
	SendChannelLotteryPresent(ctx context.Context, in *channel_lottery.SendChannelLotteryPresentReq, opts ...grpc.CallOption) (*channel_lottery.SendChannelLotteryPresentResp, error)
	// 获取开始抽奖信息
	GetChannelLotteryInfo(ctx context.Context, in *channel_lottery.GetChannelLotteryInfoReq, opts ...grpc.CallOption) (*channel_lottery.GetChannelLotteryInfoResp, error)
	// 获取自定义礼物列表
	SearchCustomGifts(ctx context.Context, in *channel_lottery.SearchCustomGiftsReq, opts ...grpc.CallOption) (*channel_lottery.SearchCustomGiftsResp, error)
	// 上报通过分享链接进抽奖房间
	ReportEnterShareChannel(ctx context.Context, in *channel_lottery.ReportEnterShareChannelReq, opts ...grpc.CallOption) (*channel_lottery.ReportEnterShareChannelResp, error)
	// 获取抽奖期间进房人数
	GetEnterChannelUserCnt(ctx context.Context, in *channel_lottery.GetEnterChannelUserCntReq, opts ...grpc.CallOption) (*channel_lottery.GetEnterChannelUserCntResp, error)
	// 获取用户任务进度
	GetConditionMissionProgress(ctx context.Context, in *channel_lottery.GetConditionMissionProgressReq, opts ...grpc.CallOption) (*channel_lottery.GetConditionMissionProgressResp, error)
}

type channelLotteryLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelLotteryLogicClient(cc *grpc.ClientConn) ChannelLotteryLogicClient {
	return &channelLotteryLogicClient{cc}
}

func (c *channelLotteryLogicClient) ShowChannelLotterySetting(ctx context.Context, in *channel_lottery.ShowChannelLotterySettingReq, opts ...grpc.CallOption) (*channel_lottery.ShowChannelLotterySettingResp, error) {
	out := new(channel_lottery.ShowChannelLotterySettingResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/ShowChannelLotterySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) GetChannelLotterySetting(ctx context.Context, in *channel_lottery.GetChannelLotterySettingReq, opts ...grpc.CallOption) (*channel_lottery.GetChannelLotterySettingResp, error) {
	out := new(channel_lottery.GetChannelLotterySettingResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/GetChannelLotterySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) SetChannelLotteryInfo(ctx context.Context, in *channel_lottery.SetChannelLotteryInfoReq, opts ...grpc.CallOption) (*channel_lottery.SetChannelLotteryInfoResp, error) {
	out := new(channel_lottery.SetChannelLotteryInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/SetChannelLotteryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) JoinChannelLottery(ctx context.Context, in *channel_lottery.JoinChannelLotteryReq, opts ...grpc.CallOption) (*channel_lottery.JoinChannelLotteryResp, error) {
	out := new(channel_lottery.JoinChannelLotteryResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/JoinChannelLottery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) GetChannelLotteryInfoList(ctx context.Context, in *channel_lottery.GetChannelLotteryInfoListReq, opts ...grpc.CallOption) (*channel_lottery.GetChannelLotteryInfoListResp, error) {
	out := new(channel_lottery.GetChannelLotteryInfoListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/GetChannelLotteryInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) BeginChannelLottery(ctx context.Context, in *channel_lottery.BeginChannelLotteryReq, opts ...grpc.CallOption) (*channel_lottery.BeginChannelLotteryResp, error) {
	out := new(channel_lottery.BeginChannelLotteryResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/BeginChannelLottery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) SendChannelLotteryPresent(ctx context.Context, in *channel_lottery.SendChannelLotteryPresentReq, opts ...grpc.CallOption) (*channel_lottery.SendChannelLotteryPresentResp, error) {
	out := new(channel_lottery.SendChannelLotteryPresentResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/SendChannelLotteryPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) GetChannelLotteryInfo(ctx context.Context, in *channel_lottery.GetChannelLotteryInfoReq, opts ...grpc.CallOption) (*channel_lottery.GetChannelLotteryInfoResp, error) {
	out := new(channel_lottery.GetChannelLotteryInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/GetChannelLotteryInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) SearchCustomGifts(ctx context.Context, in *channel_lottery.SearchCustomGiftsReq, opts ...grpc.CallOption) (*channel_lottery.SearchCustomGiftsResp, error) {
	out := new(channel_lottery.SearchCustomGiftsResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/SearchCustomGifts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) ReportEnterShareChannel(ctx context.Context, in *channel_lottery.ReportEnterShareChannelReq, opts ...grpc.CallOption) (*channel_lottery.ReportEnterShareChannelResp, error) {
	out := new(channel_lottery.ReportEnterShareChannelResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/ReportEnterShareChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) GetEnterChannelUserCnt(ctx context.Context, in *channel_lottery.GetEnterChannelUserCntReq, opts ...grpc.CallOption) (*channel_lottery.GetEnterChannelUserCntResp, error) {
	out := new(channel_lottery.GetEnterChannelUserCntResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/GetEnterChannelUserCnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLotteryLogicClient) GetConditionMissionProgress(ctx context.Context, in *channel_lottery.GetConditionMissionProgressReq, opts ...grpc.CallOption) (*channel_lottery.GetConditionMissionProgressResp, error) {
	out := new(channel_lottery.GetConditionMissionProgressResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelLotteryLogic/GetConditionMissionProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLotteryLogicServer is the server API for ChannelLotteryLogic service.
type ChannelLotteryLogicServer interface {
	// -------------------- 官频-抽奖 --------------------
	// 获取是否有抽奖功能
	ShowChannelLotterySetting(context.Context, *channel_lottery.ShowChannelLotterySettingReq) (*channel_lottery.ShowChannelLotterySettingResp, error)
	// 获取抽奖设置信息
	GetChannelLotterySetting(context.Context, *channel_lottery.GetChannelLotterySettingReq) (*channel_lottery.GetChannelLotterySettingResp, error)
	// 设置抽奖信息
	SetChannelLotteryInfo(context.Context, *channel_lottery.SetChannelLotteryInfoReq) (*channel_lottery.SetChannelLotteryInfoResp, error)
	// 点击参与抽奖
	JoinChannelLottery(context.Context, *channel_lottery.JoinChannelLotteryReq) (*channel_lottery.JoinChannelLotteryResp, error)
	// 获取本次排班所有抽奖配置和抽奖结果
	GetChannelLotteryInfoList(context.Context, *channel_lottery.GetChannelLotteryInfoListReq) (*channel_lottery.GetChannelLotteryInfoListResp, error)
	// 确定并开始抽奖
	BeginChannelLottery(context.Context, *channel_lottery.BeginChannelLotteryReq) (*channel_lottery.BeginChannelLotteryResp, error)
	// 确认送礼
	SendChannelLotteryPresent(context.Context, *channel_lottery.SendChannelLotteryPresentReq) (*channel_lottery.SendChannelLotteryPresentResp, error)
	// 获取开始抽奖信息
	GetChannelLotteryInfo(context.Context, *channel_lottery.GetChannelLotteryInfoReq) (*channel_lottery.GetChannelLotteryInfoResp, error)
	// 获取自定义礼物列表
	SearchCustomGifts(context.Context, *channel_lottery.SearchCustomGiftsReq) (*channel_lottery.SearchCustomGiftsResp, error)
	// 上报通过分享链接进抽奖房间
	ReportEnterShareChannel(context.Context, *channel_lottery.ReportEnterShareChannelReq) (*channel_lottery.ReportEnterShareChannelResp, error)
	// 获取抽奖期间进房人数
	GetEnterChannelUserCnt(context.Context, *channel_lottery.GetEnterChannelUserCntReq) (*channel_lottery.GetEnterChannelUserCntResp, error)
	// 获取用户任务进度
	GetConditionMissionProgress(context.Context, *channel_lottery.GetConditionMissionProgressReq) (*channel_lottery.GetConditionMissionProgressResp, error)
}

func RegisterChannelLotteryLogicServer(s *grpc.Server, srv ChannelLotteryLogicServer) {
	s.RegisterService(&_ChannelLotteryLogic_serviceDesc, srv)
}

func _ChannelLotteryLogic_ShowChannelLotterySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.ShowChannelLotterySettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).ShowChannelLotterySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/ShowChannelLotterySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).ShowChannelLotterySetting(ctx, req.(*channel_lottery.ShowChannelLotterySettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_GetChannelLotterySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.GetChannelLotterySettingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).GetChannelLotterySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/GetChannelLotterySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).GetChannelLotterySetting(ctx, req.(*channel_lottery.GetChannelLotterySettingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_SetChannelLotteryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.SetChannelLotteryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).SetChannelLotteryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/SetChannelLotteryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).SetChannelLotteryInfo(ctx, req.(*channel_lottery.SetChannelLotteryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_JoinChannelLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.JoinChannelLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).JoinChannelLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/JoinChannelLottery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).JoinChannelLottery(ctx, req.(*channel_lottery.JoinChannelLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_GetChannelLotteryInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.GetChannelLotteryInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).GetChannelLotteryInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/GetChannelLotteryInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).GetChannelLotteryInfoList(ctx, req.(*channel_lottery.GetChannelLotteryInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_BeginChannelLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.BeginChannelLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).BeginChannelLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/BeginChannelLottery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).BeginChannelLottery(ctx, req.(*channel_lottery.BeginChannelLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_SendChannelLotteryPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.SendChannelLotteryPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).SendChannelLotteryPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/SendChannelLotteryPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).SendChannelLotteryPresent(ctx, req.(*channel_lottery.SendChannelLotteryPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_GetChannelLotteryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.GetChannelLotteryInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).GetChannelLotteryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/GetChannelLotteryInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).GetChannelLotteryInfo(ctx, req.(*channel_lottery.GetChannelLotteryInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_SearchCustomGifts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.SearchCustomGiftsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).SearchCustomGifts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/SearchCustomGifts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).SearchCustomGifts(ctx, req.(*channel_lottery.SearchCustomGiftsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_ReportEnterShareChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.ReportEnterShareChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).ReportEnterShareChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/ReportEnterShareChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).ReportEnterShareChannel(ctx, req.(*channel_lottery.ReportEnterShareChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_GetEnterChannelUserCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.GetEnterChannelUserCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).GetEnterChannelUserCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/GetEnterChannelUserCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).GetEnterChannelUserCnt(ctx, req.(*channel_lottery.GetEnterChannelUserCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLotteryLogic_GetConditionMissionProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_lottery.GetConditionMissionProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLotteryLogicServer).GetConditionMissionProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelLotteryLogic/GetConditionMissionProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLotteryLogicServer).GetConditionMissionProgress(ctx, req.(*channel_lottery.GetConditionMissionProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLotteryLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelLotteryLogic",
	HandlerType: (*ChannelLotteryLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ShowChannelLotterySetting",
			Handler:    _ChannelLotteryLogic_ShowChannelLotterySetting_Handler,
		},
		{
			MethodName: "GetChannelLotterySetting",
			Handler:    _ChannelLotteryLogic_GetChannelLotterySetting_Handler,
		},
		{
			MethodName: "SetChannelLotteryInfo",
			Handler:    _ChannelLotteryLogic_SetChannelLotteryInfo_Handler,
		},
		{
			MethodName: "JoinChannelLottery",
			Handler:    _ChannelLotteryLogic_JoinChannelLottery_Handler,
		},
		{
			MethodName: "GetChannelLotteryInfoList",
			Handler:    _ChannelLotteryLogic_GetChannelLotteryInfoList_Handler,
		},
		{
			MethodName: "BeginChannelLottery",
			Handler:    _ChannelLotteryLogic_BeginChannelLottery_Handler,
		},
		{
			MethodName: "SendChannelLotteryPresent",
			Handler:    _ChannelLotteryLogic_SendChannelLotteryPresent_Handler,
		},
		{
			MethodName: "GetChannelLotteryInfo",
			Handler:    _ChannelLotteryLogic_GetChannelLotteryInfo_Handler,
		},
		{
			MethodName: "SearchCustomGifts",
			Handler:    _ChannelLotteryLogic_SearchCustomGifts_Handler,
		},
		{
			MethodName: "ReportEnterShareChannel",
			Handler:    _ChannelLotteryLogic_ReportEnterShareChannel_Handler,
		},
		{
			MethodName: "GetEnterChannelUserCnt",
			Handler:    _ChannelLotteryLogic_GetEnterChannelUserCnt_Handler,
		},
		{
			MethodName: "GetConditionMissionProgress",
			Handler:    _ChannelLotteryLogic_GetConditionMissionProgress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-lottery-logic/channel-lottery-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-lottery-logic/channel-lottery-logic.proto", fileDescriptor_channel_lottery_logic_5988a2693d75cefd)
}

var fileDescriptor_channel_lottery_logic_5988a2693d75cefd = []byte{
	// 538 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x95, 0x4f, 0x6f, 0xd3, 0x30,
	0x14, 0xc0, 0xe5, 0x09, 0x10, 0x58, 0x5c, 0xf0, 0x34, 0x06, 0x81, 0x13, 0x27, 0x0a, 0xd4, 0x19,
	0x9d, 0x38, 0x72, 0x59, 0x35, 0x45, 0xa0, 0x22, 0x4d, 0x8b, 0xb8, 0x70, 0x99, 0x42, 0xf6, 0x96,
	0x5a, 0xca, 0xec, 0x60, 0x9b, 0x4e, 0x3d, 0x21, 0x21, 0x21, 0x21, 0x71, 0xe2, 0x03, 0xf4, 0x03,
	0x00, 0x9f, 0x60, 0x9f, 0x62, 0x1f, 0x81, 0xff, 0x70, 0xca, 0x05, 0x6e, 0x9c, 0x90, 0x93, 0x4c,
	0xa6, 0x9d, 0xcd, 0x92, 0x53, 0xd5, 0xbe, 0xdf, 0x7b, 0xef, 0xe7, 0x97, 0xd7, 0x18, 0xdf, 0xcf,
	0x45, 0xc6, 0x52, 0x35, 0x91, 0xfd, 0x4c, 0x84, 0xe9, 0x38, 0xe1, 0x1c, 0xf2, 0x7e, 0x2e, 0xb4,
	0x06, 0x39, 0xed, 0x57, 0x31, 0xf7, 0xaf, 0xb4, 0x90, 0x42, 0x0b, 0x72, 0xb6, 0xfa, 0x12, 0xf4,
	0xfe, 0xad, 0x92, 0x25, 0x1a, 0x0e, 0x92, 0x69, 0x28, 0x0a, 0xcd, 0x04, 0x57, 0xc7, 0x9f, 0x75,
	0x46, 0x10, 0x24, 0x45, 0xb1, 0x58, 0x72, 0xa7, 0x8e, 0x0d, 0x7e, 0x5f, 0xc4, 0xcb, 0xc3, 0x3a,
	0x34, 0xaa, 0x23, 0x23, 0x53, 0x97, 0xbc, 0x41, 0xf8, 0x6a, 0x3c, 0x16, 0x07, 0xf3, 0xb1, 0x18,
	0xb4, 0x66, 0x3c, 0x23, 0x6b, 0x34, 0x4b, 0x68, 0x53, 0x71, 0xa7, 0xa9, 0x48, 0xbd, 0xf8, 0x36,
	0x3c, 0x0b, 0xee, 0x76, 0xcc, 0x50, 0xc5, 0x8d, 0x0b, 0xbf, 0x8e, 0x0e, 0xe9, 0x99, 0xf3, 0x1f,
	0x4b, 0x44, 0x5e, 0x23, 0x7c, 0x25, 0x02, 0xed, 0x96, 0x09, 0x5d, 0xa5, 0x7d, 0xb4, 0x71, 0x59,
	0xeb, 0x96, 0x60, 0x55, 0x3e, 0x95, 0x88, 0xbc, 0xc0, 0x2b, 0xf1, 0x22, 0xfa, 0x80, 0xef, 0x09,
	0x72, 0xc7, 0x79, 0x42, 0x17, 0x6a, 0x1c, 0xfa, 0x1d, 0x68, 0x2b, 0xf0, 0xb9, 0x44, 0x64, 0x82,
	0xc9, 0x43, 0xc1, 0xf8, 0x3c, 0x48, 0x7a, 0xae, 0x7a, 0x27, 0x39, 0xd3, 0xfa, 0x56, 0x5b, 0xd4,
	0xf6, 0xfd, 0x52, 0xa2, 0x6a, 0x23, 0x22, 0x97, 0xe0, 0x88, 0x29, 0x4d, 0xda, 0xcd, 0xf4, 0x18,
	0xf7, 0x6e, 0xc4, 0x7f, 0x32, 0xac, 0xcd, 0xd7, 0x12, 0x91, 0x29, 0x5e, 0xde, 0x80, 0xec, 0xc4,
	0x18, 0x9c, 0x67, 0x73, 0x80, 0x46, 0xe0, 0x76, 0x6b, 0xd6, 0xb6, 0xfe, 0xd6, 0x0c, 0x22, 0x06,
	0xbe, 0x3b, 0x4f, 0x6d, 0x49, 0x50, 0xc0, 0x3d, 0x83, 0xf0, 0xe2, 0xfe, 0xbf, 0x86, 0x3f, 0xc3,
	0xda, 0x7c, 0xaf, 0xf7, 0x31, 0x6a, 0xbf, 0x8f, 0x51, 0xa7, 0x7d, 0x8c, 0x4e, 0xdb, 0xc7, 0x1f,
	0x25, 0x22, 0x0a, 0x5f, 0x8a, 0x21, 0x91, 0xe9, 0x78, 0xf8, 0x5c, 0x69, 0xb1, 0x1f, 0xb1, 0x3d,
	0xad, 0xc8, 0x4d, 0xf7, 0x99, 0x16, 0x30, 0xd3, 0xb8, 0xd7, 0x92, 0xb4, 0x4d, 0x7f, 0x96, 0x88,
	0xbc, 0x42, 0x78, 0x75, 0x1b, 0x0a, 0x21, 0xf5, 0x26, 0xd7, 0x20, 0xe3, 0x71, 0x22, 0xa1, 0x51,
	0x25, 0xd4, 0x55, 0xd1, 0x03, 0x1b, 0x83, 0xb0, 0x13, 0x6f, 0x3d, 0xde, 0xcd, 0x96, 0xc8, 0x4b,
	0x84, 0x2f, 0x47, 0x50, 0x73, 0x0d, 0xf2, 0x58, 0x81, 0x1c, 0x72, 0x4d, 0x7c, 0x13, 0x75, 0xb0,
	0xc6, 0x82, 0x76, 0xc1, 0xad, 0xc4, 0xfb, 0xd9, 0x12, 0x79, 0x8b, 0xf0, 0x35, 0xf3, 0xa8, 0x04,
	0xdf, 0x65, 0xe6, 0xbd, 0xff, 0x88, 0x29, 0xc5, 0x04, 0xdf, 0x92, 0x22, 0x93, 0xa0, 0x14, 0x19,
	0xf8, 0x9e, 0xad, 0x27, 0xc1, 0xe8, 0xac, 0x77, 0xce, 0xb1, 0x4e, 0x1f, 0x66, 0x4b, 0xc1, 0xf5,
	0x3f, 0x47, 0x87, 0x74, 0x15, 0xaf, 0x38, 0x6f, 0xb2, 0x8d, 0xcd, 0x27, 0xc3, 0x4c, 0xe4, 0x09,
	0xcf, 0xe8, 0xbd, 0x81, 0xd6, 0x34, 0x15, 0xfb, 0x61, 0x75, 0x1d, 0xa5, 0x22, 0x0f, 0x15, 0xc8,
	0x09, 0x4b, 0x41, 0x85, 0xa7, 0x5e, 0x93, 0x4f, 0xcf, 0x55, 0x49, 0xeb, 0x7f, 0x03, 0x00, 0x00,
	0xff, 0xff, 0x4c, 0x43, 0x8b, 0xc3, 0x52, 0x07, 0x00, 0x00,
}
