// Code generated by protoc-gen-go. DO NOT EDIT.
// source: push-notification/v2/push-funnel.proto

package push_notification // import "golang.52tt.com/protocol/services/push-notification/v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type METRICS_SOURCE int32

const (
	METRICS_SOURCE_PUSH_V2         METRICS_SOURCE = 0
	METRICS_SOURCE_PUSH_CAST       METRICS_SOURCE = 1
	METRICS_SOURCE_PUSH_AGENT      METRICS_SOURCE = 2
	METRICS_SOURCE_PUSH_PROXY      METRICS_SOURCE = 3
	METRICS_SOURCE_GA_PROXY_NOTIFY METRICS_SOURCE = 4
)

var METRICS_SOURCE_name = map[int32]string{
	0: "PUSH_V2",
	1: "PUSH_CAST",
	2: "PUSH_AGENT",
	3: "PUSH_PROXY",
	4: "GA_PROXY_NOTIFY",
}
var METRICS_SOURCE_value = map[string]int32{
	"PUSH_V2":         0,
	"PUSH_CAST":       1,
	"PUSH_AGENT":      2,
	"PUSH_PROXY":      3,
	"GA_PROXY_NOTIFY": 4,
}

func (x METRICS_SOURCE) String() string {
	return proto.EnumName(METRICS_SOURCE_name, int32(x))
}
func (METRICS_SOURCE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{0}
}

type V2_AGENT_STAGE int32

const (
	V2_AGENT_STAGE_V2AgentReceive     V2_AGENT_STAGE = 0
	V2_AGENT_STAGE_V2AgentSkipped     V2_AGENT_STAGE = 1
	V2_AGENT_STAGE_V2AgentBroken      V2_AGENT_STAGE = 2
	V2_AGENT_STAGE_V2AgentRpcFailure  V2_AGENT_STAGE = 3
	V2_AGENT_STAGE_V2AgentSaveFailure V2_AGENT_STAGE = 4
	V2_AGENT_STAGE_V2AgentSaveSuccess V2_AGENT_STAGE = 5
	V2_AGENT_STAGE_V2AgentFailure     V2_AGENT_STAGE = 6
	V2_AGENT_STAGE_V2AgentSuccess     V2_AGENT_STAGE = 7
	V2_AGENT_STAGE_V2AgentStageMax    V2_AGENT_STAGE = 8
)

var V2_AGENT_STAGE_name = map[int32]string{
	0: "V2AgentReceive",
	1: "V2AgentSkipped",
	2: "V2AgentBroken",
	3: "V2AgentRpcFailure",
	4: "V2AgentSaveFailure",
	5: "V2AgentSaveSuccess",
	6: "V2AgentFailure",
	7: "V2AgentSuccess",
	8: "V2AgentStageMax",
}
var V2_AGENT_STAGE_value = map[string]int32{
	"V2AgentReceive":     0,
	"V2AgentSkipped":     1,
	"V2AgentBroken":      2,
	"V2AgentRpcFailure":  3,
	"V2AgentSaveFailure": 4,
	"V2AgentSaveSuccess": 5,
	"V2AgentFailure":     6,
	"V2AgentSuccess":     7,
	"V2AgentStageMax":    8,
}

func (x V2_AGENT_STAGE) String() string {
	return proto.EnumName(V2_AGENT_STAGE_name, int32(x))
}
func (V2_AGENT_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{1}
}

type V2_PROXY_STAGE int32

const (
	V2_PROXY_STAGE_V2ProxyReceive  V2_PROXY_STAGE = 0
	V2_PROXY_STAGE_V2ProxyFailure  V2_PROXY_STAGE = 1
	V2_PROXY_STAGE_V2ProxySuccess  V2_PROXY_STAGE = 2
	V2_PROXY_STAGE_V2ProxyStageMax V2_PROXY_STAGE = 3
)

var V2_PROXY_STAGE_name = map[int32]string{
	0: "V2ProxyReceive",
	1: "V2ProxyFailure",
	2: "V2ProxySuccess",
	3: "V2ProxyStageMax",
}
var V2_PROXY_STAGE_value = map[string]int32{
	"V2ProxyReceive":  0,
	"V2ProxyFailure":  1,
	"V2ProxySuccess":  2,
	"V2ProxyStageMax": 3,
}

func (x V2_PROXY_STAGE) String() string {
	return proto.EnumName(V2_PROXY_STAGE_name, int32(x))
}
func (V2_PROXY_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{2}
}

type CAST_AGENT_STAGE int32

const (
	CAST_AGENT_STAGE_CastAgentReceive    CAST_AGENT_STAGE = 0
	CAST_AGENT_STAGE_CastAgentSkipped    CAST_AGENT_STAGE = 1
	CAST_AGENT_STAGE_CastAgentDropped    CAST_AGENT_STAGE = 2
	CAST_AGENT_STAGE_CastAgentBroken     CAST_AGENT_STAGE = 3
	CAST_AGENT_STAGE_CastAgentRpcFailure CAST_AGENT_STAGE = 4
	CAST_AGENT_STAGE_CastAgentFailure    CAST_AGENT_STAGE = 5
	CAST_AGENT_STAGE_CastAgentSuccess    CAST_AGENT_STAGE = 6
	CAST_AGENT_STAGE_CastAgentStageMax   CAST_AGENT_STAGE = 7
)

var CAST_AGENT_STAGE_name = map[int32]string{
	0: "CastAgentReceive",
	1: "CastAgentSkipped",
	2: "CastAgentDropped",
	3: "CastAgentBroken",
	4: "CastAgentRpcFailure",
	5: "CastAgentFailure",
	6: "CastAgentSuccess",
	7: "CastAgentStageMax",
}
var CAST_AGENT_STAGE_value = map[string]int32{
	"CastAgentReceive":    0,
	"CastAgentSkipped":    1,
	"CastAgentDropped":    2,
	"CastAgentBroken":     3,
	"CastAgentRpcFailure": 4,
	"CastAgentFailure":    5,
	"CastAgentSuccess":    6,
	"CastAgentStageMax":   7,
}

func (x CAST_AGENT_STAGE) String() string {
	return proto.EnumName(CAST_AGENT_STAGE_name, int32(x))
}
func (CAST_AGENT_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{3}
}

type AGENT_RMQ_STAGE int32

const (
	AGENT_RMQ_STAGE_AgentRMQReceive  AGENT_RMQ_STAGE = 0
	AGENT_RMQ_STAGE_AgentRMQLost     AGENT_RMQ_STAGE = 1
	AGENT_RMQ_STAGE_AgentRMQTransmit AGENT_RMQ_STAGE = 2
	AGENT_RMQ_STAGE_AgentRMQStageMax AGENT_RMQ_STAGE = 3
)

var AGENT_RMQ_STAGE_name = map[int32]string{
	0: "AgentRMQReceive",
	1: "AgentRMQLost",
	2: "AgentRMQTransmit",
	3: "AgentRMQStageMax",
}
var AGENT_RMQ_STAGE_value = map[string]int32{
	"AgentRMQReceive":  0,
	"AgentRMQLost":     1,
	"AgentRMQTransmit": 2,
	"AgentRMQStageMax": 3,
}

func (x AGENT_RMQ_STAGE) String() string {
	return proto.EnumName(AGENT_RMQ_STAGE_name, int32(x))
}
func (AGENT_RMQ_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{4}
}

type AGENT_RDS_STAGE int32

const (
	AGENT_RDS_STAGE_AgentRDSReceive  AGENT_RDS_STAGE = 0
	AGENT_RDS_STAGE_AgentRDSLost     AGENT_RDS_STAGE = 1
	AGENT_RDS_STAGE_AgentRDSTransmit AGENT_RDS_STAGE = 2
	AGENT_RDS_STAGE_AgentRDSStageMax AGENT_RDS_STAGE = 3
)

var AGENT_RDS_STAGE_name = map[int32]string{
	0: "AgentRDSReceive",
	1: "AgentRDSLost",
	2: "AgentRDSTransmit",
	3: "AgentRDSStageMax",
}
var AGENT_RDS_STAGE_value = map[string]int32{
	"AgentRDSReceive":  0,
	"AgentRDSLost":     1,
	"AgentRDSTransmit": 2,
	"AgentRDSStageMax": 3,
}

func (x AGENT_RDS_STAGE) String() string {
	return proto.EnumName(AGENT_RDS_STAGE_name, int32(x))
}
func (AGENT_RDS_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{5}
}

type PROXY_RMQ_STAGE int32

const (
	PROXY_RMQ_STAGE_ProxyRMQReceive  PROXY_RMQ_STAGE = 0
	PROXY_RMQ_STAGE_ProxyRMQLost     PROXY_RMQ_STAGE = 1
	PROXY_RMQ_STAGE_ProxyRMQTransmit PROXY_RMQ_STAGE = 2
	PROXY_RMQ_STAGE_ProxyRMQStageMax PROXY_RMQ_STAGE = 3
)

var PROXY_RMQ_STAGE_name = map[int32]string{
	0: "ProxyRMQReceive",
	1: "ProxyRMQLost",
	2: "ProxyRMQTransmit",
	3: "ProxyRMQStageMax",
}
var PROXY_RMQ_STAGE_value = map[string]int32{
	"ProxyRMQReceive":  0,
	"ProxyRMQLost":     1,
	"ProxyRMQTransmit": 2,
	"ProxyRMQStageMax": 3,
}

func (x PROXY_RMQ_STAGE) String() string {
	return proto.EnumName(PROXY_RMQ_STAGE_name, int32(x))
}
func (PROXY_RMQ_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{6}
}

type GAPROXY_RMQ_STAGE int32

const (
	GAPROXY_RMQ_STAGE_GaProxyRMQReceive  GAPROXY_RMQ_STAGE = 0
	GAPROXY_RMQ_STAGE_GaProxyRMQLost     GAPROXY_RMQ_STAGE = 1
	GAPROXY_RMQ_STAGE_GaProxyRMQTransmit GAPROXY_RMQ_STAGE = 2
	GAPROXY_RMQ_STAGE_GaProxyRMQStageMax GAPROXY_RMQ_STAGE = 3
)

var GAPROXY_RMQ_STAGE_name = map[int32]string{
	0: "GaProxyRMQReceive",
	1: "GaProxyRMQLost",
	2: "GaProxyRMQTransmit",
	3: "GaProxyRMQStageMax",
}
var GAPROXY_RMQ_STAGE_value = map[string]int32{
	"GaProxyRMQReceive":  0,
	"GaProxyRMQLost":     1,
	"GaProxyRMQTransmit": 2,
	"GaProxyRMQStageMax": 3,
}

func (x GAPROXY_RMQ_STAGE) String() string {
	return proto.EnumName(GAPROXY_RMQ_STAGE_name, int32(x))
}
func (GAPROXY_RMQ_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{7}
}

type GAPROXY_RDS_STAGE int32

const (
	GAPROXY_RDS_STAGE_GaProxyRDSReceive  GAPROXY_RDS_STAGE = 0
	GAPROXY_RDS_STAGE_GaProxyRDSLost     GAPROXY_RDS_STAGE = 1
	GAPROXY_RDS_STAGE_GaProxyRDSTransmit GAPROXY_RDS_STAGE = 2
	GAPROXY_RDS_STAGE_GaProxyRDSStageMax GAPROXY_RDS_STAGE = 3
)

var GAPROXY_RDS_STAGE_name = map[int32]string{
	0: "GaProxyRDSReceive",
	1: "GaProxyRDSLost",
	2: "GaProxyRDSTransmit",
	3: "GaProxyRDSStageMax",
}
var GAPROXY_RDS_STAGE_value = map[string]int32{
	"GaProxyRDSReceive":  0,
	"GaProxyRDSLost":     1,
	"GaProxyRDSTransmit": 2,
	"GaProxyRDSStageMax": 3,
}

func (x GAPROXY_RDS_STAGE) String() string {
	return proto.EnumName(GAPROXY_RDS_STAGE_name, int32(x))
}
func (GAPROXY_RDS_STAGE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{8}
}

// 注册漏斗模型在线推送业务类型 <PUSH_TYPE_MAX
type PUSH_TYPE int32

const (
	PUSH_TYPE_UNKNOWN  PUSH_TYPE = 0
	PUSH_TYPE_TYPE_MAX PUSH_TYPE = 65535
)

var PUSH_TYPE_name = map[int32]string{
	0:     "UNKNOWN",
	65535: "TYPE_MAX",
}
var PUSH_TYPE_value = map[string]int32{
	"UNKNOWN":  0,
	"TYPE_MAX": 65535,
}

func (x PUSH_TYPE) String() string {
	return proto.EnumName(PUSH_TYPE_name, int32(x))
}
func (PUSH_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{9}
}

type QueryFunnelDataReq struct {
	PushTypeList         []uint32 `protobuf:"varint,1,rep,packed,name=push_type_list,json=pushTypeList,proto3" json:"push_type_list,omitempty"`
	SceneType            string   `protobuf:"bytes,2,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	PushChannel          string   `protobuf:"bytes,3,opt,name=push_channel,json=pushChannel,proto3" json:"push_channel,omitempty"`
	UnixTime             int64    `protobuf:"varint,4,opt,name=unix_time,json=unixTime,proto3" json:"unix_time,omitempty"`
	PushLabelList        []string `protobuf:"bytes,5,rep,name=push_label_list,json=pushLabelList,proto3" json:"push_label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryFunnelDataReq) Reset()         { *m = QueryFunnelDataReq{} }
func (m *QueryFunnelDataReq) String() string { return proto.CompactTextString(m) }
func (*QueryFunnelDataReq) ProtoMessage()    {}
func (*QueryFunnelDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{0}
}
func (m *QueryFunnelDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryFunnelDataReq.Unmarshal(m, b)
}
func (m *QueryFunnelDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryFunnelDataReq.Marshal(b, m, deterministic)
}
func (dst *QueryFunnelDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryFunnelDataReq.Merge(dst, src)
}
func (m *QueryFunnelDataReq) XXX_Size() int {
	return xxx_messageInfo_QueryFunnelDataReq.Size(m)
}
func (m *QueryFunnelDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryFunnelDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryFunnelDataReq proto.InternalMessageInfo

func (m *QueryFunnelDataReq) GetPushTypeList() []uint32 {
	if m != nil {
		return m.PushTypeList
	}
	return nil
}

func (m *QueryFunnelDataReq) GetSceneType() string {
	if m != nil {
		return m.SceneType
	}
	return ""
}

func (m *QueryFunnelDataReq) GetPushChannel() string {
	if m != nil {
		return m.PushChannel
	}
	return ""
}

func (m *QueryFunnelDataReq) GetUnixTime() int64 {
	if m != nil {
		return m.UnixTime
	}
	return 0
}

func (m *QueryFunnelDataReq) GetPushLabelList() []string {
	if m != nil {
		return m.PushLabelList
	}
	return nil
}

type FunnelStep struct {
	Stage                string   `protobuf:"bytes,1,opt,name=stage,proto3" json:"stage,omitempty"`
	Count                int64    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FunnelStep) Reset()         { *m = FunnelStep{} }
func (m *FunnelStep) String() string { return proto.CompactTextString(m) }
func (*FunnelStep) ProtoMessage()    {}
func (*FunnelStep) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{1}
}
func (m *FunnelStep) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FunnelStep.Unmarshal(m, b)
}
func (m *FunnelStep) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FunnelStep.Marshal(b, m, deterministic)
}
func (dst *FunnelStep) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FunnelStep.Merge(dst, src)
}
func (m *FunnelStep) XXX_Size() int {
	return xxx_messageInfo_FunnelStep.Size(m)
}
func (m *FunnelStep) XXX_DiscardUnknown() {
	xxx_messageInfo_FunnelStep.DiscardUnknown(m)
}

var xxx_messageInfo_FunnelStep proto.InternalMessageInfo

func (m *FunnelStep) GetStage() string {
	if m != nil {
		return m.Stage
	}
	return ""
}

func (m *FunnelStep) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type QueryFunnelDataResp struct {
	AgentChannel         []*FunnelStep `protobuf:"bytes,1,rep,name=agent_channel,json=agentChannel,proto3" json:"agent_channel,omitempty"`
	ProxyChannel         []*FunnelStep `protobuf:"bytes,2,rep,name=proxy_channel,json=proxyChannel,proto3" json:"proxy_channel,omitempty"`
	CastChannel          []*FunnelStep `protobuf:"bytes,3,rep,name=cast_channel,json=castChannel,proto3" json:"cast_channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QueryFunnelDataResp) Reset()         { *m = QueryFunnelDataResp{} }
func (m *QueryFunnelDataResp) String() string { return proto.CompactTextString(m) }
func (*QueryFunnelDataResp) ProtoMessage()    {}
func (*QueryFunnelDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{2}
}
func (m *QueryFunnelDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryFunnelDataResp.Unmarshal(m, b)
}
func (m *QueryFunnelDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryFunnelDataResp.Marshal(b, m, deterministic)
}
func (dst *QueryFunnelDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryFunnelDataResp.Merge(dst, src)
}
func (m *QueryFunnelDataResp) XXX_Size() int {
	return xxx_messageInfo_QueryFunnelDataResp.Size(m)
}
func (m *QueryFunnelDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryFunnelDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryFunnelDataResp proto.InternalMessageInfo

func (m *QueryFunnelDataResp) GetAgentChannel() []*FunnelStep {
	if m != nil {
		return m.AgentChannel
	}
	return nil
}

func (m *QueryFunnelDataResp) GetProxyChannel() []*FunnelStep {
	if m != nil {
		return m.ProxyChannel
	}
	return nil
}

func (m *QueryFunnelDataResp) GetCastChannel() []*FunnelStep {
	if m != nil {
		return m.CastChannel
	}
	return nil
}

type PushTypeMap struct {
	Stats                map[uint32]*StageMap `protobuf:"bytes,1,rep,name=stats,proto3" json:"stats,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	StatsV2              map[string]*StageMap `protobuf:"bytes,2,rep,name=stats_v2,json=statsV2,proto3" json:"stats_v2,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PushTypeMap) Reset()         { *m = PushTypeMap{} }
func (m *PushTypeMap) String() string { return proto.CompactTextString(m) }
func (*PushTypeMap) ProtoMessage()    {}
func (*PushTypeMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{3}
}
func (m *PushTypeMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushTypeMap.Unmarshal(m, b)
}
func (m *PushTypeMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushTypeMap.Marshal(b, m, deterministic)
}
func (dst *PushTypeMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushTypeMap.Merge(dst, src)
}
func (m *PushTypeMap) XXX_Size() int {
	return xxx_messageInfo_PushTypeMap.Size(m)
}
func (m *PushTypeMap) XXX_DiscardUnknown() {
	xxx_messageInfo_PushTypeMap.DiscardUnknown(m)
}

var xxx_messageInfo_PushTypeMap proto.InternalMessageInfo

func (m *PushTypeMap) GetStats() map[uint32]*StageMap {
	if m != nil {
		return m.Stats
	}
	return nil
}

func (m *PushTypeMap) GetStatsV2() map[string]*StageMap {
	if m != nil {
		return m.StatsV2
	}
	return nil
}

type StageMap struct {
	Count                map[uint32]int64 `protobuf:"bytes,1,rep,name=count,proto3" json:"count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *StageMap) Reset()         { *m = StageMap{} }
func (m *StageMap) String() string { return proto.CompactTextString(m) }
func (*StageMap) ProtoMessage()    {}
func (*StageMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{4}
}
func (m *StageMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StageMap.Unmarshal(m, b)
}
func (m *StageMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StageMap.Marshal(b, m, deterministic)
}
func (dst *StageMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StageMap.Merge(dst, src)
}
func (m *StageMap) XXX_Size() int {
	return xxx_messageInfo_StageMap.Size(m)
}
func (m *StageMap) XXX_DiscardUnknown() {
	xxx_messageInfo_StageMap.DiscardUnknown(m)
}

var xxx_messageInfo_StageMap proto.InternalMessageInfo

func (m *StageMap) GetCount() map[uint32]int64 {
	if m != nil {
		return m.Count
	}
	return nil
}

type PushV2Metrics struct {
	ChAgent              *PushTypeMap `protobuf:"bytes,1,opt,name=ch_agent,json=chAgent,proto3" json:"ch_agent,omitempty"`
	UnAgent              *PushTypeMap `protobuf:"bytes,2,opt,name=un_agent,json=unAgent,proto3" json:"un_agent,omitempty"`
	BcAgent              *PushTypeMap `protobuf:"bytes,3,opt,name=bc_agent,json=bcAgent,proto3" json:"bc_agent,omitempty"`
	DcAgent              *PushTypeMap `protobuf:"bytes,4,opt,name=dc_agent,json=dcAgent,proto3" json:"dc_agent,omitempty"`
	GpAgent              *PushTypeMap `protobuf:"bytes,5,opt,name=gp_agent,json=gpAgent,proto3" json:"gp_agent,omitempty"`
	ChProxy              *PushTypeMap `protobuf:"bytes,6,opt,name=ch_proxy,json=chProxy,proto3" json:"ch_proxy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushV2Metrics) Reset()         { *m = PushV2Metrics{} }
func (m *PushV2Metrics) String() string { return proto.CompactTextString(m) }
func (*PushV2Metrics) ProtoMessage()    {}
func (*PushV2Metrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{5}
}
func (m *PushV2Metrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushV2Metrics.Unmarshal(m, b)
}
func (m *PushV2Metrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushV2Metrics.Marshal(b, m, deterministic)
}
func (dst *PushV2Metrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushV2Metrics.Merge(dst, src)
}
func (m *PushV2Metrics) XXX_Size() int {
	return xxx_messageInfo_PushV2Metrics.Size(m)
}
func (m *PushV2Metrics) XXX_DiscardUnknown() {
	xxx_messageInfo_PushV2Metrics.DiscardUnknown(m)
}

var xxx_messageInfo_PushV2Metrics proto.InternalMessageInfo

func (m *PushV2Metrics) GetChAgent() *PushTypeMap {
	if m != nil {
		return m.ChAgent
	}
	return nil
}

func (m *PushV2Metrics) GetUnAgent() *PushTypeMap {
	if m != nil {
		return m.UnAgent
	}
	return nil
}

func (m *PushV2Metrics) GetBcAgent() *PushTypeMap {
	if m != nil {
		return m.BcAgent
	}
	return nil
}

func (m *PushV2Metrics) GetDcAgent() *PushTypeMap {
	if m != nil {
		return m.DcAgent
	}
	return nil
}

func (m *PushV2Metrics) GetGpAgent() *PushTypeMap {
	if m != nil {
		return m.GpAgent
	}
	return nil
}

func (m *PushV2Metrics) GetChProxy() *PushTypeMap {
	if m != nil {
		return m.ChProxy
	}
	return nil
}

type PushCastMetrics struct {
	ChCast               *PushTypeMap `protobuf:"bytes,1,opt,name=ch_cast,json=chCast,proto3" json:"ch_cast,omitempty"`
	UnCast               *PushTypeMap `protobuf:"bytes,2,opt,name=un_cast,json=unCast,proto3" json:"un_cast,omitempty"`
	BcCast               *PushTypeMap `protobuf:"bytes,3,opt,name=bc_cast,json=bcCast,proto3" json:"bc_cast,omitempty"`
	DcCast               *PushTypeMap `protobuf:"bytes,4,opt,name=dc_cast,json=dcCast,proto3" json:"dc_cast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushCastMetrics) Reset()         { *m = PushCastMetrics{} }
func (m *PushCastMetrics) String() string { return proto.CompactTextString(m) }
func (*PushCastMetrics) ProtoMessage()    {}
func (*PushCastMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{6}
}
func (m *PushCastMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushCastMetrics.Unmarshal(m, b)
}
func (m *PushCastMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushCastMetrics.Marshal(b, m, deterministic)
}
func (dst *PushCastMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushCastMetrics.Merge(dst, src)
}
func (m *PushCastMetrics) XXX_Size() int {
	return xxx_messageInfo_PushCastMetrics.Size(m)
}
func (m *PushCastMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_PushCastMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_PushCastMetrics proto.InternalMessageInfo

func (m *PushCastMetrics) GetChCast() *PushTypeMap {
	if m != nil {
		return m.ChCast
	}
	return nil
}

func (m *PushCastMetrics) GetUnCast() *PushTypeMap {
	if m != nil {
		return m.UnCast
	}
	return nil
}

func (m *PushCastMetrics) GetBcCast() *PushTypeMap {
	if m != nil {
		return m.BcCast
	}
	return nil
}

func (m *PushCastMetrics) GetDcCast() *PushTypeMap {
	if m != nil {
		return m.DcCast
	}
	return nil
}

type PushAgentMetrics struct {
	ChRmq                *PushTypeMap `protobuf:"bytes,1,opt,name=ch_rmq,json=chRmq,proto3" json:"ch_rmq,omitempty"`
	UnRmq                *PushTypeMap `protobuf:"bytes,2,opt,name=un_rmq,json=unRmq,proto3" json:"un_rmq,omitempty"`
	BcRmq                *PushTypeMap `protobuf:"bytes,3,opt,name=bc_rmq,json=bcRmq,proto3" json:"bc_rmq,omitempty"`
	DcRmq                *PushTypeMap `protobuf:"bytes,4,opt,name=dc_rmq,json=dcRmq,proto3" json:"dc_rmq,omitempty"`
	GpRmq                *PushTypeMap `protobuf:"bytes,5,opt,name=gp_rmq,json=gpRmq,proto3" json:"gp_rmq,omitempty"`
	ChRds                *PushTypeMap `protobuf:"bytes,6,opt,name=ch_rds,json=chRds,proto3" json:"ch_rds,omitempty"`
	UnRds                *PushTypeMap `protobuf:"bytes,7,opt,name=un_rds,json=unRds,proto3" json:"un_rds,omitempty"`
	BcRds                *PushTypeMap `protobuf:"bytes,8,opt,name=bc_rds,json=bcRds,proto3" json:"bc_rds,omitempty"`
	DcRds                *PushTypeMap `protobuf:"bytes,9,opt,name=dc_rds,json=dcRds,proto3" json:"dc_rds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushAgentMetrics) Reset()         { *m = PushAgentMetrics{} }
func (m *PushAgentMetrics) String() string { return proto.CompactTextString(m) }
func (*PushAgentMetrics) ProtoMessage()    {}
func (*PushAgentMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{7}
}
func (m *PushAgentMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushAgentMetrics.Unmarshal(m, b)
}
func (m *PushAgentMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushAgentMetrics.Marshal(b, m, deterministic)
}
func (dst *PushAgentMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushAgentMetrics.Merge(dst, src)
}
func (m *PushAgentMetrics) XXX_Size() int {
	return xxx_messageInfo_PushAgentMetrics.Size(m)
}
func (m *PushAgentMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_PushAgentMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_PushAgentMetrics proto.InternalMessageInfo

func (m *PushAgentMetrics) GetChRmq() *PushTypeMap {
	if m != nil {
		return m.ChRmq
	}
	return nil
}

func (m *PushAgentMetrics) GetUnRmq() *PushTypeMap {
	if m != nil {
		return m.UnRmq
	}
	return nil
}

func (m *PushAgentMetrics) GetBcRmq() *PushTypeMap {
	if m != nil {
		return m.BcRmq
	}
	return nil
}

func (m *PushAgentMetrics) GetDcRmq() *PushTypeMap {
	if m != nil {
		return m.DcRmq
	}
	return nil
}

func (m *PushAgentMetrics) GetGpRmq() *PushTypeMap {
	if m != nil {
		return m.GpRmq
	}
	return nil
}

func (m *PushAgentMetrics) GetChRds() *PushTypeMap {
	if m != nil {
		return m.ChRds
	}
	return nil
}

func (m *PushAgentMetrics) GetUnRds() *PushTypeMap {
	if m != nil {
		return m.UnRds
	}
	return nil
}

func (m *PushAgentMetrics) GetBcRds() *PushTypeMap {
	if m != nil {
		return m.BcRds
	}
	return nil
}

func (m *PushAgentMetrics) GetDcRds() *PushTypeMap {
	if m != nil {
		return m.DcRds
	}
	return nil
}

type PushProxyMetrics struct {
	ChRmq                *PushTypeMap `protobuf:"bytes,1,opt,name=ch_rmq,json=chRmq,proto3" json:"ch_rmq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PushProxyMetrics) Reset()         { *m = PushProxyMetrics{} }
func (m *PushProxyMetrics) String() string { return proto.CompactTextString(m) }
func (*PushProxyMetrics) ProtoMessage()    {}
func (*PushProxyMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{8}
}
func (m *PushProxyMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushProxyMetrics.Unmarshal(m, b)
}
func (m *PushProxyMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushProxyMetrics.Marshal(b, m, deterministic)
}
func (dst *PushProxyMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushProxyMetrics.Merge(dst, src)
}
func (m *PushProxyMetrics) XXX_Size() int {
	return xxx_messageInfo_PushProxyMetrics.Size(m)
}
func (m *PushProxyMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_PushProxyMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_PushProxyMetrics proto.InternalMessageInfo

func (m *PushProxyMetrics) GetChRmq() *PushTypeMap {
	if m != nil {
		return m.ChRmq
	}
	return nil
}

type GaProxyNotifyMetrics struct {
	ChRmq                *PushTypeMap `protobuf:"bytes,1,opt,name=ch_rmq,json=chRmq,proto3" json:"ch_rmq,omitempty"`
	UnRmq                *PushTypeMap `protobuf:"bytes,2,opt,name=un_rmq,json=unRmq,proto3" json:"un_rmq,omitempty"`
	BcRmq                *PushTypeMap `protobuf:"bytes,3,opt,name=bc_rmq,json=bcRmq,proto3" json:"bc_rmq,omitempty"`
	DcRmq                *PushTypeMap `protobuf:"bytes,4,opt,name=dc_rmq,json=dcRmq,proto3" json:"dc_rmq,omitempty"`
	GpRmq                *PushTypeMap `protobuf:"bytes,5,opt,name=gp_rmq,json=gpRmq,proto3" json:"gp_rmq,omitempty"`
	ChRds                *PushTypeMap `protobuf:"bytes,6,opt,name=ch_rds,json=chRds,proto3" json:"ch_rds,omitempty"`
	UnRds                *PushTypeMap `protobuf:"bytes,7,opt,name=un_rds,json=unRds,proto3" json:"un_rds,omitempty"`
	BcRds                *PushTypeMap `protobuf:"bytes,8,opt,name=bc_rds,json=bcRds,proto3" json:"bc_rds,omitempty"`
	DcRds                *PushTypeMap `protobuf:"bytes,9,opt,name=dc_rds,json=dcRds,proto3" json:"dc_rds,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GaProxyNotifyMetrics) Reset()         { *m = GaProxyNotifyMetrics{} }
func (m *GaProxyNotifyMetrics) String() string { return proto.CompactTextString(m) }
func (*GaProxyNotifyMetrics) ProtoMessage()    {}
func (*GaProxyNotifyMetrics) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{9}
}
func (m *GaProxyNotifyMetrics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GaProxyNotifyMetrics.Unmarshal(m, b)
}
func (m *GaProxyNotifyMetrics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GaProxyNotifyMetrics.Marshal(b, m, deterministic)
}
func (dst *GaProxyNotifyMetrics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GaProxyNotifyMetrics.Merge(dst, src)
}
func (m *GaProxyNotifyMetrics) XXX_Size() int {
	return xxx_messageInfo_GaProxyNotifyMetrics.Size(m)
}
func (m *GaProxyNotifyMetrics) XXX_DiscardUnknown() {
	xxx_messageInfo_GaProxyNotifyMetrics.DiscardUnknown(m)
}

var xxx_messageInfo_GaProxyNotifyMetrics proto.InternalMessageInfo

func (m *GaProxyNotifyMetrics) GetChRmq() *PushTypeMap {
	if m != nil {
		return m.ChRmq
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetUnRmq() *PushTypeMap {
	if m != nil {
		return m.UnRmq
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetBcRmq() *PushTypeMap {
	if m != nil {
		return m.BcRmq
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetDcRmq() *PushTypeMap {
	if m != nil {
		return m.DcRmq
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetGpRmq() *PushTypeMap {
	if m != nil {
		return m.GpRmq
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetChRds() *PushTypeMap {
	if m != nil {
		return m.ChRds
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetUnRds() *PushTypeMap {
	if m != nil {
		return m.UnRds
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetBcRds() *PushTypeMap {
	if m != nil {
		return m.BcRds
	}
	return nil
}

func (m *GaProxyNotifyMetrics) GetDcRds() *PushTypeMap {
	if m != nil {
		return m.DcRds
	}
	return nil
}

type GroupPushDetailData struct {
	Category             string   `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Important            bool     `protobuf:"varint,2,opt,name=important,proto3" json:"important,omitempty"`
	Sequence             uint32   `protobuf:"varint,3,opt,name=sequence,proto3" json:"sequence,omitempty"`
	GroupId              uint32   `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupType            uint32   `protobuf:"varint,5,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	RequestId            string   `protobuf:"bytes,6,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	PushType             uint32   `protobuf:"varint,7,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	ServiceName          string   `protobuf:"bytes,8,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	PushChannel          string   `protobuf:"bytes,9,opt,name=push_channel,json=pushChannel,proto3" json:"push_channel,omitempty"`
	HandleStage          uint32   `protobuf:"varint,10,opt,name=handle_stage,json=handleStage,proto3" json:"handle_stage,omitempty"`
	TimeStamp            int64    `protobuf:"varint,11,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	Describe             string   `protobuf:"bytes,12,opt,name=describe,proto3" json:"describe,omitempty"`
	DestUserId           uint32   `protobuf:"varint,13,opt,name=dest_user_id,json=destUserId,proto3" json:"dest_user_id,omitempty"`
	PushLabel            string   `protobuf:"bytes,14,opt,name=push_label,json=pushLabel,proto3" json:"push_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupPushDetailData) Reset()         { *m = GroupPushDetailData{} }
func (m *GroupPushDetailData) String() string { return proto.CompactTextString(m) }
func (*GroupPushDetailData) ProtoMessage()    {}
func (*GroupPushDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{10}
}
func (m *GroupPushDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupPushDetailData.Unmarshal(m, b)
}
func (m *GroupPushDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupPushDetailData.Marshal(b, m, deterministic)
}
func (dst *GroupPushDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupPushDetailData.Merge(dst, src)
}
func (m *GroupPushDetailData) XXX_Size() int {
	return xxx_messageInfo_GroupPushDetailData.Size(m)
}
func (m *GroupPushDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupPushDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_GroupPushDetailData proto.InternalMessageInfo

func (m *GroupPushDetailData) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *GroupPushDetailData) GetImportant() bool {
	if m != nil {
		return m.Important
	}
	return false
}

func (m *GroupPushDetailData) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *GroupPushDetailData) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupPushDetailData) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GroupPushDetailData) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *GroupPushDetailData) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *GroupPushDetailData) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *GroupPushDetailData) GetPushChannel() string {
	if m != nil {
		return m.PushChannel
	}
	return ""
}

func (m *GroupPushDetailData) GetHandleStage() uint32 {
	if m != nil {
		return m.HandleStage
	}
	return 0
}

func (m *GroupPushDetailData) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *GroupPushDetailData) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *GroupPushDetailData) GetDestUserId() uint32 {
	if m != nil {
		return m.DestUserId
	}
	return 0
}

func (m *GroupPushDetailData) GetPushLabel() string {
	if m != nil {
		return m.PushLabel
	}
	return ""
}

type ChanPushDetailData struct {
	Category             string   `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Important            bool     `protobuf:"varint,2,opt,name=important,proto3" json:"important,omitempty"`
	Sequence             uint32   `protobuf:"varint,3,opt,name=sequence,proto3" json:"sequence,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RequestId            string   `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	PushType             uint32   `protobuf:"varint,6,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	ServiceName          string   `protobuf:"bytes,7,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	PushChannel          string   `protobuf:"bytes,8,opt,name=push_channel,json=pushChannel,proto3" json:"push_channel,omitempty"`
	HandleStage          uint32   `protobuf:"varint,9,opt,name=handle_stage,json=handleStage,proto3" json:"handle_stage,omitempty"`
	TimeStamp            int64    `protobuf:"varint,10,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	Describe             string   `protobuf:"bytes,11,opt,name=describe,proto3" json:"describe,omitempty"`
	DestUserId           uint32   `protobuf:"varint,12,opt,name=dest_user_id,json=destUserId,proto3" json:"dest_user_id,omitempty"`
	PushLabel            string   `protobuf:"bytes,13,opt,name=push_label,json=pushLabel,proto3" json:"push_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChanPushDetailData) Reset()         { *m = ChanPushDetailData{} }
func (m *ChanPushDetailData) String() string { return proto.CompactTextString(m) }
func (*ChanPushDetailData) ProtoMessage()    {}
func (*ChanPushDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{11}
}
func (m *ChanPushDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChanPushDetailData.Unmarshal(m, b)
}
func (m *ChanPushDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChanPushDetailData.Marshal(b, m, deterministic)
}
func (dst *ChanPushDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChanPushDetailData.Merge(dst, src)
}
func (m *ChanPushDetailData) XXX_Size() int {
	return xxx_messageInfo_ChanPushDetailData.Size(m)
}
func (m *ChanPushDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_ChanPushDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_ChanPushDetailData proto.InternalMessageInfo

func (m *ChanPushDetailData) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ChanPushDetailData) GetImportant() bool {
	if m != nil {
		return m.Important
	}
	return false
}

func (m *ChanPushDetailData) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *ChanPushDetailData) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChanPushDetailData) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *ChanPushDetailData) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *ChanPushDetailData) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *ChanPushDetailData) GetPushChannel() string {
	if m != nil {
		return m.PushChannel
	}
	return ""
}

func (m *ChanPushDetailData) GetHandleStage() uint32 {
	if m != nil {
		return m.HandleStage
	}
	return 0
}

func (m *ChanPushDetailData) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *ChanPushDetailData) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *ChanPushDetailData) GetDestUserId() uint32 {
	if m != nil {
		return m.DestUserId
	}
	return 0
}

func (m *ChanPushDetailData) GetPushLabel() string {
	if m != nil {
		return m.PushLabel
	}
	return ""
}

type UserPushDetailData struct {
	Category             string   `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Important            bool     `protobuf:"varint,2,opt,name=important,proto3" json:"important,omitempty"`
	Sequence             uint32   `protobuf:"varint,3,opt,name=sequence,proto3" json:"sequence,omitempty"`
	UserIdList           []uint64 `protobuf:"varint,4,rep,packed,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"`
	RequestId            string   `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	PushType             uint32   `protobuf:"varint,6,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	ServiceName          string   `protobuf:"bytes,7,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	PushChannel          string   `protobuf:"bytes,8,opt,name=push_channel,json=pushChannel,proto3" json:"push_channel,omitempty"`
	HandleStage          uint32   `protobuf:"varint,9,opt,name=handle_stage,json=handleStage,proto3" json:"handle_stage,omitempty"`
	TimeStamp            int64    `protobuf:"varint,10,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	Describe             string   `protobuf:"bytes,11,opt,name=describe,proto3" json:"describe,omitempty"`
	DestUserId           uint32   `protobuf:"varint,12,opt,name=dest_user_id,json=destUserId,proto3" json:"dest_user_id,omitempty"`
	PushLabel            string   `protobuf:"bytes,13,opt,name=push_label,json=pushLabel,proto3" json:"push_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPushDetailData) Reset()         { *m = UserPushDetailData{} }
func (m *UserPushDetailData) String() string { return proto.CompactTextString(m) }
func (*UserPushDetailData) ProtoMessage()    {}
func (*UserPushDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{12}
}
func (m *UserPushDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPushDetailData.Unmarshal(m, b)
}
func (m *UserPushDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPushDetailData.Marshal(b, m, deterministic)
}
func (dst *UserPushDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPushDetailData.Merge(dst, src)
}
func (m *UserPushDetailData) XXX_Size() int {
	return xxx_messageInfo_UserPushDetailData.Size(m)
}
func (m *UserPushDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPushDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_UserPushDetailData proto.InternalMessageInfo

func (m *UserPushDetailData) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *UserPushDetailData) GetImportant() bool {
	if m != nil {
		return m.Important
	}
	return false
}

func (m *UserPushDetailData) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *UserPushDetailData) GetUserIdList() []uint64 {
	if m != nil {
		return m.UserIdList
	}
	return nil
}

func (m *UserPushDetailData) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *UserPushDetailData) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *UserPushDetailData) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *UserPushDetailData) GetPushChannel() string {
	if m != nil {
		return m.PushChannel
	}
	return ""
}

func (m *UserPushDetailData) GetHandleStage() uint32 {
	if m != nil {
		return m.HandleStage
	}
	return 0
}

func (m *UserPushDetailData) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *UserPushDetailData) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *UserPushDetailData) GetDestUserId() uint32 {
	if m != nil {
		return m.DestUserId
	}
	return 0
}

func (m *UserPushDetailData) GetPushLabel() string {
	if m != nil {
		return m.PushLabel
	}
	return ""
}

type BroadcastDetailData struct {
	Category             string   `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Important            bool     `protobuf:"varint,2,opt,name=important,proto3" json:"important,omitempty"`
	Sequence             uint32   `protobuf:"varint,3,opt,name=sequence,proto3" json:"sequence,omitempty"`
	BroadcastId          uint32   `protobuf:"varint,4,opt,name=broadcast_id,json=broadcastId,proto3" json:"broadcast_id,omitempty"`
	RequestId            string   `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	PushType             uint32   `protobuf:"varint,6,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	ServiceName          string   `protobuf:"bytes,7,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	PushChannel          string   `protobuf:"bytes,8,opt,name=push_channel,json=pushChannel,proto3" json:"push_channel,omitempty"`
	HandleStage          uint32   `protobuf:"varint,9,opt,name=handle_stage,json=handleStage,proto3" json:"handle_stage,omitempty"`
	TimeStamp            int64    `protobuf:"varint,10,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	Describe             string   `protobuf:"bytes,11,opt,name=describe,proto3" json:"describe,omitempty"`
	DestUserId           uint32   `protobuf:"varint,12,opt,name=dest_user_id,json=destUserId,proto3" json:"dest_user_id,omitempty"`
	PushLabel            string   `protobuf:"bytes,13,opt,name=push_label,json=pushLabel,proto3" json:"push_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BroadcastDetailData) Reset()         { *m = BroadcastDetailData{} }
func (m *BroadcastDetailData) String() string { return proto.CompactTextString(m) }
func (*BroadcastDetailData) ProtoMessage()    {}
func (*BroadcastDetailData) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{13}
}
func (m *BroadcastDetailData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BroadcastDetailData.Unmarshal(m, b)
}
func (m *BroadcastDetailData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BroadcastDetailData.Marshal(b, m, deterministic)
}
func (dst *BroadcastDetailData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BroadcastDetailData.Merge(dst, src)
}
func (m *BroadcastDetailData) XXX_Size() int {
	return xxx_messageInfo_BroadcastDetailData.Size(m)
}
func (m *BroadcastDetailData) XXX_DiscardUnknown() {
	xxx_messageInfo_BroadcastDetailData.DiscardUnknown(m)
}

var xxx_messageInfo_BroadcastDetailData proto.InternalMessageInfo

func (m *BroadcastDetailData) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *BroadcastDetailData) GetImportant() bool {
	if m != nil {
		return m.Important
	}
	return false
}

func (m *BroadcastDetailData) GetSequence() uint32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *BroadcastDetailData) GetBroadcastId() uint32 {
	if m != nil {
		return m.BroadcastId
	}
	return 0
}

func (m *BroadcastDetailData) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *BroadcastDetailData) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *BroadcastDetailData) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *BroadcastDetailData) GetPushChannel() string {
	if m != nil {
		return m.PushChannel
	}
	return ""
}

func (m *BroadcastDetailData) GetHandleStage() uint32 {
	if m != nil {
		return m.HandleStage
	}
	return 0
}

func (m *BroadcastDetailData) GetTimeStamp() int64 {
	if m != nil {
		return m.TimeStamp
	}
	return 0
}

func (m *BroadcastDetailData) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *BroadcastDetailData) GetDestUserId() uint32 {
	if m != nil {
		return m.DestUserId
	}
	return 0
}

func (m *BroadcastDetailData) GetPushLabel() string {
	if m != nil {
		return m.PushLabel
	}
	return ""
}

type MetricsData struct {
	Source               METRICS_SOURCE `protobuf:"varint,1,opt,name=source,proto3,enum=PushNotification.funnel.METRICS_SOURCE" json:"source,omitempty"`
	Payload              []byte         `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MetricsData) Reset()         { *m = MetricsData{} }
func (m *MetricsData) String() string { return proto.CompactTextString(m) }
func (*MetricsData) ProtoMessage()    {}
func (*MetricsData) Descriptor() ([]byte, []int) {
	return fileDescriptor_push_funnel_66378fa2a29096d1, []int{14}
}
func (m *MetricsData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MetricsData.Unmarshal(m, b)
}
func (m *MetricsData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MetricsData.Marshal(b, m, deterministic)
}
func (dst *MetricsData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricsData.Merge(dst, src)
}
func (m *MetricsData) XXX_Size() int {
	return xxx_messageInfo_MetricsData.Size(m)
}
func (m *MetricsData) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricsData.DiscardUnknown(m)
}

var xxx_messageInfo_MetricsData proto.InternalMessageInfo

func (m *MetricsData) GetSource() METRICS_SOURCE {
	if m != nil {
		return m.Source
	}
	return METRICS_SOURCE_PUSH_V2
}

func (m *MetricsData) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func init() {
	proto.RegisterType((*QueryFunnelDataReq)(nil), "PushNotification.funnel.QueryFunnelDataReq")
	proto.RegisterType((*FunnelStep)(nil), "PushNotification.funnel.FunnelStep")
	proto.RegisterType((*QueryFunnelDataResp)(nil), "PushNotification.funnel.QueryFunnelDataResp")
	proto.RegisterType((*PushTypeMap)(nil), "PushNotification.funnel.PushTypeMap")
	proto.RegisterMapType((map[uint32]*StageMap)(nil), "PushNotification.funnel.PushTypeMap.StatsEntry")
	proto.RegisterMapType((map[string]*StageMap)(nil), "PushNotification.funnel.PushTypeMap.StatsV2Entry")
	proto.RegisterType((*StageMap)(nil), "PushNotification.funnel.StageMap")
	proto.RegisterMapType((map[uint32]int64)(nil), "PushNotification.funnel.StageMap.CountEntry")
	proto.RegisterType((*PushV2Metrics)(nil), "PushNotification.funnel.PushV2Metrics")
	proto.RegisterType((*PushCastMetrics)(nil), "PushNotification.funnel.PushCastMetrics")
	proto.RegisterType((*PushAgentMetrics)(nil), "PushNotification.funnel.PushAgentMetrics")
	proto.RegisterType((*PushProxyMetrics)(nil), "PushNotification.funnel.PushProxyMetrics")
	proto.RegisterType((*GaProxyNotifyMetrics)(nil), "PushNotification.funnel.GaProxyNotifyMetrics")
	proto.RegisterType((*GroupPushDetailData)(nil), "PushNotification.funnel.GroupPushDetailData")
	proto.RegisterType((*ChanPushDetailData)(nil), "PushNotification.funnel.ChanPushDetailData")
	proto.RegisterType((*UserPushDetailData)(nil), "PushNotification.funnel.UserPushDetailData")
	proto.RegisterType((*BroadcastDetailData)(nil), "PushNotification.funnel.BroadcastDetailData")
	proto.RegisterType((*MetricsData)(nil), "PushNotification.funnel.MetricsData")
	proto.RegisterEnum("PushNotification.funnel.METRICS_SOURCE", METRICS_SOURCE_name, METRICS_SOURCE_value)
	proto.RegisterEnum("PushNotification.funnel.V2_AGENT_STAGE", V2_AGENT_STAGE_name, V2_AGENT_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.V2_PROXY_STAGE", V2_PROXY_STAGE_name, V2_PROXY_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.CAST_AGENT_STAGE", CAST_AGENT_STAGE_name, CAST_AGENT_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.AGENT_RMQ_STAGE", AGENT_RMQ_STAGE_name, AGENT_RMQ_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.AGENT_RDS_STAGE", AGENT_RDS_STAGE_name, AGENT_RDS_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.PROXY_RMQ_STAGE", PROXY_RMQ_STAGE_name, PROXY_RMQ_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.GAPROXY_RMQ_STAGE", GAPROXY_RMQ_STAGE_name, GAPROXY_RMQ_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.GAPROXY_RDS_STAGE", GAPROXY_RDS_STAGE_name, GAPROXY_RDS_STAGE_value)
	proto.RegisterEnum("PushNotification.funnel.PUSH_TYPE", PUSH_TYPE_name, PUSH_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PushFunnelClient is the client API for PushFunnel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PushFunnelClient interface {
	QueryFunnelData(ctx context.Context, in *QueryFunnelDataReq, opts ...grpc.CallOption) (*QueryFunnelDataResp, error)
}

type pushFunnelClient struct {
	cc *grpc.ClientConn
}

func NewPushFunnelClient(cc *grpc.ClientConn) PushFunnelClient {
	return &pushFunnelClient{cc}
}

func (c *pushFunnelClient) QueryFunnelData(ctx context.Context, in *QueryFunnelDataReq, opts ...grpc.CallOption) (*QueryFunnelDataResp, error) {
	out := new(QueryFunnelDataResp)
	err := c.cc.Invoke(ctx, "/PushNotification.funnel.PushFunnel/QueryFunnelData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushFunnelServer is the server API for PushFunnel service.
type PushFunnelServer interface {
	QueryFunnelData(context.Context, *QueryFunnelDataReq) (*QueryFunnelDataResp, error)
}

func RegisterPushFunnelServer(s *grpc.Server, srv PushFunnelServer) {
	s.RegisterService(&_PushFunnel_serviceDesc, srv)
}

func _PushFunnel_QueryFunnelData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFunnelDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushFunnelServer).QueryFunnelData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/PushNotification.funnel.PushFunnel/QueryFunnelData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushFunnelServer).QueryFunnelData(ctx, req.(*QueryFunnelDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushFunnel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "PushNotification.funnel.PushFunnel",
	HandlerType: (*PushFunnelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryFunnelData",
			Handler:    _PushFunnel_QueryFunnelData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification/v2/push-funnel.proto",
}

func init() {
	proto.RegisterFile("push-notification/v2/push-funnel.proto", fileDescriptor_push_funnel_66378fa2a29096d1)
}

var fileDescriptor_push_funnel_66378fa2a29096d1 = []byte{
	// 1606 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0xcb, 0x6e, 0xe3, 0x54,
	0x18, 0x6e, 0xe2, 0x5c, 0xff, 0x5c, 0xea, 0x9e, 0x76, 0x66, 0x42, 0x61, 0xa4, 0x36, 0x8c, 0x86,
	0xaa, 0x0c, 0xa9, 0x08, 0x42, 0x8c, 0x18, 0xa1, 0x51, 0xdb, 0xf4, 0x06, 0xbd, 0x8d, 0x9d, 0x96,
	0x19, 0x10, 0xb2, 0x1c, 0xfb, 0x4c, 0x62, 0x4d, 0x62, 0xbb, 0xbe, 0x54, 0x13, 0x89, 0x07, 0x60,
	0xc3, 0x92, 0x97, 0x40, 0x42, 0x2c, 0x59, 0xb0, 0x62, 0x01, 0x5b, 0xde, 0x80, 0x77, 0xe0, 0x09,
	0x06, 0x9d, 0x5b, 0xec, 0xa4, 0xed, 0xa4, 0x16, 0x30, 0xab, 0xee, 0x7a, 0x3e, 0x9f, 0xef, 0xff,
	0xce, 0xff, 0x7d, 0xc7, 0xce, 0xb1, 0x0b, 0xf7, 0xdd, 0xd0, 0xef, 0x7d, 0x60, 0x3b, 0x81, 0xf5,
	0xdc, 0x32, 0xf4, 0xc0, 0x72, 0xec, 0xb5, 0xf3, 0xe6, 0x1a, 0x05, 0x9f, 0x87, 0xb6, 0x8d, 0xfb,
	0x0d, 0xd7, 0x73, 0x02, 0x07, 0xdd, 0x39, 0x0e, 0xfd, 0xde, 0x61, 0x6c, 0x5a, 0x83, 0x5d, 0xae,
	0xff, 0x96, 0x02, 0xf4, 0x24, 0xc4, 0xde, 0x70, 0x9b, 0x8e, 0x5b, 0x7a, 0xa0, 0x2b, 0xf8, 0x0c,
	0xdd, 0x83, 0x2a, 0x29, 0xa2, 0x05, 0x43, 0x17, 0x6b, 0x7d, 0xcb, 0x0f, 0x6a, 0xa9, 0x25, 0x69,
	0xa5, 0xa2, 0x94, 0x09, 0xda, 0x1e, 0xba, 0x78, 0xdf, 0xf2, 0x03, 0x74, 0x17, 0xc0, 0x37, 0xb0,
	0x8d, 0xe9, 0xb4, 0x5a, 0x7a, 0x29, 0xb5, 0x52, 0x54, 0x8a, 0x14, 0x21, 0x53, 0xd0, 0x32, 0xd0,
	0xe9, 0x9a, 0xd1, 0xd3, 0x49, 0xed, 0x9a, 0x44, 0x27, 0x94, 0x08, 0xb6, 0xc9, 0x20, 0xf4, 0x36,
	0x14, 0x43, 0xdb, 0x7a, 0xa9, 0x05, 0xd6, 0x00, 0xd7, 0x32, 0x4b, 0xa9, 0x15, 0x49, 0x29, 0x10,
	0xa0, 0x6d, 0x0d, 0x30, 0xba, 0x0f, 0xb3, 0x94, 0xdf, 0xd7, 0x3b, 0xb8, 0xcf, 0x56, 0x91, 0x5d,
	0x92, 0x56, 0x8a, 0x4a, 0x85, 0xc0, 0xfb, 0x04, 0x25, 0xcb, 0xa8, 0x3f, 0x04, 0x60, 0xab, 0x57,
	0x03, 0xec, 0xa2, 0x05, 0xc8, 0xfa, 0x81, 0xde, 0xc5, 0xb5, 0x14, 0x95, 0x63, 0x03, 0x82, 0x1a,
	0x4e, 0x68, 0x07, 0x74, 0x95, 0x92, 0xc2, 0x06, 0xf5, 0xbf, 0x53, 0x30, 0x7f, 0xa1, 0x7b, 0xdf,
	0x45, 0xbb, 0x50, 0xd1, 0xbb, 0xd8, 0x0e, 0x46, 0x4b, 0x27, 0xdd, 0x97, 0x9a, 0xef, 0x36, 0xae,
	0xb0, 0xb1, 0x11, 0xe9, 0x2b, 0x65, 0xca, 0x14, 0x0d, 0xee, 0x42, 0xc5, 0xf5, 0x9c, 0x97, 0xc3,
	0x51, 0xa5, 0x74, 0x82, 0x4a, 0x94, 0x29, 0x2a, 0x6d, 0x43, 0xd9, 0xd0, 0xfd, 0x20, 0xe6, 0xe6,
	0xb5, 0x0b, 0x95, 0x08, 0x91, 0xd7, 0xa9, 0xff, 0x95, 0x86, 0xd2, 0x31, 0x4f, 0xf1, 0x40, 0x77,
	0xd1, 0x16, 0xf5, 0x2b, 0xf0, 0x79, 0x8f, 0x6b, 0x57, 0x16, 0x8c, 0x91, 0x1a, 0x2a, 0x61, 0x6c,
	0xd9, 0x81, 0x37, 0x54, 0x18, 0x1b, 0xed, 0x43, 0x81, 0xfe, 0xa1, 0x9d, 0x37, 0x79, 0x8f, 0x1f,
	0x5e, 0xbf, 0xd2, 0x69, 0x93, 0xd5, 0xca, 0xfb, 0x6c, 0xb4, 0xf8, 0x35, 0x40, 0x24, 0x81, 0x64,
	0x90, 0x5e, 0xe0, 0x21, 0x0d, 0xb4, 0xa2, 0x90, 0x3f, 0xd1, 0x27, 0x90, 0x3d, 0xd7, 0xfb, 0x21,
	0xdb, 0x74, 0xa5, 0xe6, 0xf2, 0x95, 0x52, 0x2a, 0x49, 0xff, 0x40, 0x77, 0x15, 0x36, 0xff, 0xd3,
	0xf4, 0xc3, 0xd4, 0xe2, 0x37, 0x50, 0x8e, 0xab, 0xc6, 0xcb, 0x17, 0xff, 0x7d, 0xf9, 0xfa, 0x77,
	0x29, 0x28, 0x08, 0x1c, 0x6d, 0x88, 0x7d, 0xc7, 0xdc, 0x7d, 0x30, 0xb5, 0x52, 0x63, 0x93, 0x4c,
	0xe7, 0xd6, 0x52, 0xea, 0xe2, 0x43, 0x80, 0x08, 0xbc, 0xc4, 0x8c, 0x85, 0xf8, 0x6a, 0xa5, 0xf8,
	0x52, 0x7e, 0x90, 0xa0, 0x42, 0x04, 0x4f, 0x9b, 0x07, 0x38, 0xf0, 0x2c, 0xc3, 0x47, 0x8f, 0xa1,
	0x60, 0xf4, 0x34, 0xba, 0x45, 0x69, 0x89, 0x52, 0xf3, 0xde, 0x75, 0x62, 0x52, 0xf2, 0x46, 0x6f,
	0x9d, 0x90, 0x48, 0x81, 0xd0, 0xe6, 0x05, 0xd2, 0x49, 0x0a, 0x84, 0xf6, 0xa8, 0x40, 0xc7, 0xe0,
	0x05, 0xa4, 0x24, 0x05, 0x3a, 0xc6, 0xa8, 0x80, 0x29, 0x0a, 0x64, 0x92, 0x14, 0x30, 0xa3, 0x02,
	0x5d, 0x97, 0x17, 0xc8, 0x26, 0x29, 0xd0, 0x75, 0x47, 0x05, 0x8c, 0x9e, 0x46, 0xef, 0xce, 0x5a,
	0x2e, 0x99, 0x89, 0xc7, 0x84, 0x54, 0xff, 0x3e, 0x0d, 0xb3, 0xe4, 0xc2, 0xa6, 0xee, 0x07, 0x22,
	0x99, 0xcf, 0x20, 0x6f, 0xf4, 0x34, 0x72, 0xa7, 0x26, 0x0a, 0x26, 0x67, 0xd0, 0x2a, 0x84, 0x1e,
	0xda, 0x8c, 0x9e, 0x24, 0x96, 0x5c, 0x68, 0x0b, 0x7a, 0xc7, 0x60, 0xf4, 0x24, 0xa1, 0xe4, 0x3a,
	0x86, 0xa0, 0x9b, 0x9c, 0x9e, 0x24, 0x92, 0x9c, 0x49, 0xe9, 0xf5, 0x5f, 0x32, 0x20, 0x13, 0x9c,
	0xda, 0x2b, 0x0c, 0x79, 0x04, 0x39, 0xa3, 0xa7, 0x79, 0x83, 0xb3, 0x44, 0x7e, 0x64, 0x8d, 0x9e,
	0x32, 0x38, 0x23, 0xe4, 0xd0, 0xa6, 0xe4, 0x24, 0x6e, 0x64, 0x43, 0x9b, 0x93, 0x3b, 0x06, 0x25,
	0x27, 0xf1, 0x22, 0xdb, 0x31, 0x38, 0xd9, 0x64, 0xe4, 0x24, 0x4e, 0x64, 0x4d, 0x41, 0xee, 0xba,
	0x94, 0x9c, 0x64, 0x63, 0x66, 0xbb, 0x2e, 0x27, 0x13, 0xc3, 0x4c, 0x3f, 0xd1, 0xa6, 0x24, 0x86,
	0x99, 0xbe, 0x30, 0xcc, 0xf4, 0x6b, 0xf9, 0x84, 0x86, 0x31, 0x32, 0x31, 0xcc, 0xf4, 0x6b, 0x85,
	0x84, 0x86, 0x31, 0xb2, 0xc9, 0xc8, 0xc5, 0x84, 0x86, 0x99, 0x7e, 0xfd, 0x88, 0x6d, 0x1c, 0x7a,
	0x5b, 0xfd, 0x17, 0x1b, 0xa7, 0xfe, 0x6b, 0x06, 0x16, 0x76, 0x74, 0x5a, 0x8f, 0x32, 0x86, 0x37,
	0xdb, 0xf1, 0x66, 0x3b, 0x5e, 0x7b, 0x3b, 0xfe, 0x21, 0xc1, 0xfc, 0x8e, 0xe7, 0x84, 0x2e, 0xb9,
	0xd6, 0xc2, 0x81, 0x6e, 0xd1, 0x43, 0x25, 0x5a, 0x84, 0x82, 0xa1, 0x07, 0xb8, 0xeb, 0x78, 0xe2,
	0x9c, 0x31, 0x1a, 0xa3, 0x77, 0xa0, 0x68, 0x0d, 0x5c, 0xc7, 0x0b, 0x74, 0xfe, 0x93, 0x5a, 0x50,
	0x22, 0x80, 0x30, 0x7d, 0x7c, 0x16, 0x62, 0xdb, 0xc0, 0x34, 0xfe, 0x8a, 0x32, 0x1a, 0xa3, 0xb7,
	0xa0, 0xd0, 0x25, 0x62, 0x9a, 0x65, 0xd2, 0x74, 0x2b, 0x4a, 0x9e, 0x8e, 0xf7, 0x4c, 0x72, 0x34,
	0x67, 0x97, 0xe8, 0xd1, 0x3c, 0x4b, 0x2f, 0x16, 0x29, 0x42, 0x8f, 0xe6, 0x77, 0x01, 0x3c, 0x52,
	0xc5, 0x0f, 0x08, 0x37, 0xc7, 0x4e, 0xee, 0x1c, 0xd9, 0x33, 0xc9, 0xb1, 0x7c, 0x74, 0xfc, 0xa7,
	0x01, 0x54, 0x94, 0x82, 0x38, 0xf9, 0x93, 0x63, 0xbd, 0x8f, 0xbd, 0x73, 0xcb, 0xc0, 0x9a, 0xad,
	0x0f, 0x30, 0xf5, 0xb8, 0xa8, 0x94, 0x38, 0x76, 0xa8, 0x0f, 0x2e, 0x9e, 0xfc, 0x8b, 0x17, 0x4f,
	0xfe, 0xcb, 0x50, 0xee, 0xe9, 0xb6, 0xd9, 0xc7, 0x1a, 0x3b, 0xad, 0x03, 0x55, 0x29, 0x31, 0x8c,
	0x1e, 0x86, 0xc8, 0x22, 0xc9, 0x7b, 0x01, 0x99, 0x30, 0x70, 0x6b, 0x25, 0x7a, 0xb8, 0x29, 0x12,
	0x44, 0x25, 0x00, 0x71, 0xc6, 0xc4, 0xbe, 0xe1, 0x59, 0x1d, 0x5c, 0x2b, 0x33, 0x4f, 0xc5, 0x18,
	0x2d, 0x41, 0xd9, 0x24, 0xcd, 0x85, 0x3e, 0xf6, 0x48, 0x87, 0x15, 0x5a, 0x1d, 0x08, 0x76, 0xe2,
	0x63, 0x8f, 0x19, 0x14, 0xbd, 0x5c, 0xd4, 0xaa, 0xcc, 0x81, 0xd1, 0x7b, 0x45, 0xfd, 0x47, 0x09,
	0x10, 0x59, 0xea, 0x1b, 0xc9, 0xf1, 0x2e, 0x00, 0x77, 0x2a, 0x4a, 0xb2, 0xc8, 0x11, 0xb6, 0xd4,
	0x58, 0x58, 0xd9, 0xd7, 0x86, 0x95, 0x9b, 0x12, 0x56, 0x7e, 0x7a, 0x58, 0x85, 0xe9, 0x61, 0x15,
	0xa7, 0x85, 0x05, 0xaf, 0x0b, 0xab, 0x34, 0x25, 0xac, 0xf2, 0x94, 0xb0, 0x2a, 0x93, 0x61, 0xfd,
	0x24, 0x01, 0x22, 0x33, 0xdf, 0x48, 0x58, 0x4b, 0x50, 0xe6, 0x0b, 0x65, 0xaf, 0xa4, 0x99, 0x25,
	0x69, 0x25, 0xa3, 0x40, 0x48, 0x57, 0x2a, 0x5e, 0x8b, 0x6f, 0xf2, 0x7a, 0x5d, 0x5e, 0x3f, 0x4b,
	0x30, 0xbf, 0xe1, 0x39, 0xba, 0x49, 0x0e, 0x8c, 0xff, 0x7b, 0x60, 0xcb, 0x50, 0xee, 0x08, 0xb1,
	0xe8, 0xfe, 0x2a, 0x8d, 0xb0, 0x9b, 0x3b, 0x6c, 0x7a, 0x62, 0x3d, 0x28, 0xf1, 0x73, 0x10, 0x0d,
	0xea, 0x31, 0xe4, 0x7c, 0x27, 0xf4, 0x0c, 0xf6, 0x91, 0xa5, 0xda, 0x7c, 0xef, 0xca, 0xdf, 0xc8,
	0x83, 0xad, 0xb6, 0xb2, 0xb7, 0xa9, 0x6a, 0xea, 0xd1, 0x89, 0xb2, 0xb9, 0xa5, 0x70, 0x1a, 0xaa,
	0x41, 0xde, 0xd5, 0x87, 0x7d, 0x47, 0x37, 0x69, 0x96, 0x65, 0x45, 0x0c, 0x57, 0x75, 0xa8, 0x8e,
	0x73, 0x50, 0x09, 0xf2, 0xc7, 0x27, 0xea, 0xae, 0x76, 0xda, 0x94, 0x67, 0x50, 0x05, 0x8a, 0x74,
	0xb0, 0xb9, 0xae, 0xb6, 0xe5, 0x14, 0xaa, 0x02, 0xd0, 0xe1, 0xfa, 0xce, 0xd6, 0x61, 0x5b, 0x4e,
	0x8f, 0xc6, 0xc7, 0xca, 0xd1, 0xd3, 0x67, 0xb2, 0x84, 0xe6, 0x61, 0x76, 0x67, 0x9d, 0x8d, 0xb4,
	0xc3, 0xa3, 0xf6, 0xde, 0xf6, 0x33, 0x39, 0xb3, 0xfa, 0x67, 0x0a, 0xaa, 0xa7, 0x4d, 0xc6, 0xd1,
	0xd4, 0xf6, 0xfa, 0xce, 0x16, 0x42, 0x04, 0xa1, 0x6f, 0x1f, 0x0a, 0x36, 0xb0, 0x75, 0x8e, 0xe5,
	0x99, 0x18, 0xa6, 0xbe, 0xb0, 0x5c, 0x17, 0x9b, 0x72, 0x0a, 0xcd, 0x41, 0x85, 0x63, 0x1b, 0x9e,
	0xf3, 0x02, 0xdb, 0x72, 0x1a, 0xdd, 0x82, 0x39, 0x41, 0x75, 0x8d, 0x6d, 0xdd, 0xea, 0x87, 0x1e,
	0x96, 0x25, 0x74, 0x1b, 0x90, 0x60, 0xeb, 0xe7, 0x58, 0xe0, 0x99, 0x09, 0x5c, 0x0d, 0x0d, 0x03,
	0xfb, 0xbe, 0x9c, 0x8d, 0xa9, 0x89, 0xb9, 0xb9, 0xf8, 0x0a, 0xf8, 0xbc, 0x3c, 0xe9, 0x48, 0x60,
	0xec, 0x8b, 0xc1, 0x4b, 0xb9, 0x40, 0x4c, 0x3b, 0x6d, 0xf2, 0x36, 0x63, 0x0d, 0xd1, 0x53, 0xec,
	0x44, 0x43, 0x14, 0x13, 0x12, 0xa9, 0x18, 0x26, 0x24, 0xd2, 0x4c, 0x82, 0x61, 0x42, 0x42, 0x5a,
	0xfd, 0x3d, 0x05, 0x32, 0x31, 0x7d, 0xcc, 0xb6, 0x05, 0x90, 0xc9, 0xfb, 0xdb, 0x84, 0x71, 0x71,
	0x34, 0xb2, 0x2e, 0x8e, 0xb6, 0x3c, 0x87, 0xa2, 0x54, 0x6b, 0x84, 0x72, 0x4b, 0x25, 0x74, 0x07,
	0xe6, 0xa3, 0xb2, 0x91, 0xa9, 0x99, 0xb1, 0x1a, 0x02, 0xcd, 0x8e, 0xeb, 0xf1, 0x2e, 0x72, 0x24,
	0x97, 0x08, 0x15, 0x7d, 0xe4, 0x57, 0x4d, 0x98, 0x65, 0x1d, 0x28, 0x07, 0x4f, 0x78, 0x17, 0xf3,
	0x30, 0xcb, 0xa4, 0x0e, 0x9e, 0x44, 0x4d, 0xc8, 0x50, 0x16, 0xe0, 0xbe, 0xe3, 0x07, 0xac, 0x01,
	0x81, 0xb4, 0x3d, 0xdd, 0xf6, 0x07, 0x56, 0x20, 0xa7, 0xe3, 0x68, 0xcc, 0xad, 0x48, 0xa5, 0xa5,
	0x4e, 0xaa, 0xb4, 0xd4, 0x4b, 0x54, 0x5a, 0xea, 0xa4, 0x4a, 0x4b, 0xbd, 0x4c, 0xa5, 0xa5, 0x8e,
	0xab, 0xb0, 0xcc, 0xc7, 0x7a, 0x61, 0xa9, 0x4f, 0xf4, 0x22, 0xc0, 0x48, 0x45, 0x20, 0xe3, 0x2a,
	0x02, 0x8d, 0xa9, 0xd8, 0x30, 0xb7, 0xb3, 0x3e, 0xa9, 0x73, 0x0b, 0xe6, 0xf8, 0x5b, 0xd2, 0x98,
	0x12, 0x82, 0x6a, 0x04, 0x73, 0xad, 0xdb, 0x80, 0x22, 0x2c, 0xa6, 0x36, 0x86, 0x5f, 0xa1, 0x37,
	0x72, 0x2f, 0xa6, 0x17, 0xf7, 0x2f, 0xa6, 0x37, 0x72, 0x30, 0x56, 0x77, 0xcc, 0xc3, 0x31, 0x3c,
	0xa6, 0xb7, 0xc2, 0x1f, 0x29, 0xed, 0x67, 0xc7, 0xf4, 0x61, 0x73, 0x72, 0xf8, 0xc5, 0xe1, 0xd1,
	0x97, 0x87, 0xf2, 0x0c, 0xaa, 0x42, 0x81, 0x80, 0xda, 0xc1, 0xfa, 0x53, 0xf9, 0xd5, 0x2b, 0xa9,
	0xf9, 0x2d, 0x00, 0x79, 0xce, 0xb1, 0x2f, 0xab, 0xc8, 0x86, 0xd9, 0x89, 0x6f, 0xc7, 0xe8, 0xfd,
	0x2b, 0x9f, 0x83, 0x17, 0xbf, 0xb1, 0x2f, 0x3e, 0xb8, 0xfe, 0x64, 0xdf, 0xad, 0xcf, 0x6c, 0x7c,
	0xfe, 0xd5, 0x6e, 0xd7, 0xe9, 0xeb, 0x76, 0xb7, 0xf1, 0x71, 0x33, 0x08, 0x1a, 0x86, 0x33, 0x58,
	0xa3, 0x1f, 0xf7, 0x0d, 0xa7, 0xbf, 0xc6, 0x7f, 0x6d, 0xfc, 0xb5, 0xcb, 0xfe, 0x1f, 0xf0, 0x88,
	0x3e, 0xd9, 0xe3, 0x60, 0x27, 0x47, 0x99, 0x1f, 0xfd, 0x13, 0x00, 0x00, 0xff, 0xff, 0x5b, 0x35,
	0xfc, 0x9d, 0x40, 0x18, 0x00, 0x00,
}
