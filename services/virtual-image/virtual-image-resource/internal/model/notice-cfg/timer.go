package notice_cfg

import (
    "bytes"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "reflect"
    "runtime"
    "runtime/debug"
    "time"
)

func (m *Mgr) StartTimer() {
    go m.TimerHandle(3*time.Second, m.reloadNoticeCfg)
}

func (m *Mgr) TimerHandle(d time.Duration, handle func() error) {
    m.wg.Add(1)
    defer m.wg.Done()

    delay := time.NewTicker(d)
    for {
        select {
        case <-m.shutDown:
            return
        case <-delay.C:
            m.handleWithPanicCatch(handle)
        }
    }
}

func (m *Mgr) handleWithPanicCatch(handle func() error) {
    defer func() {
        if err := recover(); err != nil {
            var stack string
            var buf bytes.Buffer
            buf.Write(debug.Stack())
            stack = buf.String()

            nowTime := time.Now().Format("2006-01-02 15:04:05")
            fmt.Printf("%s %v %s %s", nowTime, err, "\n", stack)

            funcName := runtime.FuncForPC(reflect.ValueOf(handle).Pointer()).Name()
            log.Errorf("handleWithPanicCatch panic func:%s, err:%v", funcName, err)
        }
    }()

    _ = handle()
}
