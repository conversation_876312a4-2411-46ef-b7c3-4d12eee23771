package store

import (
	"context"
	"testing"
)

func TestStore_AddLotteryRecord(t *testing.T) {
	err := testStore.AddLotteryRecord(context.TODO(), &LotteryRecord{
		Uid:         2465835,
		OrderId:     "testorderid",
		ItemId:      "123",
		ItemType:    1,
		WinningType: 2,
		Cost:        100,
	})
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
	}
}

func TestStore_BatchGetLotteryRecordByUidAndType(t *testing.T) {
	rs, err := testStore.BatchGetLotteryRecordByUidAndType(context.TODO(), 2465835, 0, 0, 10)
	if err != nil {
		t.Error(err)
	}
	t.Logf("%+v", rs)
}

func TestStore_GetMyLotteryRecordType(t *testing.T) {
	rs, err := testStore.GetMyLotteryRecordType(context.TODO(), 2465835)
	if err != nil {
		t.<PERSON>rror(err)
	}
	t.Logf("%+v", rs)
}
