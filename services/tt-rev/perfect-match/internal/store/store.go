package store

import (
    "context"

    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
)

type Store struct {
    mysql.DBx
}

func NewStore(ctx context.Context, cfg *mysqlConnect.MysqlConfig) (*Store, error) {
    db, err := mysqlConnect.NewClient(ctx, cfg)
    if err != nil {
        return nil, err
    }

    s := &Store{
        db,
    }
    return s, nil
}
