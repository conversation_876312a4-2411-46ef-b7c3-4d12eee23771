syntax = "proto3";

package conf;

import "tt-protocol/service/quicksilver/extend/apollo/annotations.proto";

option go_package = "./;conf";

// 替换项目的appid
option (apollo.app_id) = "tt-";
// 替换项目的secret
option (apollo.secret) = "fsffff";


// 动态配置例子（默认是动态）：
//message HelloWorld {
//    option (apollo.namespace) = "hello_world.json";
//    // 默认的名称
//    string name = 1;
//    // 默认的超时配置
//    uint32 timeout = 2;
//}

// 静态配置例子：
//message HelloWorld1 {
//    option (apollo.namespace) = "hello_world.json";
//    option (apollo.config_type) = STATIC;
//    // 默认的名称
//    string name = 1;
//    // 默认的超时配置
//    uint32 timeout = 2;
//}





