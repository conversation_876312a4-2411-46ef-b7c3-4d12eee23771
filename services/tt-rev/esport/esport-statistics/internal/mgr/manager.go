package mgr

import (
	"context"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/esport-statistics"
	esport_trade_appeal "golang.52tt.com/protocol/services/esport-trade-appeal"
	kfk_esport_trade "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafka-esport-trade"
	"golang.52tt.com/services/tt-rev/esport/common/collection/transform"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/cache"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/conf"
	"golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/store"
	"google.golang.org/grpc/codes"
	"time"
)

//go:generate mockgen -destination=../mocks/mock_store.go -mock_names IStore=MockStore -package=mocks golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/store IStore
//go:generate mockgen -destination=../mocks/mock_cache.go -mock_names ICache=MockCache -package=mocks golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/cache ICache
//go:generate mockgen -destination=../mocks/mock_bc.go -mock_names ICache=MockBc -package=mocks golang.52tt.com/services/tt-rev/esport/esport-statistics/internal/conf IBusinessConfManager
//go:generate mockgen -destination=../mocks/refunc_service.go -mock_names IClient=MockRefundServiceClient -package=mocks golang.52tt.com/protocol/services/esport-trade-appeal RefundServiceClient

type Manager struct {
	cache         cache.ICache
	store         store.IStore
	bc            conf.IBusinessConfManager
	refundService esport_trade_appeal.RefundServiceClient
}

func NewMgr(iCache cache.ICache, iStore store.IStore, refundService esport_trade_appeal.RefundServiceClient) *Manager {
	bc, _ := conf.NewBusinessConfManager()

	return &Manager{
		cache:         iCache,
		store:         iStore,
		bc:            bc,
		refundService: refundService,
	}
}
func (m *Manager) Close() {
	_ = m.cache.Close()
	_ = m.store.Close()
}

func (m *Manager) BatchGetCoachDurationOrderNum(ctx context.Context, uidList []uint32, gameId, dayCnt uint32) (map[uint32]uint32, error) {
	return m.store.BatchGetCoachDurationOrderNum(ctx, uidList, gameId, dayCnt)
}

func (m *Manager) InsertTradeTime(ctx context.Context, uid, playerUid uint32, tradeTime int64) error {
	err := m.cache.AddCoachPlayerList(ctx, uid, playerUid, tradeTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertTradeTime AddCoachPlayerList failed, uid:%d, err", uid, err)
		return err
	}

	return m.cache.InsertFirstTradeTime(ctx, uid, tradeTime)
}

func (m *Manager) BatchGetUsersFirstTradeTime(ctx context.Context, uidList []uint32) (*pb.BatchGetUserFirstTakeOrdersTimeResponse, error) {
	out := &pb.BatchGetUserFirstTakeOrdersTimeResponse{}
	if len(uidList) == 0 {
		log.ErrorWithCtx(ctx, "BatchGetUsersFirstTradeTime uidList is empty")
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	res, err := m.cache.BatchGetUsersFirstTradeTime(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetUsersFirstTradeTime failed, uidList:%v, err", uidList, err)
		return out, err
	}
	out.TimeMap = res
	return out, nil
}

func (m *Manager) InsertTradeRecord(ctx context.Context, event *kfk_esport_trade.EsportTradeStatusChangeEvent) error {
	err := m.store.InsertTradeRecord(ctx, event)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertTradeRecord failed, event:%v, err:%v", event, err)
		return err
	}
	if err := m.cache.DelOrderCntByGameId(ctx, event.CoachUid, event.GameId); err != nil {
		log.WarnWithCtx(ctx, "InsertTradeRecord DelOrderCntByGameId failed, event:%v, err:%v", event, err)
	}
	return nil
}

func (m *Manager) GetCoachTradeData(ctx context.Context, uid, gameId uint32) (*pb.GetCoachStatisticsResponse, error) {
	out := &pb.GetCoachStatisticsResponse{}
	if uid == 0 || gameId == 0 {
		log.ErrorWithCtx(ctx, "GetCoachTradeData uid or gameId is zero, uid:%d, gameId:%d", uid, gameId)
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	res, err := m.store.GetCoachTradeData(ctx, uid, gameId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCoachTradeData failed, uid:%d, err:%v", uid, err)
		return out, err
	}
	out.Data = &pb.StatisticsData{
		OrderNum:    res.OrderNum,
		CustomerNum: res.UserCnt,
	}

	return out, nil
}

func (m *Manager) GetAllCoachTradeData(ctx context.Context, uid uint32) (*pb.GetCoachAllStatisticsResponse, error) {
	out := &pb.GetCoachAllStatisticsResponse{}
	if uid == 0 {
		log.ErrorWithCtx(ctx, "GetAllCoachTradeData uid is zero")
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}

	dataMap, err := m.store.GetCoachAllTradeData(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllCoachTradeData failed, uid:%d, err:%v", uid, err)
		return out, err
	}

	out.GameDataMap = make(map[uint32]*pb.StatisticsData)
	for gameId, data := range dataMap {
		out.GameDataMap[gameId] = &pb.StatisticsData{
			OrderNum:    data.OrderNum,
			CustomerNum: data.UserCnt,
		}
	}
	return out, nil
}

func (m *Manager) IncrOrderGameCnt(ctx context.Context, uid, gameId uint32) error {
	return m.cache.IncrOrderGameCnt(ctx, uid, gameId)
}

func (m *Manager) GetUserMostPayGame(ctx context.Context, uid uint32) (uint32, error) {
	return m.cache.GetUserMostOrderGame(ctx, uid)
}

func (m *Manager) HandleRecallCoach(c context.Context, uid uint32) error {
	return m.cache.RemoveUserTradeTime(c, uid)
}

func (m *Manager) IsPartialRefund(ctx context.Context, orderId string) (bool, uint32, error) {
	if orderId == "" {
		log.ErrorWithCtx(ctx, "IsPartialRefund orderId is empty")
		return false, 0, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	res, err := m.refundService.GetOrderRefundView(ctx, &esport_trade_appeal.GetOrderRefundViewRequest{
		OrderId: orderId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "IsPartialRefund failed to BatchGetOrderRefundStatus, orderId:%s, err:%v", orderId, err)
		return false, 0, err
	}
	log.DebugWithCtx(ctx, "IsPartialRefund res:%+v", res)
	return res.GetOrderRefundView().GetRefundType() == esport_trade_appeal.RefundType_REFUND_TYPE_PARTIAL, res.GetOrderRefundView().GetRefundPrice(), nil
}

// BatchGetOrderCntByGameId 批量获取用户游戏订单数
func (m *Manager) BatchGetOrderCntByGameId(c context.Context, uidList []uint32, gameId uint32) (*pb.BatchGetOrderCntByGameIdResponse, error) {
	out := &pb.BatchGetOrderCntByGameIdResponse{}
	if len(uidList) == 0 || gameId == 0 {
		log.ErrorWithCtx(c, "BatchGetOrderCntByGameId uidList is empty")
		return out, protocol.NewExactServerError(codes.OK, status.ErrRequestParamInvalid)
	}
	// Step 1: Try to get data from cache
	cacheData, err := m.cache.BatchGetOrderCntByGameId(c, uidList, gameId)
	if err != nil {
		log.ErrorWithCtx(c, "BatchGetOrderCntByGameId failed to get data from cache, uidList:%v, gameId:%d, err:%v", uidList, gameId, err)
		return out, err
	}

	// Step 2: Find the uids that are not in the cache
	var missingUidList []uint32
	for _, uid := range uidList {
		if _, found := cacheData[uid]; !found {
			missingUidList = append(missingUidList, uid)
		}
	}
	if len(missingUidList) == 0 {
		out.OrderNumMap = cacheData
		log.DebugWithCtx(c, "BatchGetOrderCntByGameId all data found in cache, uidList:%v, gameId:%d", uidList, gameId)
		return out, nil
	}

	// Step 3: Get the missing data from the store
	storeData, err := m.store.BatchGetOrderCntByGameId(c, missingUidList, gameId)
	if err != nil {
		log.ErrorWithCtx(c, "BatchGetOrderCntByGameId failed to get data from store, missingUids:%v, gameId:%d, err:%v", missingUidList, gameId, err)
		return out, err
	}
	log.DebugWithCtx(c, "BatchGetOrderCntByGameId missingUidList:%v", missingUidList)

	// Step 4: Set the data from the store back into the cache
	orderCntList := make([]*cache.OrderCnt, 0, len(storeData))
	for uid, orderCnt := range storeData {
		orderCntList = append(orderCntList, &cache.OrderCnt{CoachUid: uid, OrderCnt: orderCnt})
	}
	if err := m.cache.BatchSetOrderCntByGameId(c, orderCntList, gameId); err != nil {
		log.ErrorWithCtx(c, "BatchGetOrderCntByGameId failed to set data to cache, orderCntList:%v, gameId:%d, err:%v", orderCntList, gameId, err)
		// 忽略缓存设置错误
	}

	if cacheData == nil {
		cacheData = make(map[uint32]uint32)
	}
	// Merge the data from the cache and the store
	for uid, orderCnt := range storeData {
		cacheData[uid] = orderCnt
	}

	out.OrderNumMap = cacheData
	return out, nil
}

func (m *Manager) GetUserCoachLeastOrder(c context.Context, request *pb.GetUserCoachLeastOrderRequest) (*pb.GetUserCoachLeastOrderResponse, error) {

	record, err := m.store.GetTradeRecordByCoachUidAndUid(c, request.GetCoachUid(), request.GetUid())
	if err != nil {
		log.ErrorWithCtx(c, "GetTradeRecordByCoachUidAndUid failed, coachUid:%d, uid:%d, err:%v", request.GetCoachUid(), request.GetUid(), err)
		if errors.Is(err, store.NotFound) {
			return &pb.GetUserCoachLeastOrderResponse{}, nil
		}
		return nil, fmt.Errorf("GetTradeRecordByCoachUidAndUid failed, coachUid:%d, uid:%d, err:%v", request.GetCoachUid(), request.GetUid(), err)
	}
	return &pb.GetUserCoachLeastOrderResponse{
		OrderTime: record.PayTime.Unix(),
	}, nil
}

// GetUserOrderCoachList 获取用户在指定时间段内下过单的大神
func (m *Manager) GetUserOrderCoachList(c context.Context, request *pb.GetUserOrderCoachListRequest) (*pb.GetUserOrderCoachListResponse, error) {
	list, err := m.cache.GetUserOrderCoachList(c, request.GetUid(), request.GetStartTime(), request.GetEndTime())
	if err != nil {
		log.ErrorWithCtx(c, "GetUserOrderCoachList failed, uid:%d, startTime:%d, endTime:%d, err:%v", request.GetUid(), request.GetStartTime(), request.GetEndTime(), err)
		return nil, fmt.Errorf("GetUserOrderCoachList failed")
	}
	return &pb.GetUserOrderCoachListResponse{
		CoachOrderTimeMap: list,
	}, nil

}

func (m *Manager) InsertUserLatestTrade(ctx context.Context, uid, coachUid uint32, tradeTime int64) error {
	return m.cache.InsertUserLatestTrade(ctx, uid, coachUid, tradeTime)
}

// RebuildUserOrderCoachList 重建用户下单大神列表
func (m *Manager) RebuildUserOrderCoachList(ctx context.Context) error {

	// 只处理近90天的数据
	startTime := time.Now().AddDate(0, 0, -90).Unix()
	endTime := time.Now().Unix()

	// 遍历所有的大神订单记录表
	for tableNum := 0; tableNum < 100; tableNum++ {
		lastOrderId := ""
		allData := make([]*store.TradeRecord, 0, 1000)
		for i := 0; i < 10000; i++ {
			records, err := m.store.GetTradeRecordByPage(ctx, tableNum, lastOrderId, 100, startTime, endTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "RebuildUserOrderCoachList GetTradeRecordByPage failed, tableNum:%d, lastOrderId:%s, err:%v", tableNum, lastOrderId, err)
				return err
			}
			if len(records) == 0 {
				break
			}
			lastOrderId = records[len(records)-1].OrderId
			allData = append(allData, records...)
		}
		if len(allData) != 0 {
			log.InfoWithCtx(ctx, "RebuildUserOrderCoachList GetTradeRecordByPage success, tableNum:%d, startTime:%d, endTime:%d, dataLen:%d", tableNum, startTime, endTime, len(allData))
			// 根据 player_uid 分类
			playerUidMap := make(map[uint32]struct {
				coachUids  []uint32
				tradeTimes []int64
			})
			transform.Reduce(allData, playerUidMap, func(m map[uint32]struct {
				coachUids  []uint32
				tradeTimes []int64
			}, item *store.TradeRecord) map[uint32]struct {
				coachUids  []uint32
				tradeTimes []int64
			} {
				if _, ok := playerUidMap[item.PlayerUid]; !ok {
					playerUidMap[item.PlayerUid] = struct {
						coachUids  []uint32
						tradeTimes []int64
					}{
						coachUids:  make([]uint32, 0, 10),
						tradeTimes: make([]int64, 0, 10),
					}
				}
				playerUidMap[item.PlayerUid] = struct {
					coachUids  []uint32
					tradeTimes []int64
				}{
					coachUids:  append(playerUidMap[item.PlayerUid].coachUids, item.CoachUid),
					tradeTimes: append(playerUidMap[item.PlayerUid].tradeTimes, item.PayTime.Unix()),
				}
				return playerUidMap
			})
			// 批量插入
			for playerUid, item := range playerUidMap {
				if err := m.cache.BatchInsertUserLatestTrade(ctx, playerUid, item.coachUids, item.tradeTimes); err != nil {
					log.ErrorWithCtx(ctx, "RebuildUserOrderCoachList BatchInsertUserLatestTrade failed, playerUid:%d, err:%v", playerUid, err)
				}
			}
		}
	}

	return nil
}
