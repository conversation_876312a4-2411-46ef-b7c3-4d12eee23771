package mgr

import (
	"context"
	"errors"
	"testing"
	//"time"

	"github.com/golang/mock/gomock"
	pb "golang.52tt.com/protocol/services/backpack-base"
	//"golang.52tt.com/services/backpack-base/conf"
	mock_userpresent "golang.52tt.com/clients/mocks/userpresent"
	"golang.52tt.com/protocol/services/userpresent"
	"golang.52tt.com/services/backpack-base/mocks"
	"golang.52tt.com/services/backpack-base/rpc"
	"golang.52tt.com/services/backpack-base/store"
)

func Test_ProcBackpackItemTimeout(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockUserPresent := mock_userpresent.NewMockIClient(ctl)

	clients := &rpc.RpcClients{
		UserpresentCli: mockUserPresent,
	}

	ctx := context.Background()
	uid := uint32(100)

	mockUserPresent.EXPECT().GetPresentConfigList(gomock.Any()).Return(&userpresent.GetPresentConfigListResp{}, nil).AnyTimes()
	mockStore.EXPECT().GetPackageCfg(gomock.Any()).Return([]*pb.PackageCfg{}, nil).AnyTimes()
	mockStore.EXPECT().GetPackageItemCfg(gomock.Any()).Return([]*pb.PackageItemCfg{}, nil).AnyTimes()
	mockStore.EXPECT().GetFragmentCfg(gomock.Any(), nil).Return(nil, nil).AnyTimes()
	mockStore.EXPECT().GetItemWeightCfg(gomock.Any()).Return(nil, nil).AnyTimes()
	mockCache.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
	mockCache.EXPECT().UnLock(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

	funccardcfgMgr := NewFuncCardCfgMgr(clients)
	fragmentCfgMgr := NewFragmentMgr(mockStore)
	presentCfgMgr := NewPresentCfgMgr(clients)
	packageCfgMgr := NewPackageCfgMgr(mockStore, presentCfgMgr)
	asyncTaskMgr := NewAsyncTaskMgr(mockStore, presentCfgMgr, fragmentCfgMgr, funccardcfgMgr)

	testCases := []struct {
		name       string
		mockExpect func()
		req        *pb.ProcBackpackItemTimeoutReq
		exceptErr  bool
	}{
		{
			name: "ProcBackpackItemTimeout ok",
			req: &pb.ProcBackpackItemTimeoutReq{
				Uid:        uid,
				UserItemId: 2,
			},
			mockExpect: func() {
				mockStore.EXPECT().ProcBackpackItemTimeout(ctx, uid, gomock.Any(), gomock.Any()).Return(true, nil)
				mockCache.EXPECT().CleanUserBackpack(gomock.Any(), uid).Return(errors.New("mock error")).AnyTimes()
			},
		},
		{
			name: "ProcBackpackItemTimeout param err",
			req: &pb.ProcBackpackItemTimeoutReq{
				Uid:        uid,
				UserItemId: 0,
			},
			mockExpect: func() {
				//mockStore.EXPECT().ProcBackpackItemTimeout(ctx, uid, gomock.Any(), gomock.Any()).Return(true, nil)
			},
			exceptErr: true,
		},
		{
			name: "ProcBackpackItemTimeout err",
			req: &pb.ProcBackpackItemTimeoutReq{
				Uid:        uid,
				UserItemId: 2,
			},
			mockExpect: func() {
				mockStore.EXPECT().ProcBackpackItemTimeout(ctx, uid, gomock.Any(), gomock.Any()).Return(true, errors.New("mock err"))
			},
			exceptErr: true,
		},
	}

	m, _ := NewManager(mockStore, mockCache, packageCfgMgr, asyncTaskMgr, clients)
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			err := m.ProcBackpackItemTimeout(ctx, tc.req)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
}

func Test_DoExpiredItem(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockUserPresent := mock_userpresent.NewMockIClient(ctl)
	clients := &rpc.RpcClients{
		UserpresentCli: mockUserPresent,
	}

	mockUserPresent.EXPECT().GetPresentConfigList(gomock.Any()).Return(&userpresent.GetPresentConfigListResp{}, nil).AnyTimes()
	mockStore.EXPECT().GetPackageCfg(gomock.Any()).Return([]*pb.PackageCfg{}, nil).AnyTimes()
	mockStore.EXPECT().GetPackageItemCfg(gomock.Any()).Return([]*pb.PackageItemCfg{}, nil).AnyTimes()
	mockStore.EXPECT().GetFragmentCfg(gomock.Any(), nil).Return(nil, nil).AnyTimes()
	mockStore.EXPECT().GetItemWeightCfg(gomock.Any()).Return(nil, nil).AnyTimes()
	mockCache.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()
	mockCache.EXPECT().UnLock(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

	funccardcfgMgr := NewFuncCardCfgMgr(clients)
	fragmentCfgMgr := NewFragmentMgr(mockStore)
	presentCfgMgr := NewPresentCfgMgr(clients)
	asyncTaskMgr := NewAsyncTaskMgr(mockStore, presentCfgMgr, fragmentCfgMgr, funccardcfgMgr)

	ctx := context.Background()
	uid := uint32(100)

	testCases := []struct {
		name       string
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_DoExpiredItem ok",
			mockExpect: func() {
				mockStore.EXPECT().ScanUserExpireBackpack(ctx, gomock.Any(), gomock.Any()).Return([]*store.UserBackpackItem{
					&store.UserBackpackItem{
						UserItemId: 2,
						Uid:        uid,
						ItemType:   1,
						SourceId:   2,
						ItemCount:  100,
					},
					&store.UserBackpackItem{
						UserItemId: 3,
						Uid:        uid + 10,
						ItemType:   1,
						SourceId:   2,
						ItemCount:  100,
					},
				}, nil)
				mockStore.EXPECT().UseBackpackItem(ctx, gomock.Any(), gomock.Any(), gomock.Any(), false, false).Return(errors.New("mock err"))
				mockStore.EXPECT().UseBackpackItem(ctx, gomock.Any(), gomock.Any(), gomock.Any(), false, false).Return(nil)
				mockCache.EXPECT().CleanUserBackpack(gomock.Any(), gomock.Any()).Return(errors.New("mock error")).AnyTimes()
			},
		},
	}

	m := &Manager{
		cache:        mockCache,
		store:        mockStore,
		asyncTaskMgr: asyncTaskMgr,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			err := m.DoExpiredItem(ctx, 0)
			if (err != nil) != tc.exceptErr {
				t.Errorf("name:%s, exceptErr %v, but got err: %v", tc.name, tc.exceptErr, err)
				return
			}
		})
	}
}

func Test_NearlyExpireBackpackItemNotify(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockCache := mocks.NewMockICache(ctl)
	mockStore := mocks.NewMockIStore(ctl)
	mockUserPresent := mock_userpresent.NewMockIClient(ctl)

	clients := &rpc.RpcClients{
		UserpresentCli: mockUserPresent,
	}

	mockUserPresent.EXPECT().GetPresentConfigList(gomock.Any()).Return(&userpresent.GetPresentConfigListResp{}, nil).AnyTimes()
	mockStore.EXPECT().GetPackageCfg(gomock.Any()).Return([]*pb.PackageCfg{}, nil).AnyTimes()
	mockStore.EXPECT().GetPackageItemCfg(gomock.Any()).Return([]*pb.PackageItemCfg{}, nil).AnyTimes()
	mockStore.EXPECT().GetFragmentCfg(gomock.Any(), nil).Return(nil, nil).AnyTimes()
	mockStore.EXPECT().GetItemWeightCfg(gomock.Any()).Return(nil, nil).AnyTimes()

	funccardcfgMgr := NewFuncCardCfgMgr(clients)
	fragmentCfgMgr := NewFragmentMgr(mockStore)
	presentCfgMgr := NewPresentCfgMgr(clients)
	packageCfgMgr := NewPackageCfgMgr(mockStore, presentCfgMgr)
	asyncTaskMgr := NewAsyncTaskMgr(mockStore, presentCfgMgr, fragmentCfgMgr, funccardcfgMgr)

	uid := uint32(100)

	testCases := []struct {
		name       string
		mockExpect func()
		exceptErr  bool
	}{
		{
			name: "Test_NearlyExpireBackpackItemNotify ok",
			mockExpect: func() {
				mockCache.EXPECT().Lock(gomock.Any(), gomock.Any(), gomock.Any(), false).Return(true)
				mockStore.EXPECT().ScanUserNearlyExpireBackpack(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.UserBackpackItem{
					&store.UserBackpackItem{
						UserItemId: 2,
						Uid:        uid,
						ItemType:   1,
						SourceId:   2,
						ItemCount:  100,
					},
					&store.UserBackpackItem{
						UserItemId: 2,
						Uid:        uid + 10,
						ItemType:   1,
						SourceId:   2,
						ItemCount:  100,
					},
				}, nil).AnyTimes()
				//mockCache.EXPECT().UnLock(gomock.Any(), gomock.Any(), false)
			},
		},
	}

	m := &Manager{
		store:         mockStore,
		cache:         mockCache,
		packageCfgMgr: packageCfgMgr,
		asyncTaskMgr:  asyncTaskMgr,
		clients:       clients,
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.mockExpect()
			m.NearlyExpireBackpackItemNotify(1, 1)
		})
	}
}
