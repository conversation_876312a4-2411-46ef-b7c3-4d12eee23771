package server

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	singing_hall "golang.52tt.com/protocol/services/singing-hall"
	singinghallpb "golang.52tt.com/protocol/services/singing-hall"
	"golang.52tt.com/services/singing-hall/store"
)

func Test_logJson(t *testing.T) {
	type args struct {
		logData interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{"{}"},
			want: `"{}"`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := logJson(tt.args.logData); got != tt.want {
				t.Errorf("logJson() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_singingHallServiceServer_AddSong(t *testing.T) {
	ctrl := before(t)
	defer ctrl.Finish()

	type args struct {
		context context.Context
		req     *singing_hall.AddSongReq
	}
	tests := []struct {
		name    string
		args    args
		want    *singing_hall.AddSongResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				context: ctx,
				req:     &singinghallpb.AddSongReq{},
			},
			want:    &singinghallpb.AddSongResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockMgr.EXPECT().CreateSong(gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes()
			got, err := s.AddSong(tt.args.context, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddSong() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddSong() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_singingHallServiceServer_EditSongList(t *testing.T) {
	ctrl := before(t)
	defer ctrl.Finish()

	type args struct {
		context context.Context
		req     *singing_hall.EditSongListReq
	}
	tests := []struct {
		name    string
		args    args
		want    *singing_hall.EditSongListResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				context: ctx,
				req:     &singinghallpb.EditSongListReq{},
			},
			want:    &singinghallpb.EditSongListResp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockMgr.EXPECT().EditSong(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			got, err := s.EditSongList(tt.args.context, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("EditSongList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("EditSongList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_singingHallServiceServer_GetSongList(t *testing.T) {
	ctrl := before(t)
	defer ctrl.Finish()

	type args struct {
		context context.Context
		req     *singing_hall.GetSongListReq
	}
	tests := []struct {
		name    string
		args    args
		want    *singing_hall.GetSongListResp
		wantErr bool
	}{
		{
			name: "",
			args: args{
				context: ctx,
				req: &singinghallpb.GetSongListReq{
					MenuId: 1,
				},
			},
			want: &singinghallpb.GetSongListResp{
				SongList: []*singinghallpb.SongItem{{
					Id:    1,
					Name:  "2",
					Index: 3,
				}},
				Total: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockMgr.EXPECT().GetSong(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*store.Song{{
				ID:    1,
				Name:  "2",
				Index: 3,
			}}, uint32(1), nil)
			got, err := s.GetSongList(tt.args.context, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSongList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSongList() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_songDB2VO(t *testing.T) {
	type args struct {
		dbSong *store.Song
	}
	tests := []struct {
		name string
		args args
		want *singing_hall.SongItem
	}{
		{
			name: "",
			args: args{dbSong: &store.Song{
				ID:     1,
				Name:   "2",
				Index:  3,
				MenuId: 4,
			}},
			want: &singinghallpb.SongItem{
				Id:    1,
				Name:  "2",
				Index: 3,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := songDB2VO(tt.args.dbSong); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("songDB2VO() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_songsDB2VO(t *testing.T) {
	type args struct {
		dbSong []*store.Song
	}
	tests := []struct {
		name string
		args args
		want []*singing_hall.SongItem
	}{
		{
			name: "",
			args: args{
				dbSong: []*store.Song{
					{
						ID:     1,
						Name:   "2",
						Index:  3,
						MenuId: 4,
					},
				},
			},
			want: []*singinghallpb.SongItem{{
				Id:    1,
				Name:  "2",
				Index: 3,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := songsDB2VO(tt.args.dbSong); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("songsDB2VO() = %v, want %v", got, tt.want)
			}
		})
	}
}
