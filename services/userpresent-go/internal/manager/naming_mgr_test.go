package manager

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/pkg/timer"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/userpresent-go/internal/cache"
	config "golang.52tt.com/services/userpresent-go/internal/config/ttconfig/userpresent_go"
	"golang.52tt.com/services/userpresent-go/internal/mock"
	"golang.52tt.com/services/userpresent-go/internal/producer"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"reflect"
	"testing"
)

func TestUserPresentGoMgr_AddNamingPresentInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	sceneStoreMock := store.NewMockISceneStore(ctrl)
	sceneStoreMock.EXPECT().AddNamingPresentInfo(gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *userpresent.AddNamingPresentInfoReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *userpresent.AddNamingPresentInfoResp
		wantErr  bool
	}{
		{
			name: "test",
			fields: fields{
				sceneStore: sceneStoreMock,
			},
			args: args{
				ctx: context.Background(),
				req: &userpresent.AddNamingPresentInfoReq{},
			},
			wantResp: &userpresent.AddNamingPresentInfoResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.AddNamingPresentInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddNamingPresentInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("AddNamingPresentInfo() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}

func TestUserPresentGoMgr_DelNamingPresentInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	sceneStoreMock := store.NewMockISceneStore(ctrl)
	sceneStoreMock.EXPECT().DelNamingPresentInfo(gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *userpresent.DelNamingPresentInfoReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *userpresent.DelNamingPresentInfoResp
		wantErr  bool
	}{
		{
			name: "test",
			fields: fields{
				sceneStore: sceneStoreMock,
			},
			args: args{
				ctx: context.Background(),
				req: &userpresent.DelNamingPresentInfoReq{},
			},
			wantResp: &userpresent.DelNamingPresentInfoResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.DelNamingPresentInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DelNamingPresentInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("DelNamingPresentInfo() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}

func TestUserPresentGoMgr_GetNamingPresentInfoList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	sceneStoreMock := store.NewMockISceneStore(ctrl)
	sceneStoreMock.EXPECT().GetNamingPresentInfoList(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *userpresent.GetNamingPresentInfoListReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *userpresent.GetNamingPresentInfoListResp
		wantErr  bool
	}{
		{
			name: "test",
			fields: fields{
				sceneStore: sceneStoreMock,
			},
			args: args{
				ctx: context.Background(),
				req: &userpresent.GetNamingPresentInfoListReq{},
			},
			wantResp: &userpresent.GetNamingPresentInfoListResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.GetNamingPresentInfoList(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNamingPresentInfoList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("GetNamingPresentInfoList() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}

func TestUserPresentGoMgr_GetValidNamingPresentInfos(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	presentCacheMock := mock.NewMockIPresentMemCache(ctrl)
	presentCacheMock.EXPECT().GetValidNamingPresent()
	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *userpresent.GetValidNamingPresentInfosReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *userpresent.GetValidNamingPresentInfosResp
		wantErr  bool
	}{
		{
			name: "test",
			fields: fields{
				presentCache: presentCacheMock,
			},
			args: args{
				ctx: context.Background(),
				req: &userpresent.GetValidNamingPresentInfosReq{},
			},
			wantResp: &userpresent.GetValidNamingPresentInfosResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.GetValidNamingPresentInfos(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetValidNamingPresentInfos() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("GetValidNamingPresentInfos() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}

func TestUserPresentGoMgr_UpdateNamingPresentInfo(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	sceneStoreMock := store.NewMockISceneStore(ctrl)
	sceneStoreMock.EXPECT().UpdateNamingPresentInfo(gomock.Any(), gomock.Any(), gomock.Any())

	type fields struct {
		store         store.IStore
		sceneStore    store.ISceneStore
		detailStore   store.IDetailStore
		cache         cache.ICache
		sceneCache    cache.ISceneCache
		presentCache  cache.IPresentMemCache
		config        config.UserPresentGoConf
		kafkaProducer producer.IKafkaProduceMgr
		mTimer        *timer.Timer
	}
	type args struct {
		ctx context.Context
		req *userpresent.UpdateNamingPresentInfoReq
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantResp *userpresent.UpdateNamingPresentInfoResp
		wantErr  bool
	}{
		{
			name: "test",
			fields: fields{
				sceneStore: sceneStoreMock,
			},
			args: args{
				ctx: context.Background(),
				req: &userpresent.UpdateNamingPresentInfoReq{},
			},
			wantResp: &userpresent.UpdateNamingPresentInfoResp{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &UserPresentGoMgr{
				store:         tt.fields.store,
				sceneStore:    tt.fields.sceneStore,
				detailStore:   tt.fields.detailStore,
				cache:         tt.fields.cache,
				sceneCache:    tt.fields.sceneCache,
				presentCache:  tt.fields.presentCache,
				config:        tt.fields.config,
				kafkaProducer: tt.fields.kafkaProducer,
				mTimer:        tt.fields.mTimer,
			}
			gotResp, err := m.UpdateNamingPresentInfo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateNamingPresentInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotResp, tt.wantResp) {
				t.Errorf("UpdateNamingPresentInfo() gotResp = %v, want %v", gotResp, tt.wantResp)
			}
		})
	}
}
