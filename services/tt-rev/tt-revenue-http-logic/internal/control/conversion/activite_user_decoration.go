package conversion

// 原C++ httplogic /conversion/activiteUserDecoration 服务

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	channelPersonalPb "golang.52tt.com/protocol/services/channelpersonalization"
	"net/http"
	"time"
)

type ActiviteUserDecorationReq struct {
	Uid            uint32 `json:"uid"`
	DecorationId   string `json:"decoration_id"`
	DecorationType uint32 `json:"decoration_type"`
}

type ActiviteUserDecorationRsp struct {
}

func (mgr *ConversionMgr) ActiveiteUserDecorationHandler(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*3)
	defer cancel()
	body := string(authInfo.Body)
	in := &ActiviteUserDecorationReq{}
	err := json.Unmarshal(authInfo.Body, in)
	if err != nil {
		log.Errorf("ActiveiteUserDecorationHandler Unmarshal body:%v, err:%v", body, err)
		web.ServeBadReq(w)
		return
	}

	out := &ActiviteUserDecorationRsp{}
	uid := authInfo.UserID
	_, err = mgr.channelPersonalizeCli.ActivateUserDecoration(ctx, uid, uint32(channelPersonalPb.DecorationType_CHANNEL_ENTER_SPECIAL_EFFECT), in.DecorationId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ActiveiteUserDecorationHandler failed to call ActivateUserDecoration uid:%d, deId:%d, err:%v", uid, in.DecorationId, err)
		_ = web.ServeAPICodeJson(w, -2, "佩戴失败~", nil)
		return
	}

	_ = web.ServeAPIJsonV2(w, out)
	log.InfoWithCtx(ctx, "ActiveiteUserDecorationHandler ok uid:%d req:%v", uid, in)
}
