package store

import(
	context "context"
	sql "database/sql"
	entity "golang.52tt.com/services/smash-egg-notify/internal/entity"
)

type IStore interface {
	AddNotifyOpLog(ctx context.Context, notify *entity.NotifyOpLog) error
	AddSmashLightEffects(ctx context.Context, infos []*LightEffect) ([]uint32,error)
	CreateGiftNotifyTable() error
	CreateSmashEggNotifyTable() error
	CreateSmashLightEffectTbl() error
	DelLightEffectByPackId(ctx context.Context, packId uint32) error
	DeleteGiftNotify(ctx context.Context, id uint32) error
	GetAllGiftNotify(ctx context.Context) (notify []entity.GiftNotify,err error)
	GetAllSmashLightEffects(ctx context.Context) ([]*LightEffect,error)
	GetCurrentGiftNotify(ctx context.Context) (notify *entity.GiftNotify,err error)
	GetMaxMtimeOfLightEffTbl(ctx context.Context) (int64,error)
	GetSmashEggNotify(ctx context.Context, notifyType, activityType int) (notify []entity.SmashEggNotify,err error)
	Init() (err error)
	SetGiftNotify(ctx context.Context, notify entity.GiftNotify) error
	SetSmashEggNotify(ctx context.Context, notify entity.SmashEggNotify) error
	TransBegin() (interface{},error)
	TransEnd(tx interface{}, commit bool) (err error)
	Transaction(ctx context.Context, f func(tx *sql.Tx) error) error
	UpdateLightEffectByPackId(ctx context.Context, info *LightEffect) error
}

