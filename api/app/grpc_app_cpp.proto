syntax = "proto3";
package ga.api.app;



import "api/extension/extension.proto";
import "app/app.proto";
import "contact/contact.proto";
import "lbs/lbs_.proto";

//server_name=applogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/app;app";
option objc_class_prefix = "RPC";

service AppLogic {
    //lbs.proto
    rpc CheckUserGrantService(ga.contact.CheckUserGrantReq) returns (ga.contact.CheckUserGrantResp) {
        option (ga.api.extension.command) = {
            id: 417
        };
    }

    rpc ReportUserGeo(ga.lbs.ReportUserGeoReq) returns (ga.lbs.ReportUserGeoResp) {
        option (ga.api.extension.command) = {
            id: 2220
        };
    }

    rpc ReportUserLocation(ga.lbs.ReportUserLocationReq) returns (ga.lbs.ReportUserLocationResp) {
        option (ga.api.extension.command) = {
            id: 2225
        };
    }

    //app.proto
    rpc AppBehaviorReport(ga.app.AppBehaviorReportReq) returns (ga.app.AppBehaviorReportResp) {
        option (ga.api.extension.command) = {
            id: 173
        };
    }

    rpc AppReportBlackRiskCheck(ga.app.ReportBlackRiskCheckReq) returns (ga.app.ReportBlackRiskCheckResp) {
        option (ga.api.extension.command) = {
            id: 5001
        };
    }

    rpc AppReportOpenApp(ga.app.ReportOpenAppReq) returns (ga.app.ReportOpenAppResp) {
        option (ga.api.extension.command) = {
            id: 3304
        };
    }

    rpc AppReport(ga.app.AppReportReq) returns (ga.app.AppReportResp) {
        option (ga.api.extension.command) = {
            id: 162
        };
    }

    rpc ClientReportStatis(ga.app.ClientReportStatisReq) returns (ga.app.ClientReportStatisResp) {
        option (ga.api.extension.command) = {
            id: 200
        };
    }

    option (ga.api.extension.logic_service_name) = "applogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
