syntax = "proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package officialcert;

// 枚举值为 1 2 4 8 16 32 ... 位运算
enum ECertAttrType {
  ECertAttrTypeNone = 0;
  ECertAttrTypeImNoLimit = 1; // 达人账号对ta的非关注的粉丝 发IM消息 不限制
}

enum LocationType {
	Personal_Page = 0; // 个人主页
	Channel = 1; 	   // 房间个人资料卡
	Anchor_Card = 2;   // 主播资料卡
  PersonalityDress = 3; // 个性装扮 图标
}

enum STATUS_TYPE {
  STATUS_TYPE_UNUSE = 0; // 未佩戴 0,1 表示正常
  STATUS_TYPE_USE = 1; // 佩戴中
  STATUS_TYPE_DEL = 2; // 已删除
  STATUS_TYPE_OVERDUE = 3; // 已过期
}


message OfficialCertInfo {

  required uint32 uid = 1;
  optional string title = 2;
  optional string intro = 3;
  optional string style = 4;
  optional uint32 id = 5;
  optional uint64 begin_ts = 6;
  optional uint64 end_ts = 7;
  optional bool is_use = 8; // 用户是否正在佩戴
  optional uint32 attribute = 9; // 特殊 权限 属性，ECertAttrType 或运算后得到
  optional string certify_special_effect_title = 10; // 带有文字背景
  optional string certify_special_effect_icon = 11;  // 只有图标
}

message UserOfficialCertInfo {
    required bool is_certified = 1;
    optional OfficialCertInfo cert = 2;
    repeated OfficialCertInfo cert_list = 3;
}

message GetUserOfficialCertReq {
    required uint32 uid = 1;
    optional uint32 request_type = 2; // see CertType
}
message GetUserOfficialCertResp {
    optional UserOfficialCertInfo info = 2;
}

message ListUserOfficialCertReq {
}
message ListUserOfficialCertResp {
    repeated OfficialCertInfo cert_list = 1;
}
message SetUserOfficialCertReq {
    required OfficialCertInfo cert = 1;
}
message SetUserOfficialCertResp {
}

message DelUserOfficialCertReq {
    required uint32 uid = 1;
    optional uint32 id = 2;
}
message DelUserOfficialCertResp {
}

message DelCertInfo {
   required uint32 uid = 1;
   required uint32 id = 2;
}

message BatchDelOfficalCertsReq {
   repeated DelCertInfo info_list = 1;
}
message BatchDelOfficalCertsResp {
}

message BatchGetUserOfficialCertReq {
    repeated uint32 uid_list = 1;
}
message BatchGetUserOfficialCertResp {
    repeated UserOfficialCertInfo info_list = 1;
}

// 设置用户佩戴的大v认证样式
message SetUserWearCertificationReq {
   required uint32 uid = 1;
   required uint32 id = 2;
}
message SetUserWearCertificationResp {
}

message GetUserAllOfficialCertsReq {
    required uint32 uid = 1;
    optional uint32 request_type = 2; // see CertType
}
message GetUserAllOfficialCertsResp {
    required bool is_certified = 1;
    repeated OfficialCertInfo cert_list = 2;
}

// 获取用户有效的特权属性
message GetUserCertAttributeReq { required uint32 uid = 1; }

// 有效的全部特权 ECertAttrType
message GetUserCertAttributeResp { optional uint32 attr = 1; }

// 查询是否有如下特权
message CheckUserCertAttributeReq {
  required uint32 uid = 1;
  required uint32 attr = 2;
}

message CheckUserCertAttributeResp { required bool ok = 1; }


//新增 乐窝主理人
// 主理人信息
message DirectorCertInfo {
  required uint32 uid = 1;                // uid
  required string ttid = 2;               // ttid
  required string nickname = 3;           // 昵称
  required uint32 cooperation_type = 4;   // 合作类型 1签约 2商单 3临时
  required uint32 status = 5;             // 主理人状态 0否 1是
  required string introduce = 6;          // 介绍
  required string manager = 7;            // 负责人
  required uint64 ts = 8;                 // 创建时间
}

message UserDirectorCertInfo  {
    required bool is_certified = 1;     // 是否主理人
    optional DirectorCertInfo cert = 2;
}

message GetUserDirectorCertReq {
    required uint32 uid = 1;
}

message GetUserDirectorCertResp {
    required UserDirectorCertInfo info = 1;
}

message BatchGetUserDirectorCertReq {
    repeated uint32 uid_list = 1;
}

message BatchGetUserDirectorCertResp {
    repeated UserDirectorCertInfo info_list = 1;
}

message SetUserDirectorCertReq {
    required uint32 uid = 1;              // uid
    required uint32 status = 2;           // 主理人状态  1通过 0失效
    required uint32 cooperation_type = 3; // 合作类型 1签约 2商单 3临时
    required string introduce = 4;        // 介绍
    required string manager = 5;          // 负责人
}

message SetUserDirectorCertResp {
}

message AddUserDirectorCertReq {
    required uint32 uid = 1;                // uid
    required uint32 cooperation_type = 2;   // 合作类型 1签约 2商单 3临时
}

message AddUserDirectorCertResp {
}

message ListUserDirectorCertReq{
    optional uint32 offset = 1; 
    optional uint32 page_size = 2; 
    optional string ttid = 3; 
    optional string nickname = 4; 
    optional string manager = 5; 
    optional uint32 cooperation_type = 6; 
    optional uint32 status = 7; 
}
message ListUserDirectorCertResp{
   required uint32 total = 1;
   repeated UserDirectorCertInfo info_list = 2;
}



service OfficialCert {
  option (tlvpickle.Magic) = 14006;  // 服务监听端口号

  rpc GetUserOfficialCert(GetUserOfficialCertReq) returns (GetUserOfficialCertResp) {
    option (tlvpickle.CmdID) = 1;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc ListUserOfficialCert(ListUserOfficialCertReq) returns (ListUserOfficialCertResp) {
    option (tlvpickle.CmdID) = 2;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc SetUserOfficialCert(SetUserOfficialCertReq) returns (SetUserOfficialCertResp) {
    option (tlvpickle.CmdID) = 3;
    option (tlvpickle.OptString) = "u:t:i:s:b:e:";
    option (tlvpickle.Usage) = "-u <uid> -t <title> -i <intro> -s <style> -b <begin_time> -e <end_time>";
  }

  rpc DelUserOfficialCert(DelUserOfficialCertReq) returns (DelUserOfficialCertResp) {
    option (tlvpickle.CmdID) = 4;
    option (tlvpickle.OptString) = "u:i:";
    option (tlvpickle.Usage) = "-u <uid> -i <id>";
  }

  rpc BatchGetUserOfficialCert(BatchGetUserOfficialCertReq) returns (BatchGetUserOfficialCertResp) {
    option (tlvpickle.CmdID) = 5;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid1,uid2,...>";
  }

  rpc SetUserWearCertification(SetUserWearCertificationReq) returns (SetUserWearCertificationResp) {
    option (tlvpickle.CmdID) = 6;
    option (tlvpickle.OptString) = "u:i:";
    option (tlvpickle.Usage) = "-u <uid> -i <id>";
  }

  rpc GetUserAllOfficialCerts(GetUserAllOfficialCertsReq) returns (GetUserAllOfficialCertsResp) {
    option (tlvpickle.CmdID) = 7;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc GetUserCertAttribute(GetUserCertAttributeReq) returns (GetUserCertAttributeResp) {
    option (tlvpickle.CmdID) = 8;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

    rpc CheckUserCertAttribute(CheckUserCertAttributeReq) returns (CheckUserCertAttributeResp) {
    option (tlvpickle.CmdID) = 9;
    option (tlvpickle.OptString) = "u:a:";
    option (tlvpickle.Usage) = "-u <uid> -a <attr>";
  }

  rpc BatchDelOfficalCerts(BatchDelOfficalCertsReq) returns (BatchDelOfficalCertsResp) {
    option (tlvpickle.CmdID) = 10;
    option (tlvpickle.OptString) = "u:i:";
    option (tlvpickle.Usage) = "-u <uid1,uid2,...> -i <id1,id2,...>";
  }


  // 新增
       // 获取用户的主理人认证信息
    rpc GetUserDirectorCert(GetUserDirectorCertReq) returns (GetUserDirectorCertResp){
      option (tlvpickle.CmdID) = 11;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }
    
    // 批量获取用户的主理人认证信息
    rpc BatchGetUserDirectorCert(BatchGetUserDirectorCertReq) returns (BatchGetUserDirectorCertResp){
      option (tlvpickle.CmdID) = 12;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }
    
    // 创建主理人
    rpc AddUserDirectorCert(AddUserDirectorCertReq) returns (AddUserDirectorCertResp){
      option (tlvpickle.CmdID) = 13;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }
    
    // 后台配置主理人相关信息
    rpc SetUserDirectorCert(SetUserDirectorCertReq) returns (SetUserDirectorCertResp){
      option (tlvpickle.CmdID) = 14;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    }
    
    // 获取主理人列表
    rpc ListUserDirectorCert(ListUserDirectorCertReq) returns (ListUserDirectorCertResp){
      option (tlvpickle.CmdID) = 15;
      option (tlvpickle.OptString) = "u:";
      option (tlvpickle.Usage) = "-u <uid>";
    } 

    // 此接口会返回主理人的style类型，仅供esgwlogic调用。避免大v的运营后台无法识别主理人作为特殊大v类型
    rpc ListUserOfficialCertV2(ListUserOfficialCertReq) returns (ListUserOfficialCertResp) {
      option (tlvpickle.CmdID) = 16;
      option (tlvpickle.OptString) = "";
      option (tlvpickle.Usage) = "";
    }

}

//message OfficialCertCacheData {
//    required bool is_certified = 1;
//    optional OfficialCertInfo cert = 2;
//}




