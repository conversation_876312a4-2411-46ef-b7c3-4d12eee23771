package client

import (
    "context"
    "golang.52tt.com/pkg/client"
    grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
    "golang.52tt.com/protocol/services/unified_pay/cb"
    "google.golang.org/grpc"
)

type Client struct {
    client.BaseClient
}

func NewClient(serviceName string, dopts ...grpc.DialOption) (*Client, error) {
    dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
    return &Client{
        BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
            return cb.NewPayCallbackClient(cc)
        }, dopts...),
    }, nil
}

func (c *Client) typedStub() cb.PayCallbackClient {
    return c.Stub().(cb.PayCallbackClient)
}

func (c *Client)Notify(ctx context.Context, n *cb.PayNotify) (confirm bool, op cb.Op, err error) {
    r, err := c.typedStub().Notify(ctx, n)
    if err == nil {
        confirm, op = r.Get<PERSON>onfirmed(), r.<PERSON>p()
    }
    return
}
