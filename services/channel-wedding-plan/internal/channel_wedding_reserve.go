package internal

import (
	"context"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channel-wedding-plan"
)

func (s *Server) GetChannelReservedInfo(ctx context.Context, request *pb.GetChannelReservedInfoRequest) (*pb.GetChannelReservedInfoResponse, error) {
	resp, err := s.mgr.GetChannelReservedInfo(ctx, request)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelReservedInfo failed, req: %+v, err: %v", request, err)
		return resp, err
	}

	return resp, nil
}