package event

import (
	"context"
	"runtime/debug"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol/grpc"
)

const channSize = 8

type DataChannel chan *innerNotify
type DataChannelSlice []DataChannel

type innerNotify struct {
	notify Notify
	ctx    context.Context
}

type inMemoryBus struct {
	chanMap map[string]DataChannelSlice
}

func NewInMemoryEventBus() Bus {
	return &inMemoryBus{
		chanMap: make(map[string]DataChannelSlice),
	}
}

func (i *inMemoryBus) Publish(ctx context.Context, notify Notify) {
	// 带上ctx
	innerNotify := &innerNotify{
		notify: notify,
		ctx:    ctx,
	}

	// 发布消息到该topic下所有channel
	if dataChannelSlice, ok := i.chanMap[notify.Topic()]; ok {
		for _, dataChannel := range dataChannelSlice {
			dataChannel <- innerNotify
		}
	}
}

func (i *inMemoryBus) Subscribe(topic string, handler <PERSON><PERSON>) {

	dataChannel := make(DataChannel, channSize)
	i.chanMap[topic] = append(i.chanMap[topic], dataChannel)

	// 读取msg，完成处理
	go func() {
		for msg := range dataChannel {
			log.DebugWithCtx(msg.ctx, "Subscribe receive msg:%+v, topic:%s", msg, topic)
			go func(msg *innerNotify) {
				ctx, cancel := grpc.NewContextWithInfoTimeout(msg.ctx, time.Second*5)
				defer cancel()
				i.handle(ctx, msg.notify, handler)
			}(msg)
		}
	}()
}

func (i *inMemoryBus) handle(ctx context.Context, notify Notify, handler Handler) {
	defer func() {
		if err := recover(); err != nil {
			log.ErrorWithCtx(ctx, "[EventBus]处理消息出错，notify: %+v\n调用栈：\n%s", notify, debug.Stack())
		}
	}()
	err := handler.HandleMsg(ctx, notify)
	if err != nil {
		log.ErrorWithCtx(ctx, "[EventBus]处理消息出错，notify: %+v, err: %+v", notify, err)
	}
}

func (i *inMemoryBus) Stop(ctx context.Context) error {
	return nil
}
