package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	"golang.52tt.com/services/present-go-logic/internal/model/info"
	"golang.52tt.com/services/present-go-logic/internal/model/interceptor"
	batch_send2 "golang.52tt.com/services/present-go-logic/internal/model/interceptor/batch_send"
	common2 "golang.52tt.com/services/present-go-logic/internal/model/interceptor/common"
	"golang.52tt.com/services/present-go-logic/internal/model/metrics"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"time"

	"strconv"
)

// PresentBatchSend 批量送礼，cmd1170
func (s *PresentGoLogic_) PresentBatchSend(ctx context.Context, in *presentPB_.BatchSendPresentReq) (out *presentPB_.BatchSendPresentResp, err error) {
	out = &presentPB_.BatchSendPresentResp{}

	// 自定义上报
	startTime := time.Now()
	defer func() {
		metrics.ReportMetrics(int32(in.GetItemSource()), err, time.Since(startTime))
	}()

	// 先获取送礼所需的一些相关info

	sendInfo, err := info.InitRequestBaseInfoWithBatchSendReq(ctx, s.sendPresentConf.GetConf(), in, out)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSendPresent InitRequestBaseInfoWithBatchSendReq fail , req: %v , err: %v", in, err)
		return out, err
	}

	// 拦截器
	err = interceptor.WithInterceptors(sendInfo, batch_send2.IopInterceptor, common2.UsualDeviceInterceptor, common2.LiveAddGroupInterceptor,
		common2.NobilityInterceptor, common2.CurrencyLimitInterceptor, batch_send2.SinglePriceInterceptor, batch_send2.PresentCountInterceptor,
		common2.ChannelInterceptor, common2.OptValidInterceptor, common2.CustomPresentInterceptor, batch_send2.PresentBoxInterceptor, common2.RevenueGameInterceptor,
		batch_send2.TicketInterceptor, common2.TreasurePrivilegeInterceptor, common2.FansPresentInterceptor, common2.PresentSetInterceptor, batch_send2.InternalInterceptor,
		common2.BirthdayPresentInterceptor, common2.PgcTitleInterceptor)

	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSendPresent WithInterceptors fail , req: %v , err: %v", in, err)
		return out, err
	}

	// 送礼
	req := &present_middleware.SendPresentReq{
		SendUid:   sendInfo.SendPresentUser.GetUid(),
		BatchType: uint32(present_middleware.PresentBatchSendType_PRESENT_SOURCE_ALL_MIC),
		TargetInfo: &present_middleware.PresentTargetInfo{
			Target: &present_middleware.PresentTargetInfo_MultiTarget{
				MultiTarget: &present_middleware.MultiTargetUser{
					Uid:    in.GetUidList(),
					ItemId: in.GetItemId(),
					Count:  in.GetCount(),
				},
			},
		},
		ChannelId:      in.GetChannelId(),
		SendSource:     in.GetSendSource(),
		ItemSource:     in.GetItemSource(),
		SendMethod:     uint32(present_middleware.PresentSendMethodType_PRESENT_TYPE_ROOM),
		SendType:       in.GetSendType(),
		IsOptValid:     sendInfo.IsOpValid,
		WithPay:        true,
		BackpackItemId: in.GetSourceId(),
		WithPush:       true,
		PushInfo: &present_middleware.PushInfo{
			ChannelPushType:  uint32(present_middleware.PushInfo_Channel_ALLMIC),
			PersonalPushType: uint32(present_middleware.PushInfo_Person_NORMAL),
			ImMsgType:        uint32(present_middleware.PushInfo_IM_NONE),
		},
		ClientInfo: &present_middleware.PresentClientInfo{
			AppId:    sendInfo.GetBaseReq().GetAppId(),
			MarketId: sendInfo.GetBaseReq().GetMarketId(),
			ServiceInfo: &present_middleware.ServiceCtrlInfo{
				ClientIp:      strconv.Itoa(int(sendInfo.GetServiceInfo().ClientIP)),
				ClientType:    uint32(sendInfo.GetServiceInfo().ClientType),
				ClientVersion: sendInfo.GetServiceInfo().ClientVersion,
				DeviceId:      sendInfo.GetServiceInfo().DeviceID,
				TerminalType:  sendInfo.GetServiceInfo().TerminalType,
			},
		},
		SendTime:            0,
		SurpriseEffectCount: 0,
		ChannelGameId:       sendInfo.ExtGameType,
		BirthdayType:        sendInfo.BirthDayPresentType,
	}

	// 涂鸦的需要额外处理下
	req.DrawPresentPic = getDrawPicResp(in.GetDrawPresentPic())

	resp, err := client.PresentMiddlewareCli.SendPresent(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchSendPresent SendPresent fail , req: %v , err: %v", in, err)
		return out, err
	}

	// 填充回包
	fillBatchResp(in, out, resp)

	return out, err
}

func fillBatchResp(in *presentPB_.BatchSendPresentReq, out *presentPB_.BatchSendPresentResp, resp *present_middleware.SendPresentResp) {
	// 单个送礼只会有一个msginfo
	if len(resp.GetMsgInfo()) == 0 {
		return
	}

	out.SourceId = resp.GetSourceId()
	out.ExpireTime = resp.GetExpireTime()
	out.CurTbeans = uint64(resp.GetCurTbeans())
	out.SourceRemain = resp.GetSourceRemain()
	out.ItemSource = resp.GetItemSource()

	out.ItemInfo = &presentPB_.PresentSendItemInfo{
		ItemId:            resp.GetMsgInfo()[0].GetItemInfo().GetItemId(),
		Count:             resp.GetMsgInfo()[0].GetItemInfo().GetCount(),
		ShowEffect:        resp.GetMsgInfo()[0].GetItemInfo().GetShowEffect(),
		ShowEffectV2:      resp.GetMsgInfo()[0].GetItemInfo().GetShowEffectV2(),
		FlowId:            resp.GetMsgInfo()[0].GetItemInfo().GetFlowId(),
		IsBatch:           resp.GetMsgInfo()[0].GetItemInfo().GetIsBatch(),
		ShowBatchEffect:   resp.GetMsgInfo()[0].GetItemInfo().GetShowBatchEffect(),
		SendType:          resp.GetMsgInfo()[0].GetItemInfo().GetSendType(),
		DrawPresentPic:    in.GetDrawPresentPic(),
		DynamicTemplateId: resp.GetMsgInfo()[0].GetItemInfo().GetDynamicTemplateId(),
		IsVisibleToSender: resp.GetMsgInfo()[0].GetItemInfo().GetIsVisibleToSender(),
		CustomTextJson:    resp.GetMsgInfo()[0].GetItemInfo().GetCustomTextJson(),
	}

	if len(resp.GetBatchInfo()) == 0 {
		return
	}

	msgInfo := resp.GetMsgInfo()[0]
	out.MsgInfo = &presentPB_.PresentBatchInfoMsg{
		ItemInfo: &presentPB_.PresentSendItemInfo{
			ItemId:            msgInfo.GetItemInfo().GetItemId(),
			Count:             msgInfo.GetItemInfo().GetCount(),
			ShowEffect:        msgInfo.GetItemInfo().GetShowEffect(),
			ShowEffectV2:      msgInfo.GetItemInfo().GetShowEffectV2(),
			FlowId:            msgInfo.GetItemInfo().GetFlowId(),
			IsBatch:           msgInfo.GetItemInfo().GetIsBatch(),
			ShowBatchEffect:   msgInfo.GetItemInfo().GetShowBatchEffect(),
			SendType:          msgInfo.GetItemInfo().GetSendType(),
			DrawPresentPic:    in.GetDrawPresentPic(),
			DynamicTemplateId: msgInfo.GetItemInfo().GetDynamicTemplateId(),
			IsVisibleToSender: msgInfo.GetItemInfo().IsVisibleToSender,
			CustomTextJson:    msgInfo.GetItemInfo().GetCustomTextJson(),
		},
		ItemId:         in.GetItemId(),
		TotalItemCount: in.GetCount() * uint32(len(resp.GetBatchInfo()[0].GetTargetList())),
		BatchType:      in.GetBatchType(),
		SendTime:       msgInfo.GetSendTime(),
		ChannelId:      msgInfo.GetChannelId(),
		SendUid:        msgInfo.GetSendUid(),
		SendAccount:    msgInfo.GetSendAccount(),
		SendNickname:   msgInfo.GetSendNickname(),
		ExtendJson:     msgInfo.GetExtendJson(),
		FromUserProfile: &app.UserProfile{
			Uid:          msgInfo.GetFromUserProfile().GetUid(),
			Account:      msgInfo.GetFromUserProfile().GetAccount(),
			Nickname:     msgInfo.GetFromUserProfile().GetNickname(),
			AccountAlias: msgInfo.GetFromUserProfile().GetAccountAlias(),
			Sex:          msgInfo.GetFromUserProfile().GetSex(),
			Privilege: &app.UserPrivilege{
				Account:  msgInfo.GetFromUserProfile().GetPrivilege().GetAccount(),
				Nickname: msgInfo.GetFromUserProfile().GetPrivilege().GetNickname(),
				Type:     msgInfo.GetFromUserProfile().GetPrivilege().GetType(),
				Options:  msgInfo.GetFromUserProfile().GetPrivilege().GetOptions(),
			},
		},
	}

	targetList := make([]*presentPB_.PresentBatchTargetInfo, 0)
	for _, item := range resp.GetBatchInfo()[0].GetTargetList() {
		targetList = append(targetList, &presentPB_.PresentBatchTargetInfo{
			Uid:        item.GetUid(),
			Account:    item.GetAccount(),
			Nickname:   item.GetNickname(),
			ExtendJson: item.GetExtendJson(),
			UserProfile: &app.UserProfile{
				Uid:          item.GetUserProfile().GetUid(),
				Account:      item.GetUserProfile().GetAccount(),
				Nickname:     item.GetUserProfile().GetNickname(),
				AccountAlias: item.GetUserProfile().GetAccountAlias(),
				Sex:          item.GetUserProfile().GetSex(),
				Privilege: &app.UserPrivilege{
					Account:  item.GetUserProfile().GetPrivilege().GetAccount(),
					Nickname: item.GetUserProfile().GetPrivilege().GetNickname(),
					Type:     item.GetUserProfile().GetPrivilege().GetType(),
					Options:  item.GetUserProfile().GetPrivilege().GetOptions(),
				},
			},
			CustomText: item.GetCustomText(),
		})
	}

	batchTargetList := make([]*presentPB_.PresentTargetUserInfo, 0)
	for _, item := range resp.GetBatchInfo()[0].GetTargetList() {
		batchTargetList = append(batchTargetList, &presentPB_.PresentTargetUserInfo{
			Uid:     item.GetUid(),
			Account: item.GetAccount(),
			Name:    item.GetNickname(),
			UserProfile: &app.UserProfile{
				Uid:          item.GetUserProfile().GetUid(),
				Account:      item.GetUserProfile().GetAccount(),
				Nickname:     item.GetUserProfile().GetNickname(),
				AccountAlias: item.GetUserProfile().GetAccountAlias(),
				Sex:          item.GetUserProfile().GetSex(),
				Privilege: &app.UserPrivilege{
					Account:  item.GetUserProfile().GetPrivilege().GetAccount(),
					Nickname: item.GetUserProfile().GetPrivilege().GetNickname(),
					Type:     item.GetUserProfile().GetPrivilege().GetType(),
					Options:  item.GetUserProfile().GetPrivilege().GetOptions(),
				},
			},
		})
	}

	out.TargetList = batchTargetList
	out.MsgInfo.TargetList = targetList
}
