// Code generated by protoc-gen-go. DO NOT EDIT.
// source: exchange_api.proto

package api

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	any "github.com/golang/protobuf/ptypes/any"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CurrencyType int32

const (
	CurrencyType_Unspecified CurrencyType = 0
	CurrencyType_TBean       CurrencyType = 1
	CurrencyType_Points      CurrencyType = 2
	CurrencyType_RedDiamond  CurrencyType = 3
	CurrencyType_Commission  CurrencyType = 4
)

var CurrencyType_name = map[int32]string{
	0: "Unspecified",
	1: "TBean",
	2: "Points",
	3: "RedDiamond",
	4: "Commission",
}

var CurrencyType_value = map[string]int32{
	"Unspecified": 0,
	"TBean":       1,
	"Points":      2,
	"RedDiamond":  3,
	"Commission":  4,
}

func (x CurrencyType) String() string {
	return proto.EnumName(CurrencyType_name, int32(x))
}

func (CurrencyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{0}
}

type SettlementBillType int32

const (
	SettlementBillType_UnKnownBillType     SettlementBillType = 0
	SettlementBillType_GiftScore           SettlementBillType = 1
	SettlementBillType_AwardScore          SettlementBillType = 2
	SettlementBillType_MaskPKScore         SettlementBillType = 3
	SettlementBillType_AmuseCommission     SettlementBillType = 4
	SettlementBillType_YuyinBaseCommission SettlementBillType = 5
	SettlementBillType_MonthMiddle         SettlementBillType = 6
	SettlementBillType_DeepCoop            SettlementBillType = 7
	SettlementBillType_YuyinSubsidy        SettlementBillType = 8
	SettlementBillType_KnightScore         SettlementBillType = 9
	SettlementBillType_AmuseExtra          SettlementBillType = 10
)

var SettlementBillType_name = map[int32]string{
	0:  "UnKnownBillType",
	1:  "GiftScore",
	2:  "AwardScore",
	3:  "MaskPKScore",
	4:  "AmuseCommission",
	5:  "YuyinBaseCommission",
	6:  "MonthMiddle",
	7:  "DeepCoop",
	8:  "YuyinSubsidy",
	9:  "KnightScore",
	10: "AmuseExtra",
}

var SettlementBillType_value = map[string]int32{
	"UnKnownBillType":     0,
	"GiftScore":           1,
	"AwardScore":          2,
	"MaskPKScore":         3,
	"AmuseCommission":     4,
	"YuyinBaseCommission": 5,
	"MonthMiddle":         6,
	"DeepCoop":            7,
	"YuyinSubsidy":        8,
	"KnightScore":         9,
	"AmuseExtra":          10,
}

func (x SettlementBillType) String() string {
	return proto.EnumName(SettlementBillType_name, int32(x))
}

func (SettlementBillType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{1}
}

type Response struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	ServerTime           uint32   `protobuf:"varint,3,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	Data                 *any.Any `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{0}
}

func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Response) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *Response) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *Response) GetData() *any.Any {
	if m != nil {
		return m.Data
	}
	return nil
}

type RedDiamondExchangeItem struct {
	ItemId               uint32       `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Desc                 string       `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	Amount               uint32       `protobuf:"varint,4,opt,name=amount,proto3" json:"amount"`
	BonusAmount          uint32       `protobuf:"varint,5,opt,name=bonus_amount,json=bonusAmount,proto3" json:"bonus_amount"`
	CurrencyType         CurrencyType `protobuf:"varint,6,opt,name=currency_type,json=currencyType,proto3,enum=api.CurrencyType" json:"currency_type"`
	Price                uint32       `protobuf:"varint,7,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RedDiamondExchangeItem) Reset()         { *m = RedDiamondExchangeItem{} }
func (m *RedDiamondExchangeItem) String() string { return proto.CompactTextString(m) }
func (*RedDiamondExchangeItem) ProtoMessage()    {}
func (*RedDiamondExchangeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{1}
}

func (m *RedDiamondExchangeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RedDiamondExchangeItem.Unmarshal(m, b)
}
func (m *RedDiamondExchangeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RedDiamondExchangeItem.Marshal(b, m, deterministic)
}
func (m *RedDiamondExchangeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RedDiamondExchangeItem.Merge(m, src)
}
func (m *RedDiamondExchangeItem) XXX_Size() int {
	return xxx_messageInfo_RedDiamondExchangeItem.Size(m)
}
func (m *RedDiamondExchangeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RedDiamondExchangeItem.DiscardUnknown(m)
}

var xxx_messageInfo_RedDiamondExchangeItem proto.InternalMessageInfo

func (m *RedDiamondExchangeItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *RedDiamondExchangeItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RedDiamondExchangeItem) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *RedDiamondExchangeItem) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *RedDiamondExchangeItem) GetBonusAmount() uint32 {
	if m != nil {
		return m.BonusAmount
	}
	return 0
}

func (m *RedDiamondExchangeItem) GetCurrencyType() CurrencyType {
	if m != nil {
		return m.CurrencyType
	}
	return CurrencyType_Unspecified
}

func (m *RedDiamondExchangeItem) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type GetRedDiamondExchangeItemListResp struct {
	CurrencyType         CurrencyType              `protobuf:"varint,1,opt,name=currency_type,json=currencyType,proto3,enum=api.CurrencyType" json:"currency_type"`
	ItemList             []*RedDiamondExchangeItem `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetRedDiamondExchangeItemListResp) Reset()         { *m = GetRedDiamondExchangeItemListResp{} }
func (m *GetRedDiamondExchangeItemListResp) String() string { return proto.CompactTextString(m) }
func (*GetRedDiamondExchangeItemListResp) ProtoMessage()    {}
func (*GetRedDiamondExchangeItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{2}
}

func (m *GetRedDiamondExchangeItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRedDiamondExchangeItemListResp.Unmarshal(m, b)
}
func (m *GetRedDiamondExchangeItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRedDiamondExchangeItemListResp.Marshal(b, m, deterministic)
}
func (m *GetRedDiamondExchangeItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRedDiamondExchangeItemListResp.Merge(m, src)
}
func (m *GetRedDiamondExchangeItemListResp) XXX_Size() int {
	return xxx_messageInfo_GetRedDiamondExchangeItemListResp.Size(m)
}
func (m *GetRedDiamondExchangeItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRedDiamondExchangeItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRedDiamondExchangeItemListResp proto.InternalMessageInfo

func (m *GetRedDiamondExchangeItemListResp) GetCurrencyType() CurrencyType {
	if m != nil {
		return m.CurrencyType
	}
	return CurrencyType_Unspecified
}

func (m *GetRedDiamondExchangeItemListResp) GetItemList() []*RedDiamondExchangeItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ExchangeResult struct {
	// CurrencyType currency_type = 1;
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount"`
	BonusAmount          uint32   `protobuf:"varint,3,opt,name=bonus_amount,json=bonusAmount,proto3" json:"bonus_amount"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeResult) Reset()         { *m = ExchangeResult{} }
func (m *ExchangeResult) String() string { return proto.CompactTextString(m) }
func (*ExchangeResult) ProtoMessage()    {}
func (*ExchangeResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{3}
}

func (m *ExchangeResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeResult.Unmarshal(m, b)
}
func (m *ExchangeResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeResult.Marshal(b, m, deterministic)
}
func (m *ExchangeResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeResult.Merge(m, src)
}
func (m *ExchangeResult) XXX_Size() int {
	return xxx_messageInfo_ExchangeResult.Size(m)
}
func (m *ExchangeResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeResult.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeResult proto.InternalMessageInfo

func (m *ExchangeResult) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ExchangeResult) GetBonusAmount() uint32 {
	if m != nil {
		return m.BonusAmount
	}
	return 0
}

func (m *ExchangeResult) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

type ExchangeReq struct {
	CurrencyType         CurrencyType    `protobuf:"varint,1,opt,name=currency_type,json=currencyType,proto3,enum=api.CurrencyType" json:"currency_type"`
	ExpectedResult       *ExchangeResult `protobuf:"bytes,2,opt,name=expected_result,json=expectedResult,proto3" json:"expected_result"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ExchangeReq) Reset()         { *m = ExchangeReq{} }
func (m *ExchangeReq) String() string { return proto.CompactTextString(m) }
func (*ExchangeReq) ProtoMessage()    {}
func (*ExchangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{4}
}

func (m *ExchangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeReq.Unmarshal(m, b)
}
func (m *ExchangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeReq.Marshal(b, m, deterministic)
}
func (m *ExchangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeReq.Merge(m, src)
}
func (m *ExchangeReq) XXX_Size() int {
	return xxx_messageInfo_ExchangeReq.Size(m)
}
func (m *ExchangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeReq proto.InternalMessageInfo

func (m *ExchangeReq) GetCurrencyType() CurrencyType {
	if m != nil {
		return m.CurrencyType
	}
	return CurrencyType_Unspecified
}

func (m *ExchangeReq) GetExpectedResult() *ExchangeResult {
	if m != nil {
		return m.ExpectedResult
	}
	return nil
}

type ExchangeResp struct {
	Record               *Transaction `protobuf:"bytes,1,opt,name=record,proto3" json:"record"`
	Done                 bool         `protobuf:"varint,2,opt,name=Done,proto3" json:"Done"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExchangeResp) Reset()         { *m = ExchangeResp{} }
func (m *ExchangeResp) String() string { return proto.CompactTextString(m) }
func (*ExchangeResp) ProtoMessage()    {}
func (*ExchangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{5}
}

func (m *ExchangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeResp.Unmarshal(m, b)
}
func (m *ExchangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeResp.Marshal(b, m, deterministic)
}
func (m *ExchangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeResp.Merge(m, src)
}
func (m *ExchangeResp) XXX_Size() int {
	return xxx_messageInfo_ExchangeResp.Size(m)
}
func (m *ExchangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeResp proto.InternalMessageInfo

func (m *ExchangeResp) GetRecord() *Transaction {
	if m != nil {
		return m.Record
	}
	return nil
}

func (m *ExchangeResp) GetDone() bool {
	if m != nil {
		return m.Done
	}
	return false
}

type ProfileResp struct {
	RedDiamonds           int32           `protobuf:"varint,1,opt,name=red_diamonds,json=redDiamonds,proto3" json:"red_diamonds"`
	Tbeans                int32           `protobuf:"varint,2,opt,name=tbeans,proto3" json:"tbeans"`
	Points                int32           `protobuf:"varint,3,opt,name=points,proto3" json:"points"`
	MaxTbeanToRedDiamond  *ExchangeResult `protobuf:"bytes,4,opt,name=max_tbean_to_red_diamond,json=maxTbeanToRedDiamond,proto3" json:"max_tbean_to_red_diamond"`
	MaxPointsToRedDiamond *ExchangeResult `protobuf:"bytes,5,opt,name=max_points_to_red_diamond,json=maxPointsToRedDiamond,proto3" json:"max_points_to_red_diamond"`
	XXX_NoUnkeyedLiteral  struct{}        `json:"-"`
	XXX_unrecognized      []byte          `json:"-"`
	XXX_sizecache         int32           `json:"-"`
}

func (m *ProfileResp) Reset()         { *m = ProfileResp{} }
func (m *ProfileResp) String() string { return proto.CompactTextString(m) }
func (*ProfileResp) ProtoMessage()    {}
func (*ProfileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{6}
}

func (m *ProfileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProfileResp.Unmarshal(m, b)
}
func (m *ProfileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProfileResp.Marshal(b, m, deterministic)
}
func (m *ProfileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProfileResp.Merge(m, src)
}
func (m *ProfileResp) XXX_Size() int {
	return xxx_messageInfo_ProfileResp.Size(m)
}
func (m *ProfileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProfileResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProfileResp proto.InternalMessageInfo

func (m *ProfileResp) GetRedDiamonds() int32 {
	if m != nil {
		return m.RedDiamonds
	}
	return 0
}

func (m *ProfileResp) GetTbeans() int32 {
	if m != nil {
		return m.Tbeans
	}
	return 0
}

func (m *ProfileResp) GetPoints() int32 {
	if m != nil {
		return m.Points
	}
	return 0
}

func (m *ProfileResp) GetMaxTbeanToRedDiamond() *ExchangeResult {
	if m != nil {
		return m.MaxTbeanToRedDiamond
	}
	return nil
}

func (m *ProfileResp) GetMaxPointsToRedDiamond() *ExchangeResult {
	if m != nil {
		return m.MaxPointsToRedDiamond
	}
	return nil
}

type Transaction struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	TargetType           CurrencyType `protobuf:"varint,3,opt,name=target_type,json=targetType,proto3,enum=api.CurrencyType" json:"target_type"`
	Amount               uint32       `protobuf:"varint,4,opt,name=amount,proto3" json:"amount"`
	CurrencyType         CurrencyType `protobuf:"varint,5,opt,name=currency_type,json=currencyType,proto3,enum=api.CurrencyType" json:"currency_type"`
	Price                uint32       `protobuf:"varint,6,opt,name=price,proto3" json:"price"`
	CreateAt             uint32       `protobuf:"varint,7,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	Desc                 string       `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Transaction) Reset()         { *m = Transaction{} }
func (m *Transaction) String() string { return proto.CompactTextString(m) }
func (*Transaction) ProtoMessage()    {}
func (*Transaction) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{7}
}

func (m *Transaction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Transaction.Unmarshal(m, b)
}
func (m *Transaction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Transaction.Marshal(b, m, deterministic)
}
func (m *Transaction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Transaction.Merge(m, src)
}
func (m *Transaction) XXX_Size() int {
	return xxx_messageInfo_Transaction.Size(m)
}
func (m *Transaction) XXX_DiscardUnknown() {
	xxx_messageInfo_Transaction.DiscardUnknown(m)
}

var xxx_messageInfo_Transaction proto.InternalMessageInfo

func (m *Transaction) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Transaction) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Transaction) GetTargetType() CurrencyType {
	if m != nil {
		return m.TargetType
	}
	return CurrencyType_Unspecified
}

func (m *Transaction) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *Transaction) GetCurrencyType() CurrencyType {
	if m != nil {
		return m.CurrencyType
	}
	return CurrencyType_Unspecified
}

func (m *Transaction) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *Transaction) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *Transaction) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type TransactionV2 struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	TargetType           CurrencyType `protobuf:"varint,3,opt,name=target_type,json=targetType,proto3,enum=api.CurrencyType" json:"target_type"`
	Amount               int32        `protobuf:"varint,4,opt,name=amount,proto3" json:"amount"`
	CurrencyType         CurrencyType `protobuf:"varint,5,opt,name=currency_type,json=currencyType,proto3,enum=api.CurrencyType" json:"currency_type"`
	Price                int32        `protobuf:"varint,6,opt,name=price,proto3" json:"price"`
	CreateAt             uint32       `protobuf:"varint,7,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	Desc                 string       `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc"`
	OrderId              string       `protobuf:"bytes,9,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TransactionV2) Reset()         { *m = TransactionV2{} }
func (m *TransactionV2) String() string { return proto.CompactTextString(m) }
func (*TransactionV2) ProtoMessage()    {}
func (*TransactionV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{8}
}

func (m *TransactionV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransactionV2.Unmarshal(m, b)
}
func (m *TransactionV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransactionV2.Marshal(b, m, deterministic)
}
func (m *TransactionV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransactionV2.Merge(m, src)
}
func (m *TransactionV2) XXX_Size() int {
	return xxx_messageInfo_TransactionV2.Size(m)
}
func (m *TransactionV2) XXX_DiscardUnknown() {
	xxx_messageInfo_TransactionV2.DiscardUnknown(m)
}

var xxx_messageInfo_TransactionV2 proto.InternalMessageInfo

func (m *TransactionV2) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TransactionV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransactionV2) GetTargetType() CurrencyType {
	if m != nil {
		return m.TargetType
	}
	return CurrencyType_Unspecified
}

func (m *TransactionV2) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *TransactionV2) GetCurrencyType() CurrencyType {
	if m != nil {
		return m.CurrencyType
	}
	return CurrencyType_Unspecified
}

func (m *TransactionV2) GetPrice() int32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TransactionV2) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TransactionV2) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *TransactionV2) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetTransactionsResp struct {
	List                 []*TransactionV2 `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetTransactionsResp) Reset()         { *m = GetTransactionsResp{} }
func (m *GetTransactionsResp) String() string { return proto.CompactTextString(m) }
func (*GetTransactionsResp) ProtoMessage()    {}
func (*GetTransactionsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{9}
}

func (m *GetTransactionsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTransactionsResp.Unmarshal(m, b)
}
func (m *GetTransactionsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTransactionsResp.Marshal(b, m, deterministic)
}
func (m *GetTransactionsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTransactionsResp.Merge(m, src)
}
func (m *GetTransactionsResp) XXX_Size() int {
	return xxx_messageInfo_GetTransactionsResp.Size(m)
}
func (m *GetTransactionsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTransactionsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTransactionsResp proto.InternalMessageInfo

func (m *GetTransactionsResp) GetList() []*TransactionV2 {
	if m != nil {
		return m.List
	}
	return nil
}

type EstimateResp struct {
	ExpectedResult       *ExchangeResult `protobuf:"bytes,1,opt,name=expected_result,json=expectedResult,proto3" json:"expected_result"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *EstimateResp) Reset()         { *m = EstimateResp{} }
func (m *EstimateResp) String() string { return proto.CompactTextString(m) }
func (*EstimateResp) ProtoMessage()    {}
func (*EstimateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{10}
}

func (m *EstimateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EstimateResp.Unmarshal(m, b)
}
func (m *EstimateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EstimateResp.Marshal(b, m, deterministic)
}
func (m *EstimateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstimateResp.Merge(m, src)
}
func (m *EstimateResp) XXX_Size() int {
	return xxx_messageInfo_EstimateResp.Size(m)
}
func (m *EstimateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EstimateResp.DiscardUnknown(m)
}

var xxx_messageInfo_EstimateResp proto.InternalMessageInfo

func (m *EstimateResp) GetExpectedResult() *ExchangeResult {
	if m != nil {
		return m.ExpectedResult
	}
	return nil
}

type PointsToTBeanReq struct {
	OrderId                    string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Points                     uint32   `protobuf:"varint,2,opt,name=points,proto3" json:"points"`
	MarketId                   string   `protobuf:"bytes,3,opt,name=market_id,json=marketId,proto3" json:"market_id"`
	RequestId                  string   `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id"`
	DeviceId                   string   `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	ClientVersion              uint32   `protobuf:"varint,6,opt,name=client_version,json=clientVersion,proto3" json:"client_version"`
	ClientType                 uint32   `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type"`
	FaceAuthToken              string   `protobuf:"bytes,8,opt,name=face_auth_token,json=faceAuthToken,proto3" json:"face_auth_token"`
	FaceAuthScene              uint32   `protobuf:"varint,9,opt,name=face_auth_scene,json=faceAuthScene,proto3" json:"face_auth_scene"`
	SourceType                 uint32   `protobuf:"varint,10,opt,name=source_type,json=sourceType,proto3" json:"source_type"`
	FaceAuthProviderCode       string   `protobuf:"bytes,11,opt,name=face_auth_provider_code,json=faceAuthProviderCode,proto3" json:"face_auth_provider_code"`
	FaceAuthProviderResultCode string   `protobuf:"bytes,12,opt,name=face_auth_provider_result_code,json=faceAuthProviderResultCode,proto3" json:"face_auth_provider_result_code"`
	FaceAuthResultToken        string   `protobuf:"bytes,13,opt,name=face_auth_result_token,json=faceAuthResultToken,proto3" json:"face_auth_result_token"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *PointsToTBeanReq) Reset()         { *m = PointsToTBeanReq{} }
func (m *PointsToTBeanReq) String() string { return proto.CompactTextString(m) }
func (*PointsToTBeanReq) ProtoMessage()    {}
func (*PointsToTBeanReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{11}
}

func (m *PointsToTBeanReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToTBeanReq.Unmarshal(m, b)
}
func (m *PointsToTBeanReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToTBeanReq.Marshal(b, m, deterministic)
}
func (m *PointsToTBeanReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToTBeanReq.Merge(m, src)
}
func (m *PointsToTBeanReq) XXX_Size() int {
	return xxx_messageInfo_PointsToTBeanReq.Size(m)
}
func (m *PointsToTBeanReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToTBeanReq.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToTBeanReq proto.InternalMessageInfo

func (m *PointsToTBeanReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *PointsToTBeanReq) GetPoints() uint32 {
	if m != nil {
		return m.Points
	}
	return 0
}

func (m *PointsToTBeanReq) GetMarketId() string {
	if m != nil {
		return m.MarketId
	}
	return ""
}

func (m *PointsToTBeanReq) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PointsToTBeanReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *PointsToTBeanReq) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *PointsToTBeanReq) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *PointsToTBeanReq) GetFaceAuthToken() string {
	if m != nil {
		return m.FaceAuthToken
	}
	return ""
}

func (m *PointsToTBeanReq) GetFaceAuthScene() uint32 {
	if m != nil {
		return m.FaceAuthScene
	}
	return 0
}

func (m *PointsToTBeanReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *PointsToTBeanReq) GetFaceAuthProviderCode() string {
	if m != nil {
		return m.FaceAuthProviderCode
	}
	return ""
}

func (m *PointsToTBeanReq) GetFaceAuthProviderResultCode() string {
	if m != nil {
		return m.FaceAuthProviderResultCode
	}
	return ""
}

func (m *PointsToTBeanReq) GetFaceAuthResultToken() string {
	if m != nil {
		return m.FaceAuthResultToken
	}
	return ""
}

type PointsToTBeanResp struct {
	Record               *Transaction `protobuf:"bytes,1,opt,name=record,proto3" json:"record"`
	Done                 bool         `protobuf:"varint,2,opt,name=Done,proto3" json:"Done"`
	RequestId            string       `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id"`
	FaceAuthScene        uint32       `protobuf:"varint,4,opt,name=face_auth_scene,json=faceAuthScene,proto3" json:"face_auth_scene"`
	FaceAuthContextJson  string       `protobuf:"bytes,5,opt,name=face_auth_context_json,json=faceAuthContextJson,proto3" json:"face_auth_context_json"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PointsToTBeanResp) Reset()         { *m = PointsToTBeanResp{} }
func (m *PointsToTBeanResp) String() string { return proto.CompactTextString(m) }
func (*PointsToTBeanResp) ProtoMessage()    {}
func (*PointsToTBeanResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{12}
}

func (m *PointsToTBeanResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToTBeanResp.Unmarshal(m, b)
}
func (m *PointsToTBeanResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToTBeanResp.Marshal(b, m, deterministic)
}
func (m *PointsToTBeanResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToTBeanResp.Merge(m, src)
}
func (m *PointsToTBeanResp) XXX_Size() int {
	return xxx_messageInfo_PointsToTBeanResp.Size(m)
}
func (m *PointsToTBeanResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToTBeanResp.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToTBeanResp proto.InternalMessageInfo

func (m *PointsToTBeanResp) GetRecord() *Transaction {
	if m != nil {
		return m.Record
	}
	return nil
}

func (m *PointsToTBeanResp) GetDone() bool {
	if m != nil {
		return m.Done
	}
	return false
}

func (m *PointsToTBeanResp) GetRequestId() string {
	if m != nil {
		return m.RequestId
	}
	return ""
}

func (m *PointsToTBeanResp) GetFaceAuthScene() uint32 {
	if m != nil {
		return m.FaceAuthScene
	}
	return 0
}

func (m *PointsToTBeanResp) GetFaceAuthContextJson() string {
	if m != nil {
		return m.FaceAuthContextJson
	}
	return ""
}

type VerifyCodeResp struct {
	Cooldown             uint32   `protobuf:"varint,1,opt,name=cooldown,proto3" json:"cooldown"`
	Code                 int32    `protobuf:"varint,2,opt,name=code,proto3" json:"code"`
	Msg                  string   `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg"`
	Optype               int32    `protobuf:"varint,4,opt,name=optype,proto3" json:"optype"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyCodeResp) Reset()         { *m = VerifyCodeResp{} }
func (m *VerifyCodeResp) String() string { return proto.CompactTextString(m) }
func (*VerifyCodeResp) ProtoMessage()    {}
func (*VerifyCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{13}
}

func (m *VerifyCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyCodeResp.Unmarshal(m, b)
}
func (m *VerifyCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyCodeResp.Marshal(b, m, deterministic)
}
func (m *VerifyCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyCodeResp.Merge(m, src)
}
func (m *VerifyCodeResp) XXX_Size() int {
	return xxx_messageInfo_VerifyCodeResp.Size(m)
}
func (m *VerifyCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyCodeResp proto.InternalMessageInfo

func (m *VerifyCodeResp) GetCooldown() uint32 {
	if m != nil {
		return m.Cooldown
	}
	return 0
}

func (m *VerifyCodeResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *VerifyCodeResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *VerifyCodeResp) GetOptype() int32 {
	if m != nil {
		return m.Optype
	}
	return 0
}

type PointsToCashRecord struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	BankName             string   `protobuf:"bytes,2,opt,name=bank_name,json=bankName,proto3" json:"bank_name"`
	CardNo               string   `protobuf:"bytes,3,opt,name=card_no,json=cardNo,proto3" json:"card_no"`
	Income               float32  `protobuf:"fixed32,4,opt,name=income,proto3" json:"income"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Time                 uint32   `protobuf:"varint,6,opt,name=time,proto3" json:"time"`
	Remark               string   `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PointsToCashRecord) Reset()         { *m = PointsToCashRecord{} }
func (m *PointsToCashRecord) String() string { return proto.CompactTextString(m) }
func (*PointsToCashRecord) ProtoMessage()    {}
func (*PointsToCashRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{14}
}

func (m *PointsToCashRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToCashRecord.Unmarshal(m, b)
}
func (m *PointsToCashRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToCashRecord.Marshal(b, m, deterministic)
}
func (m *PointsToCashRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToCashRecord.Merge(m, src)
}
func (m *PointsToCashRecord) XXX_Size() int {
	return xxx_messageInfo_PointsToCashRecord.Size(m)
}
func (m *PointsToCashRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToCashRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToCashRecord proto.InternalMessageInfo

func (m *PointsToCashRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PointsToCashRecord) GetBankName() string {
	if m != nil {
		return m.BankName
	}
	return ""
}

func (m *PointsToCashRecord) GetCardNo() string {
	if m != nil {
		return m.CardNo
	}
	return ""
}

func (m *PointsToCashRecord) GetIncome() float32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *PointsToCashRecord) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PointsToCashRecord) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *PointsToCashRecord) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type PointsToCashRecordResult struct {
	List                 []*PointsToCashRecord `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	ServerTime           uint32                `protobuf:"varint,3,opt,name=server_time,json=serverTime,proto3" json:"server_time"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *PointsToCashRecordResult) Reset()         { *m = PointsToCashRecordResult{} }
func (m *PointsToCashRecordResult) String() string { return proto.CompactTextString(m) }
func (*PointsToCashRecordResult) ProtoMessage()    {}
func (*PointsToCashRecordResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{15}
}

func (m *PointsToCashRecordResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToCashRecordResult.Unmarshal(m, b)
}
func (m *PointsToCashRecordResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToCashRecordResult.Marshal(b, m, deterministic)
}
func (m *PointsToCashRecordResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToCashRecordResult.Merge(m, src)
}
func (m *PointsToCashRecordResult) XXX_Size() int {
	return xxx_messageInfo_PointsToCashRecordResult.Size(m)
}
func (m *PointsToCashRecordResult) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToCashRecordResult.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToCashRecordResult proto.InternalMessageInfo

func (m *PointsToCashRecordResult) GetList() []*PointsToCashRecord {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *PointsToCashRecordResult) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *PointsToCashRecordResult) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

type PointsToCashInfo struct {
	Income               uint32   `protobuf:"varint,1,opt,name=income,proto3" json:"income"`
	Points               uint32   `protobuf:"varint,2,opt,name=points,proto3" json:"points"`
	WithdrawStatus       uint32   `protobuf:"varint,3,opt,name=withdraw_status,json=withdrawStatus,proto3" json:"withdraw_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PointsToCashInfo) Reset()         { *m = PointsToCashInfo{} }
func (m *PointsToCashInfo) String() string { return proto.CompactTextString(m) }
func (*PointsToCashInfo) ProtoMessage()    {}
func (*PointsToCashInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{16}
}

func (m *PointsToCashInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToCashInfo.Unmarshal(m, b)
}
func (m *PointsToCashInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToCashInfo.Marshal(b, m, deterministic)
}
func (m *PointsToCashInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToCashInfo.Merge(m, src)
}
func (m *PointsToCashInfo) XXX_Size() int {
	return xxx_messageInfo_PointsToCashInfo.Size(m)
}
func (m *PointsToCashInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToCashInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToCashInfo proto.InternalMessageInfo

func (m *PointsToCashInfo) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *PointsToCashInfo) GetPoints() uint32 {
	if m != nil {
		return m.Points
	}
	return 0
}

func (m *PointsToCashInfo) GetWithdrawStatus() uint32 {
	if m != nil {
		return m.WithdrawStatus
	}
	return 0
}

type PointsToCashResp struct {
	PointsInfo           *PointsToCashInfo         `protobuf:"bytes,1,opt,name=points_info,json=pointsInfo,proto3" json:"points_info"`
	DrawingRecord        *PointsToCashRecordResult `protobuf:"bytes,2,opt,name=drawing_record,json=drawingRecord,proto3" json:"drawing_record"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PointsToCashResp) Reset()         { *m = PointsToCashResp{} }
func (m *PointsToCashResp) String() string { return proto.CompactTextString(m) }
func (*PointsToCashResp) ProtoMessage()    {}
func (*PointsToCashResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{17}
}

func (m *PointsToCashResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToCashResp.Unmarshal(m, b)
}
func (m *PointsToCashResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToCashResp.Marshal(b, m, deterministic)
}
func (m *PointsToCashResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToCashResp.Merge(m, src)
}
func (m *PointsToCashResp) XXX_Size() int {
	return xxx_messageInfo_PointsToCashResp.Size(m)
}
func (m *PointsToCashResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToCashResp.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToCashResp proto.InternalMessageInfo

func (m *PointsToCashResp) GetPointsInfo() *PointsToCashInfo {
	if m != nil {
		return m.PointsInfo
	}
	return nil
}

func (m *PointsToCashResp) GetDrawingRecord() *PointsToCashRecordResult {
	if m != nil {
		return m.DrawingRecord
	}
	return nil
}

type CheckEncashmentResult struct {
	WithdrawStatus       uint32   `protobuf:"varint,1,opt,name=withdraw_status,json=withdrawStatus,proto3" json:"withdraw_status"`
	DateStatus           uint32   `protobuf:"varint,2,opt,name=date_status,json=dateStatus,proto3" json:"date_status"`
	WithdrawType         uint32   `protobuf:"varint,3,opt,name=withdraw_type,json=withdrawType,proto3" json:"withdraw_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckEncashmentResult) Reset()         { *m = CheckEncashmentResult{} }
func (m *CheckEncashmentResult) String() string { return proto.CompactTextString(m) }
func (*CheckEncashmentResult) ProtoMessage()    {}
func (*CheckEncashmentResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{18}
}

func (m *CheckEncashmentResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckEncashmentResult.Unmarshal(m, b)
}
func (m *CheckEncashmentResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckEncashmentResult.Marshal(b, m, deterministic)
}
func (m *CheckEncashmentResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckEncashmentResult.Merge(m, src)
}
func (m *CheckEncashmentResult) XXX_Size() int {
	return xxx_messageInfo_CheckEncashmentResult.Size(m)
}
func (m *CheckEncashmentResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckEncashmentResult.DiscardUnknown(m)
}

var xxx_messageInfo_CheckEncashmentResult proto.InternalMessageInfo

func (m *CheckEncashmentResult) GetWithdrawStatus() uint32 {
	if m != nil {
		return m.WithdrawStatus
	}
	return 0
}

func (m *CheckEncashmentResult) GetDateStatus() uint32 {
	if m != nil {
		return m.DateStatus
	}
	return 0
}

func (m *CheckEncashmentResult) GetWithdrawType() uint32 {
	if m != nil {
		return m.WithdrawType
	}
	return 0
}

type PointsBalance struct {
	Balance              uint32   `protobuf:"varint,1,opt,name=balance,proto3" json:"balance"`
	Hidden               bool     `protobuf:"varint,2,opt,name=hidden,proto3" json:"hidden"`
	HasWithdraw          bool     `protobuf:"varint,3,opt,name=has_withdraw,json=hasWithdraw,proto3" json:"has_withdraw"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PointsBalance) Reset()         { *m = PointsBalance{} }
func (m *PointsBalance) String() string { return proto.CompactTextString(m) }
func (*PointsBalance) ProtoMessage()    {}
func (*PointsBalance) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{19}
}

func (m *PointsBalance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsBalance.Unmarshal(m, b)
}
func (m *PointsBalance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsBalance.Marshal(b, m, deterministic)
}
func (m *PointsBalance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsBalance.Merge(m, src)
}
func (m *PointsBalance) XXX_Size() int {
	return xxx_messageInfo_PointsBalance.Size(m)
}
func (m *PointsBalance) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsBalance.DiscardUnknown(m)
}

var xxx_messageInfo_PointsBalance proto.InternalMessageInfo

func (m *PointsBalance) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *PointsBalance) GetHidden() bool {
	if m != nil {
		return m.Hidden
	}
	return false
}

func (m *PointsBalance) GetHasWithdraw() bool {
	if m != nil {
		return m.HasWithdraw
	}
	return false
}

type TransactionV3 struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	Amount               int32    `protobuf:"varint,2,opt,name=amount,proto3" json:"amount"`
	CreateAt             uint32   `protobuf:"varint,3,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransactionV3) Reset()         { *m = TransactionV3{} }
func (m *TransactionV3) String() string { return proto.CompactTextString(m) }
func (*TransactionV3) ProtoMessage()    {}
func (*TransactionV3) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{20}
}

func (m *TransactionV3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransactionV3.Unmarshal(m, b)
}
func (m *TransactionV3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransactionV3.Marshal(b, m, deterministic)
}
func (m *TransactionV3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransactionV3.Merge(m, src)
}
func (m *TransactionV3) XXX_Size() int {
	return xxx_messageInfo_TransactionV3.Size(m)
}
func (m *TransactionV3) XXX_DiscardUnknown() {
	xxx_messageInfo_TransactionV3.DiscardUnknown(m)
}

var xxx_messageInfo_TransactionV3 proto.InternalMessageInfo

func (m *TransactionV3) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *TransactionV3) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *TransactionV3) GetCreateAt() uint32 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *TransactionV3) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetTransactionsV3Resp struct {
	List                 []*TransactionV3 `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetTransactionsV3Resp) Reset()         { *m = GetTransactionsV3Resp{} }
func (m *GetTransactionsV3Resp) String() string { return proto.CompactTextString(m) }
func (*GetTransactionsV3Resp) ProtoMessage()    {}
func (*GetTransactionsV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{21}
}

func (m *GetTransactionsV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTransactionsV3Resp.Unmarshal(m, b)
}
func (m *GetTransactionsV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTransactionsV3Resp.Marshal(b, m, deterministic)
}
func (m *GetTransactionsV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTransactionsV3Resp.Merge(m, src)
}
func (m *GetTransactionsV3Resp) XXX_Size() int {
	return xxx_messageInfo_GetTransactionsV3Resp.Size(m)
}
func (m *GetTransactionsV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTransactionsV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTransactionsV3Resp proto.InternalMessageInfo

func (m *GetTransactionsV3Resp) GetList() []*TransactionV3 {
	if m != nil {
		return m.List
	}
	return nil
}

type PointsToCashLimitResp struct {
	LimitRemain          uint32   `protobuf:"varint,1,opt,name=limit_remain,json=limitRemain,proto3" json:"limit_remain"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PointsToCashLimitResp) Reset()         { *m = PointsToCashLimitResp{} }
func (m *PointsToCashLimitResp) String() string { return proto.CompactTextString(m) }
func (*PointsToCashLimitResp) ProtoMessage()    {}
func (*PointsToCashLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{22}
}

func (m *PointsToCashLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PointsToCashLimitResp.Unmarshal(m, b)
}
func (m *PointsToCashLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PointsToCashLimitResp.Marshal(b, m, deterministic)
}
func (m *PointsToCashLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PointsToCashLimitResp.Merge(m, src)
}
func (m *PointsToCashLimitResp) XXX_Size() int {
	return xxx_messageInfo_PointsToCashLimitResp.Size(m)
}
func (m *PointsToCashLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PointsToCashLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_PointsToCashLimitResp proto.InternalMessageInfo

func (m *PointsToCashLimitResp) GetLimitRemain() uint32 {
	if m != nil {
		return m.LimitRemain
	}
	return 0
}

type AnchorScoreWithdrawRecord struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid"`
	GuildOwner           uint32   `protobuf:"varint,2,opt,name=guild_owner,json=guildOwner,proto3" json:"guild_owner"`
	BillId               string   `protobuf:"bytes,3,opt,name=bill_id,json=billId,proto3" json:"bill_id"`
	GuildOwnerWdMoney    uint64   `protobuf:"varint,4,opt,name=guild_owner_wd_money,json=guildOwnerWdMoney,proto3" json:"guild_owner_wd_money"`
	GuildOwnerWdMoneyCny string   `protobuf:"bytes,5,opt,name=guild_owner_wd_money_cny,json=guildOwnerWdMoneyCny,proto3" json:"guild_owner_wd_money_cny"`
	AnchorWdMoney        uint64   `protobuf:"varint,6,opt,name=anchor_wd_money,json=anchorWdMoney,proto3" json:"anchor_wd_money"`
	AnchorWdMoneyCny     string   `protobuf:"bytes,7,opt,name=anchor_wd_money_cny,json=anchorWdMoneyCny,proto3" json:"anchor_wd_money_cny"`
	SettleStart          uint64   `protobuf:"varint,8,opt,name=settle_start,json=settleStart,proto3" json:"settle_start"`
	SettleEnd            uint64   `protobuf:"varint,9,opt,name=settle_end,json=settleEnd,proto3" json:"settle_end"`
	WithdrawTime         uint64   `protobuf:"varint,10,opt,name=withdraw_time,json=withdrawTime,proto3" json:"withdraw_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorScoreWithdrawRecord) Reset()         { *m = AnchorScoreWithdrawRecord{} }
func (m *AnchorScoreWithdrawRecord) String() string { return proto.CompactTextString(m) }
func (*AnchorScoreWithdrawRecord) ProtoMessage()    {}
func (*AnchorScoreWithdrawRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{23}
}

func (m *AnchorScoreWithdrawRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorScoreWithdrawRecord.Unmarshal(m, b)
}
func (m *AnchorScoreWithdrawRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorScoreWithdrawRecord.Marshal(b, m, deterministic)
}
func (m *AnchorScoreWithdrawRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorScoreWithdrawRecord.Merge(m, src)
}
func (m *AnchorScoreWithdrawRecord) XXX_Size() int {
	return xxx_messageInfo_AnchorScoreWithdrawRecord.Size(m)
}
func (m *AnchorScoreWithdrawRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorScoreWithdrawRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorScoreWithdrawRecord proto.InternalMessageInfo

func (m *AnchorScoreWithdrawRecord) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetGuildOwner() uint32 {
	if m != nil {
		return m.GuildOwner
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *AnchorScoreWithdrawRecord) GetGuildOwnerWdMoney() uint64 {
	if m != nil {
		return m.GuildOwnerWdMoney
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetGuildOwnerWdMoneyCny() string {
	if m != nil {
		return m.GuildOwnerWdMoneyCny
	}
	return ""
}

func (m *AnchorScoreWithdrawRecord) GetAnchorWdMoney() uint64 {
	if m != nil {
		return m.AnchorWdMoney
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetAnchorWdMoneyCny() string {
	if m != nil {
		return m.AnchorWdMoneyCny
	}
	return ""
}

func (m *AnchorScoreWithdrawRecord) GetSettleStart() uint64 {
	if m != nil {
		return m.SettleStart
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetSettleEnd() uint64 {
	if m != nil {
		return m.SettleEnd
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetWithdrawTime() uint64 {
	if m != nil {
		return m.WithdrawTime
	}
	return 0
}

type GetAnchorScoreWithdrawRecordsResp struct {
	AnchorWithdrawList   []*AnchorScoreWithdrawRecord `protobuf:"bytes,1,rep,name=anchor_withdraw_list,json=anchorWithdrawList,proto3" json:"anchor_withdraw_list"`
	Total                uint32                       `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetAnchorScoreWithdrawRecordsResp) Reset()         { *m = GetAnchorScoreWithdrawRecordsResp{} }
func (m *GetAnchorScoreWithdrawRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorScoreWithdrawRecordsResp) ProtoMessage()    {}
func (*GetAnchorScoreWithdrawRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{24}
}

func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Unmarshal(m, b)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Marshal(b, m, deterministic)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Merge(m, src)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Size(m)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp proto.InternalMessageInfo

func (m *GetAnchorScoreWithdrawRecordsResp) GetAnchorWithdrawList() []*AnchorScoreWithdrawRecord {
	if m != nil {
		return m.AnchorWithdrawList
	}
	return nil
}

func (m *GetAnchorScoreWithdrawRecordsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UserBankInfo struct {
	IsAdult              int32    `protobuf:"varint,1,opt,name=is_adult,json=isAdult,proto3" json:"is_adult"`
	TtMobile             string   `protobuf:"bytes,2,opt,name=tt_mobile,json=ttMobile,proto3" json:"tt_mobile"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	IdentificationCard   string   `protobuf:"bytes,5,opt,name=identification_card,json=identificationCard,proto3" json:"identification_card"`
	Mobile               string   `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile"`
	Status               uint32   `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	RealnameAuthStatus   uint32   `protobuf:"varint,8,opt,name=realname_auth_status,json=realnameAuthStatus,proto3" json:"realname_auth_status"`
	CardNo               string   `protobuf:"bytes,9,opt,name=card_no,json=cardNo,proto3" json:"card_no"`
	BankName             string   `protobuf:"bytes,10,opt,name=bank_name,json=bankName,proto3" json:"bank_name"`
	BankProvince         string   `protobuf:"bytes,11,opt,name=bank_province,json=bankProvince,proto3" json:"bank_province"`
	BankCity             string   `protobuf:"bytes,12,opt,name=bank_city,json=bankCity,proto3" json:"bank_city"`
	OpeningBank          string   `protobuf:"bytes,13,opt,name=opening_bank,json=openingBank,proto3" json:"opening_bank"`
	AccountType          uint32   `protobuf:"varint,14,opt,name=account_type,json=accountType,proto3" json:"account_type"`
	CompanyName          string   `protobuf:"bytes,15,opt,name=company_name,json=companyName,proto3" json:"company_name"`
	InvalidBankInfo      uint32   `protobuf:"varint,16,opt,name=invalidBankInfo,proto3" json:"invalidBankInfo"`
	ExchangeInvisible    uint32   `protobuf:"varint,17,opt,name=exchange_invisible,json=exchangeInvisible,proto3" json:"exchange_invisible"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBankInfo) Reset()         { *m = UserBankInfo{} }
func (m *UserBankInfo) String() string { return proto.CompactTextString(m) }
func (*UserBankInfo) ProtoMessage()    {}
func (*UserBankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{25}
}

func (m *UserBankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBankInfo.Unmarshal(m, b)
}
func (m *UserBankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBankInfo.Marshal(b, m, deterministic)
}
func (m *UserBankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBankInfo.Merge(m, src)
}
func (m *UserBankInfo) XXX_Size() int {
	return xxx_messageInfo_UserBankInfo.Size(m)
}
func (m *UserBankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserBankInfo proto.InternalMessageInfo

func (m *UserBankInfo) GetIsAdult() int32 {
	if m != nil {
		return m.IsAdult
	}
	return 0
}

func (m *UserBankInfo) GetTtMobile() string {
	if m != nil {
		return m.TtMobile
	}
	return ""
}

func (m *UserBankInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBankInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserBankInfo) GetIdentificationCard() string {
	if m != nil {
		return m.IdentificationCard
	}
	return ""
}

func (m *UserBankInfo) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *UserBankInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UserBankInfo) GetRealnameAuthStatus() uint32 {
	if m != nil {
		return m.RealnameAuthStatus
	}
	return 0
}

func (m *UserBankInfo) GetCardNo() string {
	if m != nil {
		return m.CardNo
	}
	return ""
}

func (m *UserBankInfo) GetBankName() string {
	if m != nil {
		return m.BankName
	}
	return ""
}

func (m *UserBankInfo) GetBankProvince() string {
	if m != nil {
		return m.BankProvince
	}
	return ""
}

func (m *UserBankInfo) GetBankCity() string {
	if m != nil {
		return m.BankCity
	}
	return ""
}

func (m *UserBankInfo) GetOpeningBank() string {
	if m != nil {
		return m.OpeningBank
	}
	return ""
}

func (m *UserBankInfo) GetAccountType() uint32 {
	if m != nil {
		return m.AccountType
	}
	return 0
}

func (m *UserBankInfo) GetCompanyName() string {
	if m != nil {
		return m.CompanyName
	}
	return ""
}

func (m *UserBankInfo) GetInvalidBankInfo() uint32 {
	if m != nil {
		return m.InvalidBankInfo
	}
	return 0
}

func (m *UserBankInfo) GetExchangeInvisible() uint32 {
	if m != nil {
		return m.ExchangeInvisible
	}
	return 0
}

type DistributeContract struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DistributeContract) Reset()         { *m = DistributeContract{} }
func (m *DistributeContract) String() string { return proto.CompactTextString(m) }
func (*DistributeContract) ProtoMessage()    {}
func (*DistributeContract) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{26}
}

func (m *DistributeContract) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributeContract.Unmarshal(m, b)
}
func (m *DistributeContract) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributeContract.Marshal(b, m, deterministic)
}
func (m *DistributeContract) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributeContract.Merge(m, src)
}
func (m *DistributeContract) XXX_Size() int {
	return xxx_messageInfo_DistributeContract.Size(m)
}
func (m *DistributeContract) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributeContract.DiscardUnknown(m)
}

var xxx_messageInfo_DistributeContract proto.InternalMessageInfo

func (m *DistributeContract) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DistributeContract) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type DistributeContractResp struct {
	List                 []*DistributeContract `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *DistributeContractResp) Reset()         { *m = DistributeContractResp{} }
func (m *DistributeContractResp) String() string { return proto.CompactTextString(m) }
func (*DistributeContractResp) ProtoMessage()    {}
func (*DistributeContractResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_01b82e773a2aa73c, []int{27}
}

func (m *DistributeContractResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DistributeContractResp.Unmarshal(m, b)
}
func (m *DistributeContractResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DistributeContractResp.Marshal(b, m, deterministic)
}
func (m *DistributeContractResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DistributeContractResp.Merge(m, src)
}
func (m *DistributeContractResp) XXX_Size() int {
	return xxx_messageInfo_DistributeContractResp.Size(m)
}
func (m *DistributeContractResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DistributeContractResp.DiscardUnknown(m)
}

var xxx_messageInfo_DistributeContractResp proto.InternalMessageInfo

func (m *DistributeContractResp) GetList() []*DistributeContract {
	if m != nil {
		return m.List
	}
	return nil
}

func init() {
	proto.RegisterEnum("api.CurrencyType", CurrencyType_name, CurrencyType_value)
	proto.RegisterEnum("api.SettlementBillType", SettlementBillType_name, SettlementBillType_value)
	proto.RegisterType((*Response)(nil), "api.Response")
	proto.RegisterType((*RedDiamondExchangeItem)(nil), "api.RedDiamondExchangeItem")
	proto.RegisterType((*GetRedDiamondExchangeItemListResp)(nil), "api.GetRedDiamondExchangeItemListResp")
	proto.RegisterType((*ExchangeResult)(nil), "api.ExchangeResult")
	proto.RegisterType((*ExchangeReq)(nil), "api.ExchangeReq")
	proto.RegisterType((*ExchangeResp)(nil), "api.ExchangeResp")
	proto.RegisterType((*ProfileResp)(nil), "api.ProfileResp")
	proto.RegisterType((*Transaction)(nil), "api.Transaction")
	proto.RegisterType((*TransactionV2)(nil), "api.TransactionV2")
	proto.RegisterType((*GetTransactionsResp)(nil), "api.GetTransactionsResp")
	proto.RegisterType((*EstimateResp)(nil), "api.EstimateResp")
	proto.RegisterType((*PointsToTBeanReq)(nil), "api.PointsToTBeanReq")
	proto.RegisterType((*PointsToTBeanResp)(nil), "api.PointsToTBeanResp")
	proto.RegisterType((*VerifyCodeResp)(nil), "api.VerifyCodeResp")
	proto.RegisterType((*PointsToCashRecord)(nil), "api.PointsToCashRecord")
	proto.RegisterType((*PointsToCashRecordResult)(nil), "api.PointsToCashRecordResult")
	proto.RegisterType((*PointsToCashInfo)(nil), "api.PointsToCashInfo")
	proto.RegisterType((*PointsToCashResp)(nil), "api.PointsToCashResp")
	proto.RegisterType((*CheckEncashmentResult)(nil), "api.CheckEncashmentResult")
	proto.RegisterType((*PointsBalance)(nil), "api.PointsBalance")
	proto.RegisterType((*TransactionV3)(nil), "api.TransactionV3")
	proto.RegisterType((*GetTransactionsV3Resp)(nil), "api.GetTransactionsV3Resp")
	proto.RegisterType((*PointsToCashLimitResp)(nil), "api.PointsToCashLimitResp")
	proto.RegisterType((*AnchorScoreWithdrawRecord)(nil), "api.AnchorScoreWithdrawRecord")
	proto.RegisterType((*GetAnchorScoreWithdrawRecordsResp)(nil), "api.GetAnchorScoreWithdrawRecordsResp")
	proto.RegisterType((*UserBankInfo)(nil), "api.UserBankInfo")
	proto.RegisterType((*DistributeContract)(nil), "api.DistributeContract")
	proto.RegisterType((*DistributeContractResp)(nil), "api.DistributeContractResp")
}

func init() { proto.RegisterFile("exchange_api.proto", fileDescriptor_01b82e773a2aa73c) }

var fileDescriptor_01b82e773a2aa73c = []byte{
	// 2044 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0xdd, 0x6e, 0x23, 0x49,
	0xf5, 0xdf, 0xf6, 0x57, 0xec, 0xe3, 0x8f, 0x78, 0x3a, 0x99, 0x89, 0x33, 0xa3, 0xd9, 0x7f, 0xd2,
	0xab, 0xff, 0x12, 0x2d, 0xda, 0x0c, 0x4a, 0xc4, 0x0a, 0xad, 0x40, 0x28, 0x71, 0xa2, 0x51, 0x98,
	0xc9, 0x12, 0x75, 0x32, 0x59, 0xc1, 0x4d, 0xab, 0xdc, 0x5d, 0x89, 0x6b, 0xd3, 0x5d, 0xe5, 0xed,
	0x2e, 0x27, 0xb1, 0x90, 0xb8, 0x41, 0x82, 0x0b, 0x6e, 0xb8, 0x41, 0x5c, 0xf0, 0x1e, 0x48, 0x3c,
	0x04, 0x0f, 0xc0, 0x2d, 0x8f, 0x81, 0xb8, 0x40, 0xe7, 0x54, 0xb5, 0xdd, 0x76, 0x1c, 0x76, 0x58,
	0x10, 0x77, 0x7d, 0x7e, 0x75, 0x3e, 0xea, 0x9c, 0x53, 0xe7, 0xc3, 0x06, 0x97, 0xdf, 0x87, 0x43,
	0x26, 0xaf, 0x79, 0xc0, 0x46, 0x62, 0x77, 0x94, 0x2a, 0xad, 0xdc, 0x32, 0x1b, 0x89, 0xe7, 0x9b,
	0xd7, 0x4a, 0x5d, 0xc7, 0xfc, 0x15, 0x41, 0x83, 0xf1, 0xd5, 0x2b, 0x26, 0x27, 0xe6, 0xdc, 0xfb,
	0x05, 0xd4, 0x7d, 0x9e, 0x8d, 0x94, 0xcc, 0xb8, 0xeb, 0x42, 0x25, 0x54, 0x11, 0xef, 0x39, 0x5b,
	0xce, 0x4e, 0xd5, 0xa7, 0x6f, 0xb7, 0x0b, 0xe5, 0x24, 0xbb, 0xee, 0x95, 0xb6, 0x9c, 0x9d, 0x86,
	0x8f, 0x9f, 0xee, 0xff, 0x41, 0x33, 0xe3, 0xe9, 0x2d, 0x4f, 0x03, 0x2d, 0x12, 0xde, 0x2b, 0x6f,
	0x39, 0x3b, 0x6d, 0x1f, 0x0c, 0x74, 0x21, 0x12, 0xee, 0xee, 0x40, 0x25, 0x62, 0x9a, 0xf5, 0x2a,
	0x5b, 0xce, 0x4e, 0x73, 0x6f, 0x7d, 0xd7, 0x18, 0xdf, 0xcd, 0x8d, 0xef, 0x1e, 0xc8, 0x89, 0x4f,
	0x1c, 0xde, 0xdf, 0x1c, 0x78, 0xe6, 0xf3, 0xe8, 0x48, 0xb0, 0x44, 0xc9, 0xe8, 0xd8, 0xde, 0xfe,
	0x44, 0xf3, 0xc4, 0xdd, 0x80, 0x15, 0xa1, 0x79, 0x12, 0x88, 0x88, 0xae, 0xd3, 0xf6, 0x6b, 0x48,
	0x9e, 0x44, 0x78, 0x49, 0xc9, 0x12, 0x6e, 0x6f, 0x44, 0xdf, 0x88, 0x45, 0x3c, 0x0b, 0xe9, 0x2e,
	0x0d, 0x9f, 0xbe, 0xdd, 0x67, 0x50, 0x63, 0x89, 0x1a, 0x4b, 0x4d, 0xf7, 0x68, 0xfb, 0x96, 0x72,
	0xb7, 0xa1, 0x35, 0x50, 0x72, 0x9c, 0x05, 0xf6, 0xb4, 0x4a, 0xa7, 0x4d, 0xc2, 0x0e, 0x0c, 0xcb,
	0x67, 0xd0, 0x0e, 0xc7, 0x69, 0xca, 0x65, 0x38, 0x09, 0xf4, 0x64, 0xc4, 0x7b, 0xb5, 0x2d, 0x67,
	0xa7, 0xb3, 0xf7, 0x64, 0x17, 0xc3, 0xda, 0xb7, 0x27, 0x17, 0x93, 0x11, 0xf7, 0x5b, 0x61, 0x81,
	0x72, 0xd7, 0xa1, 0x3a, 0x4a, 0x45, 0xc8, 0x7b, 0x2b, 0xa4, 0xd3, 0x10, 0xde, 0xef, 0x1d, 0xd8,
	0x7e, 0xcd, 0xf5, 0x72, 0x3f, 0xdf, 0x8a, 0x4c, 0x63, 0xfc, 0x1f, 0xda, 0x74, 0xde, 0xcf, 0xe6,
	0x0f, 0xa0, 0x41, 0x71, 0x8a, 0x45, 0xa6, 0x7b, 0xa5, 0xad, 0xf2, 0x4e, 0x73, 0xef, 0x05, 0xc9,
	0x2c, 0xb7, 0xe7, 0xd7, 0x85, 0xb5, 0xea, 0x31, 0xe8, 0xe4, 0x27, 0x3e, 0xcf, 0xc6, 0xb1, 0x2e,
	0x84, 0xac, 0xf4, 0x2f, 0x43, 0x56, 0x7e, 0x18, 0xb2, 0xa9, 0xeb, 0x95, 0xa2, 0xeb, 0xbf, 0x72,
	0xa0, 0x39, 0xb3, 0xf1, 0xf5, 0xb7, 0x76, 0xf2, 0x87, 0xb0, 0xca, 0xef, 0x47, 0x3c, 0xd4, 0x3c,
	0x0a, 0x52, 0xba, 0x2b, 0xdd, 0xb0, 0xb9, 0xb7, 0x46, 0x92, 0xf3, 0x6e, 0xf8, 0x9d, 0x9c, 0xd7,
	0xd0, 0xde, 0x5b, 0x68, 0x15, 0x38, 0x46, 0xee, 0x0e, 0xd4, 0x52, 0x1e, 0xaa, 0xd4, 0xbc, 0xac,
	0xe6, 0x5e, 0x97, 0x94, 0x5c, 0xa4, 0x4c, 0x66, 0x2c, 0xd4, 0x42, 0x49, 0xdf, 0x9e, 0xe3, 0xbb,
	0x3a, 0x52, 0xd2, 0xbc, 0xb5, 0xba, 0x4f, 0xdf, 0xde, 0x3f, 0x1c, 0x68, 0x9e, 0xa5, 0xea, 0x4a,
	0xc4, 0x46, 0xdb, 0x36, 0xb4, 0x52, 0x1e, 0x05, 0x91, 0x89, 0x75, 0x66, 0x8b, 0xa7, 0x99, 0x4e,
	0xc3, 0x9f, 0x61, 0x5c, 0xf5, 0x80, 0x33, 0x99, 0x91, 0xa2, 0xaa, 0x6f, 0x29, 0xc4, 0x47, 0x4a,
	0x48, 0x9d, 0x51, 0x44, 0xab, 0xbe, 0xa5, 0xdc, 0x37, 0xd0, 0x4b, 0xd8, 0x7d, 0x40, 0x5c, 0x81,
	0x56, 0x41, 0x41, 0xbf, 0x2d, 0xaa, 0xa5, 0x7e, 0xaf, 0x27, 0xec, 0xfe, 0x02, 0x65, 0x2e, 0xd4,
	0x2c, 0xf9, 0xee, 0x29, 0x6c, 0xa2, 0x32, 0xa3, 0x7a, 0x51, 0x5b, 0xf5, 0x71, 0x6d, 0x4f, 0x13,
	0x76, 0x7f, 0x46, 0x42, 0x45, 0x75, 0xde, 0xdf, 0x1d, 0x68, 0x16, 0x42, 0xe5, 0x76, 0xa0, 0x34,
	0x2d, 0xd1, 0x92, 0x88, 0xb0, 0x5f, 0x8c, 0x45, 0x64, 0x1f, 0x10, 0x7e, 0xba, 0x7b, 0xd0, 0xd4,
	0x2c, 0xbd, 0xe6, 0xda, 0xa4, 0xbc, 0xfc, 0x58, 0xca, 0xc1, 0x70, 0x51, 0xc2, 0x1f, 0x2b, 0xde,
	0x07, 0x0f, 0xa8, 0xfa, 0x6f, 0x56, 0x66, 0xad, 0xf0, 0x3c, 0xdd, 0x17, 0xd0, 0x08, 0x53, 0xce,
	0x34, 0x0f, 0x98, 0xb6, 0x35, 0x5b, 0x37, 0xc0, 0x81, 0x9e, 0xf6, 0x94, 0xfa, 0xac, 0xa7, 0x78,
	0x7f, 0x28, 0x41, 0xbb, 0xe0, 0xfc, 0xe5, 0xde, 0xff, 0xc4, 0xfd, 0xea, 0x7f, 0xd7, 0xfd, 0xea,
	0xb7, 0x75, 0xdf, 0xdd, 0x84, 0xba, 0x4a, 0x23, 0x9e, 0x62, 0x53, 0x6e, 0x10, 0xbe, 0x42, 0xf4,
	0x49, 0xe4, 0xfd, 0x08, 0xd6, 0x5e, 0x73, 0x5d, 0x88, 0x4d, 0x46, 0xc5, 0xf1, 0x31, 0x54, 0xa8,
	0x31, 0x39, 0xd4, 0x98, 0xdc, 0xc5, 0x42, 0xbb, 0xdc, 0xf3, 0xe9, 0x9c, 0x4a, 0x34, 0xd3, 0x22,
	0x61, 0xda, 0x14, 0xd5, 0x92, 0x82, 0x77, 0xde, 0xbf, 0xe0, 0x7f, 0x53, 0x81, 0x6e, 0xfe, 0x74,
	0x2f, 0x0e, 0x39, 0x93, 0xd8, 0x7b, 0x8a, 0x97, 0x77, 0xe6, 0x2e, 0x5f, 0xa8, 0x43, 0xdb, 0xf7,
	0x6c, 0x1d, 0xbe, 0x80, 0x46, 0xc2, 0xd2, 0x1b, 0xae, 0x51, 0xc6, 0xcc, 0x96, 0xba, 0x01, 0x4e,
	0x22, 0xf7, 0x25, 0x40, 0xca, 0xbf, 0x1e, 0xf3, 0x8c, 0x4e, 0x2b, 0x74, 0xda, 0xb0, 0xc8, 0x49,
	0x84, 0xb2, 0x11, 0xbf, 0x15, 0x21, 0xc7, 0xd3, 0xaa, 0x91, 0x35, 0xc0, 0x49, 0xe4, 0xfe, 0x3f,
	0x74, 0xc2, 0x58, 0x70, 0xa9, 0x83, 0x5b, 0x9e, 0x66, 0x42, 0x49, 0xfb, 0x2e, 0xdb, 0x06, 0xbd,
	0x34, 0x20, 0x4e, 0x5a, 0xcb, 0x46, 0xc9, 0x36, 0x29, 0x02, 0x03, 0x51, 0x5e, 0x3f, 0x86, 0xd5,
	0x2b, 0x16, 0xf2, 0x80, 0x8d, 0xf5, 0x30, 0xd0, 0xea, 0x86, 0x4b, 0x9b, 0xaf, 0x36, 0xc2, 0x07,
	0x63, 0x3d, 0xbc, 0x40, 0x70, 0x9e, 0x2f, 0x0b, 0xb9, 0xe4, 0x94, 0xbf, 0xf6, 0x8c, 0xef, 0x1c,
	0x41, 0x1a, 0xed, 0x6a, 0x9c, 0x86, 0xdc, 0x18, 0x04, 0x3b, 0xda, 0x09, 0x22, 0x83, 0xdf, 0x87,
	0x8d, 0x99, 0xa2, 0x51, 0xaa, 0x6e, 0x05, 0x46, 0x94, 0x96, 0x86, 0x26, 0x19, 0x5e, 0xcf, 0x15,
	0x9e, 0xd9, 0xc3, 0x3e, 0x2e, 0x11, 0x87, 0xf0, 0xe1, 0x12, 0x31, 0x93, 0x58, 0x23, 0xdd, 0x22,
	0xe9, 0xe7, 0x8b, 0xd2, 0x26, 0xa1, 0xa4, 0x63, 0x1f, 0x9e, 0xcd, 0x74, 0x58, 0x51, 0xe3, 0x72,
	0x9b, 0x64, 0xd7, 0x72, 0x59, 0x23, 0x43, 0x8e, 0x7b, 0x7f, 0x71, 0xe0, 0xc9, 0xc2, 0x4b, 0xf8,
	0x4f, 0x07, 0xc0, 0x42, 0xe2, 0xcb, 0x8b, 0x89, 0x5f, 0x12, 0xeb, 0xca, 0xb2, 0x58, 0xcf, 0xf9,
	0x13, 0x2a, 0xa9, 0xf9, 0xbd, 0x0e, 0xbe, 0xca, 0x94, 0xb4, 0xaf, 0x65, 0xea, 0x4f, 0xdf, 0x9c,
	0xfd, 0x24, 0x53, 0xd2, 0xfb, 0x0a, 0x3a, 0x97, 0x3c, 0x15, 0x57, 0x13, 0x0c, 0x09, 0xf9, 0xf2,
	0x1c, 0xea, 0xa1, 0x52, 0x71, 0xa4, 0xee, 0xa4, 0x6d, 0x43, 0x53, 0x7a, 0xba, 0xcf, 0x95, 0x1e,
	0xee, 0x73, 0xe5, 0xd9, 0x3e, 0xf7, 0x0c, 0x6a, 0x6a, 0x44, 0xf9, 0xb6, 0xcd, 0xc6, 0x50, 0xde,
	0x9f, 0x1c, 0x70, 0xf3, 0xd8, 0xf5, 0x59, 0x36, 0xf4, 0x4d, 0x48, 0x66, 0x1d, 0xaf, 0x41, 0x1d,
	0xef, 0x05, 0x34, 0x06, 0x4c, 0xde, 0x04, 0x85, 0xa5, 0xac, 0x8e, 0xc0, 0x17, 0xb8, 0x98, 0x6d,
	0xc0, 0x4a, 0xc8, 0xd2, 0x28, 0x90, 0xca, 0x5a, 0xac, 0x21, 0xf9, 0x85, 0x42, 0xa3, 0x42, 0x86,
	0x2a, 0x31, 0x46, 0x4b, 0xbe, 0xa5, 0x10, 0xcf, 0x34, 0xd3, 0xe3, 0xcc, 0xee, 0x65, 0x96, 0x42,
	0x57, 0x68, 0xdb, 0x34, 0x75, 0x42, 0xdf, 0xc8, 0x9b, 0x72, 0xac, 0x47, 0xaa, 0x8c, 0x86, 0x6f,
	0x29, 0xef, 0x97, 0xd0, 0x7b, 0x78, 0x6f, 0xbb, 0xe2, 0x7c, 0x77, 0xae, 0x21, 0x6d, 0x50, 0xe2,
	0x97, 0x30, 0x13, 0x13, 0xb6, 0x4d, 0xad, 0x34, 0x8b, 0x6d, 0x5b, 0x30, 0xc4, 0x37, 0xee, 0xbf,
	0xde, 0xcd, 0xac, 0xfb, 0xa0, 0xca, 0x13, 0x79, 0x55, 0xf4, 0x37, 0xdf, 0x66, 0xa7, 0xfe, 0x2e,
	0x6d, 0x3d, 0xdf, 0x81, 0xd5, 0x3b, 0xa1, 0x87, 0x51, 0xca, 0xee, 0x02, 0x1b, 0x10, 0x63, 0xa8,
	0x93, 0xc3, 0xe7, 0x84, 0x7a, 0xbf, 0x73, 0xe6, 0xad, 0xd9, 0x65, 0xb2, 0x69, 0xe7, 0xbd, 0x90,
	0x57, 0xca, 0xbe, 0xf2, 0xa7, 0x0f, 0x9c, 0xc5, 0x9b, 0xf9, 0x60, 0x38, 0xe9, 0x96, 0x47, 0xd0,
	0x41, 0xd5, 0x42, 0x5e, 0x07, 0xb6, 0x40, 0xcc, 0x9a, 0xf5, 0xf2, 0xb1, 0x38, 0x99, 0xfe, 0xdb,
	0xb6, 0x42, 0x06, 0xf4, 0x7e, 0xed, 0xc0, 0xd3, 0xfe, 0x90, 0x87, 0x37, 0xc7, 0x32, 0x64, 0xd9,
	0x30, 0xe1, 0x52, 0xdb, 0xe8, 0x2f, 0xf1, 0xca, 0x59, 0xe6, 0x15, 0xc6, 0x38, 0xc2, 0xc1, 0x64,
	0x99, 0x4c, 0x6c, 0x00, 0x21, 0xcb, 0xf0, 0x11, 0xb4, 0xa7, 0x9a, 0xa6, 0x73, 0xb5, 0xed, 0xb7,
	0x72, 0x10, 0xbb, 0x95, 0x17, 0x41, 0xdb, 0xdc, 0xf9, 0x90, 0xc5, 0x4c, 0x86, 0xdc, 0xed, 0xc1,
	0xca, 0xc0, 0x7c, 0x5a, 0xbb, 0x39, 0x89, 0x79, 0x18, 0x8a, 0x28, 0xe2, 0xd2, 0x96, 0xba, 0xa5,
	0x70, 0xbb, 0x1b, 0xb2, 0x2c, 0xc8, 0xd5, 0x92, 0x99, 0xba, 0xdf, 0x1c, 0xb2, 0xec, 0x4b, 0x0b,
	0x79, 0xd9, 0xfc, 0x4e, 0xb0, 0xff, 0x0d, 0x93, 0xa6, 0xb0, 0x61, 0xcf, 0x06, 0xfb, 0xdc, 0x28,
	0x2e, 0x3f, 0x32, 0x8a, 0x2b, 0x85, 0x4d, 0xe4, 0xc7, 0xf0, 0x74, 0x61, 0xde, 0x5e, 0xee, 0xbf,
	0xd7, 0xc4, 0xdd, 0xb7, 0x13, 0xf7, 0x73, 0x78, 0x5a, 0xcc, 0xe7, 0x5b, 0x91, 0x08, 0x9d, 0xef,
	0xb3, 0x31, 0x12, 0x01, 0x56, 0x93, 0xc8, 0x9b, 0x4a, 0x33, 0x36, 0x0c, 0x08, 0x79, 0x7f, 0x2c,
	0xc3, 0xe6, 0x81, 0x0c, 0x87, 0x2a, 0x3d, 0x0f, 0x55, 0xca, 0xf3, 0x48, 0xd8, 0x06, 0xf1, 0x12,
	0x80, 0xd1, 0x61, 0x30, 0x9e, 0xae, 0x46, 0x0d, 0x83, 0xbc, 0x13, 0x11, 0xa6, 0xf6, 0x7a, 0x2c,
	0xe2, 0x28, 0x50, 0x77, 0x92, 0xa7, 0x79, 0x6a, 0x09, 0xfa, 0x29, 0x22, 0xd8, 0x33, 0x06, 0x22,
	0x8e, 0x67, 0xcd, 0xb5, 0x86, 0xe4, 0x49, 0xe4, 0xbe, 0x82, 0xf5, 0x82, 0x64, 0x70, 0x17, 0x05,
	0x89, 0x92, 0x7c, 0x42, 0x71, 0xa9, 0xf8, 0x4f, 0x66, 0x2a, 0xbe, 0x8c, 0x4e, 0xf1, 0xc0, 0xfd,
	0x0c, 0x7a, 0xcb, 0x04, 0x82, 0x50, 0x4e, 0x6c, 0x93, 0x5d, 0x7f, 0x20, 0xd4, 0x97, 0x13, 0x6c,
	0xe1, 0xd6, 0x83, 0xa9, 0x8d, 0x1a, 0xd9, 0x68, 0x1b, 0x38, 0xd7, 0xff, 0x29, 0xac, 0x2d, 0xf0,
	0x91, 0x6a, 0xd3, 0x8d, 0xba, 0x73, 0xbc, 0xa8, 0x76, 0x1b, 0x5a, 0x19, 0xd7, 0x3a, 0xa6, 0x67,
	0x9d, 0x6a, 0x1a, 0xd5, 0x15, 0xbf, 0x69, 0xb0, 0x73, 0x84, 0x30, 0x76, 0x96, 0x85, 0x4b, 0xb3,
	0x63, 0x55, 0xfc, 0x86, 0x41, 0x8e, 0x65, 0x34, 0xff, 0xea, 0xb1, 0xf9, 0x00, 0x71, 0xcc, 0x5e,
	0x3d, 0xb6, 0x9f, 0xdf, 0x9a, 0xdf, 0x9b, 0x8f, 0x26, 0xc8, 0x6c, 0x66, 0x67, 0xb0, 0x9e, 0xdf,
	0x3d, 0xd7, 0x58, 0x78, 0x37, 0x1f, 0xd2, 0xbb, 0x79, 0x54, 0x85, 0xef, 0x5a, 0xe7, 0x2c, 0xfa,
	0xf6, 0xd1, 0x6e, 0xe9, 0xfd, 0xb9, 0x02, 0xad, 0x77, 0x19, 0x4f, 0x0f, 0x99, 0xbc, 0xa1, 0x1e,
	0xb3, 0x09, 0x75, 0x91, 0x05, 0x2c, 0xca, 0x77, 0xba, 0xaa, 0xbf, 0x22, 0xb2, 0x03, 0x24, 0xb1,
	0x0a, 0xb4, 0x0e, 0x12, 0x35, 0x10, 0xf1, 0x74, 0x94, 0x68, 0x7d, 0x4a, 0x74, 0xbe, 0x59, 0x97,
	0x67, 0x9b, 0x75, 0xfe, 0x4f, 0x40, 0xa5, 0xf0, 0x4f, 0xc0, 0x2b, 0x58, 0x13, 0x11, 0x97, 0x5a,
	0x5c, 0x89, 0x90, 0xe1, 0x83, 0x0f, 0x70, 0xe0, 0xd8, 0x6c, 0xbb, 0xf3, 0x47, 0x7d, 0x96, 0x52,
	0x45, 0x5a, 0x83, 0x35, 0xf3, 0xd8, 0x0c, 0x55, 0x18, 0x44, 0x2b, 0x73, 0x83, 0xe8, 0x7b, 0xb0,
	0x9e, 0x72, 0x16, 0xa3, 0x31, 0x3b, 0xe2, 0x0d, 0x57, 0x9d, 0xb8, 0xdc, 0xfc, 0x8c, 0xe6, 0xbc,
	0x91, 0x28, 0xcc, 0xc0, 0xc6, 0xdc, 0x0c, 0x9c, 0x9b, 0x9c, 0xb0, 0x30, 0x39, 0x3f, 0x82, 0x36,
	0x1d, 0xd2, 0xb6, 0x84, 0x0d, 0xcb, 0xec, 0x57, 0x2d, 0x04, 0xcf, 0x2c, 0x36, 0xd5, 0x10, 0x0a,
	0x3d, 0xb1, 0x2b, 0x14, 0x69, 0xe8, 0x0b, 0x4d, 0xcf, 0x4d, 0x8d, 0xb8, 0xc4, 0x66, 0x8e, 0x98,
	0x5d, 0x93, 0x9a, 0x16, 0xc3, 0x7c, 0x20, 0x0b, 0x0b, 0x43, 0xec, 0x40, 0xa6, 0x89, 0x76, 0x4c,
	0xad, 0x5b, 0x8c, 0x36, 0xbe, 0x6d, 0x68, 0x85, 0x2a, 0x19, 0x31, 0x39, 0x31, 0xf7, 0x5c, 0x35,
	0x5a, 0x2c, 0x46, 0x57, 0xdd, 0x81, 0x55, 0x21, 0x6f, 0x59, 0x2c, 0xa2, 0x3c, 0xc9, 0xbd, 0x2e,
	0x29, 0x5a, 0x84, 0xdd, 0x4f, 0x0b, 0x7f, 0x51, 0x09, 0x79, 0x2b, 0x32, 0x31, 0x88, 0x79, 0xef,
	0x09, 0x31, 0x3f, 0xc9, 0x4f, 0x4e, 0xf2, 0x03, 0xef, 0x73, 0x70, 0x8f, 0x44, 0xa6, 0x53, 0x31,
	0x18, 0x6b, 0x8e, 0x6b, 0x50, 0xca, 0x42, 0x3d, 0x4d, 0xbb, 0x53, 0x48, 0x3b, 0x3e, 0x8e, 0x34,
	0xce, 0xff, 0xa5, 0x1a, 0xa7, 0xb1, 0x77, 0x0c, 0xcf, 0x1e, 0xca, 0xd2, 0xcb, 0x5f, 0xb6, 0x02,
	0x2c, 0x61, 0x25, 0xa6, 0x4f, 0x2e, 0xa1, 0x55, 0xfc, 0x5d, 0xe5, 0xae, 0x42, 0xf3, 0x9d, 0xcc,
	0x46, 0x3c, 0x14, 0x57, 0x82, 0x47, 0xdd, 0x0f, 0xdc, 0x06, 0x54, 0x69, 0xb1, 0xec, 0x3a, 0x2e,
	0x40, 0xcd, 0xb4, 0xd4, 0x6e, 0xc9, 0xed, 0x00, 0xcc, 0x7e, 0x34, 0x77, 0xcb, 0x48, 0xf7, 0x55,
	0x92, 0x88, 0x0c, 0x17, 0xfb, 0x6e, 0xe5, 0x93, 0xbf, 0x3a, 0xe0, 0x9e, 0x53, 0x5d, 0xe3, 0x78,
	0x3c, 0x14, 0x71, 0x4c, 0xea, 0xd7, 0x60, 0xf5, 0x9d, 0x7c, 0x23, 0xd5, 0x9d, 0xcc, 0xa1, 0xee,
	0x07, 0x6e, 0x1b, 0x1a, 0xaf, 0xc5, 0x95, 0xa6, 0x3a, 0xec, 0x3a, 0xa8, 0xea, 0xe0, 0x8e, 0xa5,
	0x91, 0xa1, 0x4b, 0x78, 0xa5, 0x53, 0x96, 0xdd, 0x9c, 0xbd, 0x31, 0x40, 0x19, 0x95, 0x1c, 0x24,
	0xe3, 0x8c, 0x17, 0x0d, 0xba, 0x1b, 0xb0, 0xf6, 0xb3, 0xf1, 0x44, 0xc8, 0x43, 0x36, 0x77, 0x50,
	0x25, 0x71, 0x25, 0xf5, 0xf0, 0x54, 0x44, 0x51, 0xcc, 0xbb, 0x35, 0xb7, 0x05, 0xf5, 0x23, 0xce,
	0x47, 0x7d, 0xa5, 0x46, 0xdd, 0x15, 0xb7, 0x0b, 0x2d, 0x92, 0x3b, 0x1f, 0x0f, 0x32, 0x11, 0x4d,
	0xba, 0x75, 0x14, 0x78, 0x23, 0xc5, 0xf5, 0xd0, 0x5e, 0xa8, 0x41, 0x17, 0x42, 0x7b, 0xc7, 0xf7,
	0x3a, 0x65, 0x5d, 0x38, 0x2c, 0xff, 0xdc, 0xd9, 0x1d, 0xd4, 0xe8, 0xef, 0xbe, 0xfd, 0x7f, 0x06,
	0x00, 0x00, 0xff, 0xff, 0x36, 0x75, 0x9b, 0xe1, 0x94, 0x14, 0x00, 0x00,
}
