package muse_interest_hub

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-interest-hub/muse-interest-hub"
	"google.golang.org/grpc"
)

const (
	serviceName = "muse-interest-hub"
)

type Client struct {
	client.BaseClient
}

func (c *Client) typedStub() pb.MuseInterestHubClient {
	return c.Stub().(pb.MuseInterestHubClient)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMuseInterestHubClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetMuseSwitchHub(ctx context.Context) (*pb.GetMuseSwitchHubResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetMuseSwitchHub(ctx, &pb.GetMuseSwitchHubRequest{})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetMuseSwitchHub(ctx context.Context, switchType uint32, IsOpen bool) protocol.ServerError {
	_, err := c.typedStub().SetMuseSwitchHub(ctx, &pb.SetMuseSwitchHubRequest{SwitchType: switchType, IsOpen: IsOpen})
	return protocol.ToServerError(err)
}

func (c *Client) BatGetUserMuseSwitch(ctx context.Context, uids []uint32, switchType uint32) (*pb.BatGetUserMuseSwitchResponse, protocol.ServerError) {
	resp, err := c.typedStub().BatGetUserMuseSwitch(ctx, &pb.BatGetUserMuseSwitchRequest{UidList: uids, SwitchType: switchType})
	return resp, protocol.ToServerError(err)
}
