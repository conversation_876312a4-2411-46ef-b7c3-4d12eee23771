syntax = "proto2";

package tlvpickle;

import "google/protobuf/descriptor.proto";

option java_package = "com.tencent.tlvpickle";
option java_outer_classname = "tlvpickle";

option optimize_for = CODE_SIZE;

option go_package = "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle;tlvpickle";

message SKBuiltinInt32_PB {
    required uint32 iVal = 1;
}

message SKBuiltinUint32_PB {
    required uint32 uiVal = 1;
}

message SKBuiltinChar_PB {
    required int32  iVal = 1;
}

message SKBuiltinUchar_PB {
    required uint32 uiVal = 1;
}

message SKBuiltinInt8_PB {
    required int32 iVal = 1;
}

message SKBuiltinUint8_PB {
    required uint32 uiVal = 1;
}

message SKBuiltinInt16_PB {
    required int32 iVal = 1;
}

message SKBuiltinUint16_PB {
    required uint32 uiVal = 1;
}

message SKBuiltinInt64_PB {
    required int64 llVal = 1;
}

message SKBuiltinUint64_PB {
    required uint64 ullVal = 1;
}

message SKBuiltinFloat32_PB {
    required float fVal = 1;
}

message SKBuiltinDouble64_PB {
    required double dVal = 1;
}

message SKBuiltinBuffer_PB {
    required uint32 iLen = 1;
    optional bytes Buffer = 2;
}

message SKBuiltinString_PB {
    optional string String = 1;
}

message SKBuiltinEmpty_PB {
}

message SKBuiltinEchoInfo_PB {
    required int32 EchoLen = 1;
    required bytes EchoStr = 2;
}

//////////////////////////////

extend google.protobuf.ServiceOptions {
    optional string ServerType = 1000000;
	optional int32 Magic = 1000001;
}

extend google.protobuf.MethodOptions {
    optional int32  CmdID = 1000000;
    optional string OptString = 1000001;
    optional string Usage = 1000002;
}

