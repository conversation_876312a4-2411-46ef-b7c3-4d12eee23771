syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";

import "ga_base.proto";
option go_package = "golang.52tt.com/protocol/app/guild";

////------- 公会圈业务基本类型 BEGIN ----//

//游戏圈主题配图
message GuildCircleTopicImage{
    required string thumb_url                               = 1;
    required string image_url                               = 2;
    optional uint32 image_width                             = 3;
    optional uint32 image_height                            = 4;
}

//游戏圈主题
message GuildCircleTopic{
    required uint32 guild_id                               = 2;
    required uint32 topic_id                                = 3;
    required string title                                   = 4;
    required string content                                 = 5;
    required uint32 create_time                             = 6;
    repeated GuildCircleTopicImage image_list                    = 7;
    required uint32 like_count                              = 8;
    required uint32 comment_count                           = 9;
    required uint32 is_liked                                = 10;

    enum TOPIC_STATE{
        STATE_HIGHT_LIGHT   = 1;    //精华
    }
    required uint32 topic_state                             = 11;
    required uint32 last_comment_time                       = 12;
    optional CircleUser creator                             = 13;
    required string create_time_desc                        = 14;   //create_time的可读串
    required string last_comment_time_desc                  = 15;   //last_comment_time的可读串
}

//游戏圈评论配图
message GuildCircleCommentImage{
	required string thumb_url                               = 1;
    required string image_url                               = 2;
}

//游戏圈评论
message GuildCircleTopicComment {
    required uint32 comment_id                  = 1;
    required uint32 circle_id                   = 2;
    required uint32 topic_id                    = 3;  //评论所属的topic
    required string content                     = 4;  //评论内容
    required CircleUser creator                 = 5;  //发送者
    required uint32 create_time                 = 6;  //发送时间
    enum TopicCommentStatus {
        NORMAL  = 0;        // 正常
        DELETED = 1;        // 被删除  1 << 0
        SHIELD  = 2;        // 被屏蔽  1 << 1
    }
    required uint32 status                      = 7;  // 评论状态(TopicCommentStatus mask)
    optional GuildCircleTopicComment replied_comment = 8;  // 如果有, 表示评论的目标
    required string create_time_desc            = 9;  // create_time的可读串
	repeated GuildCircleCommentImage image_list  	= 10; // 评论图片
	optional int32 floor                        = 11; // 楼层 -1 为老数据 可能没有楼层概念
}


// 圈子概要数据
message GuildCircleDynamicData {
	required uint32 guild_id 					= 1;
	optional uint32 topic_count                 = 2;
    optional uint32 today_topic_count           = 3;
}

////------- 游戏圈业务基本类型 END ----//

////------- 请求应答 BEGIN ----//

//发表主题
message GuildCirclePostTopicReq {
    required BaseReq base_req       = 1;
    required uint32 guild_id        = 2;
    required string client_id       = 3;
    optional string title           = 4;
    required string content         = 5;
    repeated string img_key_list    = 6;
	optional bool highlight			= 7;
}

message GuildCirclePostTopicResp {
    required BaseResp base_resp     = 1;
    required string client_id       = 2;
    required GuildCircleTopic topic = 3;
}

// 获取topic列表
message GuildCircleGetTopicListReq {
    required BaseReq base_req           = 1;
    required uint32 guild_id            = 2;
    required uint32 page_count          = 3; //请求获取的条数
    required uint32 page_position       = 4; //页码
	required bool	highlight			= 5;
	optional bool	require_count		= 6; // 是否需要数量
}

message GuildCircleGetTopicListResp {
    required BaseResp base_resp             = 1;
    required uint32 guild_id                = 2;
    required uint32 page_count              = 3; //请求获取的条数
    required uint32 page_position           = 4; //页码
    repeated GuildCircleTopic topic_list   	= 5;    //topic列表
    required uint32 newest_topic_id         = 6;//最新主题id
	optional uint32 total_topic_count		= 7;

}

//获取某条topic
message GuildCircleGetTopicReq {
    required BaseReq base_req           = 1;
    required uint32 guild_id            = 2;
    required uint32 topic_id            = 3;
    optional uint32 like_user_count     = 4;
    optional uint32 top_comment_count   = 5;
}

message GuildCircleGetTopicResp {
    required BaseResp base_resp                     = 1;
    required uint32 guild_id                        = 2;
    required uint32 topic_id                        = 3;
    required GuildCircleTopic topic 		        = 4;
    repeated string like_user_list                  = 5;
    repeated GuildCircleTopicComment top_comment_list    = 6;
}


//发表评论
message GuildCirclePostCommentReq {
    required BaseReq base_req           = 1;
    required uint32 guild_id           = 2;
    required uint32 topic_id            = 3; //所属主题
    required uint32 replied_comment_id  = 4; //如果是回复别人评论，则为评论id，回复主题则为0
    required string content             = 5;
	repeated string img_key_list    	= 6;
}

message GuildCirclePostCommentResp {
    required BaseResp base_resp         = 1;
    required uint32 guild_id           = 2;
    required uint32 topic_id            = 3;
    required uint32 replied_comment_id  = 4;
    required GuildCircleTopicComment comment = 5;
}


//赞/取消赞 消息  同旧版
message GuildCircleLikeTopicReq {
    required BaseReq base_req           = 1;
    required uint32 guild_id           = 2;
    required uint32 topic_id            = 3; //所属主题
    required bool is_like               = 4; //true为赞，false为取消赞
}

message GuildCircleLikeTopicResp {
    required BaseResp base_resp         = 1;
    required uint32 guild_id           = 2;
    required uint32 topic_id            = 3; //所属主题
    required bool is_like               = 4;
}

//举报主题 同旧版
message GuildCircleReportTopicReq {
    required BaseReq base_req = 1;
    required uint32 circle_id = 2;
    required uint32 topic_id  = 3;                     //所属主题
}

message GuildCircleReportTopicResp {
    required BaseResp base_resp = 1;
    required uint32 circle_id   = 2;
    required uint32 topic_id   = 3;
}

//删除我发的主题 同旧版
message GuildCircleDeleteTopicReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
}

message GuildCircleDeleteTopicResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
}

//删除我发的评论 同旧版
message GuildCircleDeleteCommentReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

message GuildCircleDeleteCommentResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
    required uint32 comment_id = 4;
}

//单查游戏圈
message GuildCircleGetCircleDetailReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
}

message GuildCircleGetCircleDetailResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;					// context
    required GuildCircleDynamicData guild_circle_dynamic = 3;
}

// 查我发表过的主题
message GuildCircleGetUserTopicReq {
    required BaseReq base_req = 1;
    required uint32 start_topic_id = 2;         	// 否则从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 3;            	//请求获取的条数
    required uint32 uid = 4;						//
}

message GuildCircleGetUserTopicResp {
    required BaseResp base_resp = 1;
    required uint32 start_topic_id = 3;         	// 否则从这条消息开始，获取之前的消息。为0的话，获取最新的消息
    required uint32 topic_count = 4;            	//请求获取的条数
    repeated GuildCircleTopic topic_list = 5;    		//topic列表
    required uint32 uid = 6;						//
}

// 查点赞列表
message GuildCircleGetLikeUserListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
    required uint32 offset = 4;      // 查询起始
    required uint32 limit = 5;      // 查询数量, 若为0, 则使用服务器缺省值
}

message GuildCircleGetLikeUserListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
    required uint32 offset = 4;
    required uint32 limit = 5;
    repeated string user_nick_list = 6;  // 用户列表, 按点赞时间倒排
}


///------ 以下不变
// 批量设置已读
message GuildCircleMarkReadedReq {
    required BaseReq base_req = 1;
    required uint32 svr_msg_id = 2; // 服务消息id(最大的那条id)
}

message GuildCircleMarkReadedResp {
    required BaseResp base_resp = 1;
    required uint32 svr_msg_id = 2;
}

//取消加精 -- v1.5
message GuildCircleCancelHighlightTopicReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
}

message GuildCircleCancelHighlightTopicResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
}



// 新版圈子评论列表 V3 --- BEGIN

enum GuildCircleTopicCommentStatus{
    GUILD_COMMENT_STATUS_NORMAL  = 0;        // 正常
    GUILD_COMMENT_STATUS_DELETED = 1;        // 被删除  1 << 0
	GUILD_COMMENT_STATUS_SHIELD  = 2;        // 被屏蔽  1 << 1
}

enum GuildCircleTopicCommentType {
    GUILD_COMMENT_TYPE_NORMAL = 0;		 // 普通评论
    GUILD_COMMENT_TYPE_REPLY = 1;		     // 回复"普通评论"的评论
	GUILD_COMMENT_TYPE_REPLY_REPLY = 2;    // 回复"评论'普通评论'评论"的评论
	GUILD_COMMENT_TYPE_HAVE_REPLY = 4;     // 有过被回复的"普通评论"
}
// 评论回复的目标
message GuildCommentReplayTargetBase
{
	required uint32 comment_id                  = 1;
    required CircleUser creator                 = 2;  // 发送者
    required uint32 create_time                 = 3;  // 发送时间
    required uint32 status                      = 4;  // 评论状态(CircleTopicCommentStatus mask)
	required string create_time_desc            = 5;  // create_time的可读串
}	

// 一条评论的基本数据	
message GuildCommentBase
{
	required uint32 comment_id                  = 1;
	required string content                     = 2;  // 评论内容
    required CircleUser creator                 = 3;  // 发送者
    required uint32 create_time                 = 4;  // 发送时间
    required uint32 status                      = 5;  // 评论状态(CircleTopicCommentStatus mask)
	required uint32 type                        = 6;  // 评论类型(CircleTopicCommentType mask)
	required string create_time_desc            = 7;  // create_time的可读串
	optional GuildCommentReplayTargetBase reply_target = 8; // 如果该条评论是回复某条评论的话 该处填目标评论的信息, 对于普通的评论该字段为空
}
	
// 普通评论
message GuildCircleTopicNomalComment {
	
	required GuildCommentBase comment_base         = 1;
	required int32 floor                        = 2; // 楼层 -1 为老数据 可能没有楼层概念
	repeated GuildCircleCommentImage image_list  	= 3; // 评论图片
	repeated GuildCommentBase reply_comment_list   = 4; // 回复了本评论的其他评论列表 默认3条, 如果没有回复本条评论 该字段为空
	optional uint32 reply_total_cnt             = 5; // 回复了本评论的其他评论总数 
}


//获取普通评论列表
message GuildCircleGetNomalCommentListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;                  //所属主题
    required uint32 start_comment_id = 4;          // 从这条评论开始获取之前的评论，为0则获取最新评论
    required uint32 count = 5;                     // 请求获取的条数
	optional bool include_start_id = 6;			   // 是否包含start,默认否
	optional bool is_desc = 7;			           // 是否降序排列即时间从新往旧排列 默认为否 
}

message GuildCircleGetNomalCommentListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
    required uint32 start_comment_id = 4;
    repeated GuildCircleTopicNomalComment comment_list = 5;  //结果列表
	optional uint32 nomal_left_cnt    = 6; // 从start_comment_id 开始的剩余评论数 即 剩余楼数
}

//获取指定评论的回复列表
message GuildCircleGetCommentReplyListReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;                  // 所属主题
	required uint32 parent_comment_id = 4;         // 所属父评论ID
    required uint32 start_reply_comment_id = 5;    // 
    required uint32 count = 6;                     // 请求获取的条数
}

message GuildCircleGetCommentReplyListResp {
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;
	required uint32 parent_comment_id = 4;             //所属父评论ID
    required uint32 start_reply_comment_id = 5;
    repeated GuildCommentBase reply_list = 6;  //结果列表
	optional uint32 reply_total_cnt   = 7; // 回复了本评论的回复总数
	optional uint32 reply_left_cnt    = 8; // 从start_reply_comment_id开始的回复了本评论的剩余回复数
}
// 新版圈子评论列表 V3 -- END


message GuildCircleAddTopicTagReq {
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 topic_id = 3;        
	required uint32 tag = 4;
	required bool add = 5;
}

message GuildCircleAddTopicTagResp {
    required BaseResp base_resp = 1;
	required uint32 guild_id = 2;
    required uint32 topic_id = 3;        
    
}
////------- 请求应答 END ------//
