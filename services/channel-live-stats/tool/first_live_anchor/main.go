package main

import (
	"context"
	"fmt"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/config"
	"google.golang.org/grpc"
	"time"
)

type Tool struct {
	statsDb    *gorm.DB
	accountCli *account.Client
}

func initTool() (*Tool, error) {
	statsMysqlConf := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channellive",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	/*statsMysqlConf := &config.MysqlConfig{
		Host:         "*************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "appsvr",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}*/
	mysqlDb, err := gorm.Open("mysql", statsMysqlConf.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return nil, err
	}

	accountCli, err := account.NewClient(grpc.WithBlock())
	if err != nil {
		fmt.Printf("Failed to golddiamonn.NewClient %v", err)
		return nil, err
	}

	return &Tool{
		statsDb:    mysqlDb,
		accountCli: accountCli,
	}, nil
}

func main() {
	tool, err := initTool()
	if err != nil {
		fmt.Printf("Failed to initTool %v", err)
		return
	}

	ctx := context.Background()

	wbFile := xlsx.NewFile()
	sh, err := wbFile.AddSheet("first_live_anchor")
	if err != nil {
		fmt.Printf("gainBackpackData AddSheet err:%v", err)
	}

	title := sh.AddRow()
	title.AddCell().SetString("主播昵称")
	title.AddCell().SetString("ttid")
	title.AddCell().SetString("uid")
	title.AddCell().SetString("历史首次开播时间")

	stats, err := tool.getStats()
	if err != nil {
		fmt.Printf("Failed to getStats %v", err)
		return
	}

	now := time.Now()
	begin := uint32(0)
	limit := uint32(100)
	end := limit
	num := 0

	uidList := make([]uint32, 0, len(stats))
	statsMap := make(map[uint32]*Stats)
	for _, info := range stats {
		uidList = append(uidList, info.Uid)
		statsMap[info.Uid] = info
	}

	for {
		if end > uint32(len(uidList)) {
			end = uint32(len(uidList))
		}
		queryUidList := uidList[begin:end]
		userMap, serr := tool.accountCli.GetUsersMap(ctx, queryUidList)
		if serr != nil {
			fmt.Printf("Failed to GetGuildList %v", serr)
			return
		}

		for _, uid := range queryUidList {

			row := sh.AddRow()
			row.AddCell().SetString(userMap[uid].GetNickname())
			row.AddCell().SetString(userMap[uid].GetAlias())
			row.AddCell().SetInt64(int64(statsMap[uid].Uid))
			row.AddCell().SetString(statsMap[uid].FirstLiveTime.String())

			num++
		}

		fmt.Printf("num:%v\n", num)
		if uint32(len(queryUidList)) < limit {
			break
		}

		begin = end
		end = begin + limit
		time.Sleep(50 * time.Millisecond)
	}

	err = wbFile.Save("first_live_anchor.xlsx")
	if err != nil {
		fmt.Printf("Failed to Save %v\n", err)
		return
	}

	fmt.Printf("extra stat done num:%v cost:%v\n", num, time.Since(now))
}

type Stats struct {
	Uid           uint32
	FirstLiveTime time.Time
}

func (t *Tool) getStats() (stat []*Stats, err error) {
	stat = make([]*Stats, 0, 100)
	err = t.statsDb.Table("tbl_anchor_channel_live_total_data").
		Select("uid,first_live_time").
		Where("first_live_time between '2021-01-01' and '2021-01-26'").
		Scan(&stat).Error

	return
}
