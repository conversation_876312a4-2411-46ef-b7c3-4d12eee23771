package glory

import (
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/pkg/protocol"
    Exchange "golang.52tt.com/protocol/services/exchange"
    "golang.52tt.com/services/exchange-logic/controllers"
    "golang.52tt.com/services/exchange-logic/models"
)

type ExchangeController struct {
    controllers.APIController
}

func (c *ExchangeController) Post() {
    ctx := c.Context()
    uid := c.AuthInfo().UserID

    id, _ := c.GetUint32("id")
    count, _ := c.GetUint32("count")

    result, err := models.ExchangeGloryClient.ExchangeItem(ctx, &Exchange.ExchangeItemReq{
        Uid:   uid,
        Id:    id,
        Count: count,
    })

    if err != nil {
        log.ErrorWithCtx(ctx, "ExchangeGloryClient.ExchangeItem err=%v", err)
        serverErr := protocol.ToServerError(err)
        c.ServeAP<PERSON>son<PERSON>ithError(serverErr.Code(), serverErr.Message())
    }

    c.<PERSON><PERSON><PERSON>(result)
}
