package mgr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/log"
	"time"
)

type BgmStatus int

const (
	BgmStatusUnknown BgmStatus = iota // 未知
	BgmStatusPlaying                  // 播放
	BgmStatusEnd                      // 结束
)

const (
	LockKeyPrefix = "pai_lock_bgm_status_"
)

var (
	ErrBgmProgressNotFound = errors.New("找不到bgm进度记录")
	ErrOperationBusy       = errors.New("其他用户在操作，请稍后再试")
	ErrInvalidVol          = errors.New("音量值不合法")
)

type BgmVolSetting struct {
	Vol       int32  `json:"vol"`
	ChannelID uint32 `json:"channel_id"`
}

func NewDefaultBgmVolSetting() *BgmVolSetting {
	return &BgmVolSetting{
		Vol:       100,
		ChannelID: 0,
	}
}

func (b *BgmVolSetting) MarshalBinary() (data []byte, err error) {
	return json.Marshal(b)
}

func (b *BgmVolSetting) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, b)
}

type BgmProgress struct {
	ID                string             `json:"id"`
	Version           int64              `json:"version"`
	Progress          int64              `json:"progress"`
	Status            BgmStatus          `json:"status"`
	StartTime         time.Time          `json:"start_time"`
	ChannelID         uint32             `json:"channelID"`
	OperationSnapshot []*TimeLineElement `json:"operation_snapshot"`
	Vol               int32              `json:"vol"` // 音量
}

func (b *BgmProgress) MarshalBinary() (data []byte, err error) {
	return json.Marshal(b)
}

func (b *BgmProgress) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, b)
}

func (b *BgmProgress) Play(bgmID string, progress int64, operationTime time.Time) {
	b.ID = bgmID
	b.Status = BgmStatusPlaying
	b.Progress = progress
	b.StartTime = operationTime
	// 记录操作
	b.OperationSnapshot = append(b.OperationSnapshot, &TimeLineElement{
		Id:        bgmID,
		Status:    TimeLineStatusBegin,
		BeginTime: operationTime.Unix(),
	})
}

func (b *BgmProgress) Stop(curProgress int64, operationTime time.Time) {
	b.Status = BgmStatusEnd
	b.Progress = curProgress
	// 记录操作
	if len(b.OperationSnapshot) > 0 {
		b.OperationSnapshot = append(b.OperationSnapshot, &TimeLineElement{
			Id:        b.OperationSnapshot[len(b.OperationSnapshot)-1].Id,
			Status:    TimeLineStatusEnd,
			BeginTime: operationTime.Unix(),
		})
	}
}

func (b *BgmProgress) Clear() {
	b.Status = BgmStatusUnknown
	b.ID = ""
	b.Progress = 0
}

func (b *BgmProgress) HasPauseOperation() bool {
	for _, item := range b.OperationSnapshot {
		if item.Status == TimeLineStatusEnd {
			return true
		}
	}
	return false
}

type BgmProgressRepo interface {
	// Save 保存bgm进度记录
	Save(ctx context.Context, bgmProgress *BgmProgress) error
	// Fetch 获取bgm进度记录，如果没有找到，返回 ErrBgmProgressNotFound
	Fetch(ctx context.Context, channelID uint32) (*BgmProgress, error)
	// FetchAndCreateIfNotPresent 获取bgm进度记录，如果不存在则创建
	FetchAndCreateIfNotPresent(ctx context.Context, channelID uint32) (*BgmProgress, error)
	// Clear 清除bgm进度记录
	Clear(ctx context.Context, channelID ...uint32) error
}

type BgmOperationService interface {
	// Get 获取bgm进度记录
	Get(ctx context.Context, channelID uint32) (*BgmProgress, error)
	// Play 播放bgm
	Play(ctx context.Context, channelID uint32, bgmID string, curProgress, nextProgress int64) error
	// Stop 停止bgm
	Stop(ctx context.Context, channelID uint32, curProgress int64) error
	// Delete 删除bgm进度记录
	Delete(ctx context.Context, channelID uint32) error
	// GetVol 获取bgm音量
	GetVol(ctx context.Context, channelID uint32) (*BgmVolSetting, error)
	// SetVol 设置bgm音量
	SetVol(ctx context.Context, channelID uint32, vol int32) error
}

type bgmOperationService struct {
	lockService                 LockService
	timeService                 TimeService
	channelPlayingDramaInfoRepo ChannelPlayingDramaInfoRepo
}

func NewBgmOperationService(
	timeService TimeService,
	lockService LockService,
	channelPlayingDramaInfoRepo ChannelPlayingDramaInfoRepo,
) BgmOperationService {
	return &bgmOperationService{
		lockService:                 lockService,
		timeService:                 timeService,
		channelPlayingDramaInfoRepo: channelPlayingDramaInfoRepo,
	}
}

func (b *bgmOperationService) Play(ctx context.Context, channelID uint32, bgmID string, curProgress, nextProgress int64) error {
	if !b.lockService.TryLock(b.getLockKey(channelID), time.Second*2) {
		log.ErrorWithCtx(ctx, "[播放bgm]其他用户在操作，请稍后再试, channelID:%d,bgmID:%s,ctx:%+v", channelID, bgmID, ctx)
		return ErrOperationBusy
	}
	defer func() {
		err := b.lockService.Unlock(b.getLockKey(channelID))
		if err != nil {
			log.ErrorWithCtx(ctx, "[播放bgm]解锁失败,channelID:%d,bgmID:%s,err:%+v,ctx:%+v", channelID, bgmID, err, ctx)
		}
	}()
	playingInfo, err := b.channelPlayingDramaInfoRepo.FetchByChannelID(ctx, channelID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[播放bgm]获取bgm进度记录失败，err:%+v", err)
		return err
	}
	// 获取当前时间
	currentTime, err := b.timeService.CurrentTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[播放bgm]获取当前时间失败，err:%+v", err)
		return err
	}
	bgmProgress := playingInfo.GetBgmProgress()
	// 如果当前的进度不是开始点（0），则先暂停
	if bgmProgress.Progress != 0 {
		bgmProgress.Stop(curProgress, currentTime)
	}
	// 再播放
	bgmProgress.Play(bgmID, nextProgress, currentTime)
	err = b.channelPlayingDramaInfoRepo.Save(ctx, playingInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "[播放bgm]保存bgm进度记录失败，err:%+v", err)
		return err
	}
	return nil
}

func (b *bgmOperationService) Stop(ctx context.Context, channelID uint32, curProgress int64) error {
	if !b.lockService.TryLock(b.getLockKey(channelID), time.Second*2) {
		log.ErrorWithCtx(ctx, "[停止bgm]其他用户在操作，请稍后再试, channelID:%d,ctx:%+v", channelID, ctx)
		return ErrOperationBusy
	}
	defer func() {
		err := b.lockService.Unlock(b.getLockKey(channelID))
		if err != nil {
			log.ErrorWithCtx(ctx, "[停止bgm]解锁失败,channelID:%d,err:%+v,ctx:%+v", channelID, err, ctx)
		}
	}()
	playingInfo, err := b.channelPlayingDramaInfoRepo.FetchByChannelID(ctx, channelID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[停止bgm]获取bgm进度记录失败，err:%+v", err)
		return err
	}
	// 获取当前时间
	currentTime, err := b.timeService.CurrentTime(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "[停止bgm]获取当前时间失败，err:%+v", err)
		return err
	}
	bgmProgress := playingInfo.GetBgmProgress()
	bgmProgress.Stop(curProgress, currentTime)
	err = b.channelPlayingDramaInfoRepo.Save(ctx, playingInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "[停止bgm]保存bgm进度记录失败，err:%+v", err)
		return err
	}
	return nil
}

func (b *bgmOperationService) Delete(ctx context.Context, channelID uint32) error {
	if !b.lockService.TryLock(b.getLockKey(channelID), time.Second*2) {
		log.ErrorWithCtx(ctx, "[删除bgm进度]其他用户在操作，请稍后再试, channelID:%d,ctx:%+v", channelID, ctx)
		return ErrOperationBusy
	}
	defer func() {
		err := b.lockService.Unlock(b.getLockKey(channelID))
		if err != nil {
			log.ErrorWithCtx(ctx, "[删除bgm进度]解锁失败,channelID:%d,err:%+v,ctx:%+v", channelID, err, ctx)
		}
	}()
	playingInfo, err := b.channelPlayingDramaInfoRepo.FetchByChannelID(ctx, channelID)
	if err != nil {
		log.ErrorWithCtx(ctx, "[删除bgm进度]获取bgm进度记录失败，err:%+v", err)
		return err
	}
	playingInfo.BgmInfo = nil
	err = b.channelPlayingDramaInfoRepo.Save(ctx, playingInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "[删除bgm进度]删除失败，err:%+v", err)
		return err
	}
	return nil
}

func (b *bgmOperationService) GetVol(ctx context.Context, channelID uint32) (*BgmVolSetting, error) {
	log.DebugWithCtx(ctx, "[获取bgm音量]channelID:%d", channelID)
	playingInfo, err := b.channelPlayingDramaInfoRepo.FetchByChannelID(ctx, channelID, WithChannelPlayingDramaInfoReadMask().MaskBgmVolInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "[获取bgm音量]获取房间走本信息失败，err:%+v", err)
		return nil, err
	}
	return playingInfo.GetBgmVolSetting(), nil
}

func (b *bgmOperationService) SetVol(ctx context.Context, channelID uint32, vol int32) error {
	log.DebugWithCtx(ctx, "[设置bgm音量]channelID:%d,vol:%d", channelID, vol)
	// 校验参数
	if vol < 0 || vol > 100 {
		return ErrInvalidVol
	}
	if channelID == 0 {
		return ErrMissingChannelID
	}
	if !b.lockService.TryLock(b.getLockKey(channelID), time.Second*2) {
		log.ErrorWithCtx(ctx, "[设置bgm音量]其他用户在操作，请稍后再试, channelID:%d,vol:%d,ctx:%+v", channelID, vol, ctx)
		return ErrOperationBusy
	}
	defer func() {
		err := b.lockService.Unlock(b.getLockKey(channelID))
		if err != nil {
			log.ErrorWithCtx(ctx, "[设置bgm音量]解锁失败,channelID:%d,err:%+v,ctx:%+v", channelID, err, ctx)
		}
	}()

	playingInfo, err := b.channelPlayingDramaInfoRepo.FetchByChannelID(ctx, channelID, WithChannelPlayingDramaInfoReadMask().MaskBgmVolInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "[设置bgm音量]获取房间走本信息失败，err:%+v", err)
		return err
	}
	// 设置音量
	setting := playingInfo.GetBgmVolSetting()
	setting.Vol = vol
	err = b.channelPlayingDramaInfoRepo.SaveBgmVol(ctx, playingInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "[设置bgm音量]设置失败，bgmProgress:%v,err:%+v", setting, err)
		return err
	}
	return nil
}

func (b *bgmOperationService) Get(ctx context.Context, channelID uint32) (*BgmProgress, error) {
	playingInfo, err := b.channelPlayingDramaInfoRepo.FetchByChannelID(ctx, channelID, WithChannelPlayingDramaInfoReadMask().MaskBgmInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "[获取bgm进度]获取房间走本信息失败，err:%+v", err)
		return nil, err
	}
	return playingInfo.GetBgmProgress(), nil
}

func (b *bgmOperationService) getLockKey(channelID uint32) string {
	return fmt.Sprintf("%s%d", PlayingControllerOperateLock, channelID)
}
