package store

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"time"
)

// CleanOrderData 清理三个月前的订单数据，每次删除 1000 条，直到删完
func (s *Store) CleanOrderData(ctx context.Context) {
	threeMonthAgo := time.Now().AddDate(0, 0, -90)
	tbl := getChannelConsumeOrderTableName(threeMonthAgo)

	sql := fmt.Sprintf("DELETE FROM %s WHERE order_time < ? LIMIT 1000", tbl)

	for {
		result, err := s.db.Exec(sql, threeMonthAgo)
		if err != nil {
			log.ErrorWithCtx(ctx, "CleanOrderData err: %v", err)
			break
		}

		// 检查删除的记录数
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			log.ErrorWithCtx(ctx, "Error fetching rows affected: %v", err)
			break
		}

		// 没有更多符合条件的数据
		if rowsAffected == 0 {
			log.InfoWithCtx(ctx, "All outdated order data has been cleaned.")
			break
		}

		log.InfoWithCtx(ctx, "Deleted %d records from %s.", rowsAffected, tbl)
	}
}
