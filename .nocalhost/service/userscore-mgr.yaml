- name: userscore-mgr # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/userscore-mgr/main.go
            - --server.configFile=/config/userscore-mgr.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug: # debug参数
            ${_INCLUDE_:- ../include/debug_args.yaml | nindent 12}
            - ./services/userscore-mgr/main.go
            - --server.configFile=/config/userscore-mgr.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "services/userscore-mgr" # 服务代码目录
            - "clients"
            - "services/notify"
            - "services/ugc/common/event"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
          ignoreFilePattern: [ ]
        env :
          - name: DYEING_ENVIRONMENT_MARK
            value: T0568
        portForward: []