// Code generated by protoc-gen-go-tt-compat. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc-compat v1.0.0
// - protoc                   v3.20.0
// source: reconcile-v2-svr/reconcile-backpack.proto

package reconcile_backpack

import (
	grpc "google.golang.org/grpc"
)

var _ReconcileBackpack_serviceDescCompat = grpc.ServiceDesc{
	ServiceName: "ReconcileBackpack.ReconcileBackpack",
	HandlerType: _ReconcileBackpack_serviceDesc.HandlerType,
	Methods:     _ReconcileBackpack_serviceDesc.Methods,
	Streams:     _ReconcileBackpack_serviceDesc.Streams,
	Metadata:    _ReconcileBackpack_serviceDesc.Metadata,
}

func RegisterReconcileBackpackServerCompat(s *grpc.Server, srv ReconcileBackpackServer) {
	s.RegisterService(&_ReconcileBackpack_serviceDescCompat, srv)
}
