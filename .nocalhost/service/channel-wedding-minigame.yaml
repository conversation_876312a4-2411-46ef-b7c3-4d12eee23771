- name: channel-wedding-minigame # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/channel-wedding-minigame/main.go
            - --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
          debug:
            ${_INCLUDE_:- ../include/debug_args.yaml | nindent 12}
            - ./services/channel-wedding-minigame/main.go
            - -- --server.configFile=/config/service-config.json
            - --server.grpcListen=:80
            - --server.logLevel=debug
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            ${_INCLUDE_:- ../include/common_deps.yaml | nindent 12}
            - "services/channel-wedding-minigame" # 服务代码目录
            - "services/helper-from-cpp"
            - "services/notify"
            - "services/risk-control/backpack-sender/utils"
            - "/services/present-score-guard/model"
            - "clients"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
          ignoreFilePattern: [ ]
        portForward: []