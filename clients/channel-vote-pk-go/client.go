package channel_vote_pk_go

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/channel-vote-pk-go"
	"google.golang.org/grpc"
)

const (
	serviceName = "channel-vote-pk-go"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewChannelVotePkGoClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.ChannelVotePkGoClient { return c.Stub().(pb.ChannelVotePkGoClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) ChannelVotePkStart(ctx context.Context, req *pb.ChannelVotePkStartReq) (*pb.ChannelVotePkStartResp, protocol.ServerError) {
	resp, err := c.typedStub().ChannelVotePkStart(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ChannelPkCancel(ctx context.Context, req *pb.ChannelPkCancelReq) (*pb.ChannelPKCancelResp, protocol.ServerError) {
	resp, err := c.typedStub().ChannelPkCancel(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelVotePKInfo(ctx context.Context, req *pb.GetChannelVotePKInfoReq) (*pb.GetChannelVotePKInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelVotePKInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ChannelVotePkVote(ctx context.Context, req *pb.ChannelVotePkVoteReq) (*pb.ChannelVotePkVoteResp, protocol.ServerError) {
	resp, err := c.typedStub().ChannelVotePkVote(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetVotePkStatus(ctx context.Context, req *pb.GetVotePkStatusReq) (*pb.GetVotePkStatusResp, protocol.ServerError) {
	resp, err := c.typedStub().GetVotePkStatus(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchPkCandidate(ctx context.Context, req *pb.SearchPkCandidateReq) (*pb.SearchPkCandidateResp, protocol.ServerError) {
	resp, err := c.typedStub().SearchPkCandidate(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ChannelCommonPkVote(ctx context.Context, req *pb.ChannelCommonPkVoteReq) (*pb.ChannelCommonPkVoteResp, protocol.ServerError) {
	resp, err := c.typedStub().ChannelCommonPkVote(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetChannelCommonPkInfo(ctx context.Context, req *pb.SetChannelCommonPkInfoReq) (*pb.SetChannelCommonPkInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().SetChannelCommonPkInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserLeftVoteCnt(ctx context.Context, req *pb.GetUserLeftVoteCntReq) (*pb.GetUserLeftVoteCntResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserLeftVoteCnt(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AddVotePkTicket(ctx context.Context, req *pb.AddVotePkTicketReq) (*pb.AddVotePkTicketResp, protocol.ServerError) {
	resp, err := c.typedStub().AddVotePkTicket(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) BatchSetChannelVote(ctx context.Context, channelIds []uint32, ttl uint32) (*pb.BatchSetChannelVoteResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchSetChannelVote(ctx, &pb.BatchSetChannelVoteReq{
		ChannelIds: channelIds,
		Ttl:        ttl,
	})
	return resp, protocol.ToServerError(err)
}
