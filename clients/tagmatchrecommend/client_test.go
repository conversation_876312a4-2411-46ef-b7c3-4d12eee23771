package tagmatchrecommend

import (
	"context"
	. "github.com/smartystreets/goconvey/convey"
	pb "golang.52tt.com/protocol/services/tagmatchrecommend"
	"google.golang.org/grpc/grpclog"
	"os"
	"testing"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestGetHelloWorld(t *testing.T) {

	<PERSON><PERSON>("GetTagmatchrecommend", t, func() {
		client, err := NewClient()
		So(err, ShouldBeNil)
		var req pb.TagmatchrecommendReq
		resp, err := client.GetTagmatchrecommend(context.Background(), req)
		So(err, ShouldBeNil)

		t.Logf("GetTagmatchrecommend %+v", resp)
	})

}
