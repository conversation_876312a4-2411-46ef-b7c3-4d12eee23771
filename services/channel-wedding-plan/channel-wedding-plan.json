{"server.grpcListen": ":80", "server.adminListen": ":8078", "mongo": {"user_name": "godman", "password": "TT8the<PERSON><PERSON><PERSON><PERSON>", "addrs": "*************:27017", "database": "channel_wedding_plan", "max_pool_size": 32, "min_pool_size": 1}, "redis": {"host": "************", "port": 6379, "protocol": "tcp", "ping_interval": 300, "pool_size": 100}, "user_online_kfk": {"brokers": "hobby-channel-kafka-broker-01.database.svc.cluster.local:9092,hobby-channel-kafka-broker-02.database.svc.cluster.local:9092,hobby-channel-kafka-broker-03.database.svc.cluster.local:9092", "topics": "onlineupdate", "version": "*******", "group_id": "channel_wedding_plan", "client_id": "channel_wedding_plan"}, "user_info_kfk": {"brokers": "hobby-channel-kafka-broker-01.database.svc.cluster.local:9092", "topics": "userinfo_chg_event", "group_id": "channel_wedding_plan", "client_id": "channel_wedding_plan"}, "fellow_kfk": {"brokers": "hobby-channel-kafka-broker-01.database.svc.cluster.local:9092,hobby-channel-kafka-broker-02.database.svc.cluster.local:9092,hobby-channel-kafka-broker-03.database.svc.cluster.local:9092", "topics": "fellow_point_change", "group_id": "channel_wedding_plan", "client_id": "channel_wedding_plan"}}