# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30801:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30801 --source api/usual_device/grpc_usual_device.proto --lang go --method /ga.api.usual_device.UsualDeviceLogic/GetFaceAuthCheckResult
30802:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30802 --source api/usual_device/grpc_usual_device.proto --lang go --method /ga.api.usual_device.UsualDeviceLogic/GetMessageCheckInfo
30803:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30803 --source api/usual_device/grpc_usual_device.proto --lang go --method /ga.api.usual_device.UsualDeviceLogic/GetMessageCheckResult
