package common

import (
	"golang.52tt.com/services/gold-commission/conf"
	"strings"
	"time"
)

// StartRecordUkw 开始记录神秘人时间 2022.11.1
func StartRecordUkw(t time.Time) bool {
	if conf.IsTest() {
		return true
	}
	st := time.Date(2022, 11, 1, 0, 0, 0, 0, t.Location())
	return t.Unix() >= st.Unix()
}

const (
	channelTagGuild = "公会"
	channelTagBlind = "相亲"
	channelTagCP    = "CP战"
)

// GetChannelTagBoost 获取房间标签权重排序
func GetChannelTagBoost(tag string) int {
	if strings.ContainsAny(tag, channelTagGuild) {
		return 99
	} else if strings.ContainsAny(tag, channelTagBlind) {
		return 98
	} else if strings.ContainsAny(tag, channelTagCP) {
		return 97
	} else {
		return 0
	}
}
