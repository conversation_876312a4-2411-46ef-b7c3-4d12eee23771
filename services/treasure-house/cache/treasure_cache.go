package cache

import (
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/treasure-house"
	"strconv"
	"time"
)

const UserConditionPrefix = "user_condition_"
const NoPrivilegeKey = "empty"

const BuyPeopleKey = "buy_people_"

func getUserConditionKey(uid uint32) string {
	return fmt.Sprintf(UserConditionPrefix+"%d", uid)
}

func getBuyPeopleKey(activityId, phaseType uint32) string {
	return fmt.Sprintf(BuyPeopleKey+"%d_%d", activityId, phaseType)
}

func (s *TreasureHouseCache) AddUserCondition(ctx context.Context, uid uint32, key, content string, pipe redis.Pipeliner) error {

	if pipe == nil {
		err := s.redisClient.HSet(getUserConditionKey(uid), key, content).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddUserPrivilege err , uid %d , err %v", uid, err)
			return err
		}

		_ = s.redisClient.Expire(getUserConditionKey(uid), time.Hour*24)
	} else {
		err := pipe.HSet(getUserConditionKey(uid), key, content).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddUserPrivilege err , uid %d , err %v", uid, err)
			return err
		}

		_ = pipe.Expire(getUserConditionKey(uid), time.Hour*24)
	}

	return nil
}

func (s *TreasureHouseCache) AddEmptyUserCondition(ctx context.Context, uid uint32, pipe redis.Pipeliner) error {

	if pipe == nil {
		err := s.redisClient.HSet(getUserConditionKey(uid), NoPrivilegeKey, "1").Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddEmptyPrivilege err , uid %d , err %v", uid, err)
			return err
		}

		_ = s.redisClient.Expire(getUserConditionKey(uid), time.Hour*24)
	} else {
		err := pipe.HSet(getUserConditionKey(uid), NoPrivilegeKey, "1").Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "AddEmptyPrivilege err , uid %d , err %v", uid, err)
			return err
		}

		_ = pipe.Expire(getUserConditionKey(uid), time.Hour*24)
	}

	return nil
}

func (s *TreasureHouseCache) GetUserConditionById(ctx context.Context, uid uint32, key string) (content string, exist bool, err error) {
	resp, err := s.redisClient.HGet(getUserConditionKey(uid), key).Result()

	if err == redis.Nil {
		return content, false, nil
	} else if err != nil {
		log.ErrorWithCtx(ctx, "GetUserConditionById err , uid %d , err %v", uid, err)
		return content, exist, err
	}

	return resp, true, nil
}

func (s *TreasureHouseCache) GetUserCondition(ctx context.Context, uid uint32) (contents map[string]string, exist bool, err error) {

	resp, err := s.redisClient.HGetAll(getUserConditionKey(uid)).Result()

	if err == redis.Nil || len(resp) == 0 {
		return nil, false, nil
	} else if err != nil {
		log.ErrorWithCtx(ctx, "AddUserCondition err , uid %d , err %v", uid, err)
		return contents, exist, err
	}

	if _, ok := resp[NoPrivilegeKey]; ok {
		return make(map[string]string), true, nil
	}

	return resp, true, nil
}

// DelUserCondition 删除权限缓存
func (s *TreasureHouseCache) DelUserCondition(ctx context.Context, uid uint32, pipe redis.Pipeliner) error {
	if pipe == nil {
		err := s.redisClient.Del(getUserConditionKey(uid)).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "DelUserPrivilege err , uid %d , err %v", uid, err)
			return err
		}
	} else {
		err := pipe.Del(getUserConditionKey(uid)).Err()
		if err != nil {
			log.ErrorWithCtx(ctx, "DelUserPrivilege err , uid %d , err %v", uid, err)
			return err
		}
	}
	return nil
}

// IncrBuyPeople 增加购买人数
func (s *TreasureHouseCache) IncrBuyPeople(ctx context.Context, activityId, phaseType uint32) error {
	err := s.redisClient.Incr(getBuyPeopleKey(activityId, phaseType)).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "IncrBuyPeople err , uid %d , err %v", err)
		return err
	}
	return nil
}

// GetBuyPeople 获取购买人数
func (s *TreasureHouseCache) GetBuyPeople(ctx context.Context, activityId, phaseType uint32) (uint32, error) {
	resp, err := s.redisClient.Get(getBuyPeopleKey(activityId, phaseType)).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBuyPeople err , uid %d , err %v", err)
		return 0, err
	}

	num, _ := strconv.Atoi(resp)

	return uint32(num), nil
}

// BatchGetBuyPeople 批量获取购买人数
func (s *TreasureHouseCache) BatchGetBuyPeople(ctx context.Context, activityIdList []uint32) (map[uint32]uint32, error) {
	out := make(map[uint32]uint32)

	keys := make([]string, 0)
	for _, item := range activityIdList {
		keys = append(keys, getBuyPeopleKey(item, uint32(pb.PhaseType_PHASE_TYPE_PURCHASE)))
	}

	resp, err := s.redisClient.MGet(keys...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetBuyPeople err , uid %d , err %v", err)
		return out, err
	}

	for i, value := range resp {
		if value != nil {
			tmp, _ := strconv.Atoi(value.(string))

			out[activityIdList[i]] = uint32(tmp)
		}
	}

	return out, nil
}

// Acquire tries to acquire the lock and returns true if successful.
func (s *TreasureHouseCache) Acquire(ctx context.Context, key string, expiration time.Duration) bool {
	ok, err := s.redisClient.SetNX(key, 1, expiration).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "Acquire err , key %v , err %v", key, err)
		return false
	}
	return ok
}

// Release releases the lock.
func (s *TreasureHouseCache) Release(ctx context.Context, key string) {
	err := s.redisClient.Del(key).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "Acquire err , key %v , err %v", key, err)
	}
}

func (s *TreasureHouseCache) BeginPipeline() redis.Pipeliner {
	return s.redisClient.Pipeline()
}

func (s *TreasureHouseCache) ExecPipeline(pipe redis.Pipeliner) error {
	_, err := pipe.Exec()
	return err
}
