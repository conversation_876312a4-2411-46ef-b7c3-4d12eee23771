package cache

import (
	"context"
	"errors"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	"golang.52tt.com/services/pia/mgr"
)

const (
	BgmStatusCacheKeyPrefix = "pia_channel_bgm_status_"
)

type bgmStatus struct {
	redisCli redis.CmdableV2
}

func NewBgmStatus(redisCli redis.CmdableV2) mgr.BgmProgressRepo {
	return &bgmStatus{
		redisCli,
	}
}

func (b *bgmStatus) Save(ctx context.Context, bgmProgress *mgr.BgmProgress) error {
	bgmProgress.Version = time.Now().UnixNano()
	binary, err := bgmProgress.MarshalBinary()
	if err != nil {
		log.ErrorWithCtx(ctx, "[保存bgm进度]序列化进度数据失败：%v,数据:%+v", err, bgmProgress)
		return err
	}
	err = b.redisCli.Set(ctx, b.getCacheKey(bgmProgress.ChannelID), binary, time.Hour*24).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "[保存bgm进度]保存进度数据失败：%v,数据:%+v", err, bgmProgress)
		return err
	}
	return nil
}

func (b *bgmStatus) Fetch(ctx context.Context, channelID uint32) (*mgr.BgmProgress, error) {
	result, err := b.redisCli.Get(ctx, b.getCacheKey(channelID)).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, mgr.ErrBgmProgressNotFound
		}
		log.ErrorWithCtx(ctx, "[获取bgm进度]获取进度数据失败：%v，channelID：%d", err, channelID)
		return nil, err
	}
	bgmProgress := &mgr.BgmProgress{}
	err = bgmProgress.UnmarshalBinary([]byte(result))
	if err != nil {
		log.ErrorWithCtx(ctx, "[获取bgm进度]反序列化进度数据失败：%v，数据：%s", err, result)
		return nil, err
	}
	return bgmProgress, nil
}

func (b *bgmStatus) FetchAndCreateIfNotPresent(ctx context.Context, channelID uint32) (*mgr.BgmProgress, error) {
	fetch, err := b.Fetch(ctx, channelID)
	if err != nil {
		if errors.Is(err, mgr.ErrBgmProgressNotFound) {
			return &mgr.BgmProgress{
				ChannelID: channelID,
			}, nil
		} else {
			return nil, err
		}
	}
	return fetch, nil
}

func (b *bgmStatus) Clear(ctx context.Context, channelID ...uint32) error {
	if len(channelID) == 0 {
		return nil
	}
	keys := make([]string, 0, len(channelID))
	for _, id := range channelID {
		keys = append(keys, b.getCacheKey(id))
	}
	err := b.redisCli.Del(ctx, keys...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "[清除bgm进度]清除进度数据失败：%v，channelID：%v", err, channelID)
		return err
	}
	return nil
}

func (b *bgmStatus) getCacheKey(channelID uint32) string {
	return fmt.Sprintf("%s%d", BgmStatusCacheKeyPrefix, channelID)
}
