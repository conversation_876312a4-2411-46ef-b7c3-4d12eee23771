package manager

import (
	"context"
	"fmt"
	"github.com/patrickmn/go-cache"
	"golang.52tt.com/clients/account"
	Exchange "golang.52tt.com/clients/exchange"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/oa"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/settlement"
	"golang.52tt.com/pkg/settlement/export"
	"golang.52tt.com/pkg/settlement/timeconv"
	"golang.52tt.com/protocol/common/status"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	exchangePb "golang.52tt.com/protocol/services/exchange"
	pb "golang.52tt.com/protocol/services/settlement-bill"
	ttcproxy "golang.52tt.com/protocol/services/ttc-proxy"
	"golang.52tt.com/services/settlement-bill/mysql"
	"golang.52tt.com/services/settlement-bill/utils"
	"google.golang.org/grpc/codes"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	DefaultExcelName = "%s.xlsx"
)

var (
	c *cache.Cache
)

func init() {
	c = cache.New(10*time.Minute, 30*time.Minute)
}

func (m *Manager) getMainDataFromCache(ctx context.Context, uid uint32) exchangePb.GetMain {
	key := fmt.Sprintf("main_data_%d", uid)
	if ret, ok := c.Get(key); ok {
		return ret.(exchangePb.GetMain)
	}
	mainInfo, err := m.exchangeGuildCli.GetMainData(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getMainDataFromCache GetMainData err: %v", err)
	}
	if mainInfo != nil {
		c.Set(key, *mainInfo, cache.DefaultExpiration)
		return *mainInfo
	}
	return exchangePb.GetMain{}
}

func (m *Manager) getUserInfoCache(ctx context.Context, uid uint32) account.User {
	key := fmt.Sprintf("user_info_%d", uid)
	if ret, ok := c.Get(key); ok {
		return ret.(account.User)
	}
	userInfo, err := m.accountCli.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getUserInfoCache GetUser err: %v", err)
	}
	if userInfo != nil {
		c.Set(key, *userInfo, cache.DefaultExpiration)
		return *userInfo
	}
	return account.User{}
}

func (m *Manager) getRealNameFromCache(ctx context.Context, uid uint32) ttcproxy.AuthIdCardInfo {
	key := fmt.Sprintf("realname_%d", uid)
	if ret, ok := c.Get(key); ok {
		return ret.(ttcproxy.AuthIdCardInfo)
	}
	realNameInfo, sErr := m.TtcProxyCli.GetUserRealNameAuthInfoV2(ctx, uint64(uid), false, true)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "getRealNameFromCache GetUserRealNameAuthInfoV2 err: %v", sErr)
	}
	if realNameInfo != nil && realNameInfo.IdcardInfo != nil {
		c.Set(key, *realNameInfo.IdcardInfo, cache.DefaultExpiration)
		return *realNameInfo.IdcardInfo
	}
	return ttcproxy.AuthIdCardInfo{}
}

func (m *Manager) getOAAccountInfoFromCache(ctx context.Context, accountID string) oa.AccountInfo {
	key := fmt.Sprintf("oa_account_%s", accountID)
	if ret, ok := c.Get(key); ok {
		return ret.(oa.AccountInfo)
	}
	infos, sErr := m.oaCli.GetAccountInfo(ctx, "", accountID)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "getOAAccountInfoFromCache GetUserRealNameAuthInfoV2 err: %v", sErr)
	}
	if len(infos) > 0 {
		c.Set(key, infos[0], cache.DefaultExpiration)
		return infos[0]
	}
	return oa.AccountInfo{}
}

const confirmStatusText = "已确定"

// ReportConfirmDeductMoney 手动推送确认扣款报表
func (m *Manager) ReportConfirmDeductMoney(ctx context.Context, req *pb.ReportConfirmReq) (*pb.ReportConfirmResp, error) {
	resp := &pb.ReportConfirmResp{}
	if !req.ConfirmSendEmail {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrSettleParamErr)
	}
	var start, end time.Time
	now := time.Now()
	if req.StartTime == 0 || req.EndTime == 0 {
		start = time.Date(now.Year(), now.Month(), 1, 10, 0, 0, 0, now.Location())
		end = time.Date(now.Year(), now.Month(), 3, 10, 0, 0, 0, now.Location())
	} else {
		start = time.Unix(int64(req.StartTime), 0)
		end = time.Unix(int64(req.EndTime), 0)
	}
	recordsI, err := m.mysqlStore.GetConfirmRecordIncome(ctx, start, end, &mysql.SettleDeductMoney{})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportConfirmDeductMoney GetConfirmDeductMoney err:%v", err)
		return resp, err
	}

	records, ok := recordsI.([]*mysql.SettleDeductMoney)
	if !ok {
		log.ErrorWithCtx(ctx, "ReportConfirmDeductMoney conv to mysql.SettleDeductMoney err")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrSettleUnknownBillTypeErr)
	}

	// gen excel
	tableRowsAmuse := make([][]interface{}, 0)
	tableRowsBase := make([][]interface{}, 0)
	for _, r := range records {
		guildName := m.getGuildName(ctx, r.GuildOwner)
		var confirmStatus string
		if r.WithdrawStatus == mysql.WithdrawStatusFinished {
			confirmStatus = confirmStatusText
		}
		billType := pb.SettlementBillType(r.BillType)
		if billType == pb.SettlementBillType_AmuseCommission && r.Money > 0 {
			tableRowsAmuse = append(tableRowsAmuse, []interface{}{
				strconv.Itoa(int(r.GuildOwner)),
				guildName,
				settlement.CentToMoney(r.Money),
				r.Remark,
				confirmStatus,
			})
		}
		if billType == pb.SettlementBillType_YuyinBaseCommission && r.Money > 0 {
			tableRowsBase = append(tableRowsBase, []interface{}{
				strconv.Itoa(int(r.GuildOwner)),
				guildName,
				settlement.CentToMoney(r.Money),
				r.Remark,
				confirmStatus,
			})
		}
	}

	headerCol := []string{"工会UID", "工会名称", "扣款金额", "扣款原因", "状态"}
	titleAmuse := "多人互动会长佣金扣款确定名单推送-" + now.Format(utils.TimeLayoutMonth)
	eAmuse := export.CreateReportTableHeader(titleAmuse, headerCol)
	eAmuse.PushRowsV2(tableRowsAmuse)
	fAmuse := fmt.Sprintf(DefaultExcelName, titleAmuse)
	eAmuse.Save(fAmuse)

	titleBase := "语音直播会长佣金扣款确定名单推送-" + now.Format(utils.TimeLayoutMonth)
	eBase := export.CreateReportTableHeader(titleBase, headerCol)
	eBase.PushRowsV2(tableRowsBase)
	fBase := fmt.Sprintf(DefaultExcelName, titleBase)
	eBase.Save(fBase)

	files := []string{fAmuse, fBase}
	defer func() {
		for _, name := range files {
			_ = os.Remove(name)
		}
	}()

	m.SendEmail(ctx, m.createEmailTitle("佣金扣款确定名单推送", now), "见附件", files)

	return resp, nil
}

// ReportConfirmDeepCoop 手动推送确认深度合作收益报表
func (m *Manager) ReportConfirmDeepCoop(ctx context.Context, req *pb.ReportConfirmReq) (*pb.ReportConfirmResp, error) {
	resp := &pb.ReportConfirmResp{}
	if !req.ConfirmSendEmail {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrSettleParamErr)
	}
	var start, end time.Time
	now := time.Now()
	if req.StartTime == 0 || req.EndTime == 0 {
		start = time.Date(now.Year(), now.Month(), 4, 0, 0, 0, 0, now.Location())
		end = time.Date(now.Year(), now.Month(), 5, 0, 0, 0, 0, now.Location())
	} else {
		start = time.Unix(int64(req.StartTime), 0)
		end = time.Unix(int64(req.EndTime), 0)
	}

	recordsI, err := m.mysqlStore.GetConfirmRecordIncome(ctx, start, end, &mysql.SettleDeepCooperation{})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportConfirmDeductMoney GetConfirmRecordIncome err:%v", err)
		return resp, err
	}

	records, ok := recordsI.([]*mysql.SettleDeepCooperation)
	if !ok {
		log.ErrorWithCtx(ctx, "ReportConfirmDeductMoney conv to mysql.SettleDeepCooperation err")
		return resp, protocol.NewExactServerError(codes.OK, status.ErrSettleUnknownBillTypeErr)
	}

	tableRows := make([][]interface{}, 0)
	for _, r := range records {
		guildName := m.getGuildName(ctx, r.GuildOwner)
		var confirmStatus string
		if r.WithdrawStatus == mysql.WithdrawStatusFinished {
			confirmStatus = confirmStatusText
		}
		tableRows = append(tableRows, []interface{}{
			strconv.Itoa(int(r.GuildOwner)),
			guildName,
			settlement.CentToMoney(r.TotalFlowThisMonth),
			settlement.CentToMoney(r.TotalFlowLastMonth),
			strconv.Itoa(int(r.GrowRate)),
			settlement.CentToMoney(r.SettlementMoney),
			settlement.CentToMoney(r.PrepaidMoney),
			settlement.CentToMoney(r.ActualSettlementMoney),
			confirmStatus,
		})
	}

	headerCol := []string{"工会UID", "工会名称", "本月总流水", "上月总流水",
		"总流水增长率", "总结算金额", "预付金额", "实际结算金额", "状态"}
	title := "深度合作收益推送"
	e := export.CreateReportTableHeader(title, headerCol)
	e.PushRowsV2(tableRows)
	f := fmt.Sprintf(DefaultExcelName, title)
	e.Save(f)
	defer func() {
		_ = os.Remove(f)
	}()

	m.SendEmail(ctx, m.createEmailTitle(title, now), "见附件", []string{f})

	return resp, nil
}

// ReportConfirmYuyinSubsidy 手动推送确认语音补贴报表
func (m *Manager) ReportConfirmYuyinSubsidy(ctx context.Context, req *pb.ReportConfirmReq) (*pb.ReportConfirmResp, error) {
	resp := &pb.ReportConfirmResp{}
	if !req.ConfirmSendEmail {
		return resp, protocol.NewExactServerError(codes.OK, status.ErrSettleParamErr)
	}
	var start, end time.Time
	now := time.Now()
	if req.StartTime == 0 || req.EndTime == 0 {
		start = time.Date(now.Year(), now.Month(), 21, 0, 0, 0, 0, now.Location())
		end = time.Date(now.Year(), now.Month(), 22, 0, 0, 0, 0, now.Location())
	} else {
		start = time.Unix(int64(req.StartTime), 0)
		end = time.Unix(int64(req.EndTime), 0)
	}
	recordsAnchorSubsidyI, err := m.mysqlStore.GetConfirmRecordIncome(ctx, start, end, &mysql.SettleAnchorSubsidy{})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportConfirmYuyinSubsidy GetConfirmRecordIncome err:%v", err)
		return resp, err
	}
	recordsNewGuildSubsidyI, err := m.mysqlStore.GetConfirmRecordIncome(ctx, start, end, &mysql.SettleNewGuildSubsidy{})
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportConfirmYuyinSubsidy GetConfirmRecordIncome err:%v", err)
		return resp, err
	}

	files := make([]string, 0)

	// 语音主播补贴
	{
		if recordsAnchorSubsidy, ok := recordsAnchorSubsidyI.([]*mysql.SettleAnchorSubsidy); ok {
			tableRows := make([][]interface{}, 0)
			for _, r := range recordsAnchorSubsidy {
				guildName := m.getGuildName(ctx, r.GuildOwner)
				var confirmStatus string
				if r.WithdrawStatus == mysql.WithdrawStatusFinished {
					confirmStatus = confirmStatusText
				}
				tableRows = append(tableRows, []interface{}{
					strconv.Itoa(int(r.GuildOwner)),
					guildName,
					settlement.CentToMoney(r.SubsidySum),
					"",
					confirmStatus,
				})
			}
			headerCol := []string{"工会UID", "工会名称", "主播补贴金额", "备注", "状态"}
			title := "语音直播主播补贴推送-" + now.Format(utils.TimeLayoutMonth)
			e := export.CreateReportTableHeader(title, headerCol)
			e.PushRowsV2(tableRows)
			f := fmt.Sprintf(DefaultExcelName, title)
			e.Save(f)
			files = append(files, f)
		}
	}

	// 新公会补贴
	{
		if recordsNewGuildSubsidy, ok := recordsNewGuildSubsidyI.([]*mysql.SettleNewGuildSubsidy); ok {
			tableRows := make([][]interface{}, 0)
			for _, r := range recordsNewGuildSubsidy {
				guildName := m.getGuildName(ctx, r.GuildOwner)
				var confirmStatus string
				if r.WithdrawStatus == mysql.WithdrawStatusFinished {
					confirmStatus = confirmStatusText
				}
				tableRows = append(tableRows, []interface{}{
					strconv.Itoa(int(r.GuildOwner)),
					guildName,
					settlement.CentToMoney(r.SubsidyMoney),
					"",
					confirmStatus,
				})
			}
			headerCol := []string{"工会UID", "工会名称", "新公会补贴金额", "备注", "状态"}
			title := "新公会补贴金额-" + now.Format(utils.TimeLayoutMonth)
			e := export.CreateReportTableHeader(title, headerCol)
			e.PushRowsV2(tableRows)
			f := fmt.Sprintf(DefaultExcelName, title)
			e.Save(f)
			files = append(files, f)
		}
	}

	defer func() {
		for _, name := range files {
			_ = os.Remove(name)
		}
	}()

	m.SendEmail(ctx, m.createEmailTitle("语音直播新公会补贴&主播补贴推送", now), "见附件", files)

	return resp, nil
}

func (m *Manager) getGuildName(ctx context.Context, uid uint32) string {
	var guildName string
	userInfo, err := m.accountCli.GetUser(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "getGuildName GetUser err:%v", err)
	}
	if userInfo != nil && userInfo.CurrentGuildId > 0 {
		guildInfo, err := m.guildCli.GetGuild(ctx, userInfo.CurrentGuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "getGuildName GetGuild err:%v", err)
		}
		guildName = guildInfo.Name
	}
	return guildName
}

func (m *Manager) SendEmail(ctx context.Context, title, html string, attachPath []string) {
	m.cfgCenter.SendEmail(ctx, title, html, attachPath)
}

func (m *Manager) createEmailTitle(title string, t time.Time) string {
	title = fmt.Sprintf("%s - %s", title, t.Format(utils.TimeLayoutMonth))
	if m.sc.IsTest() {
		title += " [测试环境]"
	}
	return title
}

func createLockLabel(billTypes []pb.SettlementBillType, now time.Time) string {
	hour := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	sort.Slice(billTypes, func(i, j int) bool {
		return billTypes[i] < billTypes[j]
	})
	tpsInt := make([]string, 0)
	for _, tp := range billTypes {
		tpsInt = append(tpsInt, strconv.Itoa(int(tp)))
	}
	return fmt.Sprintf("ReportConfirmWith_%s_%s", strings.Join(tpsInt, "_"), hour.Format("20060102"))
}

func getSettleStartByType(tp pb.SettlementBillType, end time.Time) (start time.Time, err error) {
	cycle := settlement.GetSettleCycle(tp)
	switch cycle {
	case pb.SettlementCycle_SettlementCycleWeekly:
		start = timeconv.AddDate(end, 0, 0, -7)
	case pb.SettlementCycle_SettlementCycleMonthly:
		start = timeconv.AddDate(end, 0, -1, 0)
	default:
		start = time.Time{}
		err = fmt.Errorf("ReportConfirmWith type err")
	}
	return start, err
}

func (m *Manager) ReportConfirmWith(ctx context.Context, req *pb.ReportConfirmWithReq) (*pb.ReportConfirmWithResp, error) {
	resp := &pb.ReportConfirmWithResp{}

	now := time.Now()
	gotLock, _ := m.mysqlStore.GetCronLock(ctx, createLockLabel(req.GetBillType(), now))
	if !gotLock {
		log.ErrorWithCtx(ctx, "ReportConfirmWith no lock")
		return resp, nil
	}

	err := m.ReportConfirmWithByTypes(ctx, req.BillType)
	if err != nil {
		m.alert.SendError(fmt.Sprintf("结算报表推送错误 TYPE: %s, ERR: %s", req.GetBillType(), err.Error()))
	}

	return resp, err
}

func (m *Manager) ReportConfirmWithByTypes(ctx context.Context, types []pb.SettlementBillType) error {
	now := time.Now()
	files := make([]string, 0)

	for _, tp := range types {
		var start, end time.Time
		end = now
		start, err := getSettleStartByType(tp, end)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportConfirmWith getSettleStartByType err:$v", err)
			continue
		}

		isScore := utils.SliceSettleTypeExist(tp, []pb.SettlementBillType{
			pb.SettlementBillType_GiftScore, pb.SettlementBillType_AwardScore,
			pb.SettlementBillType_MaskPKScore, pb.SettlementBillType_KnightScore,
			pb.SettlementBillType_ESportScore,
		})

		// 本周期内已提现的结算单
		bills, err := m.mysqlStore.GetSettleBillWithdrawn(ctx, tp, start, end)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportConfirmWith GetSettleBillWithdrawn err:$v", err)
			return err
		}

		// 获取本周期提现的过往周期结算单
		withdrawnFormerlyBills, err := m.mysqlStore.GetSettleBillWithdrawnFormerly(ctx, tp, start, end)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportConfirmWith GetSettleBillWithdrawnFormerly err:$v", err)
			return err
		}

		// 所有未提现的结算单
		waitWithdrawBills, err := m.mysqlStore.GetSettleBillWaitWithdraw(ctx, tp)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportConfirmWith GetSettleBillWaitWithdraw err:$v", err)
			return err
		}

		bills = append(bills, withdrawnFormerlyBills...)
		bills = append(bills, waitWithdrawBills...)

		tableRows := make([][]interface{}, 0)
		tableRowsAnchorWithdraws := make([][]interface{}, 0)
		for _, b := range bills {
			// 提现记录
			tableRows = append(tableRows, m.genBillWithdrawRecord(ctx, b))

			// 积分的特殊报表
			if isScore {
				// 会长主播仓库记录
				tableRowsAnchorWithdraws = append(tableRowsAnchorWithdraws, m.genAnchorWithdrawRecord(ctx, b)...)
			}
		}

		tpName := settlement.BillTypeLabel.Text(tp)
		title := tpName + "-" + now.Format("2006-01-02")
		e := export.CreateReportTableHeader(title, []string{"公会长号ID", "会长UID", "公司名称", "会长实名", "身份证号码", "上期剩余可提现收益金额", "本期产生可提现收益金额", "本期已提现收益金额", "本期剩余可提现收益金额", "申请提现日期"})
		e.PushRowsV2(tableRows)

		if isScore && len(tableRowsAnchorWithdraws) > 0 {
			sheet2 := "对公主播已结算明细"
			e.CreateSheet(sheet2, []string{
				"主播UID", "主播TTID", "主播昵称", "主播身份证", "结算金额", "结算时间", "对公公司名称",
				"银行账户", "银行名称", "银行开户省", "银行开户市", "主播账号绑定的手机号", "申请提现时间",
				"对公会长UID", "对公会长姓名", "对公会长身份证", "提现状态"})
			e.SheetPushRowsV2(sheet2, tableRowsAnchorWithdraws)
		}

		f := fmt.Sprintf(DefaultExcelName, title)
		e.Save(f)
		files = append(files, f)
	}
	// defer func() {
	// 	for _, name := range files {
	// 		_ = os.Remove(name)
	// 	}
	// }()
	log.InfoWithCtx(ctx, "files: %+v", files)

	m.SendEmail(ctx, m.createEmailTitle("结算报表推送", now), "见附件", files)
	return nil
}

// 生成提现记录
func (m *Manager) genBillWithdrawRecord(ctx context.Context, b *mysql.SettlementBill) []interface{} {
	var err error
	var guildId, companyName, realName, idCard, withdrawDate string
	// 总金额、本期金额、已提现金额、余额
	var incomeSum, thisIncome, withdrawn, nowBalance uint64
	mainData := m.getMainDataFromCache(ctx, b.GuildOwner)
	companyName = mainData.Company
	userInfo := m.getUserInfoCache(ctx, b.GuildOwner)
	guildId = strconv.Itoa(int(userInfo.CurrentGuildId))
	realNameInfo := m.getRealNameFromCache(ctx, b.GuildOwner)
	idCard = realNameInfo.IdentityNum
	realName = realNameInfo.Name

	isWithdraw := b.Status > uint8(pb.SettlementBillStatus_WaitWithdraw)
	if b.NeedWithdraw() && !isWithdraw {
		tp := pb.SettlementBillType(b.BillType)
		incomeSum, _, err = m.commissionCli.getClient(tp).GetBalance(ctx, b.GuildOwner)
		if err != nil {
			log.ErrorWithCtx(ctx, "ReportConfirmWith GetBalance err: %v", err)
		}
	} else {
		incomeSum = b.IncomeSum
	}
	if isWithdraw {
		withdrawn = b.IncomeSum
		nowBalance = 0
	} else {
		withdrawn = 0
		nowBalance = incomeSum
	}
	// 防止溢出
	if incomeSum > b.LastBalance {
		thisIncome = incomeSum - b.LastBalance
	} else {
		thisIncome = 0
	}

	if !b.WithdrawTime.IsZero() {
		withdrawDate = b.WithdrawTime.Format("2006-01-02")
	}

	row := []interface{}{
		guildId,
		strconv.Itoa(int(b.GuildOwner)),
		companyName,
		realName,
		idCard,
		settlement.CentToMoney(b.LastBalance),
		settlement.CentToMoney(thisIncome),
		settlement.CentToMoney(withdrawn),
		settlement.CentToMoney(nowBalance),
		withdrawDate,
	}
	return row
}

func (m *Manager) genAnchorWithdrawRecord(ctx context.Context, b *mysql.SettlementBill) [][]interface{} {
	var err error
	rows := make([][]interface{}, 0)
	scoreType := transBillType2ScoreType(pb.SettlementBillType(b.BillType))

	m.getAnchorWithdrawScoresBatch(ctx, b, Exchange.ReasonTypeSettle, func(records []*exchangePb.UserAllScore) {
		uidList := make([]uint32, 0)
		for _, item := range records {
			uidList = append(uidList, item.Uid)
		}
		var userInfoMap map[uint32]*accountPB.UserResp
		userInfoMap, err = m.accountCli.BatGetUserByUid(ctx, uidList...)
		if err != nil {
			log.ErrorWithCtx(ctx, "genGuildOwnerWithdrawRecord AccountClient.BatGetUserByUid failed %v uids:%d", err, uidList)
		}
		for _, anchor := range records {
			var nickname, phone, settleTime string
			score := getScoreByScoreType(scoreType, anchor)
			if userInfo, ok := userInfoMap[anchor.Uid]; ok {
				nickname = userInfo.Nickname
				phone = userInfo.Phone
			}
			settleTime = time.Unix(int64(anchor.CreateAt), 0).Format(utils.TimeLayout2)
			anchorRealName := m.getRealNameFromCache(ctx, anchor.Uid)
			guildRealName := m.getRealNameFromCache(ctx, b.GuildOwner)
			mainData := m.getMainDataFromCache(ctx, b.GuildOwner)
			oaAccount := m.getOAAccountInfoFromCache(ctx, mainData.OaAccountId)
			var withdrawTime, withdrawStatusText string
			if !b.WithdrawTime.IsZero() {
				withdrawTime = b.WithdrawTime.Format(utils.TimeLayout)
				withdrawStatusText = "已提现"
			}
			rows = append(rows, []interface{}{
				strconv.Itoa(int(anchor.Uid)),
				anchor.Ttid,
				nickname,
				anchorRealName.IdentityNum,
				settlement.CentToMoney(score),
				settleTime,
				mainData.Company,
				oaAccount.Account,
				oaAccount.Bank,
				oaAccount.Province,
				oaAccount.City,
				phone,
				withdrawTime,
				strconv.Itoa(int(b.GuildOwner)),
				guildRealName.Name,
				guildRealName.IdentityNum,
				withdrawStatusText,
			})
		}
	})

	return rows
}

// UpdateBillLastBalance 更新结算单上期余额
func (m *Manager) UpdateBillLastBalance(ctx context.Context, billTypes []pb.SettlementBillType) {
	// 蒙面PK类型
	for _, billType := range billTypes {
		bills, err := m.mysqlStore.GetWaitWithdrawSettlementBillByType(ctx, billType)
		if err != nil {
			log.ErrorWithCtx(ctx, "UpdateBillLastBalance GetWaitWithdrawSettlementBillByType err:%s", err)
			continue
		}

		for _, bill := range bills {
			comCli := m.commissionCli.getClient(billType)
			balance, _, err := comCli.GetBalance(ctx, bill.GuildOwner)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateBillLastBalance GetBalance err:%s", err)
				continue
			}
			if err = m.mysqlStore.UpdateSettlementBillLastBalance(ctx, bill.BillId, balance); err != nil {
				log.ErrorWithCtx(ctx, "UpdateBillLastBalance UpdateSettlementBillLastBalance err:%s", err)
				continue
			}
		}
	}
}
