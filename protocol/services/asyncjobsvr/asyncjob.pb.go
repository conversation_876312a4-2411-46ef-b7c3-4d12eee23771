// Code generated by protoc-gen-gogo.
// source: services/asyncjob/asyncjob.proto
// DO NOT EDIT!

/*
	Package AsyncJob is a generated protocol buffer package.

	It is generated from these files:
		services/asyncjob/asyncjob.proto

	It has these top-level messages:
		NotifyUserInfoUpdated
		NotifyGuildInfoUpdated
		NotifyCircleTopicDeleted
		MissionMessage
		MissionEventMessage
		CircleUpdate_NewTopic
		CircleUpdate_ActBegin
		CircleUpdate_ActEnd
		NotifyCircleUpdated
		NotifyPublicAccountUpdated
		NotifyThirdPartyRegSuccess
		AsyncJobRequest
*/
package AsyncJob

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type NotifyCircleUpdated_UpdateType int32

const (
	NotifyCircleUpdated_NEW_TOPIC NotifyCircleUpdated_UpdateType = 1
	NotifyCircleUpdated_ACT_BEGIN NotifyCircleUpdated_UpdateType = 2
	NotifyCircleUpdated_ACT_END   NotifyCircleUpdated_UpdateType = 3
)

var NotifyCircleUpdated_UpdateType_name = map[int32]string{
	1: "NEW_TOPIC",
	2: "ACT_BEGIN",
	3: "ACT_END",
}
var NotifyCircleUpdated_UpdateType_value = map[string]int32{
	"NEW_TOPIC": 1,
	"ACT_BEGIN": 2,
	"ACT_END":   3,
}

func (x NotifyCircleUpdated_UpdateType) Enum() *NotifyCircleUpdated_UpdateType {
	p := new(NotifyCircleUpdated_UpdateType)
	*p = x
	return p
}
func (x NotifyCircleUpdated_UpdateType) String() string {
	return proto.EnumName(NotifyCircleUpdated_UpdateType_name, int32(x))
}
func (x *NotifyCircleUpdated_UpdateType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NotifyCircleUpdated_UpdateType_value, data, "NotifyCircleUpdated_UpdateType")
	if err != nil {
		return err
	}
	*x = NotifyCircleUpdated_UpdateType(value)
	return nil
}
func (NotifyCircleUpdated_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAsyncjob, []int{8, 0}
}

type NotifyPublicAccountUpdated_NotifyType int32

const (
	NotifyPublicAccountUpdated_BY_PUBLIC_ID NotifyPublicAccountUpdated_NotifyType = 1
	NotifyPublicAccountUpdated_BY_BIZ_ID    NotifyPublicAccountUpdated_NotifyType = 2
)

var NotifyPublicAccountUpdated_NotifyType_name = map[int32]string{
	1: "BY_PUBLIC_ID",
	2: "BY_BIZ_ID",
}
var NotifyPublicAccountUpdated_NotifyType_value = map[string]int32{
	"BY_PUBLIC_ID": 1,
	"BY_BIZ_ID":    2,
}

func (x NotifyPublicAccountUpdated_NotifyType) Enum() *NotifyPublicAccountUpdated_NotifyType {
	p := new(NotifyPublicAccountUpdated_NotifyType)
	*p = x
	return p
}
func (x NotifyPublicAccountUpdated_NotifyType) String() string {
	return proto.EnumName(NotifyPublicAccountUpdated_NotifyType_name, int32(x))
}
func (x *NotifyPublicAccountUpdated_NotifyType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NotifyPublicAccountUpdated_NotifyType_value, data, "NotifyPublicAccountUpdated_NotifyType")
	if err != nil {
		return err
	}
	*x = NotifyPublicAccountUpdated_NotifyType(value)
	return nil
}
func (NotifyPublicAccountUpdated_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAsyncjob, []int{9, 0}
}

type NotifyThirdPartyRegSuccess_ThirdPartyType int32

const (
	NotifyThirdPartyRegSuccess_QQ     NotifyThirdPartyRegSuccess_ThirdPartyType = 1
	NotifyThirdPartyRegSuccess_WECHAT NotifyThirdPartyRegSuccess_ThirdPartyType = 2
)

var NotifyThirdPartyRegSuccess_ThirdPartyType_name = map[int32]string{
	1: "QQ",
	2: "WECHAT",
}
var NotifyThirdPartyRegSuccess_ThirdPartyType_value = map[string]int32{
	"QQ":     1,
	"WECHAT": 2,
}

func (x NotifyThirdPartyRegSuccess_ThirdPartyType) Enum() *NotifyThirdPartyRegSuccess_ThirdPartyType {
	p := new(NotifyThirdPartyRegSuccess_ThirdPartyType)
	*p = x
	return p
}
func (x NotifyThirdPartyRegSuccess_ThirdPartyType) String() string {
	return proto.EnumName(NotifyThirdPartyRegSuccess_ThirdPartyType_name, int32(x))
}
func (x *NotifyThirdPartyRegSuccess_ThirdPartyType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NotifyThirdPartyRegSuccess_ThirdPartyType_value, data, "NotifyThirdPartyRegSuccess_ThirdPartyType")
	if err != nil {
		return err
	}
	*x = NotifyThirdPartyRegSuccess_ThirdPartyType(value)
	return nil
}
func (NotifyThirdPartyRegSuccess_ThirdPartyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAsyncjob, []int{10, 0}
}

// ======================================================
// 异步任务定义
// ======================================================
type NotifyUserInfoUpdated struct {
}

func (m *NotifyUserInfoUpdated) Reset()                    { *m = NotifyUserInfoUpdated{} }
func (m *NotifyUserInfoUpdated) String() string            { return proto.CompactTextString(m) }
func (*NotifyUserInfoUpdated) ProtoMessage()               {}
func (*NotifyUserInfoUpdated) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{0} }

type NotifyGuildInfoUpdated struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *NotifyGuildInfoUpdated) Reset()                    { *m = NotifyGuildInfoUpdated{} }
func (m *NotifyGuildInfoUpdated) String() string            { return proto.CompactTextString(m) }
func (*NotifyGuildInfoUpdated) ProtoMessage()               {}
func (*NotifyGuildInfoUpdated) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{1} }

func (m *NotifyGuildInfoUpdated) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type NotifyCircleTopicDeleted struct {
	CircleId uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *NotifyCircleTopicDeleted) Reset()                    { *m = NotifyCircleTopicDeleted{} }
func (m *NotifyCircleTopicDeleted) String() string            { return proto.CompactTextString(m) }
func (*NotifyCircleTopicDeleted) ProtoMessage()               {}
func (*NotifyCircleTopicDeleted) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{2} }

func (m *NotifyCircleTopicDeleted) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *NotifyCircleTopicDeleted) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type MissionMessage struct {
	MessageCmdId uint32 `protobuf:"varint,1,req,name=message_cmd_id,json=messageCmdId" json:"message_cmd_id"`
	MessageBody  string `protobuf:"bytes,2,req,name=message_body,json=messageBody" json:"message_body"`
	Uid          uint32 `protobuf:"varint,3,opt,name=uid" json:"uid"`
	ClientType   uint32 `protobuf:"varint,4,opt,name=client_type,json=clientType" json:"client_type"`
}

func (m *MissionMessage) Reset()                    { *m = MissionMessage{} }
func (m *MissionMessage) String() string            { return proto.CompactTextString(m) }
func (*MissionMessage) ProtoMessage()               {}
func (*MissionMessage) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{3} }

func (m *MissionMessage) GetMessageCmdId() uint32 {
	if m != nil {
		return m.MessageCmdId
	}
	return 0
}

func (m *MissionMessage) GetMessageBody() string {
	if m != nil {
		return m.MessageBody
	}
	return ""
}

func (m *MissionMessage) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MissionMessage) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type MissionEventMessage struct {
	MessageBody []byte `protobuf:"bytes,1,req,name=message_body,json=messageBody" json:"message_body"`
	Uid         uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	ClientType  uint32 `protobuf:"varint,3,opt,name=client_type,json=clientType" json:"client_type"`
}

func (m *MissionEventMessage) Reset()                    { *m = MissionEventMessage{} }
func (m *MissionEventMessage) String() string            { return proto.CompactTextString(m) }
func (*MissionEventMessage) ProtoMessage()               {}
func (*MissionEventMessage) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{4} }

func (m *MissionEventMessage) GetMessageBody() []byte {
	if m != nil {
		return m.MessageBody
	}
	return nil
}

func (m *MissionEventMessage) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MissionEventMessage) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type CircleUpdate_NewTopic struct {
	TopicId uint32 `protobuf:"varint,1,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleUpdate_NewTopic) Reset()                    { *m = CircleUpdate_NewTopic{} }
func (m *CircleUpdate_NewTopic) String() string            { return proto.CompactTextString(m) }
func (*CircleUpdate_NewTopic) ProtoMessage()               {}
func (*CircleUpdate_NewTopic) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{5} }

func (m *CircleUpdate_NewTopic) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type CircleUpdate_ActBegin struct {
	TopicId    uint32 `protobuf:"varint,1,req,name=topic_id,json=topicId" json:"topic_id"`
	ActDesc    string `protobuf:"bytes,2,req,name=act_desc,json=actDesc" json:"act_desc"`
	ExpireTime uint64 `protobuf:"varint,3,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *CircleUpdate_ActBegin) Reset()                    { *m = CircleUpdate_ActBegin{} }
func (m *CircleUpdate_ActBegin) String() string            { return proto.CompactTextString(m) }
func (*CircleUpdate_ActBegin) ProtoMessage()               {}
func (*CircleUpdate_ActBegin) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{6} }

func (m *CircleUpdate_ActBegin) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleUpdate_ActBegin) GetActDesc() string {
	if m != nil {
		return m.ActDesc
	}
	return ""
}

func (m *CircleUpdate_ActBegin) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type CircleUpdate_ActEnd struct {
	TopicId uint32 `protobuf:"varint,1,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleUpdate_ActEnd) Reset()                    { *m = CircleUpdate_ActEnd{} }
func (m *CircleUpdate_ActEnd) String() string            { return proto.CompactTextString(m) }
func (*CircleUpdate_ActEnd) ProtoMessage()               {}
func (*CircleUpdate_ActEnd) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{7} }

func (m *CircleUpdate_ActEnd) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type NotifyCircleUpdated struct {
	CircleId       uint32                 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	UpdateType     uint32                 `protobuf:"varint,2,req,name=update_type,json=updateType" json:"update_type"`
	NewTopicUpdate *CircleUpdate_NewTopic `protobuf:"bytes,3,opt,name=new_topic_update,json=newTopicUpdate" json:"new_topic_update,omitempty"`
	ActBeginUpdate *CircleUpdate_ActBegin `protobuf:"bytes,4,opt,name=act_begin_update,json=actBeginUpdate" json:"act_begin_update,omitempty"`
	ActEndUpdate   *CircleUpdate_ActEnd   `protobuf:"bytes,5,opt,name=act_end_update,json=actEndUpdate" json:"act_end_update,omitempty"`
}

func (m *NotifyCircleUpdated) Reset()                    { *m = NotifyCircleUpdated{} }
func (m *NotifyCircleUpdated) String() string            { return proto.CompactTextString(m) }
func (*NotifyCircleUpdated) ProtoMessage()               {}
func (*NotifyCircleUpdated) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{8} }

func (m *NotifyCircleUpdated) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *NotifyCircleUpdated) GetUpdateType() uint32 {
	if m != nil {
		return m.UpdateType
	}
	return 0
}

func (m *NotifyCircleUpdated) GetNewTopicUpdate() *CircleUpdate_NewTopic {
	if m != nil {
		return m.NewTopicUpdate
	}
	return nil
}

func (m *NotifyCircleUpdated) GetActBeginUpdate() *CircleUpdate_ActBegin {
	if m != nil {
		return m.ActBeginUpdate
	}
	return nil
}

func (m *NotifyCircleUpdated) GetActEndUpdate() *CircleUpdate_ActEnd {
	if m != nil {
		return m.ActEndUpdate
	}
	return nil
}

type NotifyPublicAccountUpdated struct {
	NotifyType uint32 `protobuf:"varint,1,req,name=notify_type,json=notifyType" json:"notify_type"`
	PublicId   uint32 `protobuf:"varint,2,opt,name=public_id,json=publicId" json:"public_id"`
	BizType    uint32 `protobuf:"varint,3,opt,name=biz_type,json=bizType" json:"biz_type"`
	BizId      uint64 `protobuf:"varint,4,opt,name=biz_id,json=bizId" json:"biz_id"`
}

func (m *NotifyPublicAccountUpdated) Reset()         { *m = NotifyPublicAccountUpdated{} }
func (m *NotifyPublicAccountUpdated) String() string { return proto.CompactTextString(m) }
func (*NotifyPublicAccountUpdated) ProtoMessage()    {}
func (*NotifyPublicAccountUpdated) Descriptor() ([]byte, []int) {
	return fileDescriptorAsyncjob, []int{9}
}

func (m *NotifyPublicAccountUpdated) GetNotifyType() uint32 {
	if m != nil {
		return m.NotifyType
	}
	return 0
}

func (m *NotifyPublicAccountUpdated) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *NotifyPublicAccountUpdated) GetBizType() uint32 {
	if m != nil {
		return m.BizType
	}
	return 0
}

func (m *NotifyPublicAccountUpdated) GetBizId() uint64 {
	if m != nil {
		return m.BizId
	}
	return 0
}

type NotifyThirdPartyRegSuccess struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ThirdPartyType uint32 `protobuf:"varint,2,req,name=third_party_type,json=thirdPartyType" json:"third_party_type"`
	Openid         string `protobuf:"bytes,3,req,name=openid" json:"openid"`
	AccessToken    string `protobuf:"bytes,4,req,name=access_token,json=accessToken" json:"access_token"`
}

func (m *NotifyThirdPartyRegSuccess) Reset()         { *m = NotifyThirdPartyRegSuccess{} }
func (m *NotifyThirdPartyRegSuccess) String() string { return proto.CompactTextString(m) }
func (*NotifyThirdPartyRegSuccess) ProtoMessage()    {}
func (*NotifyThirdPartyRegSuccess) Descriptor() ([]byte, []int) {
	return fileDescriptorAsyncjob, []int{10}
}

func (m *NotifyThirdPartyRegSuccess) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyThirdPartyRegSuccess) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *NotifyThirdPartyRegSuccess) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *NotifyThirdPartyRegSuccess) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

// ======================================================
// 异步任务请求消息
// ======================================================
type AsyncJobRequest struct {
	Cmd     uint32 `protobuf:"varint,1,req,name=cmd" json:"cmd"`
	Key     uint32 `protobuf:"varint,2,req,name=key" json:"key"`
	JobData []byte `protobuf:"bytes,3,req,name=job_data,json=jobData" json:"job_data"`
}

func (m *AsyncJobRequest) Reset()                    { *m = AsyncJobRequest{} }
func (m *AsyncJobRequest) String() string            { return proto.CompactTextString(m) }
func (*AsyncJobRequest) ProtoMessage()               {}
func (*AsyncJobRequest) Descriptor() ([]byte, []int) { return fileDescriptorAsyncjob, []int{11} }

func (m *AsyncJobRequest) GetCmd() uint32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *AsyncJobRequest) GetKey() uint32 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *AsyncJobRequest) GetJobData() []byte {
	if m != nil {
		return m.JobData
	}
	return nil
}

func init() {
	proto.RegisterType((*NotifyUserInfoUpdated)(nil), "AsyncJob.NotifyUserInfoUpdated")
	proto.RegisterType((*NotifyGuildInfoUpdated)(nil), "AsyncJob.NotifyGuildInfoUpdated")
	proto.RegisterType((*NotifyCircleTopicDeleted)(nil), "AsyncJob.NotifyCircleTopicDeleted")
	proto.RegisterType((*MissionMessage)(nil), "AsyncJob.MissionMessage")
	proto.RegisterType((*MissionEventMessage)(nil), "AsyncJob.MissionEventMessage")
	proto.RegisterType((*CircleUpdate_NewTopic)(nil), "AsyncJob.CircleUpdate_NewTopic")
	proto.RegisterType((*CircleUpdate_ActBegin)(nil), "AsyncJob.CircleUpdate_ActBegin")
	proto.RegisterType((*CircleUpdate_ActEnd)(nil), "AsyncJob.CircleUpdate_ActEnd")
	proto.RegisterType((*NotifyCircleUpdated)(nil), "AsyncJob.NotifyCircleUpdated")
	proto.RegisterType((*NotifyPublicAccountUpdated)(nil), "AsyncJob.NotifyPublicAccountUpdated")
	proto.RegisterType((*NotifyThirdPartyRegSuccess)(nil), "AsyncJob.NotifyThirdPartyRegSuccess")
	proto.RegisterType((*AsyncJobRequest)(nil), "AsyncJob.AsyncJobRequest")
	proto.RegisterEnum("AsyncJob.NotifyCircleUpdated_UpdateType", NotifyCircleUpdated_UpdateType_name, NotifyCircleUpdated_UpdateType_value)
	proto.RegisterEnum("AsyncJob.NotifyPublicAccountUpdated_NotifyType", NotifyPublicAccountUpdated_NotifyType_name, NotifyPublicAccountUpdated_NotifyType_value)
	proto.RegisterEnum("AsyncJob.NotifyThirdPartyRegSuccess_ThirdPartyType", NotifyThirdPartyRegSuccess_ThirdPartyType_name, NotifyThirdPartyRegSuccess_ThirdPartyType_value)
}
func (m *NotifyUserInfoUpdated) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyUserInfoUpdated) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *NotifyGuildInfoUpdated) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyGuildInfoUpdated) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *NotifyCircleTopicDeleted) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyCircleTopicDeleted) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *MissionMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.MessageCmdId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(len(m.MessageBody)))
	i += copy(dAtA[i:], m.MessageBody)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ClientType))
	return i, nil
}

func (m *MissionEventMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionEventMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MessageBody != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintAsyncjob(dAtA, i, uint64(len(m.MessageBody)))
		i += copy(dAtA[i:], m.MessageBody)
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ClientType))
	return i, nil
}

func (m *CircleUpdate_NewTopic) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleUpdate_NewTopic) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleUpdate_ActBegin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleUpdate_ActBegin) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(len(m.ActDesc)))
	i += copy(dAtA[i:], m.ActDesc)
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *CircleUpdate_ActEnd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleUpdate_ActEnd) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *NotifyCircleUpdated) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyCircleUpdated) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.UpdateType))
	if m.NewTopicUpdate != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAsyncjob(dAtA, i, uint64(m.NewTopicUpdate.Size()))
		n1, err := m.NewTopicUpdate.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.ActBeginUpdate != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAsyncjob(dAtA, i, uint64(m.ActBeginUpdate.Size()))
		n2, err := m.ActBeginUpdate.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.ActEndUpdate != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAsyncjob(dAtA, i, uint64(m.ActEndUpdate.Size()))
		n3, err := m.ActEndUpdate.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *NotifyPublicAccountUpdated) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyPublicAccountUpdated) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.NotifyType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.PublicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.BizType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.BizId))
	return i, nil
}

func (m *NotifyThirdPartyRegSuccess) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyThirdPartyRegSuccess) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.ThirdPartyType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x22
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(len(m.AccessToken)))
	i += copy(dAtA[i:], m.AccessToken)
	return i, nil
}

func (m *AsyncJobRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AsyncJobRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Cmd))
	dAtA[i] = 0x10
	i++
	i = encodeVarintAsyncjob(dAtA, i, uint64(m.Key))
	if m.JobData != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintAsyncjob(dAtA, i, uint64(len(m.JobData)))
		i += copy(dAtA[i:], m.JobData)
	}
	return i, nil
}

func encodeFixed64Asyncjob(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Asyncjob(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAsyncjob(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *NotifyUserInfoUpdated) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *NotifyGuildInfoUpdated) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.GuildId))
	return n
}

func (m *NotifyCircleTopicDeleted) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.CircleId))
	n += 1 + sovAsyncjob(uint64(m.TopicId))
	return n
}

func (m *MissionMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.MessageCmdId))
	l = len(m.MessageBody)
	n += 1 + l + sovAsyncjob(uint64(l))
	n += 1 + sovAsyncjob(uint64(m.Uid))
	n += 1 + sovAsyncjob(uint64(m.ClientType))
	return n
}

func (m *MissionEventMessage) Size() (n int) {
	var l int
	_ = l
	if m.MessageBody != nil {
		l = len(m.MessageBody)
		n += 1 + l + sovAsyncjob(uint64(l))
	}
	n += 1 + sovAsyncjob(uint64(m.Uid))
	n += 1 + sovAsyncjob(uint64(m.ClientType))
	return n
}

func (m *CircleUpdate_NewTopic) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.TopicId))
	return n
}

func (m *CircleUpdate_ActBegin) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.TopicId))
	l = len(m.ActDesc)
	n += 1 + l + sovAsyncjob(uint64(l))
	n += 1 + sovAsyncjob(uint64(m.ExpireTime))
	return n
}

func (m *CircleUpdate_ActEnd) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.TopicId))
	return n
}

func (m *NotifyCircleUpdated) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.CircleId))
	n += 1 + sovAsyncjob(uint64(m.UpdateType))
	if m.NewTopicUpdate != nil {
		l = m.NewTopicUpdate.Size()
		n += 1 + l + sovAsyncjob(uint64(l))
	}
	if m.ActBeginUpdate != nil {
		l = m.ActBeginUpdate.Size()
		n += 1 + l + sovAsyncjob(uint64(l))
	}
	if m.ActEndUpdate != nil {
		l = m.ActEndUpdate.Size()
		n += 1 + l + sovAsyncjob(uint64(l))
	}
	return n
}

func (m *NotifyPublicAccountUpdated) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.NotifyType))
	n += 1 + sovAsyncjob(uint64(m.PublicId))
	n += 1 + sovAsyncjob(uint64(m.BizType))
	n += 1 + sovAsyncjob(uint64(m.BizId))
	return n
}

func (m *NotifyThirdPartyRegSuccess) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.Uid))
	n += 1 + sovAsyncjob(uint64(m.ThirdPartyType))
	l = len(m.Openid)
	n += 1 + l + sovAsyncjob(uint64(l))
	l = len(m.AccessToken)
	n += 1 + l + sovAsyncjob(uint64(l))
	return n
}

func (m *AsyncJobRequest) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovAsyncjob(uint64(m.Cmd))
	n += 1 + sovAsyncjob(uint64(m.Key))
	if m.JobData != nil {
		l = len(m.JobData)
		n += 1 + l + sovAsyncjob(uint64(l))
	}
	return n
}

func sovAsyncjob(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAsyncjob(x uint64) (n int) {
	return sovAsyncjob(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *NotifyUserInfoUpdated) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyUserInfoUpdated: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyUserInfoUpdated: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyGuildInfoUpdated) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyGuildInfoUpdated: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyGuildInfoUpdated: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyCircleTopicDeleted) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyCircleTopicDeleted: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyCircleTopicDeleted: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MissionMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MessageCmdId", wireType)
			}
			m.MessageCmdId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MessageCmdId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MessageBody", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MessageBody = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("message_cmd_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("message_body")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MissionEventMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionEventMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionEventMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MessageBody", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MessageBody = append(m.MessageBody[:0], dAtA[iNdEx:postIndex]...)
			if m.MessageBody == nil {
				m.MessageBody = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("message_body")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleUpdate_NewTopic) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleUpdate_NewTopic: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleUpdate_NewTopic: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleUpdate_ActBegin) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleUpdate_ActBegin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleUpdate_ActBegin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("act_desc")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleUpdate_ActEnd) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleUpdate_ActEnd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleUpdate_ActEnd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyCircleUpdated) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyCircleUpdated: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyCircleUpdated: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateType", wireType)
			}
			m.UpdateType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewTopicUpdate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NewTopicUpdate == nil {
				m.NewTopicUpdate = &CircleUpdate_NewTopic{}
			}
			if err := m.NewTopicUpdate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActBeginUpdate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActBeginUpdate == nil {
				m.ActBeginUpdate = &CircleUpdate_ActBegin{}
			}
			if err := m.ActBeginUpdate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActEndUpdate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ActEndUpdate == nil {
				m.ActEndUpdate = &CircleUpdate_ActEnd{}
			}
			if err := m.ActEndUpdate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("update_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyPublicAccountUpdated) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyPublicAccountUpdated: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyPublicAccountUpdated: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyType", wireType)
			}
			m.NotifyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NotifyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicId", wireType)
			}
			m.PublicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizType", wireType)
			}
			m.BizType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("notify_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyThirdPartyRegSuccess) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NotifyThirdPartyRegSuccess: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NotifyThirdPartyRegSuccess: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThirdPartyType", wireType)
			}
			m.ThirdPartyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("third_party_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("openid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("access_token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AsyncJobRequest) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AsyncJobRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AsyncJobRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cmd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			m.Key = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Key |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JobData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthAsyncjob
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JobData = append(m.JobData[:0], dAtA[iNdEx:postIndex]...)
			if m.JobData == nil {
				m.JobData = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipAsyncjob(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAsyncjob
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("cmd")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("job_data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAsyncjob(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAsyncjob
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAsyncjob
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAsyncjob
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAsyncjob
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAsyncjob(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAsyncjob = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAsyncjob   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/asyncjob/asyncjob.proto", fileDescriptorAsyncjob) }

var fileDescriptorAsyncjob = []byte{
	// 919 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x55, 0x4f, 0x6f, 0xdb, 0xb6,
	0x1b, 0xae, 0x6c, 0xd7, 0x76, 0x5e, 0xa7, 0xfe, 0x19, 0x0a, 0xda, 0x9f, 0x97, 0x75, 0x89, 0xab,
	0x35, 0x58, 0x31, 0x20, 0x0e, 0xd0, 0xc3, 0xfe, 0x18, 0x41, 0x00, 0xcb, 0x36, 0x3a, 0x6d, 0xab,
	0xe7, 0xba, 0x0a, 0x8a, 0xee, 0x30, 0x42, 0x22, 0xd9, 0x8c, 0xb6, 0x25, 0x6a, 0x16, 0x95, 0x4e,
	0x01, 0x0a, 0xf4, 0x38, 0xec, 0xb4, 0xed, 0x33, 0xf8, 0xc3, 0xf4, 0xb8, 0x61, 0xf7, 0x61, 0xc8,
	0x2e, 0x01, 0x76, 0xdc, 0x17, 0x18, 0x48, 0x5a, 0x8e, 0xe3, 0xb4, 0xcd, 0x4e, 0x22, 0xdf, 0x87,
	0xef, 0xf3, 0x3e, 0x7c, 0xf8, 0x92, 0x82, 0x46, 0x4c, 0xa7, 0xc7, 0x0c, 0xd3, 0x78, 0xcf, 0x8b,
	0xd3, 0x10, 0x8f, 0xb8, 0xbf, 0x18, 0x34, 0xa3, 0x29, 0x17, 0xdc, 0x2c, 0xb7, 0xe5, 0xfc, 0x73,
	0xee, 0x6f, 0xde, 0xc5, 0x3c, 0x08, 0x78, 0xb8, 0x27, 0x26, 0xc7, 0x11, 0xc3, 0xe3, 0x09, 0xdd,
	0x8b, 0xc7, 0x7e, 0xc2, 0x26, 0x82, 0x85, 0x22, 0x8d, 0xa8, 0x5e, 0x6f, 0xfd, 0x1f, 0x6e, 0xf6,
	0xb9, 0x60, 0xcf, 0xd2, 0xc3, 0x98, 0x4e, 0x9d, 0xf0, 0x19, 0x3f, 0x8c, 0x88, 0x27, 0x28, 0xb1,
	0x3e, 0x85, 0x5b, 0x1a, 0x78, 0x90, 0xb0, 0x09, 0x59, 0x42, 0xcc, 0x6d, 0x28, 0x1f, 0xc9, 0x18,
	0x62, 0xa4, 0x6e, 0x34, 0x72, 0xf7, 0x6e, 0xd8, 0x85, 0x57, 0x7f, 0x6c, 0x5f, 0x1b, 0x96, 0x54,
	0xd4, 0x21, 0xd6, 0x37, 0x50, 0xd7, 0xa9, 0x1d, 0x36, 0xc5, 0x13, 0xea, 0xf2, 0x88, 0xe1, 0x2e,
	0x9d, 0x50, 0x99, 0x7c, 0x07, 0xd6, 0xb0, 0x8a, 0xae, 0x66, 0x97, 0x75, 0xd8, 0x51, 0xfc, 0x42,
	0xa6, 0xc8, 0x15, 0xb9, 0x65, 0x7e, 0x15, 0x75, 0x88, 0x35, 0x33, 0xa0, 0xfa, 0x90, 0xc5, 0x31,
	0xe3, 0xe1, 0x43, 0x1a, 0xc7, 0xde, 0x11, 0x35, 0x3f, 0x84, 0x6a, 0xa0, 0x87, 0x08, 0x07, 0x97,
	0x94, 0xad, 0xcf, 0xb1, 0x4e, 0x40, 0x1c, 0x62, 0x7e, 0x00, 0xd9, 0x1c, 0xf9, 0x9c, 0xa4, 0xaa,
	0xc6, 0xda, 0x7c, 0x65, 0x65, 0x8e, 0xd8, 0x9c, 0xa4, 0xe6, 0x2d, 0xc8, 0x27, 0x8c, 0xd4, 0xf3,
	0x0d, 0x63, 0xc1, 0x24, 0x03, 0xe6, 0x0e, 0x54, 0xf0, 0x84, 0xd1, 0x50, 0x20, 0x69, 0x64, 0xbd,
	0xb0, 0x84, 0x83, 0x06, 0xdc, 0x34, 0xa2, 0xd6, 0x0b, 0xd8, 0x98, 0xab, 0xec, 0x1d, 0xd3, 0x50,
	0x64, 0x52, 0x57, 0xcb, 0x4b, 0xa1, 0xeb, 0x6f, 0x29, 0x9f, 0xbb, 0xa2, 0x7c, 0xfe, 0x0d, 0xe5,
	0x3f, 0x81, 0x9b, 0xda, 0x7f, 0x7d, 0x6e, 0xa8, 0x4f, 0x9f, 0xab, 0x93, 0xb8, 0xe0, 0xaf, 0xf1,
	0x3a, 0x7f, 0x5f, 0x1a, 0x2b, 0xa9, 0x6d, 0x2c, 0x6c, 0x7a, 0xc4, 0xc2, 0x2b, 0x53, 0xe5, 0x02,
	0x0f, 0x0b, 0x44, 0x68, 0x8c, 0x2f, 0xf8, 0x5a, 0xf2, 0xb0, 0xe8, 0xd2, 0x18, 0x4b, 0xf1, 0xf4,
	0xfb, 0x88, 0x4d, 0x29, 0x12, 0x2c, 0x90, 0xe2, 0x73, 0xf7, 0x0a, 0x99, 0x78, 0x0d, 0xb8, 0x2c,
	0xa0, 0xd6, 0x47, 0xb0, 0xb1, 0xaa, 0xa0, 0x17, 0x92, 0xab, 0xa5, 0xff, 0x93, 0x83, 0x8d, 0xe5,
	0xde, 0xcb, 0x7a, 0xf6, 0x3f, 0xb4, 0xdd, 0x0e, 0x54, 0x12, 0x5d, 0x4c, 0xd9, 0xba, 0xdc, 0x79,
	0xa0, 0x01, 0x69, 0xab, 0xe9, 0x40, 0x2d, 0xa4, 0xcf, 0x91, 0x96, 0xa1, 0xe3, 0xea, 0x08, 0x2a,
	0xf7, 0xb7, 0x9b, 0xd9, 0xdd, 0x6b, 0xbe, 0xd6, 0xf8, 0x61, 0x35, 0x9c, 0x8f, 0x34, 0x20, 0xa9,
	0xa4, 0x59, 0xbe, 0xb4, 0x36, 0xa3, 0x2a, 0xbc, 0x95, 0x2a, 0x3b, 0x88, 0x61, 0xd5, 0x9b, 0x8f,
	0xe6, 0x54, 0x1d, 0x90, 0x11, 0x44, 0x43, 0x92, 0x11, 0x5d, 0x57, 0x44, 0xef, 0xbd, 0x99, 0xa8,
	0x17, 0x92, 0xe1, 0xba, 0xa7, 0xbe, 0x3a, 0x68, 0x7d, 0x0c, 0x70, 0x78, 0xbe, 0xd1, 0x1b, 0xb0,
	0xd6, 0xef, 0x3d, 0x41, 0xee, 0x57, 0x03, 0xa7, 0x53, 0x33, 0xe4, 0xb4, 0xdd, 0x71, 0x91, 0xdd,
	0x7b, 0xe0, 0xf4, 0x6b, 0x39, 0xb3, 0x02, 0x25, 0x39, 0xed, 0xf5, 0xbb, 0xb5, 0xbc, 0xf5, 0xbb,
	0x01, 0x9b, 0xda, 0xf5, 0x41, 0xe2, 0x4f, 0x18, 0x6e, 0x63, 0xcc, 0x93, 0x50, 0x64, 0xe6, 0xef,
	0x40, 0x25, 0x54, 0xa8, 0x76, 0x76, 0xd9, 0x7e, 0xd0, 0x80, 0x2a, 0x78, 0x07, 0xd6, 0x22, 0x95,
	0x8e, 0x56, 0xba, 0xbe, 0xac, 0xc3, 0xba, 0xbd, 0x7c, 0x76, 0x72, 0xb9, 0xef, 0x4b, 0x3e, 0x3b,
	0x51, 0x1c, 0xef, 0x42, 0x51, 0x2e, 0x60, 0x44, 0x19, 0x99, 0x75, 0xd6, 0x75, 0x9f, 0x9d, 0x38,
	0xc4, 0xda, 0x05, 0xe8, 0x9f, 0x97, 0xab, 0xc1, 0xba, 0xfd, 0x14, 0x0d, 0x0e, 0xed, 0x2f, 0x9d,
	0x0e, 0x72, 0xba, 0x7a, 0x8b, 0xf6, 0x53, 0x64, 0x3b, 0x5f, 0xcb, 0x69, 0xce, 0xfa, 0x6d, 0xb1,
	0x2b, 0xf7, 0x5b, 0x36, 0x25, 0x03, 0x6f, 0x2a, 0xd2, 0x21, 0x3d, 0x7a, 0x9c, 0x60, 0x4c, 0xe3,
	0x38, 0xbb, 0x9e, 0xcb, 0xbb, 0x51, 0xd7, 0xb3, 0x09, 0x35, 0x21, 0xd7, 0xa3, 0x48, 0x26, 0x5c,
	0x6e, 0xa6, 0xaa, 0x58, 0xb0, 0x29, 0x1d, 0xb7, 0xa1, 0xc8, 0x23, 0x1a, 0xaa, 0x87, 0xe6, 0xfc,
	0xc2, 0xcc, 0x63, 0xf2, 0xb5, 0xf0, 0x54, 0x3d, 0x24, 0xf8, 0x98, 0x86, 0xf5, 0xc2, 0xf2, 0x63,
	0xa5, 0x11, 0x57, 0x02, 0xd6, 0x5d, 0xa8, 0xba, 0x17, 0x89, 0x8b, 0x90, 0x7b, 0xf4, 0xa8, 0x66,
	0x98, 0x00, 0xc5, 0x27, 0xbd, 0xce, 0x67, 0x6d, 0xb7, 0x96, 0xb3, 0x7c, 0xf8, 0x5f, 0xd6, 0x10,
	0x43, 0xfa, 0x5d, 0x42, 0x63, 0x21, 0xf7, 0x81, 0x83, 0x95, 0x7d, 0xe0, 0x80, 0xc8, 0xf8, 0x98,
	0xa6, 0x17, 0xa4, 0xcb, 0x80, 0x3c, 0x83, 0x11, 0xf7, 0x11, 0xf1, 0x84, 0xa7, 0x14, 0x67, 0x6f,
	0x57, 0x69, 0xc4, 0xfd, 0xae, 0x27, 0xbc, 0xfb, 0x3f, 0x1b, 0xb0, 0xf8, 0x0b, 0x99, 0x2f, 0xa0,
	0xd8, 0x26, 0x44, 0x8e, 0xde, 0x39, 0x6f, 0xc5, 0x15, 0x09, 0x9b, 0xb7, 0x9b, 0x8b, 0x9f, 0x54,
	0xf3, 0xf1, 0x17, 0xb6, 0xfe, 0x49, 0xf5, 0x82, 0x48, 0xa4, 0x68, 0x60, 0x5b, 0xad, 0x97, 0xb3,
	0xb3, 0xbc, 0xf1, 0xe3, 0xec, 0x2c, 0x5f, 0x4e, 0x5a, 0x41, 0x6b, 0xdc, 0x1a, 0xb5, 0x7e, 0x99,
	0x9d, 0xe5, 0xdf, 0xdf, 0x4d, 0x1a, 0xfb, 0x09, 0x23, 0x07, 0x8d, 0xdd, 0xa0, 0xb1, 0x8f, 0x03,
	0x39, 0x18, 0x37, 0xf6, 0xc7, 0x34, 0x3d, 0x68, 0xec, 0x8e, 0x1a, 0xfb, 0x23, 0xee, 0x1f, 0x6c,
	0x16, 0x7f, 0x98, 0x9d, 0xe5, 0xff, 0xe6, 0x76, 0xed, 0xd5, 0xe9, 0x96, 0xf1, 0xeb, 0xe9, 0x96,
	0xf1, 0xe7, 0xe9, 0x96, 0xf1, 0xd3, 0x5f, 0x5b, 0xd7, 0xfe, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x90,
	0x7e, 0x7e, 0x5a, 0x4b, 0x07, 0x00, 0x00,
}
