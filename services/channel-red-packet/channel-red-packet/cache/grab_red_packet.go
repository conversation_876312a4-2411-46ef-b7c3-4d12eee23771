package cache

import (
	"fmt"
	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

// 红包抢到个数排行榜
func genRedPacketClickRankKey(orderId string) string {
	return fmt.Sprintf("rp_click_rank_%s", orderId)
}

func (r *RedPacketCache) InitRedPacketClickRank(orderId string) error {
	key := genRedPacketClickRankKey(orderId)

	err := r.cli.ZAddNX(key, redis.Z{Member: 0, Score: float64(0)}).Err()
	if err != nil {
		log.Errorf("InitRedPacketClickRank fail. orderId:%v, err:%v", orderId, err)
	}

	r.cli.Expire(key, 6*time.Hour)
	return nil
}

func (r *RedPacketCache) AddRedPacketClickCnt(orderId string, uid, cnt uint32) error {
	key := genRedPacketClickRankKey(orderId)
	return r.cli.ZAddNX(key, redis.Z{Member: uid, Score: float64(cnt)}).Err()
}

func (r *RedPacketCache) GetRedPacketClickCntRankList(orderId string, begin, limit uint32) ([]uint32, error) {
	uidList := make([]uint32, 0, limit)
	key := genRedPacketClickRankKey(orderId)
	if limit == 0 {
		return uidList, nil
	}

	strList, err := r.cli.ZRevRange(key, int64(begin), int64(begin+limit-1)).Result()
	if err != nil {
		log.Errorf("GetRedPacketClickCntRankList fail. orderId:%v, err:%v", orderId, err)
		return uidList, err
	}

	for _, str := range strList {
		uid, _ := strconv.ParseInt(str, 10, 32)
		if uid != 0 {
			uidList = append(uidList, uint32(uid))
		}
	}

	return uidList, nil
}
