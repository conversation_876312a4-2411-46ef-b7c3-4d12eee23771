package commission

import (
	"golang.52tt.com/pkg/log"
	"golang.org/x/net/context"
)

type AcType uint8

const (
	AcTypeOther   AcType = 0
	AcTypePrivate AcType = 1
	AcTypePublic  AcType = 2
)

type EncashInfo struct {
	Uid         uint32 // required
	Amount      uint64 // required 提现金额 分
	Remark      string // 备注
	AcType      AcType // 提现账号类型 0-其他,1-对私,2-对公
	RemitAmount uint64 // 实际打款金额 分
	Remitter    string // 打款供应商(付款方)
	Receiver    string // 收款方
	OrderId     string // 外部订单号
	CreateTime  string //同步外部时间
}

type encashmentRequest struct {
	AppID        string `json:"appid"`
	Uid          uint32 `json:"uid"`
	Amount       uint64 `json:"amount"`
	Remark       string `json:"remark"`
	OutOrderNo   string `json:"outOrderNo"`
	EncashAcType uint8  `json:"encashAcType"`
	RemitAmount  uint64 `json:"remitAmount"`
	Remitter     string `json:"remitter"`
	Receiver     string `json:"receiver"`
	CreateTime   string `json:"createTime"`
}

type checkEncashBlockReq struct {
	Uid uint32 `json:"userId"`
}

// fuck跟文档不一样
type encashmentResponse string

const (
	encashmentURI       = "commision/encashment.do"
	checkEncashBlockURI = "commision/checkEncashBlock.do"
)

func (c *client) Encashment(ctx context.Context, info *EncashInfo) error {
	req := &encashmentRequest{
		AppID:        c.appID,
		Uid:          info.Uid,
		Amount:       info.Amount,
		Remark:       info.Remark,
		EncashAcType: uint8(info.AcType),
		Remitter:     info.Remitter,
		Receiver:     info.Receiver,
		OutOrderNo:   info.OrderId,
		CreateTime:   info.CreateTime,
	}
	if info.AcType == AcTypePublic {
		req.RemitAmount = info.Amount
	} else {
		req.RemitAmount = info.RemitAmount
	}
	var resp encashmentResponse
	_, err := c.post(ctx, encashmentURI, req, &resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "Encashment err:%s, info:%+v", err, info)
		return err
	}
	log.InfoWithCtx(ctx, "Encashment OK info:%+v", info)
	return nil
}

func (c *client) CheckEncashBlock(ctx context.Context, uid uint32) error {

	req := &checkEncashBlockReq{
		Uid: uid,
	}
	var resp string

	_, err := c.post(ctx, checkEncashBlockURI, req, resp)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckEncashBlock %d  FAILED %v", uid, err)
		return err
	}

	return nil
}
