package main

import (
	"flag"
	"fmt"
	"time"

	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/anchor-level/manager"
)

var (
	configPath = flag.String("conf", "", "配置文件路径")

	Uid = flag.Int("uid", 0, "uid")

	branch    string
	sha1ver   string // sha1 revision used to build the program
	buildTime string // when the executable was built
)

func init() {
	log.SetLevel(log.DebugLevel)
	fmt.Printf("branch=%q\n", branch)
	fmt.Printf("sha1ver=%q\n", sha1ver)
	fmt.Printf("buildTime=%q\n\n", buildTime)
}

func main() {
	log.SetLevel(log.DebugLevel)
	flag.Parse()

	fmt.Printf("conf=%s\n", *configPath)
	fmt.Printf("Uid=%d\n", *Uid)
	if *Uid == 0 {
		return
	}

	cfg, err := config.NewConfig("json", *configPath)
	if err != nil {
		log.Errorln("Failed to init ServerConfig from file:", *configPath)
		return
	}

	mgr, _, err := manager.NewAnchorLevelMgr2(cfg)
	if err != nil {
		log.Fatalln(err)
		return
	}
	time.Sleep(time.Second * 3)

	mgr.SendNewAnchorTaskRewardTest(uint32(*Uid))
}
