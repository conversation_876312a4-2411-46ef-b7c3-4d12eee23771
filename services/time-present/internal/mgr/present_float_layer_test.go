package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/protocol/services/presentextraconf"
	pb "golang.52tt.com/protocol/services/time-present"
	"reflect"
	"testing"
)

func Test_manager_AddPresentFloatLayer(t *testing.T) {
	type args struct {
		ctx         context.Context
		in          *pb.StTimePresentItemConfig
		floatImgUrl string
		jumpUrl     string
		operator    string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "AddPresentFloatLayer",
			args: args{
				ctx: context.Background(),
				in: &pb.StTimePresentItemConfig{
					Type:        1,
					ItemId:      1,
					EffectBegin: 1729094400,
					EffectEnd:   1732809600,
				},
				floatImgUrl: "https://www.baidu.com",
				jumpUrl:     "https://jump.baidu.com",
				operator:    "马富达",
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getPresentExtraCliMock().EXPECT().AddPresentFloatLayer(gomock.Any(), &presentextraconf.AddPresentFloatLayerReq{
					Layer: &presentextraconf.PresentFloatLayer{
						GiftId:        1,
						FloatImageUrl: "https://www.baidu.com",
						JumpUrl:       "https://jump.baidu.com",
						IsActivityUrl: false,
						EffectBegin:   1729094400,
						EffectEnd:     1732809600,
						Operator:      "马富达",
					},
				}).Return(&presentextraconf.AddPresentFloatLayerResp{}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.AddPresentFloatLayer(tt.args.ctx, tt.args.in, tt.args.floatImgUrl, tt.args.jumpUrl, tt.args.operator); (err != nil) != tt.wantErr {
				t.Errorf("AddPresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_UpdatePresentFloatLayer(t *testing.T) {
	type args struct {
		ctx         context.Context
		in          *pb.StTimePresentItemConfig
		floatImgUrl string
		jumpUrl     string
		operator    string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "UpdatePresentFloatLayer",
			args: args{
				ctx: context.Background(),
				in: &pb.StTimePresentItemConfig{
					Type:          1,
					ItemId:        1,
					EffectBegin:   1729094400,
					EffectEnd:     1732809600,
					FloatImageUrl: "https://www.baidu.com",
					JumpUrl:       "https://jump.baidu.com",
				},
				floatImgUrl: "https://www.baidu.com",
				jumpUrl:     "https://jump.baidu.com",
				operator:    "马富达",
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getPresentExtraCliMock().EXPECT().UpdatePresentFloatLayer(gomock.Any(), &presentextraconf.UpdatePresentFloatLayerReq{
					Layer: &presentextraconf.PresentFloatLayer{
						GiftId:        1,
						FloatImageUrl: "https://www.baidu.com",
						JumpUrl:       "https://jump.baidu.com",
						IsActivityUrl: false,
						EffectBegin:   1729094400,
						EffectEnd:     1732809600,
						Operator:      "马富达",
					},
				}).Return(&presentextraconf.UpdatePresentFloatLayerResp{}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.UpdatePresentFloatLayer(tt.args.ctx, tt.args.in, tt.args.floatImgUrl, tt.args.jumpUrl, tt.args.operator); (err != nil) != tt.wantErr {
				t.Errorf("UpdatePresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_DeletePresentFloatLayer(t *testing.T) {
	type args struct {
		ctx    context.Context
		giftId uint32
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "DeletePresentFloatLayer",
			args: args{
				ctx:    context.Background(),
				giftId: 1,
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getPresentExtraCliMock().EXPECT().DelPresentFloatLayer(gomock.Any(), &presentextraconf.DelPresentFloatLayerReq{
					GiftId: 1,
				}).Return(&presentextraconf.DelPresentFloatLayerResp{}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			if err := m.DeletePresentFloatLayer(tt.args.ctx, tt.args.giftId); (err != nil) != tt.wantErr {
				t.Errorf("DeletePresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_manager_BatchGetAllPresentFloatLayer(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		args     args
		want     map[uint32]*presentextraconf.PresentFloatLayer
		wantErr  bool
		initFunc func(m *mgrForTest)
	}{
		{
			name: "BatchGetAllPresentFloatLayer",
			args: args{
				ctx: context.Background(),
			},
			want: map[uint32]*presentextraconf.PresentFloatLayer{
				1: {
					GiftId:        1,
					FloatImageUrl: "https://www.baidu.com",
					JumpUrl:       "https://jump.baidu.com",
					IsActivityUrl: false,
					EffectBegin:   1729094400,
					EffectEnd:     1732809600,
					Operator:      "马富达",
					EffectStatus:  1,
					UpdateTime:    1732809600,
				},
			},
			wantErr: false,
			initFunc: func(m *mgrForTest) {
				m.getPresentExtraCliMock().EXPECT().GetPresentFloatLayer(gomock.Any(), &presentextraconf.GetPresentFloatLayerReq{
					Page:  0,
					Count: 10000, // 一次性获取所有浮层
				}).Return(&presentextraconf.GetPresentFloatLayerResp{
					LayerInfos: []*presentextraconf.PresentFloatInfo{
						{
							LayerInfo: &presentextraconf.PresentFloatLayer{
								GiftId:        1,
								FloatImageUrl: "https://www.baidu.com",
								JumpUrl:       "https://jump.baidu.com",
								IsActivityUrl: false,
								EffectBegin:   1729094400,
								EffectEnd:     1732809600,
								Operator:      "马富达",
								EffectStatus:  1,
								UpdateTime:    1732809600,
							},
							PresentConfig: &presentextraconf.PresentBaseConfig{
								GiftId:     1,
								GiftName:   "test",
								PriceValue: 0,
								PriceType:  0,
								GiftImage:  "https://www.baidu.com",
							},
						},
					},
					Total:          1,
					LastUpdateTime: 1732809600,
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newMgrForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.BatchGetAllPresentFloatLayer(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetAllPresentFloatLayer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BatchGetAllPresentFloatLayer() got = %v, want %v", got, tt.want)
			}
		})
	}
}
