// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-dating-game-async/cache (interfaces: IChannelDatingGameCache)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIChannelDatingGameCache is a mock of IChannelDatingGameCache interface.
type MockIChannelDatingGameCache struct {
	ctrl     *gomock.Controller
	recorder *MockIChannelDatingGameCacheMockRecorder
}

// MockIChannelDatingGameCacheMockRecorder is the mock recorder for MockIChannelDatingGameCache.
type MockIChannelDatingGameCacheMockRecorder struct {
	mock *MockIChannelDatingGameCache
}

// NewMockIChannelDatingGameCache creates a new mock instance.
func NewMockIChannelDatingGameCache(ctrl *gomock.Controller) *MockIChannelDatingGameCache {
	mock := &MockIChannelDatingGameCache{ctrl: ctrl}
	mock.recorder = &MockIChannelDatingGameCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChannelDatingGameCache) EXPECT() *MockIChannelDatingGameCacheMockRecorder {
	return m.recorder
}

// DelHatUser mocks base method.
func (m *MockIChannelDatingGameCache) DelHatUser(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelHatUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelHatUser indicates an expected call of DelHatUser.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelHatUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelHatUser", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelHatUser), arg0, arg1)
}

// DelUserCacheInfo mocks base method.
func (m *MockIChannelDatingGameCache) DelUserCacheInfo(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserCacheInfo", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserCacheInfo indicates an expected call of DelUserCacheInfo.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelUserCacheInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserCacheInfo", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelUserCacheInfo), arg0, arg1)
}

// DelUserExposure mocks base method.
func (m *MockIChannelDatingGameCache) DelUserExposure(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserExposure", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserExposure indicates an expected call of DelUserExposure.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelUserExposure(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserExposure", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelUserExposure), arg0)
}

// DelUserLikeBeatObj mocks base method.
func (m *MockIChannelDatingGameCache) DelUserLikeBeatObj(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserLikeBeatObj", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserLikeBeatObj indicates an expected call of DelUserLikeBeatObj.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelUserLikeBeatObj(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserLikeBeatObj", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelUserLikeBeatObj), arg0, arg1)
}

// DelUserLikeBeatVal mocks base method.
func (m *MockIChannelDatingGameCache) DelUserLikeBeatVal(arg0, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserLikeBeatVal", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelUserLikeBeatVal indicates an expected call of DelUserLikeBeatVal.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelUserLikeBeatVal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserLikeBeatVal", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelUserLikeBeatVal), arg0, arg1)
}

// DelUserRecvValue mocks base method.
func (m *MockIChannelDatingGameCache) DelUserRecvValue(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserRecvValue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserRecvValue indicates an expected call of DelUserRecvValue.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelUserRecvValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserRecvValue", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelUserRecvValue), arg0, arg1)
}

// DelUserSendValue mocks base method.
func (m *MockIChannelDatingGameCache) DelUserSendValue(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelUserSendValue", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelUserSendValue indicates an expected call of DelUserSendValue.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelUserSendValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelUserSendValue", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelUserSendValue), arg0, arg1)
}

// DelVipUser mocks base method.
func (m *MockIChannelDatingGameCache) DelVipUser(arg0 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelVipUser", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelVipUser indicates an expected call of DelVipUser.
func (mr *MockIChannelDatingGameCacheMockRecorder) DelVipUser(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelVipUser", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).DelVipUser), arg0)
}

// GetExposureChannelId mocks base method.
func (m *MockIChannelDatingGameCache) GetExposureChannelId(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExposureChannelId", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExposureChannelId indicates an expected call of GetExposureChannelId.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetExposureChannelId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExposureChannelId", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetExposureChannelId), arg0)
}

// GetHatUserKey mocks base method.
func (m *MockIChannelDatingGameCache) GetHatUserKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHatUserKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetHatUserKey indicates an expected call of GetHatUserKey.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetHatUserKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHatUserKey", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetHatUserKey), arg0)
}

// GetLastUid mocks base method.
func (m *MockIChannelDatingGameCache) GetLastUid(arg0, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastUid", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastUid indicates an expected call of GetLastUid.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetLastUid(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastUid", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetLastUid), arg0, arg1)
}

// GetUserIsExposure mocks base method.
func (m *MockIChannelDatingGameCache) GetUserIsExposure(arg0, arg1 uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserIsExposure", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserIsExposure indicates an expected call of GetUserIsExposure.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetUserIsExposure(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserIsExposure", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetUserIsExposure), arg0, arg1)
}

// GetUserLikeBeatObjKey mocks base method.
func (m *MockIChannelDatingGameCache) GetUserLikeBeatObjKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLikeBeatObjKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUserLikeBeatObjKey indicates an expected call of GetUserLikeBeatObjKey.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetUserLikeBeatObjKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLikeBeatObjKey", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetUserLikeBeatObjKey), arg0)
}

// GetUserLikeBeatVal mocks base method.
func (m *MockIChannelDatingGameCache) GetUserLikeBeatVal(arg0, arg1 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLikeBeatVal", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLikeBeatVal indicates an expected call of GetUserLikeBeatVal.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetUserLikeBeatVal(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLikeBeatVal", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetUserLikeBeatVal), arg0, arg1)
}

// GetUserRecvValueKey mocks base method.
func (m *MockIChannelDatingGameCache) GetUserRecvValueKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRecvValueKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUserRecvValueKey indicates an expected call of GetUserRecvValueKey.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetUserRecvValueKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRecvValueKey", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetUserRecvValueKey), arg0)
}

// GetUserSendValue mocks base method.
func (m *MockIChannelDatingGameCache) GetUserSendValue(arg0, arg1 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSendValue", arg0, arg1)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserSendValue indicates an expected call of GetUserSendValue.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetUserSendValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSendValue", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetUserSendValue), arg0, arg1)
}

// GetUserSendValueKey mocks base method.
func (m *MockIChannelDatingGameCache) GetUserSendValueKey(arg0, arg1 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserSendValueKey", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUserSendValueKey indicates an expected call of GetUserSendValueKey.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetUserSendValueKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSendValueKey", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetUserSendValueKey), arg0, arg1)
}

// GetVipUser mocks base method.
func (m *MockIChannelDatingGameCache) GetVipUser(arg0 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVipUser", arg0)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVipUser indicates an expected call of GetVipUser.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetVipUser(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVipUser", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetVipUser), arg0)
}

// GetVipUserKey mocks base method.
func (m *MockIChannelDatingGameCache) GetVipUserKey(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVipUserKey", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetVipUserKey indicates an expected call of GetVipUserKey.
func (mr *MockIChannelDatingGameCacheMockRecorder) GetVipUserKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVipUserKey", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).GetVipUserKey), arg0)
}

// SetCurrentUid mocks base method.
func (m *MockIChannelDatingGameCache) SetCurrentUid(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetCurrentUid", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCurrentUid indicates an expected call of SetCurrentUid.
func (mr *MockIChannelDatingGameCacheMockRecorder) SetCurrentUid(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCurrentUid", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).SetCurrentUid), arg0, arg1, arg2)
}

// SetUserExposure mocks base method.
func (m *MockIChannelDatingGameCache) SetUserExposure(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserExposure", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserExposure indicates an expected call of SetUserExposure.
func (mr *MockIChannelDatingGameCacheMockRecorder) SetUserExposure(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserExposure", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).SetUserExposure), arg0, arg1)
}

// SetUserSendValue mocks base method.
func (m *MockIChannelDatingGameCache) SetUserSendValue(arg0, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserSendValue", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserSendValue indicates an expected call of SetUserSendValue.
func (mr *MockIChannelDatingGameCacheMockRecorder) SetUserSendValue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserSendValue", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).SetUserSendValue), arg0, arg1, arg2)
}

// SetVipUser mocks base method.
func (m *MockIChannelDatingGameCache) SetVipUser(arg0, arg1 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVipUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetVipUser indicates an expected call of SetVipUser.
func (mr *MockIChannelDatingGameCacheMockRecorder) SetVipUser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVipUser", reflect.TypeOf((*MockIChannelDatingGameCache)(nil).SetVipUser), arg0, arg1)
}
