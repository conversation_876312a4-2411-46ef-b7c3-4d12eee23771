package hanlders

import (
	"context"
	"encoding/json"
	"golang.org/x/sync/errgroup"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	exchangePb "golang.52tt.com/protocol/services/exchange"
	goldCommission "golang.52tt.com/protocol/services/gold-commission"
	guildPb "golang.52tt.com/protocol/services/guildsvr"
	settleBillPb "golang.52tt.com/protocol/services/settlement-bill"

	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
)

// GetIncomeSummary 未结算数据，各种详情信息
func GetIncomeSummary(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetIncomeSummaryReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeSummary Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetIncomeSummary begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetIncomeSummary failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetIncomeSummary failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeSummary GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	resp := &api.GetIncomeSummaryResp{}

	eg := errgroup.Group{}
	eg.Go(func() error {
		// bin哥那边获取积分的接口
		amountRsp, err := models.GetModelServer().ExchangeGuildClientCli.GetExchangeAmount(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary ExchangeGuildClient.GetExchangeGuildAmount failed: %+v", err)
			return err
		}
		resp.GiftScore = amountRsp.Amount.Score
		resp.GiftScoreUndo = amountRsp.UndoAmount.Score

		resp.AwardScore = amountRsp.Amount.AnchorScore
		resp.AwardScoreUndo = amountRsp.UndoAmount.AnchorScore

		resp.PkScore = amountRsp.Amount.MaskedPkScore
		resp.PkScoreUndo = amountRsp.UndoAmount.MaskedPkScore

		resp.KnightScore = amountRsp.Amount.KnightScore
		resp.KnightScoreUndo = amountRsp.UndoAmount.KnightScore

		resp.EsportScore = amountRsp.Amount.EsportScore
		resp.EsportScoreUndo = amountRsp.UndoAmount.EsportScore
		return nil
	})

	// todo 后面把GetGuildsUnSettlementSummaryByGuildId整合成一个查询接口

	eg.Go(func() error {
		// 语音房 收益
		getYuyinMonthIncomeRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_YUYIN_GOLD, guilds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary GetGuildsUnSettlementSummaryByGuildId failed: %+v", err)
			return err
		}
		resp.YuyinBaseCommission = getYuyinMonthIncomeRsp.MonthIncome
		return nil
	})

	eg.Go(func() error {
		// 互动游戏 收益
		getGameMonthIncomeRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_INTERACT_GAME_GOLD, guilds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary GetGuildsUnSettlementSummaryByGuildId failed: %+v", err)
			return err
		}
		resp.InteractGameCommission = getGameMonthIncomeRsp.MonthIncome
		return nil
	})

	eg.Go(func() error {
		// 互动游戏额外奖励
		getGameMonthExtraIncomeRsp, err := models.GetModelServer().GoldCommissionCli.GetInteractGameExtraIncome(ctx, &goldCommission.GetInteractGameExtraIncomeReq{
			GuildIds:  guilds,
			MonthTime: uint32(time.Now().Unix()),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary GetInteractGameExtraIncome failed: %+v", err)
			return err
		}
		log.InfoWithCtx(ctx, "GetIncomeSummary uid=%d guilds=%v GetInteractGameExtraIncome=%s", uid, guilds, getGameMonthExtraIncomeRsp.String())
		resp.InteractGameCommissionExtra = getGameMonthExtraIncomeRsp.GetInfo().GetGameMonthExtraIncome()
		return nil
	})

	eg.Go(func() error {
		// 电竞 收益
		getESportMonthIncomeRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_ESPORT_GOLD, guilds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary GetGuildsUnSettlementSummaryByGuildId failed: %+v", err)
			return err
		}
		resp.EsportCommission = getESportMonthIncomeRsp.MonthIncome
		return nil
	})

	eg.Go(func() error {
		// 娱乐房 收益 由于会长服务号共用了接口，这里暂时查两次
		getAmuseMonthIncomeRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_AMUSE_GOLD, guilds)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary GetGuildsUnSettlementSummaryByGuildId failed: %+v", err)
			return err
		}
		resp.AmuseRoomCommission = getAmuseMonthIncomeRsp.MonthIncome
		resp.ThisMonthAmuseExtra = getAmuseMonthIncomeRsp.ThisMonthExtraIncome
		resp.LastMonthAmuseExtra = getAmuseMonthIncomeRsp.LastMonthExtraIncome
		resp.NotSettleAmuseExtra = getAmuseMonthIncomeRsp.GetNotSettleAmuseExtra()
		return nil
	})

	eg.Go(func() error {
		// 积分冻结
		getScoreFreezeRsp, err := models.GetModelServer().ExchangeGuildClientCli.GetMasterFreezeTotal(ctx, uid, exchangePb.ExchangeType_PRESENT)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetIncomeSummary GetMasterFreezeTotal failed: %+v", err)
			return err
		}
		resp.GiftScoreFreeze = getScoreFreezeRsp.GetFreezeTotalScore()
		return nil
	})

	if err = eg.Wait(); err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeSummary failed to wait err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误，请稍后重试", nil)
		return
	}

	log.DebugWithCtx(ctx, "GetIncomeSummary end uid=%d req:%v resp:%v", uid, req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetIncomeStats(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetIncomeStatsReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeStats Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetIncomeStats begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetIncomeStats failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetIncomeStats failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeSummary GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	resp := &api.GetIncomeStatsResp{}

	// todo 后面把GetGuildsUnSettlementSummaryByGuildId整合成一个查询接口

	yuYinRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_YUYIN_GOLD, guilds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeStats yuyin GetGuildsUnSettlementSummaryByGuildId [%+v].", req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	resp.YuyinSummaryDay = yuYinRsp.GetTodayIncome()
	resp.YuyinSummaryMonth = yuYinRsp.GetMonthIncome()

	AmuseRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_AMUSE_GOLD, guilds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeStats amuse GetGuildsUnSettlementSummaryByGuildId [%+v].", req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	resp.AmuseSummaryDay = AmuseRsp.GetTodayIncome()
	resp.AmuseSummaryMonth = AmuseRsp.GetMonthIncome()

	gameRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_INTERACT_GAME_GOLD, guilds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeStats game GetGuildsUnSettlementSummaryByGuildId [%+v].", req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	resp.InteractGameSummaryDay = gameRsp.GetTodayIncome()
	resp.InteractGameSummaryMonth = gameRsp.GetMonthIncome()

	eSportRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsUnSettlementSummaryByGuildId(ctx, goldCommission.GoldType_ESPORT_GOLD, guilds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetIncomeStats eSport GetGuildsUnSettlementSummaryByGuildId [%+v].", req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	resp.EsportSummaryDay = eSportRsp.GetTodayIncome()
	resp.EsportSummaryMonth = eSportRsp.GetMonthIncome()

	log.DebugWithCtx(ctx, "GetIncomeStats end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetScoreBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetScoreBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScoreBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	log.DebugWithCtx(ctx, "GetScoreBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	billId := req.GetBillId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetScoreBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}
	if !isChairman {
		log.ErrorWithCtx(ctx, "GetScoreBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	if req.Limit == 0 {
		req.Limit = 10
	}
	uidList := make([]uint32, 0)
	usernames := make([]string, 0)
	resp := &api.GetScoreBillDetailResp{}
	var scoreRsp *exchangePb.UserAllScoreList
	var scoreType exchangePb.ExchangeType

	switch settleBillPb.SettlementBillType(req.BillType) {
	case settleBillPb.SettlementBillType_GiftScore:
		scoreType = exchangePb.ExchangeType_PRESENT
	case settleBillPb.SettlementBillType_AwardScore:
		scoreType = exchangePb.ExchangeType_ANCHOR
	case settleBillPb.SettlementBillType_MaskPKScore:
		scoreType = exchangePb.ExchangeType_PK
	case settleBillPb.SettlementBillType_KnightScore:
		scoreType = exchangePb.ExchangeType_KNIGHT
	case settleBillPb.SettlementBillType_ESportScore:
		scoreType = exchangePb.ExchangeType_ESPORT
	default:
		log.ErrorWithCtx(ctx, "GetScoreBillDetail type err:%s", req.BillType)
		_ = web.ServeAPICodeJson(w, -505, "类型错误", nil)
		return
	}

	if billId == "" {
		scoreRsp, err = models.GetModelServer().ExchangeGuildClientCli.GetExchangeAmountListType(ctx, uid, uint32(scoreType), req.Offset, req.Limit)
	} else {
		billRsp, sErr := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, billId)
		if sErr != nil {
			log.ErrorWithCtx(ctx, "GetScoreBillDetail GetSettlementBill failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		startTime, endTime := billRsp.SettleStart, billRsp.SettleEnd
		log.InfoWithCtx(ctx, "GetScoreBillDetail GetSettlementBill. bill: %s rsp: %+v", billId, billRsp)
		scoreRsp, err = models.GetModelServer().ExchangeGuildClientCli.GetSettlementAttribute(
			ctx, uid, uint32(scoreType), req.Offset, req.Limit, uint64(startTime), uint64(endTime))
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScoreBillDetail get score detail err:%v", err)
		_ = web.ServeAPICodeJson(w, -505, err.Error(), nil)
		return
	}

	if scoreRsp != nil {
		for _, item := range scoreRsp.UserAllScoreList {
			uidList = append(uidList, item.Uid)
		}
	}

	guildsResp, err := models.GetModelServer().GuildCli.GetGuildBat(ctx, uid, &guildPb.GetGuildBatReq{GuildIdList: guilds})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetScoreBillDetail GetGuildBat err: %s, req: %+v", err, req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	guildsMap := make(map[uint32]*guildPb.GuildResp)
	for _, g := range guildsResp.GetGuildList() {
		guildsMap[g.GuildId] = g
	}

	if len(uidList) > 0 {
		userInfoMap, err := models.GetModelServer().AccountCli.BatGetUserByUid(ctx, uidList...)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetScoreBillDetail AccountClient.BatGetUserByUid failed %v, uid:%d uids:%d", err, uid, uidList)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		for _, uInfo := range userInfoMap {
			usernames = append(usernames, uInfo.Username)
		}
		log.DebugWithCtx(ctx, "GetScoreBillDetail get user names: %d -> %v", uidList, usernames)

		resp.InfoList = make([]*api.ScoreBillDetailItem, 0)
		for _, item := range scoreRsp.UserAllScoreList {
			guildInfo := guildsMap[item.GuildId]
			shortGuildId := item.GuildId
			if guildInfo.GetShortId() > 0 {
				shortGuildId = guildInfo.GetShortId()
			}
			uInfo := userInfoMap[item.Uid]
			var score uint64 = 0
			switch scoreType {
			case exchangePb.ExchangeType_PRESENT:
				score = item.AllScore.Score
			case exchangePb.ExchangeType_ANCHOR:
				score = item.AllScore.AnchorScore
			case exchangePb.ExchangeType_PK:
				score = item.AllScore.MaskedPkScore
			case exchangePb.ExchangeType_KNIGHT:
				score = item.AllScore.KnightScore
			case exchangePb.ExchangeType_ESPORT:
				score = item.AllScore.EsportScore
			}
			resp.InfoList = append(resp.InfoList, &api.ScoreBillDetailItem{
				GuildId:    shortGuildId,
				AnchorName: uInfo.Nickname,
				AnchorUid:  item.Uid,
				Score:      score,
				Username:   uInfo.Username,
				Alias:      uInfo.Alias,
				Date:       time.Unix(int64(item.GetUpdateAt()), 0).Format("2006-01-02"),
			})
		}
	}
	log.DebugWithCtx(ctx, "GetScoreBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetAmuseRoomBillDetail 获取娱乐房统计列表
func GetAmuseRoomBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetAmuseRoomBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetAmuseRoomBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	billId := req.GetBillId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}
	resp := &api.GetAmuseRoomBillDetailResp{}

	var settleStatus api.SettleStatus
	var unit = goldCommission.TimeFilterUnit(req.Unit)
	var beginTime, endTime uint32

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	if billId == "" {
		settleStatus = api.SettleStatus_SettleStatusWait
	} else {
		settleStatus = api.SettleStatus_SettleStatusFinished
		unit = goldCommission.TimeFilterUnit_BY_MONTH
		billRsp, err := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail GetSettlementBill failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		beginTime, endTime = billRsp.SettleStart, billRsp.SettleEnd
		log.InfoWithCtx(ctx, "GetAmuseRoomBillDetail GetSettlementBill. bill: %s rsp: %+v", billId, billRsp)
	}

	// 娱乐房 - 拉取时间段统计信息
	getReq := &goldCommission.GetGuildsAmuseChannelStatReq{
		GuildIds:     guilds,
		SettleStatus: goldCommission.ReqSettleStatus(settleStatus),
		BeginTime:    beginTime,
		EndTime:      endTime,
		Unit:         unit,
		Offset:       req.Offset,
		Limit:        req.Limit,
	}
	getRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsAmuseChannelStat(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail GetGuildsAmuseChannelStat [%+v]. err :%+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	log.DebugWithCtx(ctx, "GetAmuseRoomBillDetail GetAmuseChannelsIncome req:%+v resp:%+v", getReq, getRsp)

	channelIds := make([]uint32, 0)
	for _, item := range getRsp.GetChannelList() {
		channelIds = append(channelIds, item.GetChannelId())
	}

	channelsInfoMap, err := models.GetModelServer().ChannelCli.BatchGetChannelSimpleInfo(ctx, uid, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail list channel simple info by ids(%v) err(%v)", channelIds, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	resp.InfoList = make([]*api.AmuseRoomBillDetailItem, 0)
	for _, item := range getRsp.GetChannelList() {
		shortGuildId := item.GuildId
		guildResp, err := models.GetModelServer().GuildCli.GetGuild(ctx, item.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail GetGuild err:%v", err)
		} else {
			if guildResp.GetShortId() != 0 {
				shortGuildId = guildResp.GetShortId()
			}
		}
		roomInfo := channelsInfoMap[item.GetChannelId()]
		var roomName string
		if roomInfo != nil {
			roomName = *roomInfo.Name
		}
		resp.InfoList = append(resp.InfoList, &api.AmuseRoomBillDetailItem{
			GuildId:       shortGuildId,
			OriginGuildId: item.GuildId,
			RoomId:        item.GetChannelId(),
			RoomName:      roomName,
			Income:        item.Income,
			Flow:          item.Fee,
			GiftFlow:      item.PresentFee,
			WerewolfFlow:  item.WerewolfFee,
			Date:          time.Unix(int64(item.Date), 0).Format("2006-01"),
		})
	}

	resp.DeductList = make([]*api.DeductItem, 0)
	if billId != "" {
		billDeductList, err := models.GetModelServer().SettlementBillCli.GetDeductMoneyDetailByBillId(ctx, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail GetDeductMoneyDetailByBillId failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		for _, d := range billDeductList {
			resp.DeductList = append(resp.DeductList, &api.DeductItem{
				DeductMoney:    d.DeductMoney,
				Remark:         d.Remark,
				SettlementDate: d.SettlementDate,
			})
		}
	}

	log.DebugWithCtx(ctx, "GetAmuseRoomBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetYuyinBaseBillDetail 获取语音直播间 主播收益列表
func GetYuyinBaseBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetYuyinBaseBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetYuyinBaseBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	billId := req.GetBillId()
	// offset,limit := req.GetOffset(),req.GetLimit()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	resp := &api.GetYuyinBaseBillDetailResp{}
	resp.InfoList = make([]*api.YuyinBaseBillDetailItem, 0)
	resp.DeductList = make([]*api.DeductItem, 0)

	var settleStatus api.SettleStatus
	unit := goldCommission.TimeFilterUnit(req.Unit)
	var beginTime, endTime uint32

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	if billId == "" {
		settleStatus = api.SettleStatus_SettleStatusWait
	} else {
		settleStatus = api.SettleStatus_SettleStatusFinished
		unit = goldCommission.TimeFilterUnit_BY_MONTH
		billRsp, err := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail GetSettlementBill failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		beginTime, endTime = billRsp.SettleStart, billRsp.SettleEnd
		log.InfoWithCtx(ctx, "GetYuyinBaseBillDetail GetSettlementBill. bill: %s rsp: %+v", billId, billRsp)
	}

	log.InfoWithCtx(ctx, "GetYuyinBaseBillDetail req: %+v %d %d", req, beginTime, endTime)
	// 语音直播房 - 拉取时间段统计信息
	getReq := &goldCommission.GetGuildsYuyinAnchorStatReq{
		GuildIds:     guilds,
		SettleStatus: goldCommission.ReqSettleStatus(settleStatus),
		BeginTime:    beginTime,
		EndTime:      endTime,
		Unit:         unit,
		Offset:       req.Offset,
		Limit:        req.Limit,
	}
	getRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsYuyinAnchorStat(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail GetGuildsYuyinAnchorStat [%+v]. err :%+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	uidList := make([]uint32, 0)
	dateMap := make(map[uint32]struct{})
	for _, item := range getRsp.GetAnchorList() {
		if item.GetAnchorId() > 0 {
			uidList = append(uidList, item.AnchorId)
		}
		if _, ok := dateMap[item.Date]; !ok {
			dateMap[item.GetDate()] = struct{}{}
		}
	}

	if len(uidList) == 0 {
		_ = web.ServeAPIJson(w, resp)
		return
	}

	type TS uint32
	type AnchorUID uint32
	//ts -> uid -> income
	dayVirtualLiveIncomeMap := make(map[TS]map[AnchorUID]uint64)
	monthVirtualLiveIncomeMap := make(map[TS]map[AnchorUID]uint64)

	now := time.Now()
	nowTs := now.Unix()
	if billId == "" {
		if unit == goldCommission.TimeFilterUnit_BY_DAY {
			// 当日0点
			statResp, sErr := models.GetModelServer().ChannelLiveStatsClient.BatchGetAnchorDailyRecord(ctx, guildId, 0, uidList, uint32(nowTs), uint32(nowTs))
			if sErr != nil {
				log.ErrorWithCtx(ctx, "YuyinTodayIncomeController BatchGetAnchorDailyRecord err: %s", sErr.Error())
				web.ServeAPIError(w)
				return
			}
			for _, i := range statResp.GetList() {
				if _, ok := dayVirtualLiveIncomeMap[TS(i.GetDate())]; !ok {
					dayVirtualLiveIncomeMap[TS(i.GetDate())] = make(map[AnchorUID]uint64)
				}
				dayVirtualLiveIncomeMap[TS(i.GetDate())][AnchorUID(i.GetUid())] = uint64(i.GetVirtualFee())
			}
		} else if unit == goldCommission.TimeFilterUnit_BY_MONTH {
			// 当月
			statResp, sErr := models.GetModelServer().ChannelLiveStatsClient.BatchGetAnchorMonthlyStats(ctx, uidList, guildId, uint32(nowTs))
			if sErr != nil {
				log.ErrorWithCtx(ctx, "YuyinTodayIncomeController BatchGetAnchorMonthlyStats err: %s", sErr.Error())
				web.ServeAPIError(w)
				return
			}
			// 本月1号时间戳
			monthStartTs := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
			for _, i := range statResp.GetStatsList() {
				if _, ok := monthVirtualLiveIncomeMap[TS(monthStartTs)]; !ok {
					monthVirtualLiveIncomeMap[TS(monthStartTs)] = make(map[AnchorUID]uint64)
				}
				monthVirtualLiveIncomeMap[TS(monthStartTs)][AnchorUID(i.GetUid())] = uint64(i.GetVirtualFee())
			}
		}
	} else {
		// 指定月
		max := 6 // 超过半年的忽略
		for date := range dateMap {
			if max > 6 {
				break
			}
			statResp, sErr := models.GetModelServer().ChannelLiveStatsClient.BatchGetAnchorMonthlyStats(ctx, uidList, guildId, date)
			if sErr != nil {
				log.ErrorWithCtx(ctx, "YuyinTodayIncomeController BatchGetAnchorMonthlyStats err: %s", sErr.Error())
				web.ServeAPIError(w)
				return
			}
			for _, i := range statResp.GetStatsList() {
				if _, ok := monthVirtualLiveIncomeMap[TS(date)]; !ok {
					monthVirtualLiveIncomeMap[TS(date)] = make(map[AnchorUID]uint64)
				}
				monthVirtualLiveIncomeMap[TS(date)][AnchorUID(i.GetUid())] = uint64(i.GetVirtualFee())
			}
			max++
		}
	}

	userInfoMap, err := models.GetModelServer().AccountCli.BatGetUserByUid(ctx, uidList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail AccountClient.BatGetUserByUid failed %v, uid:%d uids:%d", err, uid, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	log.DebugWithCtx(ctx, "GetYuyinBaseBillDetail get user names: %d -> %v", uidList, userInfoMap)

	for _, item := range getRsp.GetAnchorList() {
		if uInfo, ok := userInfoMap[item.AnchorId]; ok {
			shortGuildId := item.GuildId
			guildResp, err := models.GetModelServer().GuildCli.GetGuild(ctx, item.GuildId)
			if err != nil {
				log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail GetGuild err:%v", err)
			} else {
				if guildResp.GetShortId() != 0 {
					shortGuildId = guildResp.GetShortId()
				}
			}

			virtualLiveFlow := uint64(0)
			if unit == goldCommission.TimeFilterUnit_BY_DAY {
				virtualLiveFlow = dayVirtualLiveIncomeMap[TS(item.GetDate())][AnchorUID(item.GetAnchorId())]
			} else if unit == goldCommission.TimeFilterUnit_BY_MONTH {
				virtualLiveFlow = monthVirtualLiveIncomeMap[TS(item.GetDate())][AnchorUID(item.GetAnchorId())]
			}

			resp.InfoList = append(resp.InfoList, &api.YuyinBaseBillDetailItem{
				GuildId:         shortGuildId,
				OriginGuildId:   item.GuildId,
				AnchorUid:       item.AnchorId,
				AnchorName:      uInfo.Nickname,
				Flow:            item.Fee,
				Income:          item.Income,
				GiftFlow:        item.PresentFee,
				KnightFlow:      item.KnightFee,
				Username:        uInfo.Username,
				Alias:           uInfo.Alias,
				Date:            time.Unix(int64(item.Date), 0).Format("2006-01"),
				VirtualLiveFlow: virtualLiveFlow,
			})
		}
	}

	if billId != "" {
		billDeductList, err := models.GetModelServer().SettlementBillCli.GetDeductMoneyDetailByBillId(ctx, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetYuyinBaseBillDetail GetDeductMoneyDetailByBillId failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		for _, d := range billDeductList {
			resp.DeductList = append(resp.DeductList, &api.DeductItem{
				DeductMoney:    d.DeductMoney,
				Remark:         d.Remark,
				SettlementDate: d.SettlementDate,
			})
		}
	}

	log.DebugWithCtx(ctx, "GetYuyinBaseBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetYuyinExtraBillDetail 入口：对公周结积分结算单 / 奖励积分
func GetYuyinExtraBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetYuyinExtraBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinExtraBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetYuyinExtraBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetYuyinExtraBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetYuyinExtraBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	resp := &api.GetYuyinExtraBillDetailResp{}
	nowTime := time.Now()
	var settleStatus api.SettleStatus

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseRoomBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	if req.BillId == "" {
		settleStatus = api.SettleStatus_SettleStatusWait
	} else {
		settleStatus = api.SettleStatus_SettleStatusFinished
	}

	getReq := &goldCommission.GetGuildYuyinExtraIncomeReq{
		Uid:          uid,
		GuildIds:     guilds,
		BeginTime:    time.Date(nowTime.Year(), nowTime.Month()-6, 1, 0, 0, 0, 0, time.Local).Unix(),
		EndTime:      nowTime.Unix(),
		SettleStatus: goldCommission.ReqSettleStatus(settleStatus),
	}
	getRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildYuyinExtraIncome(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildYuyinExtraIncome failed: %+v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	resp.InfoList = make([]*api.YuyinExtraBillDetailItem, 0)
	for _, item := range getRsp.List {
		var month string
		date, err := time.Parse("200601", item.Key)
		if err != nil {
			continue
		}
		month = date.Format("2006-01")
		shortGuildId := item.GuildId
		guildResp, err := models.GetModelServer().GuildCli.GetGuild(ctx, item.GuildId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetYuyinExtraBillDetail GetGuild err:%v", err)
		} else {
			if guildResp.GetShortId() != 0 {
				shortGuildId = guildResp.GetShortId()
			}
		}
		resp.InfoList = append(resp.InfoList, &api.YuyinExtraBillDetailItem{
			GuildId:            shortGuildId,
			OriginGuildId:      item.GuildId,
			MonthRecordIncome:  item.MonthRecordIncome,
			ExtraIncomeRatio:   item.ExtraIncomeRatio,
			Key:                month,
			ExtraIncomeRatioV2: item.ExtraIncomeRatioV2,
		})
	}

	log.DebugWithCtx(ctx, "GetYuyinExtraBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetDeepCoopBillDetail 多人互动深度合作结算单明细
func GetDeepCoopBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetDeepCoopBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeepCoopBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetDeepCoopBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetDeepCoopBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetDeepCoopBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	resp := &api.GetDeepCoopBillDetailResp{
		Summary: &api.DeepCoopMonthInfo{},
	}
	getReq := &settleBillPb.GetExtraIncomeDetailReq{
		Uid:    uid,
		Offset: req.GetOffset(),
		Limit:  req.GetLimit(),
		// IncomeType : settleBillPb.ExtraIncomeType_DeepCooperation,
		SettlementDate: req.GetYearMonth(),
	}
	getRsp, err := models.GetModelServer().SettlementBillCli.GetExtraIncomeDetailDeepCoop(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetDeepCoopBillDetail failed: req: %+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if getRsp != nil && getRsp.DeepCooperation != nil {
		info := getRsp.DeepCooperation
		resp.Summary.YearMonth = req.GetYearMonth()
		resp.Summary.TotalFlowThisMonth = info.TotalFlowThisMonth
		resp.Summary.TotalFlowLastMonth = info.TotalFlowLastMonth
		resp.Summary.GrowthRate = info.GrowRate
		resp.Summary.SettleMoney = info.SettlementMoney
		resp.Summary.PrepaidMoney = info.PrepaidMoney
		resp.Summary.Remarks = info.Remark
		for _, item := range info.Channels {
			resp.InfoList = append(resp.InfoList, &api.DeepCoopRoomInfo{
				Name:          item.ChannelName,
				Type:          item.ChannelType,
				GuildId:       item.GuildDisplayId,
				GuildName:     item.GuildName,
				FlowThisMonth: item.FlowThisMonth,
				FlowLastMonth: item.FlowLastMonth,
				GrowthRate:    item.GrowRate,
				SettleRate:    item.SettlementRate,
				SettleMoney:   item.SettlementMoney,
			})
		}
	}

	log.DebugWithCtx(ctx, "GetDeepCoopBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetYuyinAnchorSubsidyDetail 语音直播补贴结算单明细 - 列表部分
func GetYuyinAnchorSubsidyDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetYuyinAnchorSubsidyDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinAnchorSubsidyDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetYuyinAnchorSubsidyDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetYuyinAnchorSubsidyDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetYuyinAnchorSubsidyDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	resp := &api.GetYuyinAnchorSubsidyDetailResp{
		InfoList: []*api.YuyinAnchorSubsidyInfo{},
	}
	getReq := &settleBillPb.GetExtraIncomeDetailReq{
		Uid:            uid,
		SettlementDate: req.GetYearMonth(),
	}
	getRsp, err := models.GetModelServer().SettlementBillCli.GetExtraIncomeDetailChannelSubsidy(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetYuyinAnchorSubsidyDetail failed: req: %+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if getRsp != nil {
		info := getRsp.YuyinAnchorSubsidy
		resp.Subsidy = info.GetSubsidySum()
		if info != nil {
			for _, item := range info.Anchors {
				resp.InfoList = append(resp.InfoList, &api.YuyinAnchorSubsidyInfo{
					Ttid:          item.Ttid,
					Nickname:      item.Nickname,
					Tag:           item.Tag,
					AnchorFlag:    item.AnchorFlag,
					GuildName:     item.GuildName,
					PlanDate:      item.PlanDate,
					GiftFlow:      item.GiftFlow,
					ValidLiveDays: item.ValidLiveDays,
					SubsidyMoney:  item.SubsidyMoney,
				})
			}
		}
	}

	log.DebugWithCtx(ctx, "GetYuyinAnchorSubsidyDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetNewGuildSubsidyDetail 语音直播补贴结算单明细 - 汇总部分
func GetNewGuildSubsidyDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetNewGuildSubsidyDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewGuildSubsidyDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetNewGuildSubsidyDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetNewGuildSubsidyDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetNewGuildSubsidyDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	resp := &api.GetNewGuildSubsidyDetailResp{}
	getReq := &settleBillPb.GetExtraIncomeDetailReq{
		Uid:            uid,
		SettlementDate: req.GetYearMonth(),
	}
	getRsp, err := models.GetModelServer().SettlementBillCli.GetExtraIncomeDetailNewGuildSubsidy(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNewGuildSubsidyDetail failed: req: %+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if getRsp != nil && getRsp.YuyinNewGuildSubsidy != nil {
		info := getRsp.YuyinNewGuildSubsidy
		resp.JoinDate = info.JoinTime
		resp.FlowThisMonth = info.FlowThisMonth
		resp.SubsidyTime = info.SubsidyDate
		resp.SubsidyMoney = info.SubsidyMoney
	}
	log.DebugWithCtx(ctx, "GetNewGuildSubsidyDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetAmuseExtraBillDetail 多人互动额外奖励详情
func GetAmuseExtraBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetAmuseExtraBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetAmuseExtraBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	extraRsp, err := models.GetModelServer().GoldCommissionCli.GetAmuseExtraDetail(ctx, req.GetYearMonth(), guilds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail failed: req: %+v err: %+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	var settleStart, settleEnd uint32
	if req.GetBillId() != "" {
		billRsp, err := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, req.GetBillId())
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail GetSettlementBill err: %s, req: %+v", err, req)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		settleStart = billRsp.SettleStart
		settleEnd = billRsp.SettleEnd
	}

	channelIds := make([]uint32, 0)
	for _, c := range extraRsp.GetChannelList() {
		channelIds = append(channelIds, c.ChannelId)
	}

	channelsMap, err := models.GetModelServer().ChannelCli.BatchGetChannelSimpleInfo(ctx, uid, channelIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail BatchGetChannelSimpleInfo err: %s, req: %+v", err, req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	guildsResp, err := models.GetModelServer().GuildCli.GetGuildBat(ctx, uid, &guildPb.GetGuildBatReq{GuildIdList: guilds})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAmuseExtraBillDetail GetGuildBat err: %s, req: %+v", err, req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	guildsMap := make(map[uint32]*guildPb.GuildResp)
	for _, g := range guildsResp.GetGuildList() {
		guildsMap[g.GuildId] = g
	}

	respChannelList := make([]*api.GetAmuseExtraBillDetailResp_ChannelItem, 0)
	for _, c := range extraRsp.GetChannelList() {
		respChannel := &api.GetAmuseExtraBillDetailResp_ChannelItem{
			ChannelId:       c.ChannelId,
			ChannelTag:      c.ChannelTag,
			ThisMonthFee:    c.ThisMonthFee,
			SettlementRate:  c.SettlementRate,
			ThisMonthIncome: c.ThisMonthIncome,
			GuildId:         c.GuildId,
			LastMonthFee:    c.LastMonthFee,
			GrowRate:        c.GrowRate,
		}
		if v, ok := guildsMap[c.GuildId]; ok {
			displayId := v.GetGuildId()
			if v.GetShortId() > 0 {
				displayId = v.GetShortId()
			}
			respChannel.GuildName = v.GetName()
			respChannel.GuildDisplayId = displayId
		}
		if v, ok := channelsMap[c.ChannelId]; ok {
			displayId := v.GetChannelId()
			if v.GetDisplayId() > 0 {
				displayId = v.GetDisplayId()
			}
			respChannel.ChannelName = v.GetName()
			respChannel.ChannelDisplayId = displayId
			respChannel.ChannelViewId = v.GetChannelViewId()
		}
		respChannelList = append(respChannelList, respChannel)
	}

	resp := &api.GetAmuseExtraBillDetailResp{
		YearMonth:          extraRsp.GetYearMonth(),
		ThisMonthFee:       extraRsp.GetThisMonthFee(),
		LastMonthFee:       extraRsp.GetLastMonthFee(),
		ThisMonthIncome:    extraRsp.GetThisMonthIncome(),
		ThisMonthIncomeCny: extraRsp.GetThisMonthIncomeCny(),
		PrepaidMoney:       extraRsp.GetPrepaidMoney(),
		PrepaidMoneyCny:    extraRsp.GetPrepaidMoneyCny(),
		Remark:             extraRsp.GetRemark(),
		GrowRate:           extraRsp.GetGrowRate(),
		ChannelList:        respChannelList,
		SettleStart:        settleStart,
		SettleEnd:          settleEnd,
	}

	log.DebugWithCtx(ctx, "GetAmuseExtraBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetInteractGameBillDetail 互动游戏详情
func GetInteractGameBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetInteractGameBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetInteractGameBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	billId := req.GetBillId()
	// offset,limit := req.GetOffset(),req.GetLimit()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	resp := &api.GetInteractGameBillDetailResp{}
	resp.InfoList = make([]*api.InteractGameBillDetailItem, 0)

	var settleStatus api.SettleStatus
	unit := goldCommission.TimeFilterUnit(req.Unit)
	var beginTime, endTime uint32

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	if billId == "" {
		settleStatus = api.SettleStatus_SettleStatusWait
	} else {
		settleStatus = api.SettleStatus_SettleStatusFinished
		unit = goldCommission.TimeFilterUnit_BY_MONTH
		billRsp, err := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetInteractGameBillDetail GetSettlementBill failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		beginTime, endTime = billRsp.SettleStart, billRsp.SettleEnd
		log.InfoWithCtx(ctx, "GetInteractGameBillDetail GetSettlementBill. bill: %s rsp: %+v", billId, billRsp)
	}

	log.InfoWithCtx(ctx, "GetInteractGameBillDetail req: %+v %d %d", req, beginTime, endTime)
	// 语音直播房 - 拉取时间段统计信息
	getReq := &goldCommission.GetGuildsInteractGameAnchorStatReq{
		GuildIds:     guilds,
		SettleStatus: goldCommission.ReqSettleStatus(settleStatus),
		BeginTime:    beginTime,
		EndTime:      endTime,
		Unit:         unit,
		Offset:       req.Offset,
		Limit:        req.Limit,
	}
	getRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsInteractGameAnchorStat(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail GetGuildsInteractGameAnchorStat [%+v]. err :%+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	if len(getRsp.GetAnchorList()) == 0 {
		_ = web.ServeAPIJson(w, resp)
		return
	}

	uidList := make([]uint32, 0)
	for _, item := range getRsp.GetAnchorList() {
		uidList = append(uidList, item.AnchorId)
	}
	userInfoMap, err := models.GetModelServer().AccountCli.BatGetUserByUid(ctx, uidList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail AccountClient.BatGetUserByUid failed %v, uid:%d uids:%d", err, uid, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	log.DebugWithCtx(ctx, "GetInteractGameBillDetail get user names: %d -> %v", uidList, userInfoMap)

	guildsResp, err := models.GetModelServer().GuildCli.GetGuildBat(ctx, uid, &guildPb.GetGuildBatReq{GuildIdList: guilds})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameBillDetail GetGuildBat err: %s, req: %+v", err, req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	guildsMap := make(map[uint32]*guildPb.GuildResp)
	for _, g := range guildsResp.GetGuildList() {
		guildsMap[g.GuildId] = g
	}

	for _, item := range getRsp.GetAnchorList() {
		if uInfo, ok := userInfoMap[item.AnchorId]; ok {
			guildInfo := guildsMap[item.GuildId]
			shortGuildId := item.GuildId
			if guildInfo.GetShortId() > 0 {
				shortGuildId = guildInfo.GetShortId()
			}
			resp.InfoList = append(resp.InfoList, &api.InteractGameBillDetailItem{
				GuildId:       shortGuildId,
				OriginGuildId: item.GuildId,
				AnchorUid:     item.AnchorId,
				AnchorName:    uInfo.Nickname,
				Flow:          item.Fee,
				Income:        item.Income,
				GiftFlow:      item.PresentFee,
				Username:      uInfo.Username,
				Alias:         uInfo.Alias,
				Date:          time.Unix(int64(item.Date), 0).Format("2006-01"),
			})
		}
	}

	log.DebugWithCtx(ctx, "GetInteractGameBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

func GetInteractGameExtraBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetInteractGameExtraBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.InfoWithCtx(ctx, "GetInteractGameExtraBillDetail begin uid=%d %+v", authInfo.UserID, req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	billId := req.GetBillId()
	// offset,limit := req.GetOffset(),req.GetLimit()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	resp := &api.GetInteractGameExtraBillDetailResp{}

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	monthTime := uint32(time.Now().Unix())
	if billId == "" {
		//settleStatus = api.SettleStatus_SettleStatusWait
	} else {
		//settleStatus = api.SettleStatus_SettleStatusFinished
		billRsp, err := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail GetSettlementBill failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		monthTime = billRsp.SettleStart
		log.InfoWithCtx(ctx, "GetInteractGameExtraBillDetail GetSettlementBill. bill: %s rsp: %+v", billId, billRsp)
	}

	log.InfoWithCtx(ctx, "GetInteractGameExtraBillDetail req: %+v monthTime=%d", req, monthTime)

	getReq := &goldCommission.GetInteractGameExtraIncomeReq{
		GuildIds:  guilds,
		MonthTime: monthTime,
	}
	getRsp, err := models.GetModelServer().GoldCommissionCli.GetInteractGameExtraIncome(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail GetGuildsInteractGameAnchorStat [%+v]. err :%+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	log.InfoWithCtx(ctx, "GetInteractGameExtraBillDetail uid=%d guilds=%v GetInteractGameExtraIncome=%s", uid, guilds, getRsp.String())

	if len(getRsp.GetInfo().GetAnchorInfoList()) == 0 {
		_ = web.ServeAPIJson(w, resp)
		return
	}

	uidList := make([]uint32, 0)
	for _, item := range getRsp.GetInfo().GetAnchorInfoList() {
		uidList = append(uidList, item.GetAnchorUid())
	}
	userInfoMap, err := models.GetModelServer().AccountCli.BatGetUserByUid(ctx, uidList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetInteractGameExtraBillDetail AccountClient.BatGetUserByUid failed %v, uid:%d uids:%d", err, uid, uidList)
		_ = web.ServeAPICodeJson(w, -500, "系统错误", nil)
		return
	}
	log.DebugWithCtx(ctx, "GetInteractGameExtraBillDetail get user names: %d -> %v", uidList, userInfoMap)

	resp.GameMonthTotalFee = getRsp.GetInfo().GetGameMonthTotalFee()
	resp.GameMonthExtraIncome = getRsp.GetInfo().GetGameMonthExtraIncome()
	for _, item := range getRsp.GetInfo().GetAnchorInfoList() {
		userInfo := userInfoMap[item.AnchorUid]
		resp.InfoList = append(resp.InfoList, &api.InteractGameExtraBillDetail{
			AnchorUid:      item.AnchorUid,
			AnchorTtid:     userInfo.GetAlias(),
			AnchorAccount:  userInfo.GetUsername(),
			AnchorNickname: userInfo.GetNickname(),
			AnchorSex:      uint32(userInfo.GetSex()),
			GameMonthFee:   item.GameMonthFee,
			GameIncomeRate: item.GameIncomeRate,
			GameIncome:     item.GameIncome,
		})
	}

	log.DebugWithCtx(ctx, "GetInteractGameExtraBillDetail end uid=%d req:%+v resp:%s", uid, req, resp.String())
	_ = web.ServeAPIJson(w, resp)
}

// GetESportBillDetail 电竞会长佣金详情
func GetESportBillDetail(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetEsportBillDetailReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	log.DebugWithCtx(ctx, "GetESportBillDetail begin %s %+v", string(authInfo.Body), req)

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	billId := req.GetBillId()
	// offset,limit := req.GetOffset(),req.GetLimit()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetESportBillDetail failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	resp := &api.GetEsportBillDetailResp{}
	resp.InfoList = make([]*api.EsportBillDetailItem, 0)

	var settleStatus api.SettleStatus
	unit := goldCommission.TimeFilterUnit(req.Unit)
	var beginTime, endTime uint32

	var guilds []uint32
	guilds, err = GetUidGuilds(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail GetUidGuilds err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	if len(guilds) == 0 {
		guilds = []uint32{guildId}
	}

	if billId == "" {
		settleStatus = api.SettleStatus_SettleStatusWait
	} else {
		settleStatus = api.SettleStatus_SettleStatusFinished
		unit = goldCommission.TimeFilterUnit_BY_MONTH
		billRsp, err := models.GetModelServer().SettlementBillCli.GetSettlementBill(ctx, uid, billId)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetESportBillDetail GetSettlementBill failed: billId: %s err: %+v", billId, err)
			_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
			return
		}
		beginTime, endTime = billRsp.SettleStart, billRsp.SettleEnd
		log.InfoWithCtx(ctx, "GetESportBillDetail GetSettlementBill. bill: %s rsp: %+v", billId, billRsp)
	}

	log.InfoWithCtx(ctx, "GetESportBillDetail req: %+v %d %d", req, beginTime, endTime)
	// 语音直播房 - 拉取时间段统计信息
	getReq := &goldCommission.GetGuildsESportCoachStatReq{
		GuildIds:     guilds,
		SettleStatus: goldCommission.ReqSettleStatus(settleStatus),
		BeginTime:    beginTime,
		EndTime:      endTime,
		Unit:         unit,
		Offset:       req.Offset,
		Limit:        req.Limit,
	}
	getRsp, err := models.GetModelServer().GoldCommissionCli.GetGuildsESportCoachStat(ctx, getReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail GetGuildsESportCoachStat [%+v]. err :%+v", req, err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	if len(getRsp.GetCoachList()) == 0 {
		_ = web.ServeAPIJson(w, resp)
		return
	}

	uidList := make([]uint32, 0)
	for _, item := range getRsp.GetCoachList() {
		uidList = append(uidList, item.AnchorId)
	}
	userInfoMap, err := models.GetModelServer().AccountCli.BatGetUserByUid(ctx, uidList...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail AccountClient.BatGetUserByUid failed %v, uid:%d uids:%d", err, uid, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	log.DebugWithCtx(ctx, "GetESportBillDetail get user names: %d -> %v", uidList, userInfoMap)

	guildsResp, err := models.GetModelServer().GuildCli.GetGuildBat(ctx, uid, &guildPb.GetGuildBatReq{GuildIdList: guilds})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetESportBillDetail GetGuildBat err: %s, req: %+v", err, req)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}
	guildsMap := make(map[uint32]*guildPb.GuildResp)
	for _, g := range guildsResp.GetGuildList() {
		guildsMap[g.GuildId] = g
	}

	for _, item := range getRsp.GetCoachList() {
		if uInfo, ok := userInfoMap[item.AnchorId]; ok {
			guildInfo := guildsMap[item.GuildId]
			shortGuildId := item.GuildId
			if guildInfo.GetShortId() > 0 {
				shortGuildId = guildInfo.GetShortId()
			}
			dateStr := ""
			if unit == goldCommission.TimeFilterUnit_BY_MONTH {
				dateStr = time.Unix(int64(item.Date), 0).Format("2006-01")
			} else {
				dateStr = time.Unix(int64(item.Date), 0).Format("2006-01-02")
			}
			resp.InfoList = append(resp.InfoList, &api.EsportBillDetailItem{
				GuildId:       shortGuildId,
				OriginGuildId: item.GuildId,
				AnchorUid:     item.AnchorId,
				AnchorName:    uInfo.Nickname,
				Flow:          item.Fee,
				Income:        item.Income,
				EsportFlow:    item.Fee,
				EsportIncome:  item.Income,
				Username:      uInfo.Username,
				Alias:         uInfo.Alias,
				Date:          dateStr,
			})
		}
	}

	log.DebugWithCtx(ctx, "GetESportBillDetail end req:%v resp:%v", req, resp)
	_ = web.ServeAPIJson(w, resp)
}

// GetFreezeScoreList 冻结积分列表
func GetFreezeScoreList(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*10)
	defer cancel()

	req := &api.GetFreezeScoreListReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFreezeScoreList Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}

	uid := authInfo.UserID
	guildId := req.GetGuildId()
	isChairman, serr := CheckIsLibraryGuildChairman(ctx, uid, guildId)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetFreezeScoreList failed to CheckIsLibraryGuildChairman [%+v], err %+v", req, serr)
		_ = web.ServeAPICodeJson(w, int32(serr.Code()), serr.Message(), nil)
		return
	}

	if !isChairman {
		log.ErrorWithCtx(ctx, "GetFreezeScoreList failed to CheckGuildChairman [%+v]. User is not guild chairman.", req)
		_ = web.ServeAPICodeJson(w, -505, "账号没有权限", nil)
		return
	}

	log.DebugWithCtx(ctx, "GetFreezeScoreList begin uid:%d %+v", uid, req)

	if req.Limit == 0 || req.Limit > 100 {
		req.Limit = 100
	}

	resp := &api.GetFreezeScoreListResp{}
	resp.InfoList = make([]*api.FreezeScoreItem, 0)

	// 目前仅支持礼物积分
	if req.GetBillType() != uint32(settleBillPb.SettlementBillType_GiftScore) {
		_ = web.ServeAPIJson(w, resp)
		return
	}

	var scoreType exchangePb.ExchangeType
	switch settleBillPb.SettlementBillType(req.BillType) {
	case settleBillPb.SettlementBillType_GiftScore:
		scoreType = exchangePb.ExchangeType_PRESENT
	default:
		log.ErrorWithCtx(ctx, "GetScoreBillDetail type err:%s", req.BillType)
		_ = web.ServeAPICodeJson(w, -505, "类型错误", nil)
		return
	}

	exchangeResp, err := models.GetModelServer().ExchangeGuildClientCli.GetMasterFreezeUser(ctx, uid, scoreType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFreezeScoreList GetMasterFreezeUser err:%v", err)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	uidList := make([]uint32, 0)
	for _, item := range exchangeResp.GetList() {
		uidList = append(uidList, item.GetUid())
	}

	userInfoMap, err := batGetUser(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFreezeScoreList AccountClient.BatGetUserByUid failed %v, uid:%d uids:%d", err, uid, uidList)
		_ = web.ServeAPICodeJson(w, -500, err.Error(), nil)
		return
	}

	for _, item := range exchangeResp.GetList() {
		resp.InfoList = append(resp.InfoList, &api.FreezeScoreItem{
			AnchorName:   userInfoMap[item.GetUid()].GetNickname(),
			AnchorUid:    item.GetUid(),
			FreezeScore:  uint64(item.GetFreezeScore()),
			Alias:        userInfoMap[item.GetUid()].GetAlias(),
			Username:     userInfoMap[item.GetUid()].GetUsername(),
			GuildId:      item.GetGuildId(),
			FreezeStatus: item.GetFreezeStatus(),
			FreezeTime:   item.GetFreezeTime(),
			UnfreezeTime: item.GetUnfreezeTime(),
		})
	}

	log.DebugWithCtx(ctx, "GetFreezeScoreList end uid:%d guildId:%d type:%d len:%d", uid, guildId, req.BillType, len(resp.InfoList))
	_ = web.ServeAPIJson(w, resp)
}
