package manager

import (
	"context"
	"database/sql"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	userPresent "golang.52tt.com/clients/userpresent"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/levelup-present"
	"golang.52tt.com/services/levelup-present/cache"
	"golang.52tt.com/services/levelup-present/model"
	"golang.52tt.com/services/levelup-present/tools"
	"google.golang.org/grpc"
	"strconv"
	"time"
)
const PUSH_PERSON_RANK = 50

type LevelupPresentMgr struct {
	db         *sql.DB
	store      model.IStore
	rc         *redis.Client
	cache      cache.IRedisCache
	presentCli *userPresent.Client
	processTimer *timer.Timer
}

func NewLevelupPresentMgr(cfg config.Configer) (ILevelupPresentMgr, error) {
	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, "mysql")

	mysqlAddr := mysqlConfig.Description()
	log.Debugf("Initializing mysql connection pool to %s", mysqlAddr)

	db, err := sql.Open("mysql", mysqlAddr)
	if err != nil {
		log.Fatalln(err)
		return nil, err
	}
	db.SetMaxIdleConns(mysqlConfig.MaxIdleConns)
	db.SetMaxOpenConns(mysqlConfig.MaxOpenConns)

	mysqlPresentConfig := new(config.MysqlConfig)
	mysqlPresentConfig.Read(cfg, "mysql_present")
	mysqlPresentAddr := mysqlPresentConfig.Description()
	log.Debugf("Initializing mysql connection pool to %s", mysqlPresentAddr)

	dbPresent, err := sql.Open("mysql", mysqlPresentAddr)
	if err != nil {
		log.Fatalln(err)
		return nil, err
	}
	dbPresent.SetMaxIdleConns(mysqlPresentConfig.MaxIdleConns)
	dbPresent.SetMaxOpenConns(mysqlPresentConfig.MaxOpenConns)

	go func() {
		ticker := time.NewTicker(time.Minute)
		for {
			for range ticker.C {
				log.Debugln("PingContext")
				db.PingContext(context.Background())
				dbPresent.PingContext(context.Background())
			}
		}
	}()


	redisConfig := new(config.RedisConfig)
	redisConfig.Read(cfg, "redis")

	redisClient := redis.NewClient(&redis.Options{
		Addr: redisConfig.Addr(),
		PoolSize: redisConfig.PoolSize,
	})

	store := model.NewStore(db)
	storePresent := model.NewStore(dbPresent)

	err = store.Init()
	if err != nil {
		return nil, err
	}

	redisCache := cache.NewCache(redisClient)

	presentCli := userPresent.NewClient(grpc.WithBlock())

	pTimer, err := timer.NewTimerD(context.Background(), "levelup-present")
	if err != nil {
		return nil, err
	}
	pTimer.Start()

	err = pTimer.AddTask("0 0 3 * * *", "SendFeishu", timer.BuildFromLambda(func(ctx context.Context) {
		err = tools.SendFeishu(store, presentCli, storePresent)
	}))
	if err != nil {
		log.Errorf("AddFunc err=%v", err)
		return nil, err
	}

	//c := cron.New()
	//err = c.AddFunc("0 0 3 * * *", func() {
	//	small := grpclb.GetEtcdProcess().IsSmallProcess()
	//	if !small {
	//		log.Debugln("crontab not small")
	//		return
	//	}
	//	log.Debugln("crontab is small")
	//	err = tools.SendFeishu(store, presentCli, storePresent)
	//	log.Errorln(err)
	//})
	//if err != nil {
	//	log.Errorln(err)
	//}
	//c.Start()

	return &LevelupPresentMgr{
		db: db,
		rc: redisClient,
		store: store,
		cache: redisCache,
		presentCli: presentCli,
		processTimer: pTimer,
	}, nil
}

func (mgr *LevelupPresentMgr) Init() {
	go mgr.autoSync()
}

func (mgr *LevelupPresentMgr) ShutDown() {
	mgr.db.Close()
	mgr.rc.Close()
}

func (mgr *LevelupPresentMgr) AddLevelupParentPresent(ctx context.Context, data *pb.LevelupParentPresentData) error {
	err := mgr.store.InsertLevelupParentPresent(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertLevelupParentPresent err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresent()
}

func (mgr *LevelupPresentMgr) UpdateLevelupParentPresent(ctx context.Context, data *pb.LevelupParentPresentData) error {
	err := mgr.store.UpdateLevelupParentPresent(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLevelupParentPresent err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresent()
}

func (mgr *LevelupPresentMgr) DeleteLevelupParentPresent(ctx context.Context, itemId uint32) error {
	err := mgr.store.DeleteLevelupParentPresent(ctx, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteLevelupParentPresent err=%+v", err)
		return err
	}
	err = mgr.store.DeleteLevelupPresentBatch(ctx, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteLevelupPresentBatch err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresent()
}

func (mgr *LevelupPresentMgr) AddLevelupChildPresent(ctx context.Context, data *pb.LevelupChildPresentData) error {
	err := mgr.store.InsertLevelupChildPresent(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertLevelupChildPresent err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresent()
}

func (mgr *LevelupPresentMgr) UpdateLevelupChildPresent(ctx context.Context, data *pb.LevelupChildPresentData) error {
	err := mgr.store.UpdateLevelupChildPresent(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLevelupChildPresent err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresent()
}

func (mgr *LevelupPresentMgr) DeleteLevelupChildPresent(ctx context.Context, itemId uint32) error {
	err := mgr.store.DeleteLevelupChildPresent(ctx, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteLevelupChildPresent err=%+v", err)
		return err
	}
	err = mgr.store.DeleteLevelupPresentBatch(ctx, itemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteLevelupPresentBatch err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresent()
}

func (mgr *LevelupPresentMgr) GetAllLevelupParentPresent(ctx context.Context) (*pb.LevelupPresentParentMap, error) {
	res := &pb.LevelupPresentParentMap{}
	res.Map = make(map[uint32]*pb.LevelupParentPresentAllData)
	parentMap, err := mgr.cache.GetAllLevelupParentPresent()
	if err == redis.Nil {
		return res, nil
	}
	if err != nil {
		return res, err
	}
	for k, v := range parentMap {
		ki, _ := strconv.Atoi(k)
		data := &pb.LevelupParentPresentAllData{}
		err = proto.Unmarshal([]byte(v), data)
		if err != nil {
			return res, err
		}
		res.Map[uint32(ki)] = data
	}
	return res, nil
}

func (mgr *LevelupPresentMgr) GetLevelupParentPresentById(ctx context.Context, itemId uint32) uint32 {
	strItemId := strconv.Itoa(int(itemId))
	exist, err := mgr.cache.ExistParentId(strItemId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ExistParentId err=%+v", err)
		return 0
	}
	if exist {
		return itemId
	}

	strParentId, _ := mgr.cache.GetLevelupChildPresentById(strItemId)
	parentItemId, _ := strconv.Atoi(strParentId)
	return uint32(parentItemId)
}

func (mgr *LevelupPresentMgr) AddLevelupBatch(ctx context.Context, data *pb.LevelupPresentBatchData) error {
	err := mgr.store.InsertLevelupBatch(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertLevelupBatch err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresentBatch()
}

func (mgr *LevelupPresentMgr) UpdateLevelupBatch(ctx context.Context, data *pb.LevelupPresentBatchData) error {
	err := mgr.store.UpdateLevelupBatch(ctx, data)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLevelupBatch err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresentBatch()
}

func (mgr *LevelupPresentMgr) DeleteLevelupBatch(ctx context.Context, itemId uint32, batchCount uint32) error {
	err := mgr.store.DeleteLevelupBatch(ctx, itemId, batchCount)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateLevelupBatch err=%+v", err)
		return err
	}
	return mgr.syncLevelupPresentBatch()
}

func (mgr *LevelupPresentMgr) GetLevelupBatchById(ctx context.Context, itemId uint32) (*pb.LevelupPresentBatchList, error) {
	res := &pb.LevelupPresentBatchList{}
	strItemId := strconv.Itoa(int(itemId))
	data, err := mgr.cache.GetLevelupPresentBatchById(strItemId)
	if err == redis.Nil {
		return res, nil
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetLevelupPresentBatchById err=%+v", err)
		return res, err
	}
	err = proto.Unmarshal([]byte(data), res)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Unmarshal err=%+v", err)
		return res, err
	}
	return res, err
}

func (mgr *LevelupPresentMgr) GetAllLevelupBatch(ctx context.Context) (*pb.LevelupPresentBatchDataMap, error) {
	res := &pb.LevelupPresentBatchDataMap{}
	res.BatchMap = make(map[uint32]*pb.LevelupPresentBatchList)
	allMap, err := mgr.cache.GetAllLevelupPresentBatch()
	if err == redis.Nil {
		return res, nil
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllLevelupPresentBatch err=%+v", err)
		return res, err
	}
	for k, v := range allMap {
		ki, _ := strconv.Atoi(k)
		list := &pb.LevelupPresentBatchList{}
		err = proto.Unmarshal([]byte(v), list)
		if err != nil {
			log.ErrorWithCtx(ctx, "proto.Unmarshal err=%+v", err)
			return res, err
		}
		res.BatchMap[uint32(ki)] = list
	}
	return res, nil
}

func (mgr *LevelupPresentMgr) GetLevelupPresentVersionList(ctx context.Context, itemId uint32) (*pb.VersionList, error) {
	return mgr.store.GetLevelupPresentVersionList(ctx, itemId)
}

func (mgr *LevelupPresentMgr) AddLevelupPresentVersion(ctx context.Context, itemId uint32, version uint32) error {
	return mgr.store.AddLevelupPresentVersion(ctx, itemId, version)
}

func (mgr *LevelupPresentMgr) AddUserLevelupPresentExp(ctx context.Context, uid uint32, itemId uint32, version uint32, itemCount uint32, orderId string) (outLevel uint32, outExp uint32, err error) {
	parentItemId := mgr.GetLevelupParentPresentById(ctx, itemId)
	err = mgr.cache.DelUserItemVersionData(uid, parentItemId, version)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUserLevelupPresentExp err=%+v", err)
		return
	}
	outLevel, outExp, err = mgr.store.AddLevelupPresentUserExp(ctx, uid, parentItemId, itemId, itemCount, version, itemCount, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddLevelupPresentUserExp err=%+v", err)
		return
	}
	err = mgr.cache.DelUserItemVersionData(uid, parentItemId, version)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelUserItemVersionData err=%+v", err)
		return
	}
	return
}

func (mgr *LevelupPresentMgr) getUserLevelupPresentStatusWithParentId(ctx context.Context, uid uint32, parentItemId uint32, version uint32) (*pb.UserLevelExp, error) {
	res := &pb.UserLevelExp{}
	versionData := mgr.cache.GetUserItemVersionData(uid, parentItemId, version)
	if len(versionData) == 0 {
		level, exp, err := mgr.store.GetUserLevelupExp(ctx, uid, parentItemId, version)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetUserLevelupExp err=%+v", err)
			return res, err
		}
		if level == 0 {
			level = 1
		}
		res.Level = level
		res.Exp = exp
		marshal, err := proto.Marshal(res)
		if err != nil {
			log.ErrorWithCtx(ctx, "proto.Marshal err=%+v", err)
			return res, err
		}
		err = mgr.cache.SetUserItemVersionData(uid, parentItemId, version, marshal)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetUserItemVersionData err=%+v", err)
			return res, err
		}
		return res, nil
	} else {
		err := proto.Unmarshal([]byte(versionData), res)
		if err != nil {
			log.ErrorWithCtx(ctx, "proto.Unmarshal err=%+v", err)
			return res, err
		}
		if res.Level == 0 {
			res.Level = 1
		}
		return res, nil
	}
}

func (mgr *LevelupPresentMgr) GetUserAllLevelupPresentStatus(ctx context.Context, uid uint32) (map[uint32]*pb.UserLevelExp, error) {
	res := make(map[uint32]*pb.UserLevelExp)
	present, err := mgr.cache.GetAllLevelupParentPresent()
	if err == redis.Nil {
		return res, nil
	}
	if err != nil {
		return res, err
	}

	for strParentItemId, v := range present {
		data := &pb.LevelupParentPresentAllData{}
		err = proto.Unmarshal([]byte(v), data)
		if err != nil {
			return res, err
		}
		tempItemId, _ := strconv.Atoi(strParentItemId)
		parentItemId := uint32(tempItemId)
		userLevelExp, err := mgr.getUserLevelupPresentStatusWithParentId(ctx, uid, parentItemId, data.CurrentVersion)
		if err != nil {
			return res, err
		}
		if userLevelExp.Level == 0 {
			userLevelExp.Level = 1
		}
		res[parentItemId] = userLevelExp
	}

	return res, nil
}

func (mgr *LevelupPresentMgr) GetLevelupParentPresent(ctx context.Context, offset uint32, limit uint32, presentType int32) (*pb.LevelupParentPresentList, error) {
	res := &pb.LevelupParentPresentList{}
	list, err := mgr.store.GetLevelupParentPresent(ctx, offset, limit, presentType)
	if err != nil {
		return res, err
	}
	res.List = list
	count, err := mgr.store.GetLevelupParentPresentCount(ctx, presentType)
	if err != nil {
		return res, err
	}
	res.Total = count
	return res, nil
}

func (mgr *LevelupPresentMgr) GetLevelupChildrenPresent(ctx context.Context, parentItemId uint32) (*pb.LevelupChildPresentList, error) {
	res := &pb.LevelupChildPresentList{}
	list, err := mgr.store.GetLevelupChildrenPresent(ctx, parentItemId)
	if err != nil {
		return res, err
	}
	res.List = list
	return res, nil
}

func (mgr *LevelupPresentMgr) GetLevelupParentPresentData(ctx context.Context, parentItemId uint32) (*pb.LevelupParentPresentAllData, error) {
	res := &pb.LevelupParentPresentAllData{}
	strParentItemId := strconv.Itoa(int(parentItemId))
	data, _ := mgr.cache.GetLevelupParentPresent(strParentItemId)
	err := proto.Unmarshal([]byte(data), res)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Unmarshal err=%+v", err)
		return res, err
	}
	return res, nil
}

func (mgr *LevelupPresentMgr) GetLevelupChildPresentData(ctx context.Context, itemId uint32) (*pb.LevelupChildPresentData, error) {
	return mgr.store.GetLevelupChildPresent(ctx, itemId)
}

func (mgr *LevelupPresentMgr) AddLevelupPerson(ctx context.Context, itemId uint32, level uint32, version uint32, uid uint32) (rank int64, push bool, err error) {
	push = false
	rank, err = mgr.cache.IncrSend(itemId, version, level)
	if err != nil {
		return
	}
	if rank > 0 && rank <= PUSH_PERSON_RANK {
		maxRank := uint32(0)
		maxRank, err = mgr.store.GetMaxRank(ctx, itemId, level, version)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetMaxRank err=%+v", err)
			return
		}

		//防止缓存丢失
		if maxRank >= uint32(rank) {
			rank = int64(maxRank + 1)
			err = mgr.cache.SetRank(itemId, version, level, uint32(rank))
			if err != nil {
				log.ErrorWithCtx(ctx, "SetRank err=%+v", err)
			}
		}

		if maxRank < PUSH_PERSON_RANK {
			push = true
		}
	}
	err = mgr.store.InsertLevelupPresentSendRank(ctx, itemId, level, version, uint32(rank), uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertLevelupPresentSendRank err=%+v", err)
		return
	}
	return
}

func (mgr *LevelupPresentMgr) GetLevelupUserRank(ctx context.Context, parentItemId uint32, level uint32, version uint32, offset uint32, limit uint32) *[]*pb.UserRank {
	return mgr.store.GetRankList(ctx, parentItemId, level, version, offset, limit)
}



