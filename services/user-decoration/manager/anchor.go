package manager

import (
	"context"
	"encoding/json"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/apicentergo"
	user_decoration "golang.52tt.com/protocol/services/user-decoration"
	"strconv"
)

// 处理解约之后，删除用户的奖励

// DeleteUserInfoCard 删除用户的奖励
func (s *AwardMgr) DeleteUserInfoCard(ctx context.Context, uid uint32) error {
	// 先找到对应的装扮
	query := "select `award_id` from `award_uid` where `uid` = ? "
	rows, err := s.sto.QueryContext(ctx, query, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
		return err
	}

	var id string
	for rows.Next() {
		err = rows.Scan(&id)
		if err != nil {
			log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
			return err
		}
		break
	}

	decInfoId := uint32(0)
	cardDetail := ""
	cfg := &user_decoration.LiveAwardInfoCardConfig{}
	query = "select `id`, `live_card_detail` from `dec_info` where `decoration_id` = ? AND `version` = ? AND `typ` = ?"
	err = s.sto.QueryRowContext(ctx, query, id, "v1", uint32(pb.DecorationType_CHANNEL_INFO_CARD)).Scan(&decInfoId, &cardDetail)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
		return err
	}

	// 解析
	err = json.Unmarshal([]byte(cardDetail), &cfg)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
		return err
	}

	if cfg.GetAwardType() == user_decoration.LiveAwardInfoCardConfig_AWARD_TYPE_RICH {
		log.InfoWithCtx(ctx, "DeleteUserInfoCard failed, uid %d is rich, no need to delete", uid)
		return nil
	}

	// 删数据库数据
	query = "DELETE FROM `decorations` WHERE `uid` = ? AND `dec_info_id` = ?"
	_, err = s.sto.ExecContext(ctx, query, uid, decInfoId)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
		return err
	}

	// 删缓存
	err = s.redis.Del(strconv.Itoa(int(uid)) + ":3").Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
		return err
	}

	// 最后删奖励表
	query = "DELETE FROM `award_uid` WHERE `uid` = ? AND `award_id` = ?"
	_, err = s.sto.ExecContext(ctx, query, uid, id)
	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteUserInfoCard failed, uid %d err:%s", uid, err.Error())
		return err
	}

	return nil
}
