package mgr

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	singing_hall "golang.52tt.com/protocol/services/singing-hall"
	"golang.52tt.com/services/singing-hall/mocks"
	"golang.52tt.com/services/singing-hall/rpc"
	"golang.52tt.com/services/singing-hall/store"
)

var ctx context.Context

func init() {
	ctx = protogrpc.WithServiceInfo(context.TODO(), &protogrpc.ServiceInfo{
		UserID: 23333,
	})
}
func TestManager_BatchDelSongMenu(t *testing.T) {
	ctrl := gomock.NewController(t)
	is := mocks.NewMockIStore(ctrl)
	gomock.InOrder(is.EXPECT().BatchDelSongMenu(uint32(23333), []uint32{1, 2, 3}).Return(nil))
	type fields struct {
		store store.IStore
		rpc   *rpc.Client
	}
	type args struct {
		ctx context.Context
		ids []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{store: is, rpc: nil},
			args: args{
				ctx: ctx,
				ids: []uint32{1, 2, 3},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store: tt.fields.store,
				rpc:   tt.fields.rpc,
			}
			if err := m.BatchDelSongMenu(tt.args.ctx, tt.args.ids); (err != nil) != tt.wantErr {
				t.Errorf("BatchDelSongMenu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_CreateSongMenu(t *testing.T) {
	ctrl := gomock.NewController(t)
	is := mocks.NewMockIStore(ctrl)
	type fields struct {
		store store.IStore
		rpc   *rpc.Client
	}
	type args struct {
		ctx      context.Context
		songMenu *store.SongMenu
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		mock    func(args args)
		wantErr bool
	}{
		{
			fields: fields{store: is},
			args: args{
				ctx: ctx,
				songMenu: &store.SongMenu{
					ID:    1,
					Name:  "2",
					Index: 4,
					Uid:   5,
				},
			},
			mock: func(args args) {
				gomock.InOrder(is.EXPECT().CreateSongMenu(args.songMenu).Return(uint32(0), nil))
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store: tt.fields.store,
				rpc:   tt.fields.rpc,
			}
			if tt.mock != nil {
				tt.mock(tt.args)
			}
			if _, err := m.CreateSongMenu(tt.args.ctx, tt.args.songMenu); (err != nil) != tt.wantErr {
				t.Errorf("CreateSongMenu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_GetSongMenu(t *testing.T) {
	ctrl := gomock.NewController(t)
	is := mocks.NewMockIStore(ctrl)
	type fields struct {
		store store.IStore
		rpc   *rpc.Client
	}
	type args struct {
		uid      uint32
		offset   uint32
		pageSize uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*store.SongMenu
		mock    func(args args, want []*store.SongMenu)
		wantErr bool
	}{
		{
			fields: fields{store: is},
			args: args{
				uid:      1,
				offset:   2,
				pageSize: 3,
			},
			want: []*store.SongMenu{
				{
					ID:      1,
					Name:    "2",
					Index:   3,
					Uid:     4,
					SongNum: 5,
				},
			},
			mock: func(args args, want []*store.SongMenu) {
				gomock.InOrder(is.EXPECT().GetSongMenu(args.uid, args.offset, args.pageSize).Return(want, uint32(1), nil))
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store: tt.fields.store,
				rpc:   tt.fields.rpc,
			}
			if tt.mock != nil {
				tt.mock(tt.args, tt.want)
			}
			got, _, err := m.GetSongMenu(tt.args.uid, tt.args.offset, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetSongMenu() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSongMenu() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestManager_SortSongMenu(t *testing.T) {
	ctrl := gomock.NewController(t)
	is := mocks.NewMockIStore(ctrl)
	type fields struct {
		store store.IStore
		rpc   *rpc.Client
	}
	type args struct {
		ctx      context.Context
		songMenu []*singing_hall.SongMenuItem
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		mock    func(args args)
	}{
		{
			fields: fields{store: is},
			args: args{
				ctx: ctx,
				songMenu: []*singing_hall.SongMenuItem{
					{
						Id: 1,
					},
				},
			},
			wantErr: false,
			mock: func(args args) {
				si, _ := protogrpc.ServiceInfoFromContext(args.ctx)
				ids := make([]uint32, len(args.songMenu))
				for i, item := range args.songMenu {
					ids[i] = item.Id
				}
				gomock.InOrder(is.EXPECT().EditSongMenu(si.UserID, ids).Return(nil))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store: tt.fields.store,
				rpc:   tt.fields.rpc,
			}
			if tt.mock != nil {
				tt.mock(tt.args)
			}
			if err := m.EditSongMenu(tt.args.ctx, tt.args.songMenu); (err != nil) != tt.wantErr {
				t.Errorf("EditSongMenu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestManager_UpdateSongMenu(t *testing.T) {
	ctrl := gomock.NewController(t)
	is := mocks.NewMockIStore(ctrl)
	type fields struct {
		store store.IStore
		rpc   *rpc.Client
	}
	type args struct {
		songMenu *store.SongMenu
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		mock    func(args2 args)
	}{
		{
			fields: fields{store: is},
			args: args{
				&store.SongMenu{
					ID:   1,
					Name: "helloworld",
				},
			},
			wantErr: false,
			mock: func(args2 args) {
				gomock.InOrder(is.EXPECT().UpdateSongMenu(args2.songMenu).Return(nil))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				store: tt.fields.store,
				rpc:   tt.fields.rpc,
			}
			if tt.mock != nil {
				tt.mock(tt.args)
			}
			if err := m.UpdateSongMenu(tt.args.songMenu); (err != nil) != tt.wantErr {
				t.Errorf("UpdateSongMenu() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
