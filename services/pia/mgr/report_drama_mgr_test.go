package mgr

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func TestReportDramaMgr_Report(t *testing.T) {
	t.Skip()
	controller := gomock.NewController(t)
	type fields struct {
		dramaStore             IDramaV2
		reportDramaRecordStore ReportDramaRecordStore
		idService              IDService
		dramaWebApi            DramaWebApi
	}
	type args struct {
		ctx context.Context
		req *ReportRequest
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *ReportDramaMgr)
	}{
		{
			name: "正常",
			fields: fields{
				dramaStore:             NewMockIDramaV2(controller),
				reportDramaRecordStore: NewMockReportDramaRecordStore(controller),
				idService:              NewMockIDService(controller),
				dramaWebApi:            NewMockDramaWebApi(controller),
			},
			args: args{
				ctx: context.Background(),
				req: &ReportRequest{
					dramaID:         1,
					uid:             1,
					userNickname:    "1",
					reason:          []string{"1"},
					evidenceImgList: []string{"1"},
					reportTime:      time.Now(),
					description:     "11221",
				},
			},
			wantErr: assert.NoError,
			initFunc: func(s *ReportDramaMgr) {
				s.dramaStore.(*MockIDramaV2).EXPECT().GetDramaByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&DramaEntityV2{
					OriginID: 123,
				}, nil)
				s.idService.(*MockIDService).EXPECT().GenerateID().Return(int64(123))
				s.reportDramaRecordStore.(*MockReportDramaRecordStore).EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ReportDramaMgr{
				dramaStore:             tt.fields.dramaStore,
				reportDramaRecordStore: tt.fields.reportDramaRecordStore,
				idService:              tt.fields.idService,
				dramaWebApi:            tt.fields.dramaWebApi,
			}
			if tt.initFunc != nil {
				tt.initFunc(r)
			}
			tt.wantErr(t, r.Report(tt.args.ctx, tt.args.req), fmt.Sprintf("Report(%v, %v)", tt.args.ctx, tt.args.req))
		})
	}
}

func TestReportDramaMgr_UploadReportInfo(t *testing.T) {
	controller := gomock.NewController(t)
	type fields struct {
		dramaStore             IDramaV2
		reportDramaRecordStore ReportDramaRecordStore
		idService              IDService
		dramaWebApi            DramaWebApi
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantErr  assert.ErrorAssertionFunc
		initFunc func(s *ReportDramaMgr)
	}{
		{
			name: "正常",
			fields: fields{
				dramaStore:             NewMockIDramaV2(controller),
				reportDramaRecordStore: NewMockReportDramaRecordStore(controller),
				idService:              NewMockIDService(controller),
				dramaWebApi:            NewMockDramaWebApi(controller),
			},
			args:    args{ctx: context.Background()},
			wantErr: assert.NoError,
			initFunc: func(s *ReportDramaMgr) {
				s.reportDramaRecordStore.(*MockReportDramaRecordStore).EXPECT().FetchList(gomock.Any(), gomock.Any()).Return([]*ReportDramaRecord{
					{
						ID: "123",
					},
				}, nil)
				s.dramaWebApi.(*MockDramaWebApi).EXPECT().ReportDrama(gomock.Any(), gomock.Any()).Return(nil)
				s.reportDramaRecordStore.(*MockReportDramaRecordStore).EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ReportDramaMgr{
				dramaStore:             tt.fields.dramaStore,
				reportDramaRecordStore: tt.fields.reportDramaRecordStore,
				idService:              tt.fields.idService,
				dramaWebApi:            tt.fields.dramaWebApi,
			}
			if tt.initFunc != nil {
				tt.initFunc(r)
			}
			tt.wantErr(t, r.UploadReportInfo(tt.args.ctx), fmt.Sprintf("UploadReportInfo(%v)", tt.args.ctx))
		})
	}
}
