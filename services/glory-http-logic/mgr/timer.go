package mgr

import (
	"context"
	"time"

	gloryRewardPb "golang.52tt.com/protocol/services/glory-reward"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	backpackPb "golang.52tt.com/protocol/services/backpack-base"
	"golang.52tt.com/protocol/services/userpresent"
)

// 获取礼物配置
func TimerGetPresentConfList() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	log.InfoWithCtx(ctx, "TimerGetPresentConfList begin")

	manager := GetMgr()
	presentResp, err := manager.UserPresentCli.GetPresentConfigList(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGetPresentConfList GetPresentConfigList err:%v", err)
		return
	}

	mapId2PresentConf := make(map[uint32]*userpresent.StPresentItemConfig, 0)
	for _, present := range presentResp.GetItemList() {
		mapId2PresentConf[present.GetItemId()] = present
	}

	manager.SetPresentConfMap(mapId2PresentConf)

	log.InfoWithCtx(ctx, "TimerGetPresentConfList end mapId2PresentConf:%d", len(mapId2PresentConf))
}

// 获取碎片配置
func TimerGetBackPackFraCfgList() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	log.InfoWithCtx(ctx, "TimerGetBackPackFraCfgList begin")

	manager := GetMgr()
	itemCfgResp, err := manager.BackPackCli.GetItemCfg(ctx,
		&backpackPb.GetItemCfgReq{
			ItemType: uint32(backpackPb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			GetAll:   true,
		})
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGetBackPackFraCfgList GetItemCfg err:%v", err)
		return
	}

	mapId2Cfg := make(map[uint32]*backpackPb.LotteryFragmentCfg, 0)
	for _, itemCfg := range itemCfgResp.GetItemCfgList() {
		cfg := &backpackPb.LotteryFragmentCfg{}
		_ = proto.Unmarshal(itemCfg, cfg)
		mapId2Cfg[cfg.FragmentId] = cfg
		log.DebugWithCtx(ctx, "TimerGetBackPackFraCfgList GetItemCfg:%v", cfg)
	}

	manager.SetBackPackFraCfgMap(mapId2Cfg)

	log.InfoWithCtx(ctx, "TimerGetBackPackFraCfgList end mapId2Cfg:%d", len(mapId2Cfg))
}

// 获取礼物白名单配置
func TimerGetPresentWhiteListMap() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	log.InfoWithCtx(ctx, "TimerGetPresentWhiteListMap begin")

	manager := GetMgr()
	itemCfgResp, err := manager.GloryRewardCli.GetWhiteList(ctx, &gloryRewardPb.GetWhiteListReq{})
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerGetBackPackFraCfgList GetItemCfg err:%v", err)
		return
	}

	whiteListMap := make(map[uint32]bool)
	for _, itemId := range itemCfgResp.GetWhiteList() {
		if itemId == 0 {
			continue
		}
		whiteListMap[itemId] = true
	}

	manager.SetPresentWhiteListMap(whiteListMap)

	log.InfoWithCtx(ctx, "TimerGetBackPackFraCfgList end whiteListMap:[%+v]", whiteListMap)
}
