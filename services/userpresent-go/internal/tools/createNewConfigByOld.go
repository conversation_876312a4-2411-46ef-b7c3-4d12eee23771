package main

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	userPresent "golang.52tt.com/clients/userpresent"
	userpresent_go "golang.52tt.com/clients/userpresent-go"

	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	"golang.52tt.com/protocol/services/userpresent"

	userpresent_go2 "golang.52tt.com/protocol/services/userpresent-go"
	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"golang.52tt.com/services/userpresent-go/internal/store"
	"google.golang.org/grpc"
	"math"
	"sort"
	"strings"
)

const IsTest = true

func main() {
	//AddPresentConfig()
	//ticker := time.NewTicker(time.Minute)
	//defer ticker.Stop()
	//for {
	//	select {
	//	case <-ticker.C:
	//		GetPresentConfig()
	//	}
	//}

	GetPresentConfig()
	//GetAndCheckSever()

	//GetPresentConfigTest()
}

func AddPresentConfig() {
	ctx := context.Background()
	newStore, err := store.NewStore(ctx, &mysqlConnect.MysqlConfig{
		Host:            "************",
		Port:            3306,
		Protocol:        "tcp",
		Database:        "appsvr",
		UserName:        "godman",
		Password:        "thegodofman",
		Charset:         "utf8",
		MaxIdleConns:    0,
		MaxOpenConns:    0,
		ConnMaxIdleTime: 0,
		ConnMaxLifeTime: 0,
	}, &mysqlConnect.MysqlConfig{
		Host:            "************",
		Port:            3306,
		Protocol:        "tcp",
		Database:        "appsvr",
		UserName:        "godman",
		Password:        "thegodofman",
		Charset:         "utf8",
		MaxIdleConns:    0,
		MaxOpenConns:    0,
		ConnMaxIdleTime: 0,
		ConnMaxLifeTime: 0,
	})

	if err != nil {
		panic(err)
	}

	resp, err := newStore.GetAllPresentConfig(ctx)

	for _, cfg := range resp {
		score := uint32(0)
		if cfg.GetPriceType() == 2 {
			score = cfg.GetPrice() / 2
		}

		// 写新表
		err = newStore.AddPresentBaseConfig(ctx, nil, &store.PresentBaseConfig{
			ItemId:        cfg.GetItemId(),
			Name:          cfg.GetName(),
			IconURL:       cfg.GetIconUrl(),
			Price:         cfg.GetPrice(),
			IsDel:         cfg.GetIsDel(),
			PriceType:     cfg.GetPriceType(),
			Rich:          cfg.GetPrice(),
			Score:         score,
			Charm:         cfg.GetPrice(),
			ForceSendable: cfg.GetExtend().GetForceSendable(),
			UpdateTime:    cfg.GetUpdateTime(),
			CreateTime:    cfg.GetCreateTime(),
			IsTest:        cfg.GetExtend().GetIsTest(),
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
			return
		}

		err = newStore.AddPresentEnterConfig(ctx, nil, &store.PresentEnterConfig{
			ItemId:             cfg.GetItemId(),
			UnshowBatchOption:  cfg.GetExtend().GetUnshowBatchOption(),
			Rank:               cfg.GetRankFloat(),
			EffectBegin:        cfg.GetEffectBegin(),
			EffectEnd:          cfg.GetEffectEnd(),
			NobilityLevel:      cfg.GetExtend().GetNobilityLevel(),
			UnshowPresentShelf: cfg.GetExtend().GetUnshowPresentShelf(),
			ShowEffectEnd:      cfg.GetExtend().GetShowEffectEnd(),
			EffectEndDelay:     cfg.GetExtend().GetEffectEndDelay(),
			UpdateTime:         cfg.GetUpdateTime(),
			Tag:                cfg.GetExtend().GetTag(),
			FansLevel:          cfg.GetExtend().GetFansLevel(),
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
			return
		}

		// 写新表
		customText, _ := json.Marshal(cfg.GetExtend().GetCustomText())

		err = newStore.AddPresentEffectConfig(ctx, nil, &store.PresentEffectConfig{
			ItemId:            cfg.GetItemId(),
			VideoEffectURL:    string(cfg.GetExtend().GetVideoEffectUrl()),
			ShowEffect:        cfg.GetExtend().GetShowEffect(),
			FlowID:            cfg.GetExtend().GetFlowId(),
			NotifyAll:         cfg.GetExtend().GetNotifyAll(),
			CustomText:        customText,
			SmallVapURL:       cfg.GetExtend().GetSmallVapUrl(),
			SmallVapMD5:       cfg.GetExtend().GetSmallVapMd5(),
			MicEffectURL:      cfg.GetExtend().GetMicEffectUrl(),
			MicEffectMD5:      cfg.GetExtend().GetMicEffectMd5(),
			UpdateTime:        cfg.GetUpdateTime(),
			IsBoxBreaking:     cfg.GetExtend().GetIsBoxBreaking(),
			FusionPresent:     cfg.GetExtend().GetFusionPresent(),
			IosVideoEffectURL: string(cfg.GetExtend().GetIosExtend().GetVideoEffectUrl()),
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
			return
		}

		err = newStore.AddPresentConfigLog(ctx, nil, &store.PresentConfigLog{
			ItemId:      cfg.GetItemId(),
			OperateType: store.PresentOperateTypeAdd,
			UpdateTime:  cfg.GetUpdateTime(),
		})

		if err != nil {
			log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentConfigLog fail err %v", err)
			return
		}

	}
}

func GetPresentConfig() {
	presentCli := userPresent.NewIClient(grpc.WithBlock())
	presentGoCli := userpresent_go.NewIClient(grpc.WithBlock())
	resp, err := presentCli.GetPresentConfigListV2(context.Background())
	if err != nil {
		fmt.Println(err)
	}

	oldMap := map[uint32]string{}
	oldItemMap := map[uint32]*userpresent.StPresentItemConfig{}
	newMap := map[uint32]string{}
	newItemMap := map[uint32]*userpresent_go2.StPresentItemConfig{}
	//
	//newStore, err := store.NewStore(context.Background(), &mysqlConnect.MysqlConfig{
	//	Host:            "************",
	//	Port:            3306,
	//	Protocol:        "tcp",
	//	Database:        "appsvr",
	//	UserName:        "godman",
	//	Password:        "thegodofman",
	//	Charset:         "utf8",
	//	MaxIdleConns:    0,
	//	MaxOpenConns:    0,
	//	ConnMaxIdleTime: 0,
	//	ConnMaxLifeTime: 0,
	//}, &mysqlConnect.MysqlConfig{
	//	Host:            "************",
	//	Port:            3306,
	//	Protocol:        "tcp",
	//	Database:        "appsvr",
	//	UserName:        "godman",
	//	Password:        "thegodofman",
	//	Charset:         "utf8",
	//	MaxIdleConns:    0,
	//	MaxOpenConns:    0,
	//	ConnMaxIdleTime: 0,
	//	ConnMaxLifeTime: 0,
	//})

	newStore := &store.Store{}

	sort.Slice(resp.ItemList, func(i, j int) bool {
		return resp.ItemList[i].ItemId < resp.ItemList[j].ItemId
	})

	for _, item := range resp.GetItemList() {
		// 将结构体转换为字符串
		str := fmt.Sprintf("%+v\n", item)
		oldMap[item.GetItemId()] = str
		oldItemMap[item.GetItemId()] = item
	}

	respGo, err := presentGoCli.GetPresentConfigList(context.Background(), &userpresent_go2.GetPresentConfigListReq{})
	if err != nil {
		fmt.Println("GetPresentConfigList err ", err)
	}

	sort.Slice(respGo.ItemList, func(i, j int) bool {
		return respGo.ItemList[i].ItemId < respGo.ItemList[j].ItemId
	})

	for _, item := range respGo.GetItemList() {
		// 将结构体转换为字符串
		str2 := fmt.Sprintf("%+v\n", item)
		newMap[item.GetItemId()] = str2
		newItemMap[item.GetItemId()] = item
	}

	for id, str := range oldMap {
		str = strings.ReplaceAll(str, "ios_extend:<> ", "")

		if str != newMap[id] {
			if math.Abs(float64(oldItemMap[id].GetRankFloat())-float64(newItemMap[id].GetRankFloat())) < 0.1 && newItemMap[id] != nil && oldItemMap[id].GetUpdateTime() == newItemMap[id].GetUpdateTime() {
				continue
			}

			if id == 1407 {
				continue
			}

			if newItemMap[id] == nil {
				fmt.Println("added:", id)
				if !IsTest {
					AddConfigById(id, newStore)
				}
			} else {
				fmt.Println("updated:", id)
				if !IsTest {
					UpdateConfigById(id, newStore)
				}
			}

		}
	}

	// 反过来看下删除的
	for id, item := range newItemMap {
		ctx := context.Background()

		// 新的有旧的没有，或者删除状态不一致
		if oldItemMap[id] == nil || (oldItemMap[id].GetIsDel() && !item.GetIsDel()) {
			fmt.Println("deleted:", id)
			if !IsTest {
				fmt.Println("delete id ,", id, newStore.DeletePresentConfigNew(ctx, id))
			}
		}
	}

}

func UpdateConfigById(id uint32, newStore *store.Store) {
	ctx := context.Background()
	cfg, _ := newStore.GetPresentConfigById(context.Background(), nil, id)
	score := uint32(0)
	if cfg.GetPriceType() == 2 {
		score = cfg.GetPrice() / 2
	}

	// 写新表
	err := newStore.UpdatePresentBaseConfig(ctx, nil, &store.PresentBaseConfig{
		ItemId:        cfg.GetItemId(),
		Name:          cfg.GetName(),
		IconURL:       cfg.GetIconUrl(),
		Price:         cfg.GetPrice(),
		IsDel:         cfg.GetIsDel(),
		PriceType:     cfg.GetPriceType(),
		Rich:          cfg.GetPrice(),
		Score:         score,
		Charm:         cfg.GetPrice(),
		ForceSendable: cfg.GetExtend().GetForceSendable(),
		UpdateTime:    cfg.GetUpdateTime(),
		CreateTime:    cfg.GetCreateTime(),
		IsTest:        cfg.GetExtend().GetIsTest(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
		return
	}

	err = newStore.UpdatePresentEnterConfig(ctx, nil, &store.PresentEnterConfig{
		ItemId:             cfg.GetItemId(),
		UnshowBatchOption:  cfg.GetExtend().GetUnshowBatchOption(),
		Rank:               cfg.GetRankFloat(),
		EffectBegin:        cfg.GetEffectBegin(),
		EffectEnd:          cfg.GetEffectEnd(),
		NobilityLevel:      cfg.GetExtend().GetNobilityLevel(),
		UnshowPresentShelf: cfg.GetExtend().GetUnshowPresentShelf(),
		ShowEffectEnd:      cfg.GetExtend().GetShowEffectEnd(),
		EffectEndDelay:     cfg.GetExtend().GetEffectEndDelay(),
		UpdateTime:         cfg.GetUpdateTime(),
		Tag:                cfg.GetExtend().GetTag(),
		FansLevel:          cfg.GetExtend().GetFansLevel(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
		return
	}

	// 写新表
	customText, _ := json.Marshal(cfg.GetExtend().GetCustomText())

	err = newStore.UpdatePresentEffectConfig(ctx, nil, &store.PresentEffectConfig{
		ItemId:            cfg.GetItemId(),
		VideoEffectURL:    string(cfg.GetExtend().GetVideoEffectUrl()),
		ShowEffect:        cfg.GetExtend().GetShowEffect(),
		FlowID:            cfg.GetExtend().GetFlowId(),
		NotifyAll:         cfg.GetExtend().GetNotifyAll(),
		CustomText:        customText,
		SmallVapURL:       cfg.GetExtend().GetSmallVapUrl(),
		SmallVapMD5:       cfg.GetExtend().GetSmallVapMd5(),
		MicEffectURL:      cfg.GetExtend().GetMicEffectUrl(),
		MicEffectMD5:      cfg.GetExtend().GetMicEffectMd5(),
		UpdateTime:        cfg.GetUpdateTime(),
		IsBoxBreaking:     cfg.GetExtend().GetIsBoxBreaking(),
		FusionPresent:     cfg.GetExtend().GetFusionPresent(),
		IosVideoEffectURL: string(cfg.GetExtend().GetIosExtend().GetVideoEffectUrl()),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
		return
	}

	err = newStore.AddPresentConfigLog(ctx, nil, &store.PresentConfigLog{
		ItemId:      cfg.GetItemId(),
		OperateType: store.PresentOperateTypeUpdate,
		UpdateTime:  cfg.GetUpdateTime(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentConfigLog fail err %v", err)
		return
	}
}

func AddConfigById(id uint32, newStore *store.Store) {
	ctx := context.Background()
	cfg, _ := newStore.GetPresentConfigById(context.Background(), nil, id)
	score := uint32(0)
	if cfg.GetPriceType() == 2 {
		score = cfg.GetPrice() / 2
	}

	// 写新表
	err := newStore.AddPresentBaseConfig(ctx, nil, &store.PresentBaseConfig{
		ItemId:        cfg.GetItemId(),
		Name:          cfg.GetName(),
		IconURL:       cfg.GetIconUrl(),
		Price:         cfg.GetPrice(),
		IsDel:         cfg.GetIsDel(),
		PriceType:     cfg.GetPriceType(),
		Rich:          cfg.GetPrice(),
		Score:         score,
		Charm:         cfg.GetPrice(),
		ForceSendable: cfg.GetExtend().GetForceSendable(),
		UpdateTime:    cfg.GetUpdateTime(),
		CreateTime:    cfg.GetCreateTime(),
		IsTest:        cfg.GetExtend().GetIsTest(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
		return
	}

	err = newStore.AddPresentEnterConfig(ctx, nil, &store.PresentEnterConfig{
		ItemId:             cfg.GetItemId(),
		UnshowBatchOption:  cfg.GetExtend().GetUnshowBatchOption(),
		Rank:               cfg.GetRankFloat(),
		EffectBegin:        cfg.GetEffectBegin(),
		EffectEnd:          cfg.GetEffectEnd(),
		NobilityLevel:      cfg.GetExtend().GetNobilityLevel(),
		UnshowPresentShelf: cfg.GetExtend().GetUnshowPresentShelf(),
		ShowEffectEnd:      cfg.GetExtend().GetShowEffectEnd(),
		EffectEndDelay:     cfg.GetExtend().GetEffectEndDelay(),
		UpdateTime:         cfg.GetUpdateTime(),
		Tag:                cfg.GetExtend().GetTag(),
		FansLevel:          cfg.GetExtend().GetFansLevel(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
		return
	}

	// 写新表
	customText, _ := json.Marshal(cfg.GetExtend().GetCustomText())

	err = newStore.AddPresentEffectConfig(ctx, nil, &store.PresentEffectConfig{
		ItemId:            cfg.GetItemId(),
		VideoEffectURL:    string(cfg.GetExtend().GetVideoEffectUrl()),
		ShowEffect:        cfg.GetExtend().GetShowEffect(),
		FlowID:            cfg.GetExtend().GetFlowId(),
		NotifyAll:         cfg.GetExtend().GetNotifyAll(),
		CustomText:        customText,
		SmallVapURL:       cfg.GetExtend().GetSmallVapUrl(),
		SmallVapMD5:       cfg.GetExtend().GetSmallVapMd5(),
		MicEffectURL:      cfg.GetExtend().GetMicEffectUrl(),
		MicEffectMD5:      cfg.GetExtend().GetMicEffectMd5(),
		UpdateTime:        cfg.GetUpdateTime(),
		IsBoxBreaking:     cfg.GetExtend().GetIsBoxBreaking(),
		FusionPresent:     cfg.GetExtend().GetFusionPresent(),
		IosVideoEffectURL: string(cfg.GetExtend().GetIosExtend().GetVideoEffectUrl()),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentBaseConfig fail err %v", err)
		return
	}

	err = newStore.AddPresentConfigLog(ctx, nil, &store.PresentConfigLog{
		ItemId:      cfg.GetItemId(),
		OperateType: store.PresentOperateTypeAdd,
		UpdateTime:  cfg.GetUpdateTime(),
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "AddPresentConfigNew AddPresentConfigLog fail err %v", err)
		return
	}
}

func GetPresentConfigTest() {
	presentCli := userPresent.NewIClient(grpc.WithBlock())

	resp, _ := presentCli.GetPresentConfigList(context.Background())

	for _, item := range resp.GetItemList() {
		if item.IsDel {
			fmt.Println(item.ItemId)
		}
	}
}

func findDifference(str1, str2 string) string {
	// 将字符串转换为字符数组
	chars1 := strings.Split(str1, "")
	chars2 := strings.Split(str2, "")

	// 找出较长的字符串长度
	maxLen := len(chars1)
	if len(chars2) > maxLen {
		maxLen = len(chars2)
	}

	// 逐个字符比较，找出差异
	var diff strings.Builder
	for i := 0; i < maxLen; i++ {
		if i >= len(chars1) || i >= len(chars2) || chars1[i] != chars2[i] {
			if i < len(chars1) {
				diff.WriteString(chars1[i])
			}
			if i < len(chars2) {
				diff.WriteString(chars2[i])
			}
		}
	}

	return diff.String()
}
