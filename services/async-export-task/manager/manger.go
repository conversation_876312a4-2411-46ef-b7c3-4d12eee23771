package manager

import (
	"context"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"github.com/jmoiron/sqlx"
	gold_commission "golang.52tt.com/clients/gold-commission"
	obsObjectGateway "golang.52tt.com/clients/obsgateway"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/async-export-task"
	"golang.52tt.com/services/async-export-task/model"
	"golang.52tt.com/services/async-export-task/task"
	"google.golang.org/grpc"
	"net/http"
	"net/url"
	"os"
	"time"
)

const (
	appId   = "tt"
	scope   = "user-gift-score"
	bufSize = 1024 * 1024 * 5

	layOut = "2006-01-02 15:04:05"
)

type AsyncExportTaskMgr struct {
	store                *model.Store
	obsgatewayClient     *obsObjectGateway.Client
	goldCommissionClient *gold_commission.Client
	obsHost              string
	taskMap              map[pb.TaskType]task.HandleTask
}

func NewAsyncExportTaskMgr(cfg config.Configer) (*AsyncExportTaskMgr, error) {
	obsHost := cfg.DefaultString("obs_host", "")
	if len(obsHost) == 0 {
		log.Errorf("config obs_host is empty")
		return nil, errors.New("config obs_host is empty")
	}

	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, "mysql_config")
	log.Infof("mysql_config connect %s", mysqlConfig.ConnectionString())
	db, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		return nil, err
	}
	db.SetMaxOpenConns(mysqlConfig.MaxOpenConns)
	db.SetMaxIdleConns(mysqlConfig.MaxIdleConns)
	store := model.NewStore(db)

	err = store.CreateAllUserScoreExportTaskTable()
	if err != nil {
		log.Errorf("CreateAllUserScoreExportTaskTable err=%v", err)
		return nil, err
	}

	obsgatewayClient, err := obsObjectGateway.NewClient(grpc.WithInsecure(), grpc.WithBlock())
	if err != nil {
		return nil, err
	}

	goldCommissionClient := gold_commission.NewClient(grpc.WithInsecure(), grpc.WithBlock())

	mgr := &AsyncExportTaskMgr{store: store, obsgatewayClient: obsgatewayClient, obsHost: obsHost, goldCommissionClient: goldCommissionClient}

	mgr.taskMap = make(map[pb.TaskType]task.HandleTask)
	mgr.taskMap[pb.TaskType_CreateAmuseExtraIncomeSettleList] = task.NewCreateAmuseExtraIncomeSettleListExportTask(store)
	mgr.taskMap[pb.TaskType_QueryAnchorInfo] = task.NewQueryAnchorInfoTask(store)
	mgr.taskMap[pb.TaskType_QueryPrepareList] = task.NewQueryPrepareBackupTask(store)
	mgr.taskMap[pb.TaskType_QueryAnchorScore] = task.NewQueryAnchorScoreListTask(store)

	return mgr, nil
}

func (mgr *AsyncExportTaskMgr) ShutDown() {
	mgr.store.Close()
}

func (mgr *AsyncExportTaskMgr) GetExportAllTaskList(ctx context.Context, taskType uint32, offset uint32, limit uint32) (*[]model.TaskInfo, uint32, error) {
	list, err := mgr.store.GetTaskList(ctx, taskType, offset, limit)
	if err != nil {
		return list, 0, err
	}
	count, err := mgr.store.GetTaskCount(ctx, taskType)
	if err != nil {
		return list, count, err
	}
	return list, count, nil
}

func (mgr *AsyncExportTaskMgr) GetTempUrl(ctx context.Context, id uint32) (string, error) {
	var claims []interface{}
	claims = append(claims, &obsObjectGateway.DownloadTokenReq{
		App:        appId,
		Scope:      scope,
		Expiration: 600,
	})

	tokenResp, err := mgr.obsgatewayClient.ClaimToken(context.Background(), claims)

	if err != nil {
		log.Errorf("obsgatewayClient.ClaimToken err=%+v", err)
		return "", err
	}

	taskData, err2 := mgr.store.GetTaskData(ctx, id)
	if err2 != nil {
		log.Errorf("userScoreRoStore.GetTaskData err=%+v", err2)
		return "", protocol.NewServerError(status.ErrUserscoreMgrDbErr)
	}

	tokenUrl := fmt.Sprintf("%s%s/%s/%s?token=%s", mgr.obsHost, appId, scope, taskData.ObsKey, tokenResp[0])
	log.Infof("token url=%s", tokenUrl)

	cli := http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}
	resp, err2 := cli.Head(tokenUrl)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "cli.Head %s err=%v", tokenUrl, err2)
		return "", err2
	} else {
		//unespUrl, _ := url.PathUnescape(resp.Header.Get("Location"))
		location := resp.Header.Get("Location")
		log.Infof("location=%s", location)
		u, _ := url.Parse(location)
		respPathOnly, _ := url.PathUnescape(u.Path)
		finalUrl := fmt.Sprintf("%s://%s%s?%s", u.Scheme, u.Host, respPathOnly, u.RawQuery)
		log.Infof("finalUrl url=%s", finalUrl)
		resp.Body.Close()
		return finalUrl, nil
	}
}

func (mgr *AsyncExportTaskMgr) uploadFile(filePath string, key string, contentType string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}

	var opts []obsObjectGateway.Option
	opts = append(opts, obsObjectGateway.WithKey(key))
	opts = append(opts, obsObjectGateway.WithContentType(contentType))

	_, uploadId, _, srverr := mgr.obsgatewayClient.InitMultipartUpload(context.Background(), appId, scope, opts...)
	if srverr != nil {
		log.Errorf("obsgatewayClient.InitMultipartUpload err=%+v", srverr)
		return srverr
	}

	buf := make([]byte, bufSize)
	partNum := int32(1)
	etags := make([]string, 0)
	partnums := make([]int32, 0)
	for {
		readLen, err := file.Read(buf)
		if err != nil {
			log.Warnf("file.Read err=%+v", err)
			break
		}

		if readLen < bufSize {
			buf = buf[:readLen]
		}

		etag, serverError := mgr.obsgatewayClient.UploadPart(context.Background(), appId, scope, key, uploadId, partNum, buf)
		if serverError != nil {
			log.Errorf("obsgatewayClient.UploadPart err=%+v", serverError)
			return serverError
		}

		log.Debugf("partNum=%d", partNum)

		etags = append(etags, etag)
		partnums = append(partnums, partNum)

		if readLen < bufSize {
			break
		}
		partNum++
	}

	_, serverError := mgr.obsgatewayClient.CompleteMultipartUpload(context.Background(), appId, scope, key, uploadId, partnums, etags, contentType, "")
	if serverError != nil {
		log.Errorf("obsgatewayClient.CompleteMultipartUpload err=%+v", serverError)
		return serverError
	}

	file.Close()

	return nil
}

func (mgr *AsyncExportTaskMgr) CreateAmuseExtraIncomeSettleListExportTask(ctx context.Context, req *pb.CreateAmuseExtraIncomeSettleListExportTaskReq) error {
	params, err := proto.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal err=%v", err)
		return err
	}

	taskDesc := ""
	if req.GetGuildId() != 0 {
		taskDesc += fmt.Sprintf("公会ID=%d ", req.GetGuildId())
	}
	startTimeT := time.Unix(int64(req.GetStartTime()), 0)
	endTimeT := time.Unix(int64(req.GetEndTime()), 0)
	taskDesc += fmt.Sprintf("统计时间段=%s~%s ", startTimeT.Format("2006-01-02"), endTimeT.Format("2006-01-02"))

	if req.GetCompareStartTime() != 0 && req.GetCompareEndTime() != 0 {
		compareStartTimeT := time.Unix(int64(req.GetCompareStartTime()), 0)
		compareEndTimeT := time.Unix(int64(req.GetCompareEndTime()), 0)
		taskDesc += fmt.Sprintf("对比时间段=%s~%s ", compareStartTimeT.Format("2006-01-02"), compareEndTimeT.Format("2006-01-02"))
	}
	if req.GetMasterUid() != 0 {
		taskDesc += fmt.Sprintf("会长uid=%d", req.GetMasterUid())
	}

	_, err = mgr.store.InsertTask(ctx, taskDesc, uint32(pb.TaskType_CreateAmuseExtraIncomeSettleList), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertTask err=%v", err)
		return err
	}

	return nil
}

func genQueryAnchorInfoTaskDesc(req *pb.QueryAnchorInfoTaskReq) string {
	taskDesc := "导出全部的数据"
	if len(req.GetTtidList()) == 0 && len(req.GetUidList()) == 0 && req.GetBanStatus() != uint32(pb.BanStatusType_BanStatusNoValid) {
		if req.GetBanStatus() == uint32(pb.BanStatusType_BanStatusNo) {
			taskDesc = "导出直播间状态为“正常”的全部数据"
		}
		if req.GetBanStatus() == uint32(pb.BanStatusType_BanStatusYes) {
			taskDesc = "导出直播间状态为“封禁”的全部数据"
		}
	}

	if req.GetBanStatus() == uint32(pb.BanStatusType_BanStatusNoValid) {
		if len(req.GetTtidList()) == 1 {
			taskDesc = fmt.Sprintf("导出TTID%s的数据", req.GetTtidList()[0])
		}
		if len(req.GetUidList()) == 1 {
			taskDesc = fmt.Sprintf("导出UID%d的数据", req.GetUidList()[0])
		}

		if len(req.GetTtidList()) > 1 {
			taskDesc = fmt.Sprintf("导出TTID%s等的数据", req.GetTtidList()[0])
		}

		if len(req.GetUidList()) > 1 {
			taskDesc = fmt.Sprintf("导出UID%d等的数据", req.GetUidList()[0])
		}
	}

	if req.GetBanStatus() == uint32(pb.BanStatusType_BanStatusNo) {
		if len(req.GetTtidList()) == 1 {
			taskDesc = fmt.Sprintf("导出TTID%s和直播间封禁状态为“正常”的数据", req.GetTtidList()[0])
		}
		if len(req.GetUidList()) == 1 {
			taskDesc = fmt.Sprintf("导出UID%d和直播间封禁状态为“正常”的数据", req.GetUidList()[0])
		}

		if len(req.GetTtidList()) > 1 {
			taskDesc = fmt.Sprintf("导出TTID%s等和直播间封禁状态为“正常”的数据", req.GetTtidList()[0])
		}

		if len(req.GetUidList()) > 1 {
			taskDesc = fmt.Sprintf("导出UID%d等和直播间封禁状态为“正常”的数据", req.GetUidList()[0])
		}
	}

	if req.GetBanStatus() == uint32(pb.BanStatusType_BanStatusYes) {
		if len(req.GetTtidList()) == 1 {
			taskDesc = fmt.Sprintf("导出TTID%s和直播间封禁状态为“封禁”的数据", req.GetTtidList()[0])
		}
		if len(req.GetUidList()) == 1 {
			taskDesc = fmt.Sprintf("导出UID%d和直播间封禁状态为“封禁”的数据", req.GetUidList()[0])
		}

		if len(req.GetTtidList()) > 1 {
			taskDesc = fmt.Sprintf("导出TTID%s等和直播间封禁状态为“封禁”的数据", req.GetTtidList()[0])
		}

		if len(req.GetUidList()) > 1 {
			taskDesc = fmt.Sprintf("导出UID%d等和直播间封禁状态为“封禁”的数据", req.GetUidList()[0])
		}
	}

	return taskDesc
}

func (mgr *AsyncExportTaskMgr) CreateQueryAnchorInfoTask(ctx context.Context, req *pb.QueryAnchorInfoTaskReq) error {
	params, err := proto.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "proto.Marshal err=%v", err)
		return err
	}

	taskDesc := genQueryAnchorInfoTaskDesc(req)

	_, err = mgr.store.InsertTask(ctx, taskDesc, uint32(pb.TaskType_QueryAnchorInfo), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "InsertTask err=%v", err)
		return err
	}

	log.DebugWithCtx(ctx, "CreateQueryAnchorInfoTask end req:%v taskDesc:%s", req, taskDesc)
	return nil
}

func (mgr *AsyncExportTaskMgr) QueryPgcPrepareBackupInfo(ctx context.Context, req *pb.QueryPgcPrepareBackupInfoReq) error {
	params, err := proto.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryPgcPrepareBackupInfo proto.Marshal req:%v err=%v", req, err)
		return err
	}

	taskDesc := "推荐库备份数据"

	_, err = mgr.store.InsertTask(ctx, taskDesc, uint32(pb.TaskType_QueryPrepareList), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryPgcPrepareBackupInfo InsertTask req:%v err=%v", req, err)
		return err
	}

	log.DebugWithCtx(ctx, "QueryPgcPrepareBackupInfo end req:%v taskDesc:%s", req, taskDesc)
	return nil
}

func genQueryAnchorScoreListTaskDesc(req *pb.QueryAnchorScoreListReq) string {
	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	endTm := time.Unix(int64(req.GetEndTs()), 0)

	taskDesc := fmt.Sprintf("导出日期为%s-%s的", beginTm.Format(layOut), endTm.Format(layOut))

	if len(req.GetUidList()) != 0 || len(req.GetTtidList()) != 0 {
		if len(req.GetUidList()) != 0 {
			taskDesc += fmt.Sprintf("UID%d等数据", req.GetUidList()[0])
		}
		if len(req.GetTtidList()) != 0 {
			taskDesc += fmt.Sprintf("TTID%s等数据", req.GetTtidList()[0])
		}
	} else {
		taskDesc += "全部数据"
	}

	return taskDesc
}

func (mgr *AsyncExportTaskMgr) QueryAnchorScoreList(ctx context.Context, req *pb.QueryAnchorScoreListReq) error {
	params, err := proto.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryAnchorScoreList proto.Marshal req:%v err=%v", req, err)
		return err
	}

	beginTm := time.Unix(int64(req.GetBeginTs()), 0)
	if beginTm.Day() != 1 {
		log.ErrorWithCtx(ctx, "QueryAnchorScoreList invalid req req:%v", req)
		return protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
	}

	taskDesc := genQueryAnchorScoreListTaskDesc(req)

	_, err = mgr.store.InsertTask(ctx, taskDesc, uint32(pb.TaskType_QueryAnchorScore), params)
	if err != nil {
		log.ErrorWithCtx(ctx, "QueryAnchorScoreList InsertTask req:%v err=%v", req, err)
		return err
	}

	log.DebugWithCtx(ctx, "QueryAnchorScoreList end req:%v taskDesc:%s", req, taskDesc)
	return nil
}
