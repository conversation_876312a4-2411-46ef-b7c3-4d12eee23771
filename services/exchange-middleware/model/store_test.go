package model

import (
    "github.com/jmoiron/sqlx"
    "golang.52tt.com/pkg/config"
    "testing"
)

var mysqlStore *Store

func init() {
    var sc = config.EmptyServerConfig()
    err := sc.InitWithPath("json", "../exchange-middleware.json")
    if err != nil {
        panic(err)
    }
    mysqlConfig := new(config.MysqlConfig)
    mysqlConfig.Read(sc.Configer, "mysql")
    db, err := sqlx.Connect("mysql", mysqlConfig.ConnectionString())
    if err != nil {
        panic(err)
    }
    db.SetMaxOpenConns(mysqlConfig.MaxOpenConns)
    db.SetMaxIdleConns(mysqlConfig.MaxIdleConns)
    mysqlStore = NewStore(db)
}

func TestStore_Init(t *testing.T) {
    t.Log(mysqlStore.Init())
}

func TestStore_Close(t *testing.T) {
    mysqlStore.Close()
}
