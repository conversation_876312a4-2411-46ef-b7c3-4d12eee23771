syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/monkey";
package monkey;


service Monkey {
  rpc SendMsgToChatGroup (SendMsgToChatGroupReq) returns (SendMsgToChatGroupRsp) {}
  rpc ReportMonitor (ReportMonitorReq) returns (ReportMonitorResp) {}
  rpc TestMonitorReg (TestMonitorRegReq) returns (TestMonitorRegResp) {}
  rpc TestMonitorList (TestMonitorListReq) returns (TestMonitorListResp) {}
  rpc TestMonitorDel (TestMonitorDelReq) returns (TestMonitorDelResp) {}
  rpc SendCardMsgToChatGroup(SendCardMsgToChatGroupReq)returns(SendCardMsgToChatGroupResp){}
}

message SendMsgToChatGroupReq{
  string msg = 1;
  string chat_id = 2;
  string source = 3;
}
message SendMsgToChatGroupRsp{
}

message ReportMonitorReq{
  string key = 1;
  string user_openid = 2;
  repeated float rate = 3;
}
message ReportMonitorResp{
}


message TestMonitorRegReq{
  string keys = 1;
}
message TestMonitorRegResp{
}
message TestMonitorListReq{
}
message TestMonitorListResp{
}
message TestMonitorDelReq{
  string keys = 1;
}
message TestMonitorDelResp{
}
enum HeatStatus {
  NoStatus=0; //无状态
  StartHeat=1;      //开始加热
  OffHeat=2;     //结束加热
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message SendCardMsgToChatGroupReq{
  uint32 heat_status=1;
  string  text=2;
  uint32  channel_id=3;
  string  channel_name=4;
  uint32  channel_total_num=5;
  string  nick_name=6;
  uint32  heat_time=7;
  uint32 Tab_id=8;
}
message SendCardMsgToChatGroupResp{


}