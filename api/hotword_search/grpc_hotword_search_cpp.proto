syntax = "proto3";
package ga.api.hotword_search;



import "api/extension/extension.proto";
import "hotwordsearch/hotwordsearch_.proto";

//server_name=hotwordsearchlogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/hotword_search;hotword_search";
option objc_class_prefix = "RPC";

service HotWordSearchLogic {
    rpc FetchHotWordGroups(ga.hotwordsearch.HotWordSearchFetchReq) returns (ga.hotwordsearch.HotWordSearchFetchResp) {
        option (ga.api.extension.command) = {
            id: 5067
        };
    }

    option (ga.api.extension.logic_service_name) = "hotwordsearchlogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
