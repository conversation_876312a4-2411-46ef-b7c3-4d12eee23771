package channeldatinggame

import (
	"context"
	"strconv"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"

	tracingGrpc "golang.52tt.com/pkg/tracing/grpc"

	pb "golang.52tt.com/protocol/services/channel-dating-game"
)

const (
	serviceName = "channel-dating-game"
)

type Client struct {
	client.BaseClient
}

func (c *Client) TestDrawImage(ctx context.Context, req *pb.TestDrawImageReq) (*pb.TestDrawImageResp, error) {
	return c.typedStub().TestDrawImage(ctx, req)
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewChannelDatingGameClient(cc)
		}, dopts...),
	}
}

func (c *Client) typedStub() pb.ChannelDatingGameClient { return c.Stub().(pb.ChannelDatingGameClient) }

func (c *Client) UserApplyMic(ctx context.Context, channelId, uid uint32, cancel bool) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(0)))
	_, err := c.typedStub().UserApplyMic(ctx, &pb.UserApplyMicReq{
		ChannelId: channelId,
		Uid:       uid,
		IsCancel:  cancel,
	})
	return protocol.ToServerError(err)
}

func (c *Client) GetGamePhase(ctx context.Context, channelId uint32) (uint32, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(0)))
	resp, err := c.typedStub().GetGamePhase(ctx, &pb.GetGamePhaseReq{
		ChannelId: channelId,
	})
	if err != nil {
		return 0, protocol.ToServerError(err)
	}
	return resp.GetCurrPhase(), nil
}

func (c *Client) CheckDatingGameEntry(ctx context.Context, cid uint32) (bool, uint32, error) {
	resp, err := c.typedStub().CheckDatingGameEntry(ctx, &pb.CheckDatingGameEntryReq{ChannelId: cid})
	if err != nil {
		return false, 0, protocol.ToServerError(err)
	}

	return resp.IsOpen, resp.Level, nil
}

func (c *Client) GetDatingGameCurInfo(ctx context.Context, cid uint32) (*pb.GetDatingGameCurInfoResp, error) {
	resp, err := c.typedStub().GetDatingGameCurInfo(ctx, &pb.GetDatingGameCurInfoReq{ChannelId: cid})
	if err != nil {
		return resp, protocol.ToServerError(err)
	}
	return resp, nil
}
