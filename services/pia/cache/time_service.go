package cache

import (
	"context"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	"golang.52tt.com/services/pia/mgr"
)

type timeService struct {
	redisCli  redis.CmdableV2
	localTime time.Time
	err       error
}

func NewTimeService(redisCli redis.CmdableV2) mgr.TimeService {
	return &timeService{
		redisCli: redisCli,
	}
}

func (t *timeService) CurrentTime(ctx context.Context) (time.Time, error) {
	//if t.err != nil {
	//	return time.Time{}, t.err
	//}
	//return t.localTime, nil
	return t.redisCli.Time(ctx).Result()
}

func (t *timeService) Correction(ctx context.Context) error {
	log.DebugWithCtx(ctx, "[时间校准]开始时间校准")
	currentTime, err := t.redisCli.Time(ctx).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "[时间校准]获取当前时间失败：%v", err)
		t.err = err
		return err
	}
	t.localTime = currentTime
	t.err = nil
	log.DebugWithCtx(ctx, "[时间校准]时间校准完成, localTime:%v", t.localTime)
	return nil
}
