// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/present_illustration/grpc_present_illustration.proto

package present_illustration // import "golang.52tt.com/protocol/app/api/present_illustration"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import present_illustration_logic "golang.52tt.com/protocol/app/present_illustration_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentIllustrationLogicClient is the client API for PresentIllustrationLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentIllustrationLogicClient interface {
	// 获取图鉴摘要
	GetIllustrationSummary(ctx context.Context, in *present_illustration_logic.GetIllustrationSummaryReq, opts ...grpc.CallOption) (*present_illustration_logic.GetIllustrationSummaryResp, error)
	// 获取图鉴列表
	GetIllustrationList(ctx context.Context, in *present_illustration_logic.GetIllustrationListReq, opts ...grpc.CallOption) (*present_illustration_logic.GetIllustrationListResp, error)
	// 获取图鉴详情
	GetIllustrationDetail(ctx context.Context, in *present_illustration_logic.GetIllustrationDetailReq, opts ...grpc.CallOption) (*present_illustration_logic.GetIllustrationDetailResp, error)
}

type presentIllustrationLogicClient struct {
	cc *grpc.ClientConn
}

func NewPresentIllustrationLogicClient(cc *grpc.ClientConn) PresentIllustrationLogicClient {
	return &presentIllustrationLogicClient{cc}
}

func (c *presentIllustrationLogicClient) GetIllustrationSummary(ctx context.Context, in *present_illustration_logic.GetIllustrationSummaryReq, opts ...grpc.CallOption) (*present_illustration_logic.GetIllustrationSummaryResp, error) {
	out := new(present_illustration_logic.GetIllustrationSummaryResp)
	err := c.cc.Invoke(ctx, "/ga.api.present_illustration.PresentIllustrationLogic/GetIllustrationSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationLogicClient) GetIllustrationList(ctx context.Context, in *present_illustration_logic.GetIllustrationListReq, opts ...grpc.CallOption) (*present_illustration_logic.GetIllustrationListResp, error) {
	out := new(present_illustration_logic.GetIllustrationListResp)
	err := c.cc.Invoke(ctx, "/ga.api.present_illustration.PresentIllustrationLogic/GetIllustrationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentIllustrationLogicClient) GetIllustrationDetail(ctx context.Context, in *present_illustration_logic.GetIllustrationDetailReq, opts ...grpc.CallOption) (*present_illustration_logic.GetIllustrationDetailResp, error) {
	out := new(present_illustration_logic.GetIllustrationDetailResp)
	err := c.cc.Invoke(ctx, "/ga.api.present_illustration.PresentIllustrationLogic/GetIllustrationDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentIllustrationLogicServer is the server API for PresentIllustrationLogic service.
type PresentIllustrationLogicServer interface {
	// 获取图鉴摘要
	GetIllustrationSummary(context.Context, *present_illustration_logic.GetIllustrationSummaryReq) (*present_illustration_logic.GetIllustrationSummaryResp, error)
	// 获取图鉴列表
	GetIllustrationList(context.Context, *present_illustration_logic.GetIllustrationListReq) (*present_illustration_logic.GetIllustrationListResp, error)
	// 获取图鉴详情
	GetIllustrationDetail(context.Context, *present_illustration_logic.GetIllustrationDetailReq) (*present_illustration_logic.GetIllustrationDetailResp, error)
}

func RegisterPresentIllustrationLogicServer(s *grpc.Server, srv PresentIllustrationLogicServer) {
	s.RegisterService(&_PresentIllustrationLogic_serviceDesc, srv)
}

func _PresentIllustrationLogic_GetIllustrationSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_illustration_logic.GetIllustrationSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationLogicServer).GetIllustrationSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_illustration.PresentIllustrationLogic/GetIllustrationSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationLogicServer).GetIllustrationSummary(ctx, req.(*present_illustration_logic.GetIllustrationSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustrationLogic_GetIllustrationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_illustration_logic.GetIllustrationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationLogicServer).GetIllustrationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_illustration.PresentIllustrationLogic/GetIllustrationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationLogicServer).GetIllustrationList(ctx, req.(*present_illustration_logic.GetIllustrationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentIllustrationLogic_GetIllustrationDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(present_illustration_logic.GetIllustrationDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentIllustrationLogicServer).GetIllustrationDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.present_illustration.PresentIllustrationLogic/GetIllustrationDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentIllustrationLogicServer).GetIllustrationDetail(ctx, req.(*present_illustration_logic.GetIllustrationDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentIllustrationLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.present_illustration.PresentIllustrationLogic",
	HandlerType: (*PresentIllustrationLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetIllustrationSummary",
			Handler:    _PresentIllustrationLogic_GetIllustrationSummary_Handler,
		},
		{
			MethodName: "GetIllustrationList",
			Handler:    _PresentIllustrationLogic_GetIllustrationList_Handler,
		},
		{
			MethodName: "GetIllustrationDetail",
			Handler:    _PresentIllustrationLogic_GetIllustrationDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/present_illustration/grpc_present_illustration.proto",
}

func init() {
	proto.RegisterFile("api/present_illustration/grpc_present_illustration.proto", fileDescriptor_grpc_present_illustration_f76835c22c1420f3)
}

var fileDescriptor_grpc_present_illustration_f76835c22c1420f3 = []byte{
	// 322 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x93, 0xbf, 0x4b, 0xfc, 0x30,
	0x00, 0xc5, 0xe9, 0xf5, 0xf8, 0x72, 0x64, 0xcc, 0x17, 0x0f, 0xa9, 0x08, 0xc5, 0xfd, 0x12, 0x38,
	0x39, 0xad, 0xb8, 0xa9, 0xe0, 0x0f, 0x6e, 0x38, 0xce, 0xcd, 0xa5, 0xc4, 0x12, 0x42, 0x24, 0x6d,
	0x72, 0x6d, 0x8a, 0xba, 0x95, 0x1b, 0x1d, 0x3a, 0x88, 0xe0, 0xee, 0xe0, 0xaa, 0xfe, 0x87, 0x92,
	0x46, 0xca, 0x79, 0xb4, 0xc2, 0x75, 0x0b, 0x79, 0xef, 0x7d, 0xf2, 0x86, 0x17, 0x10, 0x10, 0xc5,
	0xb1, 0x4a, 0x69, 0x46, 0x13, 0x1d, 0x72, 0x21, 0xf2, 0x4c, 0xa7, 0x44, 0x73, 0x99, 0x60, 0x96,
	0xaa, 0x28, 0x6c, 0x52, 0x90, 0x4a, 0xa5, 0x96, 0x70, 0x87, 0x11, 0x44, 0x14, 0x47, 0x4d, 0x16,
	0xef, 0xb8, 0xe9, 0x36, 0x14, 0x92, 0xf1, 0x08, 0xb7, 0x4b, 0x96, 0xec, 0xed, 0x9a, 0x4e, 0xf4,
	0x41, 0xd3, 0x24, 0x33, 0x45, 0xea, 0x93, 0x95, 0xc7, 0xef, 0x7d, 0xb0, 0x3d, 0xb3, 0x8c, 0xcb,
	0x15, 0xc4, 0xd4, 0x10, 0xe0, 0xab, 0x03, 0x86, 0xe7, 0xf4, 0x97, 0x70, 0x9d, 0xc7, 0x31, 0x49,
	0x1f, 0x61, 0x80, 0x18, 0x41, 0x7f, 0x3c, 0xde, 0x1c, 0x9b, 0xd3, 0x85, 0x77, 0xd4, 0x31, 0x99,
	0xa9, 0xbd, 0xc1, 0xb2, 0xf0, 0xfb, 0x83, 0x8f, 0xd2, 0x85, 0xa5, 0x03, 0xfe, 0xaf, 0x19, 0xa7,
	0x3c, 0xd3, 0x70, 0xb2, 0x19, 0xdc, 0x64, 0x4c, 0xa7, 0x83, 0x2e, 0xb1, 0xba, 0xd0, 0x67, 0xe9,
	0xc2, 0x17, 0x07, 0x6c, 0xad, 0xb9, 0xce, 0xa8, 0x26, 0x5c, 0xc0, 0xc3, 0xcd, 0xd8, 0x36, 0x65,
	0x4a, 0x05, 0xdd, 0x82, 0x75, 0xad, 0xaf, 0xd2, 0xf5, 0x2e, 0x96, 0x85, 0xef, 0xfd, 0x30, 0x46,
	0xab, 0x8c, 0x51, 0xc5, 0x78, 0x2a, 0xfc, 0x1e, 0x93, 0xcf, 0x85, 0xef, 0x63, 0xcb, 0x6c, 0x9b,
	0x02, 0x3e, 0xb9, 0x03, 0xc3, 0x48, 0xc6, 0x68, 0x91, 0xdf, 0x93, 0x04, 0x69, 0x6d, 0xe7, 0x63,
	0x36, 0x7b, 0x73, 0xc5, 0xa4, 0x20, 0x09, 0x43, 0x93, 0xb1, 0xd6, 0x28, 0x92, 0x31, 0xae, 0xa4,
	0x48, 0x0a, 0x4c, 0x94, 0xc2, 0x6d, 0x5f, 0xa2, 0x71, 0xd4, 0x6f, 0x3d, 0x77, 0x3e, 0x3b, 0xbd,
	0xfd, 0x57, 0x11, 0xf6, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0xdf, 0x0c, 0x52, 0x94, 0x50, 0x03,
	0x00, 0x00,
}
