package mgr

import (
    "context"
    "testing"

    "github.com/golang/mock/gomock"
    perfectMatch "golang.52tt.com/protocol/services/perfect-match"
    "golang.52tt.com/protocol/services/userpresent"
    "golang.52tt.com/services/tt-rev/perfect-match/internal/cache"
)

func TestMgr_GetPerfectMatchEntry(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    mgr := newTestMgr(ctrl)
    type args struct {
        ctx context.Context
        uid uint32
    }
    tests := []struct {
        name    string
        args    args
        want    *perfectMatch.GetPerfectMatchEntryResp
        wantErr bool
    }{
        {
            name: "",
            args: args{
                ctx: ctx,
                uid: uid,
            },
            want:    nil,
            wantErr: false,
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mockBc.EXPECT().CheckInBlackList(gomock.Any()).Return(false).AnyTimes()
            mockBc.EXPECT().CheckInWhiteList(gomock.Any()).Return(true).AnyTimes()
            mockBc.EXPECT().GetOnceMatchCostTBean().Return(uint32(100)).AnyTimes()
            mockBc.EXPECT().GetMatchGiveGiftId().Return(uint32(100)).AnyTimes()
            mockPresentCli.EXPECT().GetPresentConfigById(gomock.Any(), gomock.Any()).Return(
                &userpresent.GetPresentConfigByIdResp{
                    ItemConfig: &userpresent.StPresentItemConfig{
                        ItemId:  uint32(100),
                        Name:    "test",
                        IconUrl: "test",
                    },
                },
                nil,
            ).AnyTimes()
            mockCache.EXPECT().GetMatchInfo(gomock.Any(), gomock.Any()).Return(&cache.MatchInfo{
                Uid:       uid,
                Phrase:    1,
                Sex:       1,
                ChannelId: cid,
                MinWait:   60,
                MaxWait:   300,
            }, nil).AnyTimes()

            mockCache.EXPECT().GetCancelMatchCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(uint32(1), nil).AnyTimes()
            mockBc.EXPECT().GetMaxCancelMatchCount().Return(uint32(10)).AnyTimes()
            mockBc.EXPECT().GetMaxRefreshQuestionCount().Return(uint32(10)).AnyTimes()
            mockBc.EXPECT().GetHeartbeatInterval().Return(uint32(10)).AnyTimes()
            mockBc.EXPECT().GetEntrySwitch().Return(true)
            mockBc.EXPECT().IsCheckUserGroupTag().Return(false)
            mockBc.EXPECT().IsCheckRiskUser().Return(false)
            mockBc.EXPECT().IsFreeMatchTime().Return(false)
            _, err := mgr.GetPerfectMatchEntry(tt.args.ctx, tt.args.uid)
            if (err != nil) != tt.wantErr {
                t.Errorf("GetPerfectMatchEntry() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
        })
    }
}
