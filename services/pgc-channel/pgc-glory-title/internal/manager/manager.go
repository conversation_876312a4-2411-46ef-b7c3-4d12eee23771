package manager

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	"golang.52tt.com/clients/channel"
	channelMsgApi "golang.52tt.com/clients/channel-msg-api"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelol"
	"golang.52tt.com/clients/entertainmentrecommendback"
	im_api "golang.52tt.com/clients/im-api"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	push "golang.52tt.com/clients/push-notification/v2"
	reconcile_present "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	signanchorstats "golang.52tt.com/clients/sign-anchor-stats"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	"golang.52tt.com/pkg/protocol"
	channelPB "golang.52tt.com/protocol/app/channel"
	gaPush "golang.52tt.com/protocol/app/push"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/pgc-channel/pgc-glory-title/internal/cache"
	"golang.52tt.com/services/pgc-channel/pgc-glory-title/internal/conf"
	"golang.52tt.com/services/pgc-channel/pgc-glory-title/internal/model"
	"time"
)

type PgcGloryTitleManager struct {
	store    *model.Store
	rc       *cache.RedisCache
	dyConfig *conf.SDyConfigHandler
	//channelMsgApiCli              channelMsgApi.IClient
	anchorcontractCli             anchorcontract_go.IClient
	pushCli                       push.IClient
	entertainmentrecommendbackCli entertainmentrecommendback.IClient
	signanchorstatsCli            signanchorstats.IClient
	userprofileCli                userprofileapi.IClient
	processTimer                  *timer.Timer
	channelCli                    channel.IClient
	channelMicCli                 channelmic.IClient
	reconcilePresentCli           reconcile_present.IClient
	imClient                      im_api.IClient
	channelOlCli                  channelol.IClient
	channelMsgApiCli              channelMsgApi.IClient
}

func NewPgcGloryTitleManager(ctx context.Context, store *model.Store, rc *cache.RedisCache, dyConfig_ *conf.SDyConfigHandler) (*PgcGloryTitleManager, error) {
	mgr := &PgcGloryTitleManager{
		store:    store,
		rc:       rc,
		dyConfig: dyConfig_,
	}

	err := mgr.Init(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "mgr.Init err=%v", err)
		return nil, err
	}

	return mgr, nil
}

func (mgr *PgcGloryTitleManager) Init(ctx context.Context) error {
	err := mgr.store.Init()
	if err != nil {
		log.ErrorWithCtx(ctx, "store.Init err=%v", err)
		return err
	}

	err = mgr.store.CreateTitleMonthHistory(0)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateTitleMonthHistory err=%v", err)
		return err
	}

	err = mgr.store.CreateTitleConfig()
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateTitleConfig err=%v", err)
		return err
	}

	err = mgr.store.CreateTitleUserStatus()
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateTitleUserStatus err=%v", err)
		return err
	}

	//mgr.channelMsgApiCli, err = channelMsgApi.NewIClient()
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "channelMsgApi.NewIClient err=%v", err)
	//	return err
	//}

	pTimer, err := timer.NewTimerD(ctx, "pgc-glory-title")
	if err != nil {
		return err
	}
	mgr.processTimer = pTimer

	mgr.anchorcontractCli = anchorcontract_go.NewIClient()

	mgr.pushCli = push.NewIClient()

	mgr.entertainmentrecommendbackCli = entertainmentrecommendback.NewIClient()

	mgr.signanchorstatsCli = signanchorstats.NewIClient()

	mgr.userprofileCli = userprofileapi.NewIClient()
	mgr.channelCli = channel.NewIClient()

	mgr.channelMicCli = channelmic.NewIClient()

	mgr.reconcilePresentCli = reconcile_present.NewIClient()

	mgr.imClient = im_api.NewIClient()

	mgr.channelOlCli = channelol.NewIClient()

	mgr.channelMsgApiCli, _ = channelMsgApi.NewIClient()

	err = mgr.processTimer.AddTask("0 */5 * * * *", "checkTitleExpireLoop", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.checkTitleExpireLoop()
	}))
	if err != nil {
		log.ErrorWithCtx(ctx, "processTimer.AddTask err=%v", err)
		return err
	}

	err = mgr.processTimer.AddTask("0 1 0 1 * *", "TimerCalcInvertorMonthRank", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.TimerCalcInvertorMonthRank(false) //金主榜月结(1号0点1分)
	}))
	if err != nil {
		log.ErrorWithCtx(ctx, "processTimer.AddTask err=%v", err)
		return err
	}
	err = mgr.processTimer.AddTask("0 2 0 * * 1", "TimerCalcAnchorWeekRank", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.TimerCalcAnchorWeekRank(false) //成员榜周结(周一0点2分)
	}))
	if err != nil {
		log.ErrorWithCtx(ctx, "processTimer.AddTask err=%v", err)
		return err
	}
	err = mgr.processTimer.AddTask("0 30 3 * * *", "TimerRankCleanExpireData", timer.BuildFromLambda(func(ctx context.Context) {
		mgr.TimerRankCleanExpireData() //清理榜单过期数据(每天3点30分)
	}))
	if err != nil {
		log.ErrorWithCtx(ctx, "processTimer.AddTask err=%v", err)
		return err
	}
	mgr.processTimer.AddIntervalTask("TimerCheckRankReconcile", time.Second*20, timer.BuildFromLambda(func(ctx context.Context) {
		mgr.TimerCheckRankReconcile() //检查榜值是否一致
	}))

	mgr.processTimer.Start()

	go mgr.createNextMonthTable()

	log.InfoWithCtx(ctx, "PgcGloryTitleManager Init success")
	return nil
}

func (mgr *PgcGloryTitleManager) ShutDown() {
	mgr.store.Close()
}

// 房间广播消息
func (mgr *PgcGloryTitleManager) PushChannelMsg(ctx context.Context, cid uint32, msgType uint32, pbMsg []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  cid,
		Type:         msgType,
		PbOptContent: pbMsg,
	}
	channelMsgBin, _ := msg.Marshal()

	seq := uint32(time.Now().Unix())
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
		SeqId:   seq,
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.ErrorWithCtx(ctx, "PushChannelMsg Marshal cid:%v, err: %v", cid, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: seq,
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	multicastMap[uint64(cid)] = fmt.Sprintf("%d@channel", cid)

	err := mgr.pushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushChannelMsg fail to PushMulticasts cid:%d, err: %s", cid, err.Error())
		return err
	}
	log.InfoWithCtx(ctx, "PushChannelMsg cid=%d seq=%d", cid, seq)
	return nil
}
