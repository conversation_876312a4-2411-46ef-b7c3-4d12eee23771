syntax="proto3";

package ugc.celebrity;

option go_package = "golang.52tt.com/protocol/services/ugc/celebrity";

service UgcCelebrity {

    /* -------运营后台------- */

    // InsertCelebrity 用于添加一位优质用户。
    rpc InsertCelebrity ( InsertCelebrityReq ) returns ( InsertCelebrityResp );

    // UpdateCelebrity 用于修改某位优质用户的信息。
    rpc UpdateCelebrity ( UpdateCelebrityReq ) returns ( UpdateCelebrityResp );

    // SearchCelebrity 用于获取某位优质用户的信息。
    rpc SearchCelebrity ( SearchCelebrityReq ) returns ( SearchCelebrityResp );

    // DeleteCelebrities 用于删除一个或多个优质用户。
    rpc DeleteCelebrities ( DeleteCelebritiesReq ) returns ( DeleteCelebritiesResp );

    // CelebritiesForTT 用于获取所有的优质用户。
    rpc CelebritiesForTT ( CelebritiesForTTReq ) returns ( CelebritiesForTTResp );

    /* ---------APP--------- */

    // Celebrities 用于获取所有的优质用户。
    rpc Celebrities ( CelebritiesReq ) returns ( CelebritiesResp );

    // Users 用于获取所有特定类别的用户。
    rpc Users ( UsersReq ) returns ( UsersResp );
}

// Celebrity 用于记录优质用户的信息。
message Celebrity {
    uint32 uid = 1; // uid 代表优质用户的ID。
    string recommendation = 2; // recommendation 代表优质用户的推荐语。
}

message InsertCelebrityReq {
    Celebrity celebrity = 1; // celebrity 是所要添加的优质用户。
}

message InsertCelebrityResp {
    bool done = 1; // done 表示是否插入成功。
}

message CelebritiesReq {
    uint32 page = 1; // page 表示请求第几页。
    uint32 number = 2; // number 代表这一页中包含多少条。
    bool all = 3; // all 代表是否包含标记为deleted的数据。
}

message CelebritiesResp {
    repeated Celebrity celebrities = 1; // celebrities 包含了所有优质用户。
}

message UpdateCelebrityReq {
    Celebrity celebrity = 1; // celebrity 指定需要修改的优质用户。
}

message UpdateCelebrityResp {
    bool done = 1; // done 表示是否修改成功。
}

message SearchCelebrityReq {
    uint32 uid = 1; // uid 代表需要查找的优质用户的UID。
}

message SearchCelebrityResp {
    Celebrity celebrity = 1; // celebrity 为优质用户的信息。
}

message DeleteCelebritiesReq {
    repeated uint32 uids = 1; // uids 包含所有需要删除的优质用户的UID。
}

message DeleteCelebritiesResp {
    bool done = 1; // done 表示是否删除成功。
}

message CelebritiesForTTReq {
    uint32 offset = 1; // offset 表示跳过数量。
    uint32 limit = 2; // limit 表示取出数量。
}

message CelebritiesForTTResp {
    repeated Celebrity celebrities = 1; // celebrities 包含了所有优质用户。
    uint32 total = 2; // total 是优质用户的总数。
}

message UsersReq {
    uint32 offset = 1; // offset 表示跳过数量。
    uint32 limit = 2; // limit 表示取出数量。
    enum UserType {
        CELEBRATED  = 0; // 优质推荐用户
        PULLED      = 1; // 用于拉流的用户
    }
    UserType user_type = 3; // user_type 代表用户类型。
}

message UsersResp {
    repeated uint32 uids = 1; // uids 包含所有特定类别用户的UID。
}
