package handler

import (
	"context"
	"fmt"
	"time"

	"github.com/urfave/cli/v2"
	"golang.52tt.com/pkg/device_id"

	// errcode "golang.52tt.com/protocol/common/status"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/services/muti-cmd-tools/common"
)

func OnlineCommand() []*cli.Command {
	return []*cli.Command{
		{

			Name: "getOnlineLog", //命令名
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:    "uid",
					Aliases: []string{"u"},
					Usage:   "UID",
				},
				&cli.StringFlag{
					Name:    "days",
					Aliases: []string{"d"},
					Usage:   "看几天的历史",
				},
			},
			Usage:  "[用户]查询用户的在线历史日志，最多100条", //命令 介绍
			Action: getUserOnlineLog,         //命令触发方法
		},
		{

			Name: "getPres", //命令名
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:    "uid",
					Aliases: []string{"u"},
					Usage:   "UID",
				},
			},
			Usage:  "[用户]查询用户的在线", //命令 介绍
			Action: getUserPres,   //命令触发方法
		},
	}
}

func getUserOnlineLog(c *cli.Context) error {
	var err error
	uidParam := c.Uint64("uid")
	daysParam := c.Uint64("days")

	fmt.Printf("Debug. uid %d dayBefore %d\n", uidParam, daysParam)
	if uidParam == 0 || daysParam <= 0 || daysParam > 180 {
		err = fmt.Errorf("error, 参数范围非法")
		return err
	}

	olCli, errTmp := common.GetUserOLCli()
	if errTmp != nil {
		err = fmt.Errorf("GetUserOL failed=%v", errTmp)
		return err
	}
	ctx, _ := context.WithTimeout(context.TODO(), time.Second*3)

	// 查询时间范围 毫秒时间戳
	from := time.Now().Add(-time.Hour * 24 * time.Duration(daysParam)).UnixMilli()
	to := time.Now().UnixMilli()

	resp, errCli := olCli.GetUserOnlineLog(ctx, uint32(uidParam), uint64(from), uint64(to), 100)
	if errCli != nil {
		err = fmt.Errorf("GetUserOnlineLog failed=%v", errCli)
		return err
	}
	// 倒序遍历打印resp中的信息
	infoList := resp.GetInfoList()
	for idx := len(infoList) - 1; idx >= 0; idx-- {

		v := infoList[idx]

		var terminal protocol.TerminalType
		terminal.UnPack(v.GetTerminalType())

		//  GetOnlineAt是毫秒时间戳，转换为最小单位是秒的时间字符串
		onlineAtStr := time.UnixMilli(int64(v.GetOnlineAt())).Format("2006-01-02 15:04:05")
		offlineAtStr := time.UnixMilli(int64(v.GetOfflineAt())).Format("2006-01-02 15:04:05")

		// 打印信息
		var clientStr string
		if v.GetClientVersion() == 0 {
			clientStr = "nil"
		} else {
			clientStr = protocol.ClientVersion(v.GetClientVersion()).String()
		}

		fmt.Printf("[%d]. online %s -> %s | proxy %s:%d \n"+
			"clientIp:%v market:%s clientVersion:%s terminal:%s\n"+
			"dev %s \n\n",
			idx, onlineAtStr, offlineAtStr, v.GetProxyIp(), v.GetProxyPort(),
			v.GetClientIp(), protocol.TTMarkertID(v.GetMarketId()).String(), clientStr, terminal.StringDetail(),
			v.GetDeviceId())

	}
	fmt.Printf("===== END ===== \n")
	return nil
}

func getUserPres(c *cli.Context) error {
	var err error
	uidParam := c.Uint64("uid")

	fmt.Printf("Debug. uid=%d\n", uidParam)

	presenceV2Cli, errTmp := common.GetPresenceV2Cli()
	if errTmp != nil {
		err = fmt.Errorf("GetPresenceV2Cli failed=%v", errTmp)
		return err
	}
	ctx, _ := context.WithTimeout(context.TODO(), time.Second*3)

	presList, errCli := presenceV2Cli.GetUserPres(ctx, uint32(uidParam))
	if errCli != nil {
		err = fmt.Errorf("GetUserPres failed=%v", errCli)
		return err
	}
	// 遍历打印resp中的信息
	for idx, presInfo := range presList.GetInfoList() {

		var term protocol.TerminalType
		term.UnPack(presInfo.Val.Terminal)

		// 时间戳转换为字符串 UTC8
		OnlineAtStr := time.Unix(int64(presInfo.Val.OnlineTs), 0).Format("2006-01-02 15:04:05")

		fmt.Printf("[%d]. %s | proxy %s:%d  \n clientIp:%v terminal:%s devID: %s\n\n",
			idx, OnlineAtStr,
			common.HostUint32ToIPv4(presInfo.Key.ProxyIp), presInfo.Key.ProxyPort,
			common.HostUint32ToIPv4(presInfo.Val.ClientIp), term.StringDetail(), device_id.ToDeviceHexId(presInfo.Val.GetDeviceId(), false))

	}
	fmt.Printf("===== END ===== \n")
	return nil
}
