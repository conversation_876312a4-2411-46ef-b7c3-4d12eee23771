package internal

import (
    "context"
    "gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channelLiveMgr "golang.52tt.com/clients/channel-live-mgr"
    channellottery "golang.52tt.com/clients/channel-lottery"
    maskedPkLive "golang.52tt.com/clients/masked-pk-live"
    official_live_channel "golang.52tt.com/clients/official-live-channel"
    revenueExtGameClient "golang.52tt.com/clients/revenue-ext-game"
    riskMngApiClient "golang.52tt.com/clients/risk-mng-api"

    userprofileapi "golang.52tt.com/clients/user-profile-api"
    ukw "golang.52tt.com/clients/you-know-who"
    "golang.52tt.com/pkg/marketid_helper"
    "golang.52tt.com/pkg/protocol"
    protogrpc "golang.52tt.com/pkg/protocol/grpc"
    "golang.52tt.com/protocol/app"
    channelPB "golang.52tt.com/protocol/app/channel"
    pb "golang.52tt.com/protocol/app/revenue-ext-game-logic"
    "golang.52tt.com/protocol/common/status"
    channel_ext_game "golang.52tt.com/protocol/services/channel-ext-game"
    "golang.52tt.com/protocol/services/demo/echo"
    revenueExtGamePb "golang.52tt.com/protocol/services/revenue-ext-game"
    user_online "golang.52tt.com/protocol/services/user-online"
    "golang.52tt.com/services/tt-rev/revenue-ext-game-logic/internal/conf"
    "strconv"
    "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_go"
)

type StartConfig struct{}

type Server struct {
    userProfileCli    userprofileapi.IClient
    channelCli        channel_go.ChannelGoClient
    revenueExtGameCli revenueExtGameClient.IClient
    channelLotteryCli channellottery.IClient
    channelLiveCli    channelLiveMgr.IClient
    officialLiveCli   official_live_channel.IClient
    maskedPkCli       maskedPkLive.IClient
    ukwCli            ukw.IClient
    channelExtGameCli channel_ext_game.ChannelExtGameClient
    userOnlineCli     user_online.UserOnlineClient

    riskMngApiCli riskMngApiClient.IClient

    bc conf.IBusinessConfManager
}

func NewServer() (*Server, error) {
    userProfileCli, _ := userprofileapi.NewClient()
    channelCli := channel_go.MustNewClient(context.Background())
    revenueExtGameCli, _ := revenueExtGameClient.NewClient()
    channelLotteryCli, _ := channellottery.NewClient()
    channelLiveCli, _ := channelLiveMgr.NewClient()
    officialLiveCli, _ := official_live_channel.NewClient()
    maskedPkCli, _ := maskedPkLive.NewClient()
    ukwCli, _ := ukw.NewClient()
    channelExtGameCli, _ := channel_ext_game.NewClient(context.Background())
    userOnlineCli, _ := user_online.NewClient(context.Background())
    riskMngApiCli, _ := riskMngApiClient.NewClient()

    bc, _ := conf.NewBusinessConfManager()

    return &Server{
        userProfileCli:    userProfileCli,
        channelCli:        channelCli,
        revenueExtGameCli: revenueExtGameCli,
        channelLotteryCli: channelLotteryCli,
        channelLiveCli:    channelLiveCli,
        officialLiveCli:   officialLiveCli,
        maskedPkCli:       maskedPkCli,
        ukwCli:            ukwCli,
        channelExtGameCli: channelExtGameCli,
        riskMngApiCli:     riskMngApiCli,
        userOnlineCli:     userOnlineCli,
        bc:                bc,
    }, nil
}

func (svr *Server) Shutdown() {
    svr.revenueExtGameCli.Close()
    svr.channelLotteryCli.Close()
    svr.channelLiveCli.Close()
    svr.officialLiveCli.Close()
    svr.maskedPkCli.Close()
    svr.ukwCli.Close()
    log.Infof("RevenueExtGameLogic shutdown...")
}

func (svr *Server) Echo(_ context.Context, message *echo.StringMessage) (*echo.StringMessage, error) {
    return message, nil
}

// checkMount 检查是否能挂载
func (svr *Server) checkMount(ctx context.Context, channelId uint32) (uint32, error) {
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("checkMount ServiceInfoFromContext fail. channelId:%+v", channelId)
        return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    // 非PC端不能挂载游戏
    if svrInfo.ClientType != protocol.ClientTypePcTT {
        return 0, protocol.NewExactServerError(nil, status.ErrRevenueExtGameNotPermission, "开启互动玩法需要在PC端开播哦～")
    }

    channelResp, err := svr.channelCli.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
        ChannelId: channelId,
        OpUid:     opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkMount fail to GetChannelSimpleInfo. channelId:%d, opUid:%d, err:%v", channelId, opUid, err)
        return 0, err
    }

    checkFuncList := make([]CheckFunc, 0)

    switch channelPB.ChannelType(channelResp.GetChannelSimple().GetChannelType()) {
    case channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE:
        if channelResp.GetChannelSimple().GetCreaterUid() != opUid {
            return 0, protocol.NewExactServerError(nil, status.ErrRevenueExtGameNotPermission, "无权限开启互动玩法")
        }

        checkFuncList = append(checkFuncList,
            //svr.checkChannelLottery,
            svr.checkOfficialLive,
            svr.checkCommLivePk,
            svr.checkMaskedLivePk)

    default:
        return 0, protocol.NewExactServerError(nil, status.ErrRevenueExtGameNotPermission, "该房间类型不支持开启互动玩法")
    }

    if err := checkFuncHandle(ctx, channelId, opUid, checkFuncList); err != nil {
        log.ErrorWithCtx(ctx, "checkMount fail to checkFuncHandle. channelId:%d, opUid:%d, err:%v", channelId, opUid, err)
        return 0, err
    }

    return opUid, nil
}

// checkUnmount 检查是否能取消挂载
func (svr *Server) checkUnmount(ctx context.Context, channelId uint32) (uint32, error) {
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("checkUnmount ServiceInfoFromContext fail. channelId:%+v", channelId)
        return 0, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    // 非PC端不能操作
    if svrInfo.ClientType != protocol.ClientTypePcTT {
        return 0, protocol.NewExactServerError(nil, status.ErrRevenueExtGameNotPermission)
    }

    channelResp, err := svr.channelCli.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
        ChannelId: channelId,
        OpUid:     opUid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "checkMount fail to GetChannelSimpleInfo. channelId:%d, opUid:%d, err:%v", channelId, opUid, err)
        return 0, err
    }

    switch channelPB.ChannelType(channelResp.GetChannelSimple().GetChannelType()) {
    case channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE:
        if channelResp.GetChannelSimple().GetCreaterUid() != opUid {
            return 0, protocol.NewExactServerError(nil, status.ErrRevenueExtGameNotPermission)
        }

    default:
        return opUid, nil
    }

    return opUid, nil
}

func (svr *Server) MountExtGame(ctx context.Context, req *pb.MountExtGameRequest) (*pb.MountExtGameResponse, error) {
    out := &pb.MountExtGameResponse{}
    channelId := req.GetChannelId()

    if channelId == 0 {
        log.ErrorWithCtx(ctx, "MountExtGame req:%+v, err param", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid, err := svr.checkMount(ctx, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "MountExtGame fail to checkMount. req:%+v, err:%v", req, err)
        return out, err
    }

    svrResp, err := svr.revenueExtGameCli.MountExtGame(ctx, &revenueExtGamePb.MountExtGameReq{
        Uid:       opUid,
        ChannelId: channelId,
        GameType:  revenueExtGamePb.ExtGameType(req.GetGameType()),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "MountExtGame fail to MountExtGame. req:%+v, err:%v", req, err)
        return out, err
    }

    out.Serial = svrResp.GetSerial()

    log.InfoWithCtx(ctx, "MountExtGame req:%+v, resp:%+v", req, out)
    return out, nil
}

func (svr *Server) UnmountExtGame(ctx context.Context, req *pb.UnmountExtGameRequest) (*pb.UnmountExtGameResponse, error) {
    out := &pb.UnmountExtGameResponse{}
    channelId := req.GetChannelId()

    if channelId == 0 {
        log.ErrorWithCtx(ctx, "UnmountExtGame req:%+v, err param", req)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    opUid, err := svr.checkUnmount(ctx, channelId)
    if err != nil {
        log.ErrorWithCtx(ctx, "UnmountExtGame fail to checkUnmount. req:%+v, err:%v", req, err)
        return out, err
    }

    _, err = svr.revenueExtGameCli.UnmountExtGame(ctx, &revenueExtGamePb.UnmountExtGameReq{
        Uid:       opUid,
        ChannelId: channelId,
        GameType:  req.GetGameType(),
    })

    if err != nil {
        log.ErrorWithCtx(ctx, "UnmountExtGame fail to UnmountExtGame. req:%+v, err:%v", req, err)
        return out, err
    }

    log.InfoWithCtx(ctx, "UnmountExtGame req:%+v, resp:%+v", req, out)
    return out, nil
}

func transCfg2LogicPbCfg(cfg *revenueExtGamePb.ExtGameCfg) *pb.ExtGameCfg {
    if cfg == nil {
        return &pb.ExtGameCfg{}
    }

    out := &pb.ExtGameCfg{
        GameType:              uint32(cfg.GameType),
        Name:                  cfg.Name,
        Desc:                  cfg.Desc,
        PicUrl:                cfg.PicUrl,
        CmsUrl:                cfg.CmsUrl,
        ResourceDisplayEnable: cfg.ResourceDisplayEnable,
        GiftList:              make([]*pb.ExtGameGiftCfg, 0, len(cfg.GiftList)),
        RankType:              cfg.GetRankType(),
    }

    for _, giftCfg := range cfg.GiftList {
        out.GiftList = append(out.GiftList, &pb.ExtGameGiftCfg{
            GiftId: giftCfg.GiftId, Desc: giftCfg.Desc,
        })
    }

    return out
}

func transOpCfgLogicPb(cfg *revenueExtGamePb.ExtGameOpCfg) *pb.ExtGameOpCfg {
    out := &pb.ExtGameOpCfg{
        QuickGiftEnable: cfg.GetQuickGiftEnable(),
        CampButtonList:  make([]*pb.CampButtonCfg, 0),
    }

    if cfg == nil {
        return out
    }

    for _, campCfg := range cfg.GetCampButtonList() {
        out.CampButtonList = append(out.CampButtonList, &pb.CampButtonCfg{
            ButtonColor: campCfg.GetButtonColor(),
            ButtonText:  campCfg.GetButtonText(),
            JoinText:    campCfg.GetJoinText(),
        })
    }

    return out
}

func (svr *Server) GetExtGameCfgList(ctx context.Context, req *pb.GetExtGameCfgListRequest) (*pb.GetExtGameCfgListResponse, error) {
    out := &pb.GetExtGameCfgListResponse{
        ConfList: make([]*pb.ExtGameCfg, 0),
    }
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetExtGameCfgList ServiceInfoFromContext fail. channelId:%+v", req.GetChannelId())
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    cfgResp, err := svr.revenueExtGameCli.GetExtGameCfgList(ctx, &revenueExtGamePb.GetExtGameCfgListReq{
        Uid: opUid, ChannelId: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetExtGameCfgList fail to GetExtGameCfgList. req:%+v, err:%v", req, err)
        return out, err
    }

    for _, cfg := range cfgResp.GetConfList() {
        out.ConfList = append(out.ConfList, transCfg2LogicPbCfg(cfg))
    }

    return out, nil
}

func (svr *Server) ReportUserWantPlay(ctx context.Context, req *pb.ReportUserWantPlayRequest) (*pb.ReportUserWantPlayResponse, error) {
    out := &pb.ReportUserWantPlayResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("ReportUserWantPlay ServiceInfoFromContext fail. channelId:%+v", req.GetChannelId())
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    _, err := svr.revenueExtGameCli.ReportUserWantPlay(ctx, &revenueExtGamePb.ReportUserWantPlayReq{
        Uid:       opUid,
        ChannelId: req.GetChannelId(),
        GameType:  revenueExtGamePb.ExtGameType(req.GetGameType()),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "ReportUserWantPlay fail to ReportUserWantPlay. req:%+v, err:%v", req, err)
        return out, err
    }

    log.InfoWithCtx(ctx, "ReportUserWantPlay req:%+v, resp:%+v", req, out)
    return out, nil
}

func (svr *Server) GetUserExtGameInfo(ctx context.Context, req *pb.GetUserExtGameInfoRequest) (*pb.GetUserExtGameInfoResponse, error) {
    out := &pb.GetUserExtGameInfoResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetUserExtGameInfo ServiceInfoFromContext fail. channelId:%+v", req.GetChannelId())
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    resp, err := svr.revenueExtGameCli.GetUserExtGameInfo(ctx, &revenueExtGamePb.GetUserExtGameInfoReq{
        Uid: opUid, ChannelId: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetUserExtGameInfo fail to GetUserExtGameInfo. req:%+v, err:%v", req, err)
        return out, err
    }

    out.RoundId = resp.GetRoundId()
    out.UserCamp = resp.GetUserCamp()
    out.GameConf = transCfg2LogicPbCfg(resp.GetGameConf())
    out.OpConf = transOpCfgLogicPb(resp.GetOpConf())

    return out, nil
}

func (svr *Server) GetChannelExtGameAccess(ctx context.Context, req *pb.GetChannelExtGameAccessRequest) (*pb.GetChannelExtGameAccessResponse, error) {
    out := &pb.GetChannelExtGameAccessResponse{}
    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.Errorf("GetChannelExtGameAccess ServiceInfoFromContext fail. channelId:%+v", req.GetChannelId())
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    opUid := svrInfo.UserID

    virtualAnchor, err := svr.checkIfVirtualAnchor(ctx, req.GetChannelId(), opUid)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelExtGameAccess fail to checkIfVirtualAnchor. req:%+v, err:%v", req, err)
        return out, err
    }

    // 虚拟主播房间不给游戏权限
    if virtualAnchor {
        return out, nil
    }

    resp, err := svr.revenueExtGameCli.GetChannelExtGameAccessStatus(ctx, &revenueExtGamePb.GetChannelExtGameAccessStatusReq{
        Uid: opUid, ChannelId: req.GetChannelId(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetChannelExtGameAccess fail to GetChannelExtGameAccessStatus. req:%+v, err:%v", req, err)
        return out, err
    }

    out.HaveAccess = resp.GetAccessStatus()
    if out.GetHaveAccess() {
        out.StreamInfo, _ = GetStreamInfo(ctx)
    }

    return out, nil
}

func GetStreamInfo(ctx context.Context) (*pb.StreamInfo, error) {
    out := &pb.StreamInfo{}

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok {
        log.ErrorWithCtx(ctx, "GetStreamInfo fail to ServiceInfoFromContext ctx:%+v", ctx)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    streamTypeStr := marketid_helper.Get("revenue_ext_game_stream_type", svrInfo.MarketID, 0)
    val, err := strconv.ParseUint(streamTypeStr, 10, 32)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetStreamInfo fail to ParseUint ctx:%+v, err:%v", ctx, err)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    out.StreamType = uint32(val)
    out.PushStreamUrl = marketid_helper.Get("revenue_ext_game_stream_push", svrInfo.MarketID, 0)
    out.PullStreamUrl = marketid_helper.Get("revenue_ext_game_stream_pull", svrInfo.MarketID, 0)

    return out, nil
}

func (svr *Server) GetExtGameScoreRank(ctx context.Context, request *pb.GetExtGameScoreRankRequest) (*pb.GetExtGameScoreRankResponse, error) {
    out := &pb.GetExtGameScoreRankResponse{}
    rankList := make([]*pb.UserRankInfo, 0)
    log.DebugWithCtx(ctx, "GetExtGameScoreRank req:%+v", request)

    svrInfo, ok := protogrpc.ServiceInfoFromContext(ctx)
    if !ok || svrInfo.UserID == 0 {
        log.ErrorWithCtx(ctx, "GetStreamInfo fail to ServiceInfoFromContext ctx:%+v", ctx)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }
    uid := svrInfo.UserID
    fakeUid := uid

    if request.GetGameType() == uint32(pb.ExtGameType_EXT_GAME_TYPE_UNSPECIFIED) ||
        request.GetRankType() == uint32(pb.GetExtGameScoreRankRequest_RANK_TYPE_UNSPECIFIED) {
        log.ErrorWithCtx(ctx, "GetExtGameScoreRank fail to GetExtGameScoreRank,gameType or rankType is invalid. req:%+v,uid:%d", request, uid)
        return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid)
    }

    // 检查用户当前是否是神秘人
    isUkw := false
    curUkwInfo, e := svr.ukwCli.GetUKWInfo(ctx, uid)
    if e != nil {
        log.ErrorWithCtx(ctx, "GetExtGameScoreRank fail to GetUKWInfo. req:%+v, err:%v", request, e)
        return out, e
    }
    if curUkwInfo.GetUkwPersonInfo().GetLevel() != 0 {
        isUkw = true
        fakeUid = curUkwInfo.GetUkwPersonInfo().GetFakeUid() // 神秘人复写uid
    }

    svrResp, err := svr.revenueExtGameCli.GetExtGameScoreRank(ctx, &revenueExtGamePb.GetExtGameScoreRankReq{
        Uid:       uid,
        FakeUid:   fakeUid,
        IsUkw:     isUkw,
        GameType:  request.GetGameType(),
        RankType:  request.GetRankType(),
        PageToken: request.GetPageToken(),
        PageSize:  request.GetPageSize(),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetExtGameScoreRank fail to GetExtGameScoreRank. req:%+v, err:%v", request, err)
        return out, err
    }

    uidList := make([]uint32, 0)
    for _, v := range svrResp.GetRankList() {
        if v.GetUid() == 0 {
            continue
        }
        uidList = append(uidList, v.GetUid())
    }

    uidList = append(uidList, uid) // user自己
    userInfoMap, err := svr.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetExtGameScoreRank fail to BatchGetUserProfileV2. req:%+v, err:%v", request, err)
        return out, err
    }

    // 积分榜单
    for _, v := range svrResp.GetRankList() {
        info := &pb.UserRankInfo{
            Scores:    v.GetScores(),
            Rank:      v.GetRank(),
            NpUrlList: v.GetNameplateUrlList(),
        }

        if _, ok := userInfoMap[v.GetUid()]; ok {
            info.UserProfile = userInfoMap[v.GetUid()]

        } else if v.GetUkwInfo().GetFakeUid() != 0 {
            info.UserProfile = fillUkwProfile(v.GetUkwInfo())

        } else {
            log.ErrorWithCtx(ctx, "GetExtGameScoreRank get user info fail, req:%+v, v:%+v", request, v)
            continue
        }

        rankList = append(rankList, info)
    }

    // 用户吸底排名
    out.UserRank = &pb.UserRankInfo{
        Scores:    svrResp.GetUserRank().GetScores(),
        Rank:      svrResp.GetUserRank().GetRank(),
        NpUrlList: svrResp.GetUserRank().GetNameplateUrlList(),
    }
    if isUkw {
        out.UserRank.UserProfile = fillUkwProfile(svrResp.GetUserRank().GetUkwInfo())
    } else {
        out.UserRank.UserProfile = userInfoMap[uid]
    }

    out.RankName = svrResp.GetRankName()
    out.RankDesc = svrResp.GetRankDesc()
    out.RankDescHighLight = svrResp.GetRankDescHighLight()
    out.RankCmsUrlSuffix = svrResp.GetRankCmsUrlSuffix()

    out.RankList = rankList
    out.NextPageToken = svrResp.GetNextPageToken()
    log.DebugWithCtx(ctx, "GetExtGameScoreRank uid:%d, req:%+v, resp:%+v", uid, request, out)
    return out, nil
}

func fillUkwProfile(ukwInfo *revenueExtGamePb.UKWProfile) *app.UserProfile {
    userProfile := &app.UserProfile{
        Uid:      ukwInfo.GetFakeUid(),
        Account:  ukwInfo.GetAccount(),
        Nickname: ukwInfo.GetNickname(),
        //AccountAlias: "",
        //Sex:          ukwInfo.GetSex(),
        Privilege: &app.UserPrivilege{
            Account:  ukwInfo.GetAccount(),
            Nickname: ukwInfo.GetNickname(),
        },
        HeadImgMd5: ukwInfo.GetHeadFrame(),
    }

    userUkwInfo := &app.UserUKWInfo{
        Level:     ukwInfo.GetLevel(),
        Medal:     ukwInfo.GetMedal(),
        HeadFrame: ukwInfo.GetHeadFrame(),
    }

    byteUkwInfo, err := proto.Marshal(userUkwInfo)
    if err != nil {
        log.Errorf("fillCounterUserProfile json.Marshal failed info:%v err:%v", userUkwInfo, err)
        return userProfile
    }

    userProfile.Privilege.Type = uint32(app.EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW)
    userProfile.Privilege.Options = byteUkwInfo

    return userProfile
}

func (svr *Server) GetExtGameRankNameplate(ctx context.Context, req *pb.GetExtGameRankNameplateRequest) (*pb.GetExtGameRankNameplateResponse, error) {
    out := &pb.GetExtGameRankNameplateResponse{}
    log.DebugWithCtx(ctx, "GetExtGameRankNameplate req:%+v", req)

    if req.GetUid() == 0 || req.GetShowType() == uint32(pb.GetExtGameRankNameplateRequest_SHOW_TYPE_UNSPECIFIED) {
        return out, nil
    }

    svrResp, err := svr.revenueExtGameCli.GetExtGameRankNameplate(ctx, &revenueExtGamePb.GetExtGameRankNameplateReq{
        Uid:      req.GetUid(),
        ShowType: revenueExtGamePb.GetExtGameRankNameplateReq_ShowType(req.GetShowType()),
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "GetExtGameRankNameplate fail to GetExtGameRankNameplate. req:%+v, err:%v", req, err)
        return out, err
    }

    npList := make([]*pb.RankNameplateInfo, 0)
    for _, v := range svrResp.GetInfoList() {
        npList = append(npList, &pb.RankNameplateInfo{
            NpUrl: v,
        })
    }

    out.InfoList = npList
    return out, nil
}
