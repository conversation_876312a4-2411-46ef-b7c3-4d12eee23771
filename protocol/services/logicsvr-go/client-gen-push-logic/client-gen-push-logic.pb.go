// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/client-gen-push-logic/client-gen-push-logic.proto

package client_gen_push_logic // import "golang.52tt.com/protocol/services/logicsvr-go/client-gen-push-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import client_gen_push_logic "golang.52tt.com/protocol/app/client-gen-push-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ClientGenPushLogicClient is the client API for ClientGenPushLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ClientGenPushLogicClient interface {
	GA_GetGenPushSwitch(ctx context.Context, in *client_gen_push_logic.GA_GetGenPushSwitchReq, opts ...grpc.CallOption) (*client_gen_push_logic.GA_GetGenPushSwitchResp, error)
	GA_GetGenPushDoc(ctx context.Context, in *client_gen_push_logic.GA_GetGenPushDocReq, opts ...grpc.CallOption) (*client_gen_push_logic.GA_GetGenPushDocResp, error)
	GA_GetPushFactoryPrivateTemplate(ctx context.Context, in *client_gen_push_logic.GA_GetPushFactoryPrivateTemplateReq, opts ...grpc.CallOption) (*client_gen_push_logic.GA_GetPushFactoryPrivateTemplateResp, error)
	GetNewUserPushCntLimit(ctx context.Context, in *client_gen_push_logic.GetNewUserPushCntLimitReq, opts ...grpc.CallOption) (*client_gen_push_logic.GetNewUserPushCntLimitResp, error)
}

type clientGenPushLogicClient struct {
	cc *grpc.ClientConn
}

func NewClientGenPushLogicClient(cc *grpc.ClientConn) ClientGenPushLogicClient {
	return &clientGenPushLogicClient{cc}
}

func (c *clientGenPushLogicClient) GA_GetGenPushSwitch(ctx context.Context, in *client_gen_push_logic.GA_GetGenPushSwitchReq, opts ...grpc.CallOption) (*client_gen_push_logic.GA_GetGenPushSwitchResp, error) {
	out := new(client_gen_push_logic.GA_GetGenPushSwitchResp)
	err := c.cc.Invoke(ctx, "/logic.ClientGenPushLogic/GA_GetGenPushSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushLogicClient) GA_GetGenPushDoc(ctx context.Context, in *client_gen_push_logic.GA_GetGenPushDocReq, opts ...grpc.CallOption) (*client_gen_push_logic.GA_GetGenPushDocResp, error) {
	out := new(client_gen_push_logic.GA_GetGenPushDocResp)
	err := c.cc.Invoke(ctx, "/logic.ClientGenPushLogic/GA_GetGenPushDoc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushLogicClient) GA_GetPushFactoryPrivateTemplate(ctx context.Context, in *client_gen_push_logic.GA_GetPushFactoryPrivateTemplateReq, opts ...grpc.CallOption) (*client_gen_push_logic.GA_GetPushFactoryPrivateTemplateResp, error) {
	out := new(client_gen_push_logic.GA_GetPushFactoryPrivateTemplateResp)
	err := c.cc.Invoke(ctx, "/logic.ClientGenPushLogic/GA_GetPushFactoryPrivateTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clientGenPushLogicClient) GetNewUserPushCntLimit(ctx context.Context, in *client_gen_push_logic.GetNewUserPushCntLimitReq, opts ...grpc.CallOption) (*client_gen_push_logic.GetNewUserPushCntLimitResp, error) {
	out := new(client_gen_push_logic.GetNewUserPushCntLimitResp)
	err := c.cc.Invoke(ctx, "/logic.ClientGenPushLogic/GetNewUserPushCntLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClientGenPushLogicServer is the server API for ClientGenPushLogic service.
type ClientGenPushLogicServer interface {
	GA_GetGenPushSwitch(context.Context, *client_gen_push_logic.GA_GetGenPushSwitchReq) (*client_gen_push_logic.GA_GetGenPushSwitchResp, error)
	GA_GetGenPushDoc(context.Context, *client_gen_push_logic.GA_GetGenPushDocReq) (*client_gen_push_logic.GA_GetGenPushDocResp, error)
	GA_GetPushFactoryPrivateTemplate(context.Context, *client_gen_push_logic.GA_GetPushFactoryPrivateTemplateReq) (*client_gen_push_logic.GA_GetPushFactoryPrivateTemplateResp, error)
	GetNewUserPushCntLimit(context.Context, *client_gen_push_logic.GetNewUserPushCntLimitReq) (*client_gen_push_logic.GetNewUserPushCntLimitResp, error)
}

func RegisterClientGenPushLogicServer(s *grpc.Server, srv ClientGenPushLogicServer) {
	s.RegisterService(&_ClientGenPushLogic_serviceDesc, srv)
}

func _ClientGenPushLogic_GA_GetGenPushSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(client_gen_push_logic.GA_GetGenPushSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushLogicServer).GA_GetGenPushSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ClientGenPushLogic/GA_GetGenPushSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushLogicServer).GA_GetGenPushSwitch(ctx, req.(*client_gen_push_logic.GA_GetGenPushSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPushLogic_GA_GetGenPushDoc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(client_gen_push_logic.GA_GetGenPushDocReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushLogicServer).GA_GetGenPushDoc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ClientGenPushLogic/GA_GetGenPushDoc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushLogicServer).GA_GetGenPushDoc(ctx, req.(*client_gen_push_logic.GA_GetGenPushDocReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPushLogic_GA_GetPushFactoryPrivateTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(client_gen_push_logic.GA_GetPushFactoryPrivateTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushLogicServer).GA_GetPushFactoryPrivateTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ClientGenPushLogic/GA_GetPushFactoryPrivateTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushLogicServer).GA_GetPushFactoryPrivateTemplate(ctx, req.(*client_gen_push_logic.GA_GetPushFactoryPrivateTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClientGenPushLogic_GetNewUserPushCntLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(client_gen_push_logic.GetNewUserPushCntLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClientGenPushLogicServer).GetNewUserPushCntLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ClientGenPushLogic/GetNewUserPushCntLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClientGenPushLogicServer).GetNewUserPushCntLimit(ctx, req.(*client_gen_push_logic.GetNewUserPushCntLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ClientGenPushLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ClientGenPushLogic",
	HandlerType: (*ClientGenPushLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GA_GetGenPushSwitch",
			Handler:    _ClientGenPushLogic_GA_GetGenPushSwitch_Handler,
		},
		{
			MethodName: "GA_GetGenPushDoc",
			Handler:    _ClientGenPushLogic_GA_GetGenPushDoc_Handler,
		},
		{
			MethodName: "GA_GetPushFactoryPrivateTemplate",
			Handler:    _ClientGenPushLogic_GA_GetPushFactoryPrivateTemplate_Handler,
		},
		{
			MethodName: "GetNewUserPushCntLimit",
			Handler:    _ClientGenPushLogic_GetNewUserPushCntLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/client-gen-push-logic/client-gen-push-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/client-gen-push-logic/client-gen-push-logic.proto", fileDescriptor_client_gen_push_logic_3bef3c127cbf7e49)
}

var fileDescriptor_client_gen_push_logic_3bef3c127cbf7e49 = []byte{
	// 348 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x93, 0xbd, 0x4e, 0xf3, 0x30,
	0x14, 0x86, 0x15, 0xa9, 0xdf, 0x27, 0xf0, 0x84, 0x8c, 0x00, 0x29, 0x62, 0x40, 0x6c, 0x80, 0xe2,
	0x40, 0x11, 0x0b, 0x88, 0x01, 0x5a, 0xc8, 0x52, 0xa1, 0x8a, 0x9f, 0x85, 0xa5, 0x32, 0xd6, 0x91,
	0x6b, 0x29, 0xb5, 0x4d, 0xec, 0xa6, 0xea, 0xc4, 0xca, 0x95, 0x70, 0x0f, 0xbd, 0x8a, 0x5e, 0x02,
	0x33, 0x3f, 0x59, 0x18, 0x99, 0x90, 0x13, 0x55, 0x05, 0x94, 0x42, 0xcb, 0x14, 0xc5, 0xe7, 0x79,
	0x9f, 0xf3, 0x0e, 0x36, 0x3a, 0x8c, 0x15, 0x17, 0xcc, 0xa4, 0x49, 0xc0, 0x55, 0xc8, 0x62, 0x01,
	0xd2, 0x06, 0x1c, 0x64, 0xa0, 0xbb, 0xa6, 0x1d, 0xe4, 0xb3, 0xf2, 0x53, 0xa2, 0x13, 0x65, 0x15,
	0xfe, 0x97, 0xff, 0xf8, 0x1b, 0x9f, 0x2d, 0x9c, 0x5a, 0xe8, 0xd1, 0x7e, 0xa8, 0xb4, 0x15, 0x4a,
	0x9a, 0xd1, 0xb7, 0x48, 0xf8, 0x3e, 0xd5, 0xfa, 0xbb, 0xb2, 0x55, 0xcc, 0xaa, 0x8f, 0x15, 0x84,
	0x6b, 0xf9, 0x28, 0x02, 0xd9, 0xec, 0x9a, 0x76, 0xc3, 0x69, 0xf1, 0x1d, 0x5a, 0x8c, 0x8e, 0x5a,
	0x11, 0x8c, 0x4e, 0x2f, 0x7a, 0xc2, 0xb2, 0x36, 0x0e, 0x08, 0xa7, 0xa4, 0x30, 0x71, 0x90, 0xce,
	0x53, 0x34, 0x2b, 0x61, 0xcf, 0xe1, 0xd6, 0x27, 0xb3, 0xe0, 0x46, 0xaf, 0xcf, 0xbf, 0x0d, 0x07,
	0xa4, 0x32, 0xf7, 0x94, 0x79, 0x38, 0x45, 0x0b, 0x5f, 0xa8, 0xba, 0x62, 0x78, 0x73, 0x1a, 0x5d,
	0x5d, 0x31, 0xb7, 0x7a, 0x6b, 0x6a, 0x76, 0xbc, 0xf7, 0x39, 0xf3, 0xf0, 0x83, 0x87, 0xd6, 0x0a,
	0xc6, 0x01, 0xa7, 0x94, 0x59, 0x95, 0xf4, 0x9b, 0x89, 0x48, 0xa9, 0x85, 0x4b, 0xe8, 0xe8, 0x98,
	0x5a, 0xc0, 0xfb, 0x3f, 0xca, 0x27, 0x07, 0x5d, 0xb1, 0x83, 0x3f, 0x67, 0xc7, 0x45, 0x5f, 0x32,
	0x0f, 0xdf, 0x7b, 0x68, 0x39, 0x02, 0x7b, 0x06, 0xbd, 0x2b, 0x03, 0x89, 0xcb, 0xd5, 0xa4, 0x6d,
	0x88, 0x8e, 0xb0, 0x78, 0x7b, 0xd2, 0x8a, 0x52, 0xdc, 0x95, 0xda, 0x99, 0x31, 0x31, 0xae, 0xf2,
	0x9a, 0x79, 0xfe, 0xea, 0xfb, 0x70, 0x40, 0x56, 0xd0, 0x52, 0xe9, 0xad, 0x3d, 0x3e, 0xb9, 0xae,
	0x71, 0x15, 0x53, 0xc9, 0xc9, 0x5e, 0xd5, 0x5a, 0xc2, 0x54, 0x27, 0xcc, 0xaf, 0x1e, 0x53, 0x71,
	0x68, 0x20, 0x49, 0x05, 0x03, 0x13, 0xfe, 0xfa, 0x24, 0x6e, 0xfe, 0xe7, 0xa1, 0xdd, 0x8f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xf7, 0x33, 0xba, 0x27, 0x3e, 0x03, 0x00, 0x00,
}
