package cache

import (
    "context"
    "errors"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
    "golang.52tt.com/pkg/log"
    "strconv"
    "time"
)

const timeoutKey = "wedding_divorce_timeout"

func (c *Cache) AddDivideTimeout(ctx context.Context, uid uint32) (bool, error) {
    cnt, err := c.cmder.ZAdd(ctx, timeoutKey, &redis.Z{
        Score:  float64(time.Now().Unix()),
        Member: strconv.FormatUint(uint64(uid), 10),
    }).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "AddDivideTimeout error: %v, uid: %d", err, uid)
        return false, err
    }
    log.InfoWithCtx(ctx, "AddDivideTimeout success, uid: %d", uid)
    return cnt > 0, nil
}

func (c *Cache) GetMyDivideTimeout(ctx context.Context, uid uint32) (uint64, error) {
    res, err := c.cmder.ZScore(ctx, timeoutKey, strconv.FormatUint(uint64(uid), 10)).Result()
    if err != nil {
        if errors.Is(err, redis.Nil) {
            log.DebugWithCtx(ctx, "GetMyDivideTimeout not found, uid: %d", uid)
            return 0, nil
        }
        log.ErrorWithCtx(ctx, "GetMyDivideTimeout error: %v, uid: %d", err, uid)
        return 0, err
    }
    return uint64(res), nil
}

func (c *Cache) BatchGetUserDivorceTimeout(ctx context.Context, uidList []uint32) (map[uint32]uint64, error) {
    pipe := c.cmder.Pipeline()
    defer pipe.Close()
    var cmds []*redis.FloatCmd
    for _, uid := range uidList {
        cmds = append(cmds, pipe.ZScore(ctx, timeoutKey, strconv.FormatUint(uint64(uid), 10)))
    }
    _, err := pipe.Exec(ctx)
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserDivorceTimeout error: %v", err)
        return nil, err
    }
    res := make(map[uint32]uint64)
    for i, uid := range uidList {
        if cmds[i].Err() != nil {
            if errors.Is(cmds[i].Err(), redis.Nil) {
                continue
            }
            log.ErrorWithCtx(ctx, "BatchGetUserDivorceTimeout error: %v, uid: %d", cmds[i].Err(), uid)
            return nil, cmds[i].Err()
        }
        res[uid] = uint64(cmds[i].Val())
    }
    log.DebugWithCtx(ctx, "BatchGetUserDivorceTimeout success, res: %v", res)
    return res, nil
}

func (c *Cache) RemoveDivideTimeout(ctx context.Context, uid uint32) (bool, error) {
    res, err := c.cmder.ZRem(ctx, timeoutKey, strconv.FormatUint(uint64(uid), 10)).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "RemoveDivideTimeout error: %v, uid: %d", err, uid)
        return false, err
    }
    log.InfoWithCtx(ctx, "RemoveDivideTimeout success, uid: %d", uid)
    return res > 0, nil
}

func (c *Cache) GetDivideTimeout(ctx context.Context, timeOut int64) ([]uint32, error) {
    maxTimeStr := strconv.FormatInt(time.Now().Unix()-timeOut, 10)
    res, err := c.cmder.ZRangeByScore(ctx, timeoutKey, &redis.ZRangeBy{
        Min:    "-inf",
        Max:    maxTimeStr,
        Offset: 0,
        Count:  100,
    }).Result()
    if err != nil {
        log.ErrorWithCtx(ctx, "GetDivideTimeout error: %v", err)
        return nil, err
    }
    var uidList []uint32
    for _, v := range res {
        uid, err := strconv.ParseUint(v, 10, 32)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetDivideTimeout ParseUint error: %v", err)
            continue
        }
        uidList = append(uidList, uint32(uid))
    }
    log.DebugWithCtx(ctx, "GetDivideTimeout success, uidList: %v, maxTimeStr: %s", uidList, maxTimeStr)
    return uidList, nil
}
