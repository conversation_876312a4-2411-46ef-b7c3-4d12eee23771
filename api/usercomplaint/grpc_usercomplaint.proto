// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.usercomplaint;



import "usercomplaint_logic/user-complaint-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/usercomplaint;usercomplaint";

service UsercomplaintLogic {
    option (ga.api.extension.logic_service_name) = "user-complaint-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.usercomplaint.UsercomplaintLogic/";
    rpc GetUserComplaintEntry(ga.usercomplaint_logic.GetUserComplaintEntryReq) returns (ga.usercomplaint_logic.GetUserComplaintEntryResp) {
        option (ga.api.extension.command) = {
             id: 32006
        };
    }
}

