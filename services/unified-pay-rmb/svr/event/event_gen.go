// Code generated by quicksilver-cli. DO NOT EDIT.

package event

import (
	"github.com/google/wire"
	"golang.52tt.com/pkg/config"
)

var ProviderSetForEventGen = wire.NewSet(
	NewEvent,
	NewProcessorBuilders,
)

type Processor interface {
	Start() error
	Stop()
}

type Event struct {
	conf       *config.ServerConfig
	processors []Processor
}

func NewEvent(conf *config.ServerConfig, processorBuilders ...Processor) (*Event, func(), error) {
	e := &Event{
		conf: conf,
	}
	for _, processorBuilder := range processorBuilders {
		if err := e.RegisterEvent(processorBuilder); err != nil {
			return nil, nil, err
		}
	}
	return e, func() {
		e.Stop()
	}, nil
}

func (e *Event) Stop() {
	for _, item := range e.processors {
		item.Stop()
	}
}

func (e *Event) RegisterEvent(processor Processor) error {
	err := processor.Start()
	if err != nil {
		return err
	}
	e.processors = append(e.processors, processor)
	return nil
}

func NewProcessorBuilders() []Processor {
	return []Processor{}
}
