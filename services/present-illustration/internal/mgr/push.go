package mgr

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	apppb "golang.52tt.com/protocol/app"
	gaChannel "golang.52tt.com/protocol/app/channel"
	pblogic "golang.52tt.com/protocol/app/present_illustration_logic"
	pb "golang.52tt.com/protocol/services/present-illustration"
	"golang.52tt.com/services/present-illustration/common/dycfg"
	"time"
)

// 图鉴收集或达成消息推送
func (m *Manager) PushReachUpgradeMsg(ctx context.Context, uid, cid uint32, cfg *pb.IllustrationConfig, oldStatus, result, oldLv, newLv uint32) {
	if result == 0 || cid == 0 {
		return
	}
	if result == uint32(pblogic.PresentIllustrationPushMsgType_PRESENT_ILLUSTRATION_PUSH_MSG_TYPE_DIAMOND) && !m.dyConfig.Get().IsNeedPopWindow(newLv) {
		switch oldStatus {
		case uint32(pblogic.IllustrationCollectStatus_ILLUSTRATION_COLLECT_STATUS_COLLECTED): //已集齐->升级
			return
		case uint32(pblogic.IllustrationCollectStatus_ILLUSTRATION_COLLECT_STATUS_NOT_COLLECT): //未集齐->升级
			result = uint32(pblogic.PresentIllustrationPushMsgType_PRESENT_ILLUSTRATION_PUSH_MSG_TYPE_COLLECTED)
		default:
		}
	}

	msg := &pblogic.PresentIllustrationPushMsg{
		MsgType:          pblogic.PresentIllustrationPushMsgType(result),
		Uid:              uid,
		IllustrationId:   cfg.Id,
		IllustrationName: cfg.Name,
		IllustrationIcon: cfg.Icon,
	}
	isYkw, userName, err := m.clients.IsYkwOn(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushReachUpgradeMsg IsYKwOn uid:%d err:%v", uid, err)
		return
	}
	msg.IsYkw = isYkw
	msg.User, err = m.clients.GetUserProfile(ctx, uid, isYkw)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushReachUpgradeMsg GetUserProfile uid:%d err:%v", uid, err)
		return
	}
	if !isYkw {
		userName = msg.User.Nickname
	}

	dyCfg := m.dyConfig.Get()
	oldLvCfg := dyCfg.GetLevel(oldLv)
	newLvCfg := dyCfg.GetLevel(newLv)
	msg.OldLvRes = m.FillLvConfig(oldLvCfg)
	msg.NewLvRes = m.FillLvConfig(newLvCfg)
	switch pblogic.PresentIllustrationPushMsgType(result) {
	case pblogic.PresentIllustrationPushMsgType_PRESENT_ILLUSTRATION_PUSH_MSG_TYPE_COLLECTED:
		msg.MainPopMsg = fmt.Sprintf(dyCfg.CollectedPopMsg, "你", cfg.Name)
		msg.MainChannelMsg = fmt.Sprintf(dyCfg.CollectedChannelMsg, "你", cfg.Name)
		msg.CustomChannelMsg = fmt.Sprintf(dyCfg.CollectedChannelMsg, userName, cfg.Name)
		msg.VapResUrl = dyCfg.VapResource.Collected.Url
		msg.VapResMd5 = dyCfg.VapResource.Collected.Md5
	case pblogic.PresentIllustrationPushMsgType_PRESENT_ILLUSTRATION_PUSH_MSG_TYPE_DIAMOND:
		msg.MainPopMsg = fmt.Sprintf(dyCfg.UpgradePopMsg, "你", cfg.Name, newLvCfg.Name)
		msg.CustomPopMsg = fmt.Sprintf(dyCfg.UpgradePopMsg, userName, cfg.Name, newLvCfg.Name)
		msg.MainChannelMsg = fmt.Sprintf(dyCfg.UpgradeChannelMsg, "你", cfg.Name, newLvCfg.Name)
		msg.CustomChannelMsg = fmt.Sprintf(dyCfg.UpgradeChannelMsg, userName, cfg.Name, newLvCfg.Name)
		msg.VapResUrl = dyCfg.VapResource.Upgrade.Url
		msg.VapResMd5 = dyCfg.VapResource.Upgrade.Md5
		msg.VapExtJson = m.GenVapJson(oldLvCfg, newLvCfg, cfg.Icon)
		m.PushBreakingNews(ctx, msg.User, cfg, newLv)
	case pblogic.PresentIllustrationPushMsgType_PRESENT_ILLUSTRATION_PUSH_MSG_TYPE_STARTCOLLECT:
		msg.MainChannelMsg = fmt.Sprintf(dyCfg.StartChannelMsg, cfg.Name)
	default:
		return
	}

	msgBin, _ := proto.Marshal(msg)
	content := fmt.Sprintf("图鉴收集或升级(%s_%d)", cfg.Name, uid) //content要这样写,防止android过滤掉相同的
	err = m.clients.PushReliableChannelMsg(ctx, msgBin, content, uint32(gaChannel.ChannelMsgType_PRESENT_ILLUSTRATION_CHANNEL_MSG), cid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushReachUpgradeMsg channel im msg failed channel id:%d, err:%v", cid, err)
	} else {
		log.InfoWithCtx(ctx, "PushReachUpgradeMsg success chanelId:%v joinSubMsg:%+v", cid, msg)
	}
}

func (m *Manager) FillLvConfig(from *dycfg.LevelConf) *pblogic.IllustrationLevelResource {
	return &pblogic.IllustrationLevelResource{
		LevelId:             from.Lv,
		LevelName:           from.Name,
		BorderBackgroundUrl: from.BorderBackgroundUrl,
		VapResourceUrl:      from.VapResourceUrl,
		VapResourceMd5:      from.VapResourceMd5,
		IsHighest:           from.IsHighest,
	}
}

type VapData struct {
	Type    string `json:"type"`
	Content string `json:"content"`
	Key     string `json:"key"`
	Ext     string `json:"ext"`
}

type VapList struct {
	Vap []VapData `json:"vap"`
}

func (m *Manager) GenVapJson(oldLvCfg, newLvCfg *dycfg.LevelConf, url string) string {
	vaplist := &VapList{
		Vap: []VapData{
			{
				Type:    "img",
				Content: oldLvCfg.BorderBackgroundUrl,
				Key:     "img3",
				Ext:     "",
			},
			{
				Type:    "img",
				Content: newLvCfg.BorderBackgroundUrl,
				Key:     "img2",
				Ext:     "",
			},
			{
				Type:    "img",
				Content: url,
				Key:     "img1",
				Ext:     "",
			},
		},
	}
	jsonByte, err := json.Marshal(vaplist)
	if err != nil {
		log.Errorf("GenVapJson Marshal %++v err:%v", vaplist, err)
		return `{"vap":[]}`
	}
	return string(jsonByte)
}

// PushBreakingNews 推送钻卡升级全服公告
func (m *Manager) PushBreakingNews(ctx context.Context, user *apppb.UserProfile, cfg *pb.IllustrationConfig, newLv uint32) {
	dyCfg := m.dyConfig.Get()
	uid := user.GetUid()
	highest := dyCfg.GetHighestLevel()
	diamondPushCfg := dyCfg.GetDiamondBreakingNews()

	// 当前图鉴升级钻卡的全服公告
	if newLv != highest.Lv {
		return
	}

	// 获取房间
	cid, err := m.clients.GetUserChannelId(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushBreakingNews GetUserChannelId err:%v, uid:%d", err, uid)
	}

	log.InfoWithCtx(ctx, "PushBreakingNews push id:%d upgrade diamond breaking, uid:%d, cid:%d, newsId:%d, cfg:%+v",
		cfg.GetId(), uid, cid, diamondPushCfg.UpgradeDiamondNewsId, cfg)

	// breaking
	err = m.clients.PushUpgradeDiamondBreaking(ctx, user, diamondPushCfg.UpgradeDiamondNewsId, cid, cfg.GetName())
	if err != nil {
		log.ErrorWithCtx(ctx, "PushBreakingNews PushUpgradeDiamondBreaking err:%v, uid:%d, newsId:%d, cfg:%+v",
			err, uid, diamondPushCfg.UpgradeDiamondNewsId, cfg)
	}

	userIllustrationList, err := m.GetIllustrationList(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushBreakingNews GetIllustrationList err:%v", err)
		return
	}
	if len(userIllustrationList) == 0 {
		return
	}

	userIllustrationMap := make(map[uint32]*pb.IllustrationInfo)
	for _, i := range userIllustrationList {
		userIllustrationMap[i.GetId()] = i
	}

	if len(userIllustrationMap) == 0 {
		log.WarnWithCtx(ctx, "PushBreakingNews GetIllustrationList userIllustrationMap is empty")
		return
	}

	if len(diamondPushCfg.IllustrationIds) == 0 {
		log.WarnWithCtx(ctx, "PushBreakingNews GetIllustrationList diamondPushCfg.IllustrationIds is empty")
		return
	}

	log.DebugWithCtx(ctx, "PushBreakingNews uid:%d userIllustrationMap:%+v diamondPushCfg.IllustrationIds:%+v", uid, userIllustrationMap, diamondPushCfg.IllustrationIds)

	// 排除无效的图鉴
	validDiamondPushIllId := make([]uint32, 0)
	for _, specialId := range diamondPushCfg.IllustrationIds {
		ic := m.illCfgCache.GetIllustrationCfgCache(specialId)
		if ic == nil {
			log.WarnWithCtx(ctx, "PushBreakingNews GetIllustrationList illustration config not found, id:%d", specialId)
			continue
		}
		// 检查有效
		if ic.Status != pb.IllustrationStatus_ILLUSTRATION_STATUS_IN_PROGRESS {
			continue
		}
		validDiamondPushIllId = append(validDiamondPushIllId, specialId)
	}

	// 如果全部过期就不发推送了
	if validDiamondPushIllId == nil || len(validDiamondPushIllId) == 0 {
		log.WarnWithCtx(ctx, "PushBreakingNews GetIllustrationList validDiamondPushIllId is empty, ids:%+v", diamondPushCfg.IllustrationIds)
		return
	}

	isAllDiamond := true
	for _, specialId := range validDiamondPushIllId {
		if i, ok := userIllustrationMap[specialId]; !ok || i.GetLevel() != highest.Lv {
			log.DebugWithCtx(ctx, "PushBreakingNews GetIllustrationList illustration not highest, id:%d, ok:%v, level:%d", specialId, ok, i.GetLevel())
			isAllDiamond = false
			break
		}
	}

	// 全套图鉴升级钻卡的全服公告
	if isAllDiamond {
		log.InfoWithCtx(ctx, "PushBreakingNews push ALL upgrade diamond breaking, uid:%d, newsId:%d, illIds:%+v",
			uid, diamondPushCfg.UpgradeAllNewsId, diamondPushCfg.IllustrationIds)

		// 延迟1秒避免乱序
		time.Sleep(time.Second * 1)

		// breaking
		err = m.clients.PushUpgradeDiamondBreaking(ctx, user, diamondPushCfg.UpgradeAllNewsId, cid, "")
		if err != nil {
			log.ErrorWithCtx(ctx, "PushBreakingNews PushUpgradeDiamondBreaking err:%v, uid:%d, newsId:%d, cfg:%+v",
				err, uid, diamondPushCfg.UpgradeAllNewsId, cfg)
		}
	}
}
