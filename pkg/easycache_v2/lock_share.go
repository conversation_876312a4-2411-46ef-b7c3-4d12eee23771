/*
 * @Description:
 * @Date: 2021-02-03 14:58:14
 * @LastEditors: liang
 * @LastEditTime: 2021-03-30 19:51:15
 */
package easycache

import (
	"sync"
)

//请求锁
var _ LockedCallsInterface = new(LockedGroup)

//请求锁
type LockedGroup struct {
	mu sync.Mutex
	m  map[string]*sync.WaitGroup
}

func NewLockedCalls() LockedCallsInterface {
	return &LockedGroup{
		m: make(map[string]*sync.WaitGroup),
	}
}

func (lg *LockedGroup) makeCall(key string, wg *sync.WaitGroup, fn func() error) error {
	defer func() {
		//wg.Wait的触发是靠done的 所以 要进行先删除再done来触发解锁 否则并发的时候会有getlock一直得不到释放的情况
		lg.mu.Lock()
		delete(lg.m, key)
		lg.mu.Unlock()
		wg.Done()
	}()
	return fn()
}

func (lg *LockedGroup) Do(key string, fn func() error) error {
begin:
	lg.mu.Lock()
	if wg, ok := lg.m[key]; ok {
		lg.mu.Unlock()
		wg.Wait()
		goto begin
	}
	wg := &sync.WaitGroup{}
	wg.Add(1)
	lg.m[key] = wg
	lg.mu.Unlock()
	//解锁进行同步调用
	err := lg.makeCall(key, wg, fn)
	return err
}
