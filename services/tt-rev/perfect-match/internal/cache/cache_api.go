package cache

import (
	context "context"
	time "time"

	perfectMatch "golang.52tt.com/protocol/services/rcmd/perfect_match"
)

type ICache interface {
    AddChannelInGame(ctx context.Context, channelId, startTime uint32) error
    AddFreeMatchCnt(ctx context.Context, uid uint32, addTimes int64) error
    AddMatchChannel(ctx context.Context, channelId, score uint32) error
    AddMatchingUid(ctx context.Context, uid, sex uint32) error
    AtLeastOneMatchedTeam(ctx context.Context) bool
    BatchDelMatchInfo(ctx context.Context, uids []uint32) (err error)
    BatchGetMatchInfo(ctx context.Context, uidList []uint32) ([]*MatchInfo, error)
    BatchLockUserMatchOp(ctx context.Context, expire time.Duration, uid ...uint32) []uint32
    BatchUnlockUserMatchOp(ctx context.Context, uid ...uint32) error
    DelChannelInGame(ctx context.Context, channelId uint32) error
    DelMatchChannel(ctx context.Context, channelId uint32) error
    DelMatchingUid(ctx context.Context, femaleUid, maleUid []uint32) error
    DelUidMatchEndTime(ctx context.Context, uid uint32) error
    GetCancelMatchCount(ctx context.Context, uid uint32, date time.Time) (uint32, error)
    GetFreeMatchCnt(ctx context.Context, uid uint32) (uint32, error)
    GetMatchChannel(ctx context.Context) (uint32, error)
    GetMatchChannelSize(ctx context.Context) (uint32, error)
    GetMatchHeartBeatTimeout(ctx context.Context, cycle uint32) ([]uint32, error)
    GetMatchInfo(ctx context.Context, uid uint32) (res *MatchInfo, err error)
    GetMatchingUid(ctx context.Context) ([]uint32, []uint32, error)
    GetMinChannelInGameStartTime(ctx context.Context) (uint32, error)
    GetUidMatchTimeoutUids(ctx context.Context) ([]uint32, error)
    IncrCancelMatchCount(ctx context.Context, uid uint32, date time.Time) (uint32, error)
    IncrMatchId(ctx context.Context, matchId string) int64
    SetMatchInfo(ctx context.Context, info *MatchInfo) error
    SetUidMatchEndTime(ctx context.Context, uid uint32, matchEndTime int64) error
    Shutdown(ctx context.Context)
    UpdateMatchHeartBeat(ctx context.Context, uid uint32) error
}

type IMatchInfo interface {
    GetSelectQuestion() []*perfectMatch.Question
}
