package channel_msg_express

import (
	"context"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	channelpb "golang.52tt.com/protocol/app/channel"
	pb "golang.52tt.com/protocol/services/channel-msg-express"
)

type IClient interface {
	client.BaseClient
	SendChannelMsg(ctx context.Context, msg *channelpb.ChannelMsg) protocol.ServerError
	SendChannelBroadcastMsg(ctx context.Context, msg *channelpb.ChannelBroadcastMsg) protocol.ServerError
	PushToUsers(ctx context.Context, req *pb.PushToUsersReq) protocol.ServerError
	SetDuplicateRule(ctx context.Context, req *pb.SetDuplicateRuleReq) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
