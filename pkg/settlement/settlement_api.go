package settlement

import (
	context "context"

	pb "golang.52tt.com/protocol/services/settlement-bill"
)

type IConfigCenter interface {
	GetReportEmailTo() []string
	GetSpecialWithdrawRange(tp pb.SettlementBillType) (*SpecialWithdrawRange, bool)
	GetSpecialAnniversaryDailyWithdrawRange() *SpecialAnniversaryDailyWithdrawRange
	GetYuyinGoldKnightSubTime() int64
	GetYuyinGoldPresentSubTime() int64
	GetYuyinGoldTBeanSubTime() int64
	IsDisableReceiptVerify(uid uint32) bool
	GetGuildBlackList() []uint32
	IsGuildBlackList(guildId uint32) bool
	IsInternalWhitelist(uid uint32) bool
	IsDisableInternalRiskControl() bool
	SendEmail(ctx context.Context, title, html string, attachPath []string)
	GetFeishuAlertUrl() string
	GetSpecialSettlements() []SpecialSettlement
	IsUpdateMonthPaidCnt() bool
}

type IBillTypeLabelDict interface {
	Text(t pb.SettlementBillType) string
}
