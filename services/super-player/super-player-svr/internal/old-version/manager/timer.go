package manager

import (
	"context"
	"errors"
	"fmt"
	"math"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/conf"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/mysql"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/pay"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/rpcs"
	"golang.52tt.com/services/super-player/super-player-svr/internal/old-version/utils"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/superplayerlogic"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/superplayersvr"
)

const (
	timeoutNoticeLockKey = "super_player_timeout_notice_lock"
	timerEventLockKey    = "super_player_timer_event_lock"
	autoReduceLockKey    = "super_player_auto_reduce_lock_v2"

	autoPayLockKey    = "super_player_auto_pay_lock"
	autoCancelLockKey = "super_player_auto_cancel_lock"

	autoAddValueKey       = "super_player_auto_add_value"
	autoPayContractOffSet = "auto_pay_contract_list_off"

	cycleDays          = 30
	contractCancelCode = "30000" //接口失败
	CANCEL_RESULT      = "11000" //已经解约
)

// 异步消息处理
func (m *Manager) handlerEventTimer() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	if isLock, _ := m.cache.Lock(timerEventLockKey, time.Minute*5); !isLock {
		return
	}

	defer m.cache.UnLock(timerEventLockKey)

	events, err := m.cache.GetTimerEvent()
	if nil != err {
		return
	}

	delEvents := make([]*pb.TimerEvent, 0, len(events))
	for _, event := range events {
		err := utils.TryCall(func() error {
			return m.HandlerEvent(ctx, event)
		}, 3, time.Second)

		delEvents = append(delEvents, event)
		if nil != err {
			log.ErrorWithCtx(ctx, "HandlerEvent fail event:%+v", event)
		}
	}

	m.cache.DelTimerEvent(ctx, delEvents)
}

func (m *Manager) HandlerEvent(ctx context.Context, event *pb.TimerEvent) error {

	log.DebugWithCtx(ctx, "HandlerEvent event:%+v", event)

	superPlayerInfo, err := m.GetSuperPlayerInfo(ctx, event.SuperPlayerUid, false)
	if nil != err {
		log.ErrorWithCtx(ctx, "HandlerEvent GetSuperPlayerInfo uid:%v err:%v", event.SuperPlayerUid, err)
		return err
	}

	addTime := utils.AddTime{
		Days: event.Days,
		Mins: event.Mins,
	}

	nowTs := time.Now().Unix()
	switch event.EventType {
	case pb.EnumTimerEventType_MEMBER_OPEN_EVENT:
		expireTs := time.Unix(superPlayerInfo.ExpireTimestamp, 0)
		expireTime := utils.GetDayStr(expireTs)
		subErr, strTime := addTime.Str()
		if nil != subErr {
			log.ErrorWithCtx(ctx, "HandlerEvent addTime invalid event:%+v err:%v", event, subErr)
			return subErr
		}
		content := fmt.Sprintf("开通超级玩家成功！\n开通时长：%v\n有效期至：%v\n有效期内您将享受海量专属权益。查看超级玩家成长值、有效期信息和权益内容，点击这里进入超级玩家主页>",
			strTime, expireTime)
		packConf := conf.GetPackageByID(event.PackageId)
		if nil != packConf {
			content = fmt.Sprintf("开通超级玩家成功！\n开通时长：%v\n开通套餐：%v\n有效期至：%v\n有效期内您将享受海量专属权益。查看超级玩家成长值、有效期信息和权益内容，点击这里进入超级玩家主页>",
				strTime, packConf.Name, expireTime)
		}
		err = rpcs.SendImMsg(event.SuperPlayerUid, content, "超级玩家主页>")
	case pb.EnumTimerEventType_EMEBER_TIMEOUT_EVENT:
		if superPlayerInfo.ExpireTimestamp < nowTs && superPlayerInfo.SvipExpireTimestamp < superPlayerInfo.ExpireTimestamp {
			content := "您的超级玩家已到期，无法继续享受专属权益，成长值将持续下降。去恢复超级玩家身份>"
			err = rpcs.SendImMsg(event.SuperPlayerUid, content, "去恢复超级玩家身份>")
		}
	default:
		err = errors.New("unknown event")
	}

	log.InfoWithCtx(ctx, "HandlerEvent event:%+v err:%v", event, err)

	return err
}

func (m *Manager) loadPackageConfTimer() {
	ctx := context.Background()
	log.InfoWithCtx(ctx, "loadPackageConfFromMysql begin")

	confList, err := m.db.GetAllPackageList()
	if err != nil {
		log.ErrorWithCtx(ctx, "loadPackageConfFromMysql GetAllPackageList failed error:%v", err)
		return
	}

	var packageList = []*pb.Package{}
	for _, conf := range confList {
		bIsAuto := false
		if conf.Auto == 1 {
			bIsAuto = true
		}

		pChList := strings.Split(conf.PayChList, ",")
		tmpConf := &pb.Package{
			Id:             strconv.Itoa(int(conf.Id)),
			ProductId:      conf.ProductId,
			Name:           conf.Name,
			Desc:           conf.Desc,
			OriginalPrice:  conf.OriginalPrice,
			Price:          conf.Price,
			Value:          conf.Value,
			Auto:           bIsAuto,
			PayChannelList: pChList,
			Days:           conf.Days,
			PackageStatus:  conf.PackageStatus,
			AutoValue:      conf.AutoValue,
			Operator:       conf.Operator,
			UpdateTs:       conf.UpdateTs,
			DiscountPrice:  conf.DiscountPrice,
			MarketId:       conf.MarketId,
		}

		packageList = append(packageList, tmpConf)
	}

	conf.UpdateSuperPlayerPackage(packageList)

	log.InfoWithCtx(ctx, "loadPackageConfFromMysql end packageList:%v", packageList)
}

func getReduceLockJobKey(ts time.Time) (string, time.Time) {
	strKey := fmt.Sprintf("%v_%v", autoReduceLockKey, utils.GetHourStr(ts)) //ts.Format("2006-01-02-15")
	reduceBeginTs := time.Date(ts.Year(), ts.Month(), ts.Day(), 0, 0, 0, 0, time.Local)
	/*	if conf.IsTest() {
		//方便测试，5分钟一个生产之降分周期
		m := ts.Minute() - (ts.Minute() % 5)
		strKey = fmt.Sprintf("%v_%v-%02v", autoReduceLockKey, utils.GetHourStr(ts), m)
		reduceBeginTs = time.Date(ts.Year(), ts.Month(), ts.Day(), ts.Hour(), m, 0, 0, time.Local)
	}*/
	return strKey, reduceBeginTs
} //

// 定时减少过期会员值定时器
func (m *Manager) autoReduceValueTimer() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	nowTs := time.Now()

	//IOS存在延迟过期情况，所以在当天8小时之后才发起看成长值
	if nowTs.Hour() < int(conf.GetIosDelayExpireHour()) {
		return
	}

	if isLock, _ := m.cache.Lock(autoReduceLockKey, time.Hour*3); !isLock {
		return
	}

	autoReduceDailyKey, reduceBeginTs := getReduceLockJobKey(nowTs) //fmt.Sprintf("%v_%v", autoReduceLockKey, nowTs.Format("2006-01-02-15"))

	val, err := m.cache.Get(autoReduceDailyKey)
	isDone := nil == err && val > 0
	if isDone {
		return
	}

	log.DebugWithCtx(ctx, "autoReduceValueTimer ticker reduceBeginTs:%v", reduceBeginTs)

	var idx, tbCount uint32 = 0, 100
	sg := sync.WaitGroup{}
	sg.Add(int(tbCount))

	f := func(idx uint32) {

		defer sg.Done()

		var off, count, step int64 = 0, 0, 1024
		for {
			infos, err := m.db.BatchGetSuperPlayerInfoWithQuery(idx, nowTs, off, step, true)
			if nil != err {
				break
			}

			if len(infos) == 0 {
				break
			}

			count = count + int64(len(infos))

			for _, info := range infos {
				err := m.doAutoReduceValue(&pb.SuperPlayerInfo{
					SuperPlayerUid:   info.Uid,
					SuperPlayerValue: info.Value,
					ExpireTimestamp:  info.ExpireTime.Unix(),
				}, nowTs)
				if nil != err {
					log.ErrorWithCtx(ctx, "autoReduceValueTimer doAutoReduceValue info:%v err:%v", info, err)
				}
			}
			off = off + step

			//time.Sleep(time.Second)
		}
		log.InfoWithCtx(ctx, "autoReduceValueTimer done idx:%v count:%v", idx, count)
	}

	for ; idx < tbCount; idx++ {
		go f(idx)
	}

	sg.Wait()

	log.InfoWithCtx(ctx, "autoReduceValueTimer record done")
	m.cache.Set(autoReduceDailyKey, 1, time.Hour*24*2)

}

// 每天自动减少会员值订单
func genAutoReduceOrderID(uid uint32, ts time.Time) string {
	return fmt.Sprintf("auto_reduce_day_%v_%v", uid, utils.GetDayStr(ts))
}

func (m *Manager) doAutoReduceValue(info *pb.SuperPlayerInfo, ts time.Time) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	log.DebugWithCtx(ctx, "doAutoReduceValue info:%+v", info)

	if ts.Unix() < info.ExpireTimestamp {
		return errors.New("not expire")
	}
	if info.SuperPlayerValue == 0 {
		return errors.New("val==0")
	}
	nowTs := time.Now()

	//执行自动扣减
	orderID := genAutoReduceOrderID(info.SuperPlayerUid, ts)

	reduceValue := conf.GetReduceValue(info.SuperPlayerValue)
	if reduceValue > info.SuperPlayerValue {
		reduceValue = reduceValue - info.SuperPlayerValue
	}
	reduceValue = reduceValue * -1

	err := m.AddSuperPlayerValue(ctx, &pb.AddSuperPlayerValOrder{
		OrderId:        orderID,
		SuperPlayerUid: info.SuperPlayerUid,
		IncValue:       reduceValue,
		Reason:         "超级玩家过期，成长值自动下降",
		ServerTime:     nowTs.Unix(),
		ParentOrder:    orderID,
	}, true)

	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoReduceValue fail uid:%v err:%v", info.SuperPlayerUid, err)
		return err
	}

	//测试环境刷下，不然就等定时拉取
	if conf.IsTest() {
		userInfo, err := m.db.GetSuperPlayerInfo(ctx, info.SuperPlayerUid)
		if nil == err {
			subMsg, _ := proto.Marshal(&superplayerlogic.SuperPlayerInfo{
				SuperPlayerUid:            userInfo.Uid,
				SuperPlayerValue:          userInfo.Value,
				SuperPlayerLevel:          conf.GetLevelByValue(userInfo.Value),
				BeginTimestamp:            userInfo.BeginTime.Unix(),
				ExpireTimestamp:           userInfo.ExpireTime.Unix(),
				YearMemberExpireTimestamp: userInfo.YearMemberExpireTime.Unix(),
			})
			err = rpcs.NotifyInfoChangeV2(subMsg, superplayerlogic.SuperPlayerInfoPushMsg_ENUM_SUPER_PLAYER_INFO_CHANGE, userInfo.Uid)
			if nil != err {
				return err
			}
		}
	}
	return nil
}

// 扫描签约用户，发起自动扣款等操作
func (m *Manager) autoPayTimer() {

	if conf.IsStartNewTimer() {
		log.InfoWithCtx(context.Background(), "old version autoPayTimer not start")
		return
	}

	if isLock, _ := m.cache.Lock(autoPayLockKey, time.Minute*2); !isLock {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*8)
	defer func() {
		cancel()
		m.cache.UnLock(autoPayLockKey)
	}()

	var tmpTs, step int64 = conf.GetSPSystemBeginTs()
	beginTs, _ := m.cache.Get(autoPayContractOffSet)
	if beginTs <= tmpTs {
		beginTs = tmpTs
	}
	contracts, err := m.db.GetContractList(ctx, time.Unix(beginTs, 0), time.Unix(beginTs+step, 0), []string{utils.SIGN_CONTRACT})
	if nil != err {
		log.ErrorWithCtx(ctx, "autoPayTimer GetContractList off:%v err:%v", beginTs, err)
		return
	}

	log.InfoWithCtx(ctx, "autoPayTimer contracts off:%v sz:%v", beginTs, len(contracts))

	beginTs = beginTs + step
	if beginTs >= time.Now().Unix() {
		beginTs = tmpTs
	}
	m.cache.Set(autoPayContractOffSet, beginTs, 0)

	go m.autoPayTimerHelper(contracts)

}

// 提前5天续费通知
func (m *Manager) renewNotify(ctx context.Context, contract *mysql.ContractRecord, os protocol.OS) {
	nowTs := time.Now()
	days := conf.RenewNotifyDay()
	leftTime := nowTs.AddDate(0, 0, days)
	if contract.NextTime.After(leftTime) || contract.NextTime.Before(nowTs) {
		return
	}
	lockKey := utils.FiveDayRenewNotifyLock(contract.ContractID, contract.NextTime.Unix())
	if ok, _ := m.cache.Lock(lockKey, time.Hour*24*60); !ok {
		return
	}
	content := conf.GetRenewNotifyMsg(os, contract.NextTime)
	rpcs.SendImMsg(contract.Uid, content, "")
	log.InfoWithCtx(ctx, "renewNotify uid:%v os:%v content:%v", contract.Uid, os, content)
}

func (m *Manager) autoPayTimerHelper(contractList []mysql.ContractRecord) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)

	defer cancel()
	defer func() {
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "autoPayTimerHelper recover err:%v", r)
		}
	}()
	for _, tmpC := range contractList {
		contract := tmpC
		if contract.Status != utils.SIGN_CONTRACT {
			continue
		}

		os := protocol.ANDROID
		if utils.IsAppstore(contract.PayChannel) {
			os = protocol.IOS
		}
		//提前5天提示扣款，如果不需要续约请取消订阅
		m.renewNotify(ctx, &contract, os)

		//iOS由苹果管理下单，这里只是检查是否有扣款失败的签约然后发出助手消息
		if os == protocol.IOS {
			if time.Since(contract.NextTime).Seconds() >= conf.GetIosNoticeCancelContractTs() {
				m.sendCancelContractNotifyMsg(ctx, contract.Uid, os, &contract)
			}
			continue
		}
		err := m.doAutoPay(&contract)
		if nil != err {
			log.ErrorWithCtx(ctx, "autoPayTimer doAutoPay contract:%+v err:%v", contract, err)
		}
	}
}

func callCntKey(ts time.Time) string {
	key := fmt.Sprintf("super_player_auto_pay_cnt_limit_%v", utils.GetMinStr(ts))
	return key
}

func (m *Manager) doAutoAddScore(order *mysql.OrderRecord, isExpire bool) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	nowTs := time.Now()

	packConf := conf.GetPackageByID(order.PackageID)
	if nil == packConf {
		return 0, errors.New("packconf empty")
	}

	//还没到消耗这个单的时间，首次开通这里是OK的
	if nowTs.After(order.ExpireTime) || nowTs.Before(order.BeginTime) {
		return 0, nil //errors.New("not the time")
	}

	//这个套餐消耗多少天了
	days := utils.TimeSubDays(nowTs, order.BeginTime)
	totalDays := utils.TimeSubDays(order.ExpireTime, order.BeginTime)

	//第几个周期呀
	cyc := days / cycleDays

	//有些单是31天，多出的1天会导致加一次积分
	if (cyc+1)*conf.GetMinMonthLimit() > totalDays {
		log.DebugWithCtx(ctx, "doAutoAddScore invalid cyc orderID:%v cyc:%v totalDays:%v", order.OrderID, cyc, totalDays)
		return 0, nil
	}

	//生成对应周期的加成长值订单
	addValueOrderID := fmt.Sprintf("%v_%v_add_score", order.OrderID, cyc)

	if isLock, _ := m.cache.Lock(addValueOrderID, time.Hour*24*60); !isLock {
		return 0, nil
	}

	addVal := packConf.Value
	if cyc >= 1 || utils.IsAutoPayOrder(order.OrderID) {
		if packConf.AutoValue > 0 {
			addVal = packConf.AutoValue
		}
	}

	superPlayerUid := order.SuperPlayerUid
	if superPlayerUid == 0 {
		superPlayerUid = order.Uid
	}

	addValOrder := &pb.AddSuperPlayerValOrder{
		OrderId:        addValueOrderID,
		SuperPlayerUid: superPlayerUid,
		IncValue:       addVal,
		Reason:         fmt.Sprintf("购买了%v套餐", packConf.Name),
		ServerTime:     nowTs.Unix(),
		ParentOrder:    order.OrderID,
	}

	//ctx, _ := context.WithTimeout(context.Background(), time.Second*5)
	if err := m.AddSuperPlayerValue(ctx, addValOrder, isExpire); nil != err {
		log.ErrorWithCtx(ctx, "doAutoAddScore AddSuperPlayerValue err:%v", err)

		m.cache.UnLock(addValueOrderID)

		return 0, err
	}

	log.InfoWithCtx(ctx, "doAutoAddScore addValOrder:%+v", addValOrder)

	return addVal, nil
}

func (m *Manager) delayPay(ctx context.Context, contract *mysql.ContractRecord, nowTs time.Time) {
	//发起延迟扣款申请
	businessID, fm, err := conf.GetBusinessCfg(contract.MarketId, true)
	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoPay GetBusinessCfg err:%v", err)
		return
	}
	delayPayReq := &pb.ApiDelayPayReq{
		ContractId: contract.ContractID,
		BusinessId: businessID,
		BuyerId:    fmt.Sprintf("%v", contract.Uid),
		DeductTime: utils.GetTimeStr(nowTs),
		Memo:       "会员发起延期扣款",
		PayChannel: contract.PayChannel,
	}
	subCtx := utils.GenPayCtx(ctx, contract.Uid, contract.MarketId, fm)
	_, err = m.payCli.DelayPay(subCtx, delayPayReq)
	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoPay DelayPay delayPayReq:%+v err:%v", delayPayReq, err)
	}
}

// doAutoPay 主要是安卓签约主动扣费使用
func (m *Manager) doAutoPay(contract *mysql.ContractRecord) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()
	log.InfoWithCtx(ctx, "doAutoPay contract :%+v", contract)

	packConf := conf.GetPackageByID(contract.PackageID)
	if nil == packConf {
		log.ErrorWithCtx(ctx, "doAutoPay GetPackageByID empty contractID:%v", contract.ContractID)
		return nil
	}
	payApiPara := conf.GetPayApiPara()
	nowTs := time.Now()
	//每分钟调用次数限制
	callCnt, _ := m.cache.Get(callCntKey(nowTs))
	callPayCntLimit := payApiPara.CallPayCntLimit
	if callPayCntLimit == 0 {
		callPayCntLimit = 600
	}
	if callCnt >= callPayCntLimit {
		log.DebugWithCtx(ctx, "doAutoPay call cnt limit contract %v", contract.ContractID)
		return errors.New("call cnt limit")
	}

	superPlayerInfo, err := m.GetSuperPlayerInfo(ctx, contract.Uid, false)
	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoPay GetSuperPlayerInfo ContractID:%v err:%v", contract.ContractID, err)
		return err
	}

	//
	perr := utils.CheckAutoPayTime(ctx, superPlayerInfo, contract.NextTime, nowTs, contract.PayChannel)
	if nil != perr {
		log.ErrorWithCtx(ctx, "doAutoPay checkAutoPayTime ContractID:%v err:%v", contract.ContractID, perr)
		return perr
	}

	//离过期还有5天的时候提示
	m.leftDayNotice(superPlayerInfo, contract)

	orderID := utils.GenOrderIDForAuto(contract.Uid, contract.PayChannel, utils.OrderAutoPrifix, contract.NextTime, nowTs)

	err = m.checkOrderInterval(ctx, payApiPara, orderID, contract.ContractID)
	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoPay checkOrderInterval fail orderID:%v ContractID:%v", orderID, contract.ContractID)
		return err
	}

	tx := m.GetTx(ctx, false)

	defer func() {
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "doAutoPay recover orderID:%v ContractID:%v  r:%v", orderID, contract.ContractID, r)
			tx.Rollback()
		}
	}()

	orderInfo, err := m.db.GetOrder(ctx, orderID, nowTs, tx)

	//没报错，能查到已有订单,并且订单状态是成功状态
	if nil == err && pb.EnumOrderStatus(orderInfo.Status) == pb.EnumOrderStatus_ORDER_PAY_SUCCESS {
		log.ErrorWithCtx(ctx, "doAutoPay GetOrder orderID:%v ContractID:%v err:%v", orderID, contract.ContractID, err)
		tx.Rollback()
		return nil
	}

	cresp, err := m.queryContractInfo(contract)
	isContractOk := nil == err && cresp.Code == PAY_API_RESULT

	if (!isContractOk) && (!conf.IsTest()) {
		log.ErrorWithCtx(ctx, "doAutoPay queryContractInfo orderID:%v contractID:%v err:%v", orderID, contract.ContractID, err)
		tx.Rollback()
		return err
	}

	//先记录订单到数据库，状态是init状态
	err = m.db.PlaceOrder(&mysql.OrderRecord{
		OrderID:    orderID,
		Uid:        contract.Uid,
		PackageID:  contract.PackageID,
		PayChannel: contract.PayChannel,
		Status:     int8(pb.EnumOrderStatus_ORDER_INIT),
		OrderType:  int8(pb.EnumOrderType_ORDER_TYPE_AUTO_PAY),
		OsType:     contract.OsType,
		Version:    contract.Version,
		BundleId:   contract.BundleId,
		Price:      packConf.Price,
		MarketId:   contract.MarketId,
		BeginTime:  nowTs,
		ExpireTime: nowTs,
		ServerTime: nowTs,
	})

	if nil != err {
		tx.Rollback()
		log.ErrorWithCtx(ctx, "doAutoPay PlaceOrder contractID:%v orderID:%v err:%v", contract.ContractID, orderID, err)
		return err
	}

	businessID, fm, err := conf.GetBusinessCfg(contract.MarketId, true)
	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoPay GetBusinessCfg contractID:%v orderID:%v err:%v", contract.ContractID, orderID, err)
		return err
	}

	autoPayReq := &pb.ApiAutoPayReq{
		OrderType:     "PERIOD",
		OsType:        contract.OsType,
		PayChannel:    contract.PayChannel,
		BusinessId:    businessID,
		Fm:            fm,
		Version:       contract.Version,
		CliOrderNo:    orderID,
		CliBuyerId:    fmt.Sprintf("%v", contract.Uid),
		CliPrice:      fmt.Sprintf("%v", packConf.Price),
		CliOrderTitle: fmt.Sprintf(autoPayText, packConf.Name),
		CliOrderDesc:  fmt.Sprintf(autoPayText, packConf.Name),
		CliNotifyUrl:  payApiPara.PayCallBackUrl,
		CreateTime:    utils.GetTimeStr(nowTs),
		Remark:        "",
		BundleId:      contract.BundleId,
		ProductId:     packConf.ProductID,
		UserIp:        "",
		DeductParam: &pb.DeductParam{
			ContractId: contract.ContractID,
		},
	}

	subCtx := context.WithValue(ctx, pay.CtxUidKey, strconv.FormatInt(int64(contract.Uid), 10))
	subCtx = context.WithValue(subCtx, pay.CtxFmKey, fm)
	subCtx = context.WithValue(subCtx, pay.CtxMarketIdKey, strconv.FormatInt(int64(contract.MarketId), 10))
	autoPayResp, err := m.payCli.AutoPay(subCtx, autoPayReq)
	if nil != err {
		tx.Rollback()

		//扣款余额不足提醒
		isSendNotifyMsg := conf.IsNotBalance(err)
		if isSendNotifyMsg {
			m.sendCancelContractNotifyMsg(ctx, superPlayerInfo.GetSuperPlayerUid(), protocol.ANDROID, contract)
		}

		log.ErrorWithCtx(ctx, "doAutoPay AutoPay ContractID:%v  autoPayReq:%v isSendNotifyMsg:%v err:%v", contract.ContractID, autoPayReq, isSendNotifyMsg, err)

		//发起延迟扣款申请
		if conf.IsDelayPay(err) {
			m.delayPay(ctx, contract, nowTs)
		} else {
			if conf.IsCloseOrder(err) || conf.IsInitiativeCancelContract(contract.NextTime) {
				contract.Status = utils.SPSYS_RESCIND_CONTRACT //更新签约信息到预解约状态
				contract.SceneID = err.Error()
				m.db.UpdateContract(ctx, contract)

				m.cache.DelContractList(contract.Uid)
			}
		}

		return err
	}

	log.InfoWithCtx(ctx, "doAutoPay AutoPay ContractID:%v autoPayReq:%+v autoPayResp:%+v", contract.ContractID, autoPayReq, autoPayResp)

	//更新签约扣款时间到下个周期
	contract.LastTime, contract.NextTime = utils.GetNextPayTime(contract.LastTime, int(packConf.Days))
	err = m.db.CreateOrUpdateContract([]mysql.ContractRecord{*contract}, tx)
	if nil != err {
		log.ErrorWithCtx(ctx, "doAutoPay CreateOrUpdateContract contractID:%v orderID:%v err:%v", contract.ContractID, orderID, err)
		tx.Rollback()
		return err
	}

	//清掉签约信息缓存
	m.cache.DelContractList(contract.Uid)

	tx.Commit()

	m.cache.IncrBy(callCntKey(nowTs), 1)

	if !utils.IsAppstore(contract.PayChannel) {
		rpcs.ReportContract(ctx, contract)
	}

	log.InfoWithCtx(ctx, "doAutoPay AutoPay success %+v contract:%+v", autoPayReq, contract)

	return nil
}

func (m *Manager) sendCancelContractNotifyMsg(ctx context.Context, uid uint32, os protocol.OS, contract *mysql.ContractRecord) {
	lockKey := utils.CancelContractNotifyLock(contract.ContractID, contract.NextTime.Unix())
	if ok, _ := m.cache.Lock(lockKey, time.Hour*24*30); !ok {
		return
	}
	content := conf.GetCancelContractNotifyMsg(os, contract.NextTime.AddDate(0, 0, 2))
	rpcs.SendImMsg(uid, content, "")

	log.InfoWithCtx(ctx, "sendCancelContractNotifyMsg uid:%v os:%v content:%v", uid, os, content)
}

func (m *Manager) checkOrderInterval(ctx context.Context, payApiPara *conf.PayApiPara, orderID, ContractID string) error {
	mins := payApiPara.AutoOrderInterval
	if mins == 0 {
		mins = 60
	}

	intervalTime := time.Minute * time.Duration(mins)
	if isLock, _ := m.cache.Lock(orderID, intervalTime); !isLock {
		log.DebugWithCtx(ctx, "doAutoPay isLock orderID:%v ContractID:%v", orderID, ContractID)
		return errors.New("order lock fail")
	}
	return nil
}

func (m *Manager) autoCancelContractTimer() {

	if conf.IsStartNewTimer() {
		log.InfoWithCtx(context.Background(), "old version autoCancelContractTimer not start")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()

	if isLock, _ := m.cache.Lock(autoCancelLockKey, time.Hour*2); !isLock {
		return
	}

	nowTs := time.Now()
	status := []string{utils.SIGN_CONTRACT, utils.SPSYS_RESCIND_CONTRACT}
	var beginTs, step int64 = conf.GetSPSystemBeginTs()
	for {
		contracts, err := m.db.GetContractList(ctx, time.Unix(beginTs, 0), time.Unix(beginTs+step, 0), status)
		if nil != err {
			log.ErrorWithCtx(ctx, "autoCancelContractTimer GetContractList err:%v", err)
			break
		}

		log.DebugWithCtx(ctx, "autoCancelContractTimer contracts:%v off:%v step:%v", len(contracts), beginTs, step)

		//过期掉扣款失败的订单
		for _, c := range contracts {
			tmpC := c
			//苹果不能自动解约
			if utils.IsAppstore(tmpC.PayChannel) {
				continue
			}

			if err := m.able2cancel(ctx, &tmpC); nil == err {
				m.doCancelContract(&tmpC)
			} else {
				log.DebugWithCtx(ctx, "autoCancelContractTimer able2cancel contracts:%+v err:%v", tmpC, err)
			}
		}

		if beginTs >= nowTs.Unix() {
			break
		}
		beginTs = beginTs + step
		time.Sleep(time.Second * 3)
	}
}

func (m *Manager) CancelContract(ctx context.Context, uid uint32, contractId string) error {
	contract, err := m.db.GetContract(contractId, nil)
	if nil != err {
		return err
	}

	return m.doCancelContract(contract)
}

func (m *Manager) doCancelContract(contract *mysql.ContractRecord) error {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*16)
	defer cancel()

	log.InfoWithCtx(ctx, "doCancelContract begin contractId:%+v", contract)

	payApiPara := conf.GetPayApiPara()
	businessID, fm, err := conf.GetBusinessCfg(contract.MarketId, true)
	if nil != err {
		log.ErrorWithCtx(ctx, "doCancelContract GetBusinessCfg err:%v", err)
		return err
	}

	cancelContractReq := &pb.ApiCancelContractReq{
		ContractId:        contract.ContractID,
		BusinessId:        businessID,
		BuyerId:           fmt.Sprintf("%v", contract.Uid),
		ContractNotifyUrl: payApiPara.ContractNotifyUrl,
	}

	//发起解约
	subCtx := utils.GenPayCtx(ctx, contract.Uid, contract.MarketId, fm)
	cancelResp, err := m.payCli.CancelContract(subCtx, cancelContractReq)
	if nil != err {
		log.ErrorWithCtx(ctx, "doCancelContract CancelContract err:%v", err)
		return err
	}

	cancelContractResp := cancelResp.(pb.ApiCancelContractResp)

	if cancelContractResp.Code == contractCancelCode {
		log.ErrorWithCtx(ctx, "doCancelContract CancelContract contractId:%v code:%v", contract.ContractID, cancelContractResp.Code)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara)
	}

	if cancelContractResp.Code == CANCEL_RESULT || cancelContractResp.Code == PAY_API_RESULT {
		contract.Status = utils.RESCIND_CONTRACT
		m.db.CreateOrUpdateContract([]mysql.ContractRecord{*contract}, nil)
		rpcs.ReportContract(ctx, contract)
		//清掉签约信息缓存
		m.cache.DelContractList(contract.Uid)
	}

	log.InfoWithCtx(ctx, "doCancelContract CancelContract success contractId:%+v uid:%v code:%v", contract.ContractID, contract.Uid, cancelContractResp.Code)

	return nil
}

// 添加成长值定时器
func (m *Manager) autoAddScoreTimer() {

	if conf.IsStartValueTimer() {
		log.InfoWithCtx(context.Background(), "old version autoAddScoreTimer not start")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*60)
	defer cancel()
	log.ErrorWithCtx(ctx, "autoAddScoreTimer begin")

	defer func() {
		if r := recover(); r != nil {
			log.ErrorWithCtx(ctx, "autoAddScoreTimer failed, panic err:[%+v], stack:[%+v]",
				r, string(debug.Stack()))
		}
	}()

	if isLock, _ := m.cache.Lock(autoAddValueKey, time.Minute*10); !isLock {
		return
	}

	now := time.Now()
	currTs := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	for i := 0; i < 12*5; i++ {
		monLockKey := fmt.Sprintf("%v_%v", autoAddValueKey, utils.GetMonStr(currTs))
		if isMonLock, _ := m.cache.Lock(monLockKey, time.Minute*30); !isMonLock {
			continue
		}
		go m.autoAddValue(currTs)
		time.Sleep(time.Second * 5) // 这里将请求分散开
		currTs = currTs.AddDate(0, -1, 0)
		// 如果早于项目开始日期则不进行操作
		if currTs.Before(time.Date(2021, 5, 1, 0, 0, 0, 0, time.Local)) {
			break
		}
	}

}

func (m *Manager) autoAddValue(beginTs time.Time) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*60)
	defer cancel()
	_, step := conf.GetSPSystemBeginTs()
	for {
		endTs := beginTs.Add(time.Duration(step) * time.Second)
		orderList, err := m.db.GetPlaceOrderList(beginTs, endTs)
		if nil != err {
			log.ErrorWithCtx(ctx, "autoAddScoreTimer GetPlaceOrderList err:%v", err)
			break
		}
		log.InfoWithCtx(ctx, "autoAddValue GetPlaceOrderList beginTs:%v sz:%v", beginTs, len(orderList))
		for _, order := range orderList {
			if order.Status != int8(pb.EnumOrderStatus_ORDER_PAY_SUCCESS) {
				continue
			}
			o := order
			if _, err := m.doAutoAddScore(&o, false); nil != err {
				log.ErrorWithCtx(ctx, "autoAddScoreTimer doAutoAddScore err:%v", err)
			}
		}
		if beginTs.Month() != endTs.Month() {
			break
		}
		beginTs = endTs
	}
}

// 即将过期TT助手
func (m *Manager) autoTimeoutNoticeTimer() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	idx, err := m.cache.IncrBy("auto_time_out_notice_idx", 1)
	if nil != err {
		return
	}
	idx = idx % 100
	lockKey := fmt.Sprintf("%v_%v", timeoutNoticeLockKey, idx)
	if isLock, _ := m.cache.Lock(lockKey, time.Hour*2); !isLock {
		return
	}

	defer func() {
		if r := recover(); nil != r {
			log.ErrorWithCtx(ctx, "autoTimeoutNotice recover err:%v", err)
			m.cache.UnLock(lockKey)
		}
	}()

	var off, count int64 = 0, 1024
	nowTs := time.Now()

	for {
		infos, err := m.db.BatchGetSuperPlayerInfoWithQuery(uint32(idx), nowTs, off, count, false)
		if nil != err {
			log.ErrorWithCtx(ctx, "autoTimeoutNotice GetSuperPlayerInfoList err:%v", err)
			break
		}

		log.InfoWithCtx(ctx, "autoTimeoutNotice GetSuperPlayerInfoList begin off:%v idx:%v info sz:%v", off, idx, len(infos))

		if 0 == len(infos) {
			break
		}

		m.doTimeOutNotice(infos)

		log.InfoWithCtx(ctx, "autoTimeoutNotice GetSuperPlayerInfoList finish off:%v idx:%v info sz:%v", off, idx, len(infos))

		if len(infos) != int(count) {
			break
		}

		off = off + count

		time.Sleep(time.Second)
	}

}

func (m *Manager) doTimeOutNotice(infos []mysql.SuperPlayerInfo) error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*8)
	defer cancel()
	nowTs := time.Now()
	for _, info := range infos {
		leftHours := info.ExpireTime.Sub(nowTs).Hours()
		if leftHours > 72 || leftHours <= 0 {
			continue
		}

		contracts, _, err := m.GetSuperPlayerContract(ctx, info.Uid, false)
		if nil != err {
			log.ErrorWithCtx(ctx, "doTimeOutNotice GetSuperPlayerContract err:%v", err)
			continue
		}
		if len(contracts) > 0 {
			log.ErrorWithCtx(ctx, "doTimeOutNotice has contracts info:%+v", info)
			continue
		}

		days := int(math.Ceil(leftHours / 24))
		noticeKey := fmt.Sprintf("super_player_timeout_notice_v2_%v_%v", info.Uid, days)
		if isLock, _ := m.cache.Lock(noticeKey, time.Hour*30); !isLock {
			continue
		}

		content := fmt.Sprintf("您的超级玩家剩余天数不足%v天，即将失去专属权益。前往续费>", days)
		err = rpcs.SendImMsg(info.Uid, content, "前往续费>")
		if nil != err {
			log.ErrorWithCtx(ctx, "doTimeOutNotice SendImMsg info:%v err:%v", info, err)
		}
		log.InfoWithCtx(ctx, "doTimeOutNotice SendImMsg success info:%+v content:%v", info, content)
	}
	return nil
}

func (m *Manager) createMonthTableTimer() {
	ctx := context.Background()
	nowTs := time.Now()
	nextMonth := time.Date(nowTs.Year(), nowTs.Month()+1, 1, 0, 0, 0, 0, time.Local)
	err := m.db.CreateMonthTables(ctx, nextMonth)
	if nil != err {
		log.ErrorWithCtx(ctx, "createMonthTable err:%v", err)
	}
}
