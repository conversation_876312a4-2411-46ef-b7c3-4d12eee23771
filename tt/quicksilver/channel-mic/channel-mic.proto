syntax = "proto3";

package channel_mic;
option go_package = "golang.52tt.com/protocol/services/channel-mic";

service ChannelMic {
	rpc ApplyHoldMic(ApplyHoldMicReq) returns( ApplyHoldMicResp ){
	}
	rpc VerifyHoldMicToken(VerifyHoldMicReq) returns (VerifyHoldMicResp) {
	}
	// 申请上麦token预检查
	rpc ApplyHoldMicPreCheck(ApplyHoldMicPreCheckReq) returns (ApplyHoldMicPreCheckResp) {
	}

	// 设置麦位状态相关接口
	rpc SetChannelMicSpaceStatus(SetChannelMicSpaceStatusReq) returns (SetChannelMicSpaceStatusResp) {
	}
	rpc BatchSetChannelMicSpaceStatus(BatchSetChannelMicSpaceStatusReq) returns (BatchSetChannelMicSpaceStatusResp) {
	}
	rpc DisableAllEmptyMicrSpace(DisableAllEmptyMicrSpaceReq) returns (DisableAllEmptyMicrSpaceResp) {
	}

	rpc FakeHoldMicrSpace(FakeHoldMicrSpaceReq) returns (FakeHoldMicrSpaceResp) {
	}

  // 上麦
  rpc SimpleHoldMicrSpace( SimpleHoldMicrSpaceReq ) returns( SimpleHoldMicrSpaceResp ) {
  }
  // 下麦
  rpc SimpleReleaseMicrSpace( SimpleReleaseMicrSpaceReq ) returns( SimpleReleaseMicrSpaceResp ){
  }
  // 换麦位
  rpc ChangeMicrophone( ChangeMicrophoneReq ) returns ( ChangeMicrophoneResp ) {
  }
  // 踢下麦
  rpc KickoutChannelMic( KickoutChannelMicReq ) returns ( KickoutChannelMicResp ) {
  }
  // 初始化麦位数据
  rpc ReInitChannelMicData( ReInitChannelMicDataReq ) returns ( ReInitChannelMicDataResp ){
  }
  // 设置麦位模式，逐渐淘汰，需要换成ReInitChannelMicData这个新的接口
  rpc SetChannelMicMode( SetChannelMicModeReq ) returns ( SetChannelMicModeResp ) {
  }

  // 获取麦位列表
  rpc GetMicrList( GetMicrListReq ) returns ( GetMicrListResp ) {
  }
}

//uint32 mic_mode =4; //业务id？
message ApplyHoldMicReq{
	uint32 cid      =1;
	uint32 uid      =2;
	uint32 mic_id   =3; //指定申请哪个麦位id,为0则由麦位服务分配
  uint32 begin_mic_id = 4; // 指定申请的起始麦位id,为0不指定
  uint32 end_mic_id = 5; // 指定申请的结束麦位id,为0不指定
}

message ApplyHoldMicResp{
	uint32 mic_id   =1;
	string token    =2;
}
message VerifyHoldMicReq{
	uint32 cid      =1;
	uint32 uid      =2;
	uint32 mic_id   =3;
	string token    =4;
}
message VerifyHoldMicResp{
}

message ApplyHoldMicPreCheckReq {
	uint32 cid = 1;
	uint32 uid = 2;
	uint32 mic_id = 3; // 指定检查的麦位id，为0则检查是否能分配麦位
}

message ApplyHoldMicPreCheckResp {
}

// 麦位信息
message MicrSpaceInfo {
	uint32 mic_id = 1;                       // 麦位ID 1 - 9
	uint32 mic_state = 2;                    // ga::EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	uint32 mic_uid = 3;                      // 麦上用户 如果麦上有人的话
	uint32 mic_ts = 4;                       // 麦位最后更新时间
}

// 对所有空麦位锁麦
message DisableAllEmptyMicrSpaceReq {
	uint32 channel_id = 1;
	uint32 op_uid = 2;
}

message DisableAllEmptyMicrSpaceResp {
	repeated uint32 disable_micid_list = 1;   // 被关闭的麦位ID
	repeated MicrSpaceInfo all_mic_list = 2;  // 当前全量麦位列表信息
	uint32 mic_mode  = 3;            					// 麦模式
	uint64 server_time_ms  = 4;      					// 64bit 毫秒级 服务器时间
}

// 设置麦位状态 比如打开/关闭/禁言 麦位
message SetChannelMicSpaceStatusReq {
	uint32 channel_id = 1;
	uint32 op_uid = 2;
	MicrSpaceInfo mic_info = 3;
	uint32 channel_display_id = 4;
	uint32 channel_type = 5;
}

message SetChannelMicSpaceStatusResp {
	repeated MicrSpaceInfo all_mic_list = 1; // 当前全量麦位列表信息
	uint32 kicked_uid = 2;	                 // 如果麦位上有人 且 请求锁麦 那么原来麦上的用户会被踢下麦
	uint64 server_time_ms  = 3;              // 64bit 毫秒级 服务器时间
	uint32 mic_mode  = 4;                    // mic mode
}

message BatchSetChannelMicSpaceStatusReq {
	uint32 channel_id = 1;
	uint32 op_uid = 2;
	repeated uint32 mic_id_list = 3;
	uint32 mic_status = 4;
	uint32 channel_display_id = 5;
	uint32 channel_type = 6;
}

message BatchSetChannelMicSpaceStatusResp {
	repeated MicrSpaceInfo all_mic_list = 1;     // 当前全量麦位列表信息
	repeated MicrSpaceInfo kicked_micr_list = 2; // 如果麦位上有人 且 请求锁麦 那么原来麦上的用户会被踢下麦
	uint64 server_time_ms  = 3;                  // 64bit 毫秒级 服务器时间
	uint32 mic_mode  = 4;                        // mic mode
}

// 假上麦，触发麦位时间更新和麦位kafka
message FakeHoldMicrSpaceReq {
	uint32 channel_id = 1;
	uint32 uid = 2;
	uint32 mic_pos_id = 3;  // 麦位ID
	uint32 channel_display_id = 5;
	uint32 channel_type = 6;
	uint32 user_sex = 7;
	uint32 op_uid = 8;
}

message FakeHoldMicrSpaceResp {
	MicrSpaceInfo open_mic_info = 1;         // 麦位
	repeated MicrSpaceInfo all_mic_list = 3; // 全体麦位信息 包括各个麦位状态
	uint64 server_time_ms  = 4;              // 64bit 毫秒级 服务器时间
}

message SimpleHoldMicrSpaceReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
  uint32 mic_id = 3;  // 麦位ID
  uint32 op_uid = 4;
  uint32 channel_display_id = 5;
  uint32 channel_type = 6;
  uint32 user_sex = 7;
}

message SimpleHoldMicrSpaceResp {
  MicrSpaceInfo open_mic_info = 1;          // 麦位
  repeated MicrSpaceInfo all_mic_list = 2;  // 全体麦位信息 包括各个麦位状态
  uint64 server_time_ms  = 3;               // 64bit 毫秒级 服务器时间
}

message SimpleReleaseMicrSpaceReq {
  uint32 channel_id = 1;
  uint32 uid = 2;
  uint32 switch_flag = 3;
  uint32 channel_display_id = 4;
  uint32 channel_type = 5;
  int32 user_sex = 6;
  uint32 op_uid = 7;
}

message SimpleReleaseMicrSpaceResp {
  MicrSpaceInfo close_mic_info = 1;         // 成功被下麦的麦位信息 如果没有下麦那么该值为空
  repeated MicrSpaceInfo all_mic_list = 2;  // 全体麦位信息 包括各个麦位状态
  uint64 server_time_ms  = 3;               // 64bit 毫秒级 服务器时间
  bool is_auto_disable_mic = 4;             // 是否自动完成了锁麦
}

message ChangeMicrophoneReq {
  uint32 op_uid = 1;
  uint32 channel_id = 2;
  MicrSpaceInfo to_mic_info = 3;  // 目标麦位
  uint32 switch_flag = 4;
  uint32 channel_display_id = 5;
  uint32 channel_type = 6;
}

message ChangeMicrophoneResp {
  MicrSpaceInfo from_mic_info  = 1;         // 如果换成功 源麦位的状态信息
  MicrSpaceInfo to_mic_info  = 2;           // 如果换成功 目标麦位的状态信息
  uint64 server_time_ms  = 3;               // 64bit 毫秒级 服务器时间
  repeated MicrSpaceInfo all_mic_list = 4;  // 当前全量麦位列表信息
  uint32 mic_mode = 5;                      // 当前麦模式
}

message KickoutChannelMicReq {
  uint32 op_uid = 1;
  uint32 channel_id = 2;
  repeated uint32 target_uid_list = 3;
  uint32 ban_second = 4;   // 踢下麦后 多长时间 禁止上麦。 0为不限制
  uint32 switch_flag = 5;  // 房间开关flag
}

message KickoutChannelMicResp {
  uint32 channel_id = 1;
  repeated uint32 disable_mic_id_list = 2;      // 如果开启了自动锁麦 这里是被锁的麦位ID列表
  repeated MicrSpaceInfo kickout_mic_list = 3;  // 成功被踢的麦位列表
  repeated MicrSpaceInfo all_mic_list = 4;      // 当前全量麦位列表信息
  uint64 server_time_ms  = 5;                   // 64bit 毫秒级 服务器时间
}

message ReInitChannelMicDataReq {
  uint32 uid = 1;
  uint32 cid = 2;
  uint32 mic_num = 3;
  uint32 mic_mode = 4;        // 兼容之前的接口用
  uint32 scheme_id = 5;       // 记录是切换到什么玩法时初始化的麦位数据
  bool not_kick_out_mic = 6;  // 不踢掉麦上所有人，默认是踢的
  bool not_unlock_mic = 7;    // 不解锁已经锁上的麦位，默认是解锁的
  bool unmute_mic = 8 ;       // 把禁言的麦位解禁言，默认是不解的
  uint32 channel_display_id = 9;
  uint32 channel_type = 10;
  bool use_new_control_mic = 11;
}

message ReInitChannelMicDataResp {
  uint32 channel_id = 1;
  // 因为本次模式切换 而变化的麦位信息
  repeated MicrSpaceInfo enable_mic_list = 2;
  repeated MicrSpaceInfo kickout_mic_list = 3;
  MicrSpaceInfo hold_mic_info = 4;
  // 模式设置之后 当前的麦位信息
  repeated MicrSpaceInfo all_mic_list = 5;  // 当前全量麦位列表信息
  uint64 server_time_ms  = 6;               // 64bit 毫秒级 服务器时间
  // 兼容之前的接口
  uint32 mic_mode = 7;       // 修改后的麦模式
  uint32 from_mic_mode = 8;  // 修改前的麦模式
}

message SetChannelMicModeReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  uint32 mic_mode = 3;          // 1主席模式 2自由模式 3娱乐模式 see ga::EChannelMicMode
  bool is_disable_all_mic = 4;  // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
  bool is_need_hold_mic = 5;    // 本人是否需要上麦 仅在HAVE_MIC_SPACE_MODE模式下有效
}

message SetChannelMicModeResp {
  uint32 channel_id = 1;
  // 因为本次模式切换 而变化的麦位信息
  repeated MicrSpaceInfo disable_mic_list = 2;
  repeated MicrSpaceInfo enable_mic_list = 3;
  repeated MicrSpaceInfo kickout_mic_list = 4;
  MicrSpaceInfo hold_mic_info = 5;

  // 模式设置之后 当前的麦位信息
  repeated MicrSpaceInfo all_mic_list = 6;  // 当前全量麦位列表信息
  uint64 server_time_ms  = 7;               // 64bit 毫秒级 服务器时间

  uint32 mic_mode = 8;       // 修改后的麦模式
  uint32 from_mic_mode = 9;  // 修改前的麦模式
}

message GetMicrListReq {
  uint32 channel_id = 1;
  bool force_local_time = 2; // 此值为true则返回服务器本地时间，兼容安卓5.5.10之前的版本，返回redis时间可能导致客户端显示所有麦位被锁
}

message GetMicrListResp {
  uint32 channel_id = 1;
  repeated MicrSpaceInfo all_mic_list = 2; // 全体麦位信息 包括各个麦位状态
  uint32 micr_mode = 3;                    // 麦模式
  uint64 server_time_ms = 4;               // 64bit 毫秒级 服务器时间
  uint32 mic_num = 5;                      // 有效的麦位数量
}