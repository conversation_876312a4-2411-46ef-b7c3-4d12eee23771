package tbean

import (
	"fmt"
	"gitlab.ttyuyin.com/golang/gudetama/log"
	"golang.org/x/net/context"
	"testing"
	"time"
)

const (
	TT10100     = 10100
	transferUid = 500009
)

func init() {
	log.Init("TestClient_TransferI2C", log.DebugLevel, log.UseDefaultLogShmConfigPath)
}

func TestClient_TransferI2C(t *testing.T) {
	b1, err := cli.GetBalance(context.Background(), AppID_TT, AccountTypeCustomer, transferUid)
	if err != nil {
		t.Fatalf("Failed to get balance of transferUid before transfer, %v", err)
	}

	ib1, _ := cli.GetBalance(context.Background(), AppID_TT, AccountTypeInternal, TT10100)

	var outTradeNo = fmt.Sprintf("TestTransferI2C%d", time.Now().Unix())
	resp, err := cli.TransferI2C(context.Background(), &TransferI2CRequest{
		AppId:      AppID_TT,
		OutTradeNo: outTradeNo,
		From:       TT10100,
		To: []*TransferI2CRequest_To{
			{
				Uid:    transferUid,
				Amount: 1,
			},
		},
		Notes: "TestTransferI2C",
	})

	if err == nil {
		t.Logf("Test TransferI2C OK resp %+v", resp)
	} else {
		t.Fatalf("Test TransferI2C Failed, %v", err)
	}

	b2, err := cli.GetBalance(context.Background(), AppID_TT, AccountTypeCustomer, transferUid)
	if err != nil {
		t.Fatalf("Failed to get balance of transferUid after transfer, %v", err)
	}

	ib2, _ := cli.GetBalance(context.Background(), AppID_TT, AccountTypeInternal, TT10100)
	t.Logf("TT10100 balance: %d -> %d", ib1, ib2)
	t.Logf("%d balance: %d -> %d", transferUid, b1, b2)
}
