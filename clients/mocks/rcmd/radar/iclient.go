// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/rcmd/radar/iclient.go

// Package radar is a generated GoMock package.
package radar

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	radar "golang.52tt.com/protocol/services/rcmd/radar"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetKuolieCardList mocks base method.
func (m *MockIClient) GetKuolieCardList(ctx context.Context, req *radar.GetKuolieCardListReq) (*radar.GetKuolieCardListRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKuolieCardList", ctx, req)
	ret0, _ := ret[0].(*radar.GetKuolieCardListRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetKuolieCardList indicates an expected call of GetKuolieCardList.
func (mr *MockIClientMockRecorder) GetKuolieCardList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKuolieCardList", reflect.TypeOf((*MockIClient)(nil).GetKuolieCardList), ctx, req)
}

// GetRadarDeliveredCount mocks base method.
func (m *MockIClient) GetRadarDeliveredCount(ctx context.Context, req radar.GetRadarDeliveredCountReq) (*radar.GetRadarDeliveredCountRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRadarDeliveredCount", ctx, req)
	ret0, _ := ret[0].(*radar.GetRadarDeliveredCountRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRadarDeliveredCount indicates an expected call of GetRadarDeliveredCount.
func (mr *MockIClientMockRecorder) GetRadarDeliveredCount(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadarDeliveredCount", reflect.TypeOf((*MockIClient)(nil).GetRadarDeliveredCount), ctx, req)
}

// GetRadarList mocks base method.
func (m *MockIClient) GetRadarList(ctx context.Context, req *radar.GetRadarListReq) (*radar.GetRadarListRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRadarList", ctx, req)
	ret0, _ := ret[0].(*radar.GetRadarListRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRadarList indicates an expected call of GetRadarList.
func (mr *MockIClientMockRecorder) GetRadarList(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRadarList", reflect.TypeOf((*MockIClient)(nil).GetRadarList), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
