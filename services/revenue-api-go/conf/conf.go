package conf

import (
	revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
	"golang.52tt.com/services/revenue-api-go/mgr/dispatcher"
)

var deafaultSceneConfig = map[uint32][]uint32{
	uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypeOnMic):   {uint32(dispatcher.RevenueInfoTypeHeadwear), uint32(dispatcher.RevenueInfoTypeVirtualAvatar)},
	uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypeOffMic):  []uint32{},
	uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypeKickMic): []uint32{},
	uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypePullMic): {uint32(dispatcher.RevenueInfoTypeHeadwear), uint32(dispatcher.RevenueInfoTypeVirtualAvatar)},
	uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypeEnterChannel): {uint32(dispatcher.RevenueInfoTypePresentBox), uint32(dispatcher.RevenueInfoTypePgcChannelTicket), uint32(dispatcher.RevenueInfoTypeRoiUser),
		uint32(dispatcher.RevenueInfoTypeNobilityUser), uint32(dispatcher.RevenueInfoTypeVirtualAvatar), uint32(dispatcher.RevenueInfoTypeQualityUser), uint32(dispatcher.RevenueInfoTypeVirtualImage)},
	uint32(revenue_api_go.RevenueSceneType_RevenueSceneTypeNormalChannelMessage): {uint32(dispatcher.RevenueInfoTypePgcChannelTicket), uint32(dispatcher.RevenueInfoTypeRoiUser),
		uint32(dispatcher.RevenueInfoTypeNobilityUser), uint32(dispatcher.RevenueInfoTypeQualityUser), uint32(dispatcher.RevenueInfoTypeCommonFollowEnter),
		uint32(dispatcher.RevenueInfoTypePgcGloryTitle)},
}

func (h *RevenueApiGo) GetDeafaultSceneConfig(scene uint32) []uint32 {
	if deafaultSceneConfig[scene] == nil {
		return []uint32{}
	}
	return deafaultSceneConfig[scene]
}
