package channelmemberviprank

import (
	"context"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channelmemberVipRank"
)

type IClient interface {
	client.BaseClient
	OperAddUserChannelTCoin(ctx context.Context, uin uint32, in *pb.OperAddUserChannelTCoinReq) (*pb.OperAddUserChannelTCoinResp, protocol.ServerError)
	GetHourRankList(ctx context.Context, uin uint32, tagId, hour, begin, limit uint32) (*pb.GetChannelHourRankListResp, protocol.ServerError)
	GetMemberRankList(ctx context.Context, uin, cid, idx, size uint32) (*pb.GetMemberRankListResp, protocol.ServerError)
	BatGetUserConsumeInfo(ctx context.Context, uid, cid uint32, uidList []uint32) (map[uint32]*pb.ChannelMemberVip, protocol.ServerError)
	GetUserConsumeInfo(ctx context.Context, uid, cid uint32) (*pb.MemberConsumeInfo, protocol.ServerError)
	RemoveLiveChannelOnlineRank(ctx context.Context, uin, cid uint32) protocol.ServerError
	HideChannelConsume(ctx context.Context, uin, cid uint32, isCancel bool) protocol.ServerError
	GetConsumeTopN(ctx context.Context, uin, cid, beginIndex, viewCnt, limit, channelType uint32, isShowHidden bool) (*pb.GetConsumeTopNResp, protocol.ServerError)
	GetMemberNobilityInfo(ctx context.Context, uid, cid uint32) (*pb.GetMemberNobilityInfoResp, protocol.ServerError)
	GetUnderTheMircoRank(ctx context.Context, uin, cid, begin, limit uint32) (*pb.GetUnderTheMircoRankResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
