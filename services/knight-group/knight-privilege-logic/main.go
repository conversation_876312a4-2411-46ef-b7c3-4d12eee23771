package main

import (
	"context"
	"fmt"
	"os"

	grpcmiddleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	pb "golang.52tt.com/protocol/services/logicsvr-go/knight-privilege-logic"
	"golang.52tt.com/services/knight-group/knight-privilege-logic/server"
	"golang.52tt.com/services/runtime"
	"google.golang.org/grpc"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/logic" // 兼容tyr公共库
)

func main() {

	flags := grpcEx.ParseServerFlags(os.Args)
	fmt.Println(os.Args[0])

	var (
		svr *server.KnightPrivilegeLogicServerImpl
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.NewKnightPrivilegeLogicServerImpl(ctx, sc.Configer)
		if err != nil {
			return err
		}

		pb.RegisterKnightPrivilegeLogicServer(s, svr)
		return nil
	}

	closer := func(ctx context.Context, s *grpc.Server) error {
		return nil
	}

	unaryInt := grpcmiddleware.ChainUnaryServer(
		runtime.LogicServerUnaryInterceptor(flags.LogRequests, flags.LogResponses),
	)

	s := grpcEx.NewServer(
		flags,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("knight-privilege-logic.json", grpcEx.AdapterJSON),
	)

	s.Serve()
}
