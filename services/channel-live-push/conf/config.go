package conf

import (
	"encoding/json"
	"fmt"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
)

type ServiceConfigT struct {
	RedisConfig *config.RedisConfig `json:"redis"`
	MysqlConfig *config.MysqlConfig `json:"mysql"`

	ChLiveKafkaConfig  *config.KafkaConfig `json:"ch_live_kafka_config"`
	ChLiveFansConfig   *config.KafkaConfig `json:"ch_live_fans_config"`
	EnterChannelConfig *config.KafkaConfig `json:"enter_channel_config"`
	FollowKafkaConfig  *config.KafkaConfig `json:"follow_kafka_config"`
	MicKafkaConfig     *config.KafkaConfig `json:"mic_kafka_config"`
	PresentKafkaConfig *config.KafkaConfig `json:"present_kafka"`
	OnlineKafkaConfig  *config.KafkaConfig `json:"online_kafka_config"`
	AuthKafkaConfig    *config.KafkaConfig `json:"auth_kafka_config"`
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &sc)
	if err != nil {
		return err
	}

	log.Debugf(" server_conf:%+v", sc)

	return
}

func (sc *ServiceConfigT) GetPresentKafkaConfig() *config.KafkaConfig {
	return sc.PresentKafkaConfig
}

func (sc *ServiceConfigT) GetMysqlConfig() *config.MysqlConfig {
	return sc.MysqlConfig
}

func (sc *ServiceConfigT) GetMysqlConnectionString() string {
	return sc.MysqlConfig.ConnectionString()
}

func (sc *ServiceConfigT) GetRedisConfig() *config.RedisConfig {
	return sc.RedisConfig
}
