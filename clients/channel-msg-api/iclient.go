package channel_msg_api

import (
	"context"

	"google.golang.org/grpc"

	channelPB "golang.52tt.com/protocol/app/channel"
)

type IClient interface {
	// SimplePushToChannel 发送房间内广播消息
	//  fromUid：操作者的uid
	//  channelId：房间ID，必填参数
	//  channelMsgType：房间消息类型，必填参数
	//  content：消息的明文UTF8内容部分
	//  pbOptData：不同房间消息类型对应不同的pb协议，需要时统一定义在channel_opt_.proto
	// 返回值
	//  requestId：该消息唯一标识，用于跟踪推送链路
	SimplePushToChannel(ctx context.Context, fromUid, channelId, channelMsgType uint32,
		content string, pbOptData []byte) (requestId string, err error)

	// SimplePushToUsers 发送房间消息给指定用户
	//  recvUids：接收者uid列表，必填参数
	//  fromUid：操作者的uid
	//  channelId：房间ID，必填参数
	//  channelMsgType：房间消息类型，必填参数
	//  content：消息的明文UTF8内容部分（可为文本内容或json消息）
	//  pbOptData：不同房间消息类型对应不同的pb协议，需要时统一定义在channel_opt_.proto
	//  isReliable：是否使用推送系统的RELIABLE策略，过期前保证送达
	// 返回值
	//  requestId：该消息唯一标识，用于跟踪推送链路
	SimplePushToUsers(ctx context.Context, recvUids []uint32, fromUid, channelId, channelMsgType uint32,
		content string, pbOptData []byte, isReliable bool) (requestId string, err error)

	/*******************高级接口*******************/

	// PushToChannel 发送房间内广播消息
	PushToChannel(ctx context.Context,
		rawMsg *channelPB.ChannelBroadcastMsg, opts ...Option) (requestId string, err error)

	// PushToUsers 发送房间消息给指定用户
	PushToUsers(ctx context.Context,
		recvUids []uint32, rawMsg *channelPB.ChannelBroadcastMsg, opts ...Option) (requestId string, err error)
}

func NewIClient(opts ...grpc.DialOption) (IClient, error) {
	return newClientImpl(opts...)
}
