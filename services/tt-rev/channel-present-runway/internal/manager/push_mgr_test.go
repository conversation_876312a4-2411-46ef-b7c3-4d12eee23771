package manager

import (
	"context"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/protocol/app"
	channelIm "golang.52tt.com/protocol/services/channelim"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	"testing"
)

func TestChannelPresentRunwayMgr_pushMsgToChannel(t *testing.T) {

	type args struct {
		ctx         context.Context
		fromUid     uint32
		fakeId      uint32
		fromAccount string
		fromNick    string
		channelId   uint32
		msgType     uint32
		content     string
		data        []byte
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(helper *pushTestHelper)
	}{
		{
			name: "normal",
			args: args{
				ctx:         context.Background(),
				fromUid:     1,
				fakeId:      1,
				fromAccount: "1",
				fromNick:    "1",
				channelId:   1,
				msgType:     1,
			},
			wantErr: false,
			initFunc: func(helper *pushTestHelper) {
				helper.GetUkwCli().EXPECT().GetTrueUidByFake(gomock.Any(), gomock.Any()).Return(uint32(1), nil)
				helper.GetPushCli().EXPECT().PushMulticasts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := newPushTestHelper(t)
			if tt.initFunc != nil {
				tt.initFunc(mgr)
			}
			if err := mgr.PushMsgToChannel(tt.args.ctx, tt.args.fromUid, tt.args.fakeId, tt.args.fromAccount, tt.args.fromNick, tt.args.channelId, tt.args.msgType, tt.args.content, tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("PushMsgToChannel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChannelPresentRunwayMgr_PushBreakingNewsV3(t *testing.T) {

	type args struct {
		ctx                 context.Context
		uid                 uint32
		channelId           uint32
		breakingNewsMessage *publicNoticePb.CommonBreakingNewsV3
	}
	tests := []struct {
		name     string
		args     args
		initFunc func(helper *pushTestHelper)
	}{
		{
			name: "normal",
			args: args{
				ctx:                 context.Background(),
				uid:                 1,
				channelId:           1,
				breakingNewsMessage: &publicNoticePb.CommonBreakingNewsV3{},
			},
			initFunc: func(helper *pushTestHelper) {
				helper.GetPublicNoticeCli().EXPECT().PushBreakingNews(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := newPushTestHelper(t)
			if tt.initFunc != nil {
				tt.initFunc(mgr)
			}
			mgr.PushBreakingNewsV3(tt.args.ctx, tt.args.uid, tt.args.channelId, tt.args.breakingNewsMessage)
		})
	}
}

func TestChannelPresentRunwayMgr_pushChannelSysMsg(t *testing.T) {

	type args struct {
		ctx         context.Context
		fromUid     uint32
		channelId   uint32
		msgType     uint32
		fromAccount string
		fromNick    string
		sex         int32
		toast       string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  bool
		initFunc func(helper *pushTestHelper)
	}{
		{
			name: "normal",
			args: args{
				ctx:         context.Background(),
				fromUid:     1,
				channelId:   1,
				msgType:     1,
				fromAccount: "1",
				fromNick:    "1",
				sex:         1,
				toast:       "1",
			},
			wantErr: false,
			initFunc: func(helper *pushTestHelper) {
				helper.GetUserProfileCli().EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&app.UserProfile{}, nil)
				helper.GetChannelImCli().EXPECT().SendChannelMsg(context.Background(), uint32(1), uint32(1), uint32(1), uint32(channelIm.ChannelMsgType_CHANNEL_COMMON_MSG), gomock.Any(), uint32(0), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := newPushTestHelper(t)
			if tt.initFunc != nil {
				tt.initFunc(mgr)
			}
			if err := mgr.PushChannelSysMsg(tt.args.ctx, tt.args.fromUid, tt.args.channelId, tt.args.msgType, tt.args.fromAccount, tt.args.fromNick, tt.args.sex, tt.args.toast); (err != nil) != tt.wantErr {
				t.Errorf("PushChannelSysMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
