// Code generated by protoc-gen-gogo.
// source: services/gamelotto/voucher/gamevoucher.proto
// DO NOT EDIT!

/*
	Package gamevoucher is a generated protocol buffer package.

	It is generated from these files:
		services/gamelotto/voucher/gamevoucher.proto

	It has these top-level messages:
		GrantGameVoucherReq
		GrantGameVoucherResp
		GameVoucher
		VoucherSummaryFilter
		QueryGameVoucherSummaryReq
		GameVoucherSummary
		GameVoucherSummaryList
		VoucherListFilter
		QueryGameVoucherListReq
		GameVoucherList
		VoucherRechargeInfo
		GetGameVoucherReq
		LYGameVoucher
		GetLYGameVoucherByOrderReq
		GetLYGameVoucherByOrderResp
		GameVoucherRechargeSummary
		GameVoucherRechargeSummaryList
		QueryGameVoucherRechargeSummaryReq
*/
package gamevoucher

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import math3 "math"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 业务类别, 用于区分进行统计
type VoucherBizId int32

const (
	VoucherBizId_LoginLotto         VoucherBizId = 1
	VoucherBizId_RechargeLotto      VoucherBizId = 2
	VoucherBizId_GreenerGiftPackage VoucherBizId = 3
)

var VoucherBizId_name = map[int32]string{
	1: "LoginLotto",
	2: "RechargeLotto",
	3: "GreenerGiftPackage",
}
var VoucherBizId_value = map[string]int32{
	"LoginLotto":         1,
	"RechargeLotto":      2,
	"GreenerGiftPackage": 3,
}

func (x VoucherBizId) Enum() *VoucherBizId {
	p := new(VoucherBizId)
	*p = x
	return p
}
func (x VoucherBizId) String() string {
	return proto.EnumName(VoucherBizId_name, int32(x))
}
func (x *VoucherBizId) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(VoucherBizId_value, data, "VoucherBizId")
	if err != nil {
		return err
	}
	*x = VoucherBizId(value)
	return nil
}
func (VoucherBizId) EnumDescriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{0} }

type GrantGameVoucherReq struct {
	BizId          uint32 `protobuf:"varint,1,req,name=biz_id,json=bizId" json:"biz_id"`
	BizOrderId     string `protobuf:"bytes,2,req,name=biz_order_id,json=bizOrderId" json:"biz_order_id"`
	Uid            uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
	LyGameId       uint32 `protobuf:"varint,4,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	Amount         uint32 `protobuf:"varint,5,req,name=amount" json:"amount"`
	EffectiveDate  string `protobuf:"bytes,6,req,name=effective_date,json=effectiveDate" json:"effective_date"`
	ExpiryDate     string `protobuf:"bytes,7,req,name=expiry_date,json=expiryDate" json:"expiry_date"`
	NumericAccount string `protobuf:"bytes,8,req,name=numeric_account,json=numericAccount" json:"numeric_account"`
	LyCpId         string `protobuf:"bytes,9,opt,name=ly_cp_id,json=lyCpId" json:"ly_cp_id"`
	// 额外信息
	DeviceId    string `protobuf:"bytes,16,opt,name=device_id,json=deviceId" json:"device_id"`
	TtChannelId string `protobuf:"bytes,17,opt,name=tt_channel_id,json=ttChannelId" json:"tt_channel_id"`
	LyChannelId string `protobuf:"bytes,18,opt,name=ly_channel_id,json=lyChannelId" json:"ly_channel_id"`
	Remark      string `protobuf:"bytes,19,opt,name=remark" json:"remark"`
	BizData     []byte `protobuf:"bytes,20,opt,name=biz_data,json=bizData" json:"biz_data"`
	LimitAmount uint32 `protobuf:"varint,21,opt,name=limit_amount,json=limitAmount" json:"limit_amount"`
}

func (m *GrantGameVoucherReq) Reset()                    { *m = GrantGameVoucherReq{} }
func (m *GrantGameVoucherReq) String() string            { return proto.CompactTextString(m) }
func (*GrantGameVoucherReq) ProtoMessage()               {}
func (*GrantGameVoucherReq) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{0} }

func (m *GrantGameVoucherReq) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *GrantGameVoucherReq) GetBizOrderId() string {
	if m != nil {
		return m.BizOrderId
	}
	return ""
}

func (m *GrantGameVoucherReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GrantGameVoucherReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GrantGameVoucherReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *GrantGameVoucherReq) GetEffectiveDate() string {
	if m != nil {
		return m.EffectiveDate
	}
	return ""
}

func (m *GrantGameVoucherReq) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *GrantGameVoucherReq) GetNumericAccount() string {
	if m != nil {
		return m.NumericAccount
	}
	return ""
}

func (m *GrantGameVoucherReq) GetLyCpId() string {
	if m != nil {
		return m.LyCpId
	}
	return ""
}

func (m *GrantGameVoucherReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GrantGameVoucherReq) GetTtChannelId() string {
	if m != nil {
		return m.TtChannelId
	}
	return ""
}

func (m *GrantGameVoucherReq) GetLyChannelId() string {
	if m != nil {
		return m.LyChannelId
	}
	return ""
}

func (m *GrantGameVoucherReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *GrantGameVoucherReq) GetBizData() []byte {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *GrantGameVoucherReq) GetLimitAmount() uint32 {
	if m != nil {
		return m.LimitAmount
	}
	return 0
}

type GrantGameVoucherResp struct {
}

func (m *GrantGameVoucherResp) Reset()                    { *m = GrantGameVoucherResp{} }
func (m *GrantGameVoucherResp) String() string            { return proto.CompactTextString(m) }
func (*GrantGameVoucherResp) ProtoMessage()               {}
func (*GrantGameVoucherResp) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{1} }

type GameVoucher struct {
	VoucherId          uint64  `protobuf:"varint,1,req,name=voucher_id,json=voucherId" json:"voucher_id"`
	BizId              uint32  `protobuf:"varint,2,req,name=biz_id,json=bizId" json:"biz_id"`
	BizOrderId         string  `protobuf:"bytes,3,req,name=biz_order_id,json=bizOrderId" json:"biz_order_id"`
	BizData            []byte  `protobuf:"bytes,4,opt,name=biz_data,json=bizData" json:"biz_data"`
	Uid                uint32  `protobuf:"varint,5,req,name=uid" json:"uid"`
	NumericAccount     string  `protobuf:"bytes,6,req,name=numeric_account,json=numericAccount" json:"numeric_account"`
	LyGameId           uint32  `protobuf:"varint,7,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LyCpType           string  `protobuf:"bytes,8,req,name=ly_cp_type,json=lyCpType" json:"ly_cp_type"`
	LyCpId             string  `protobuf:"bytes,9,req,name=ly_cp_id,json=lyCpId" json:"ly_cp_id"`
	TtChannelId        string  `protobuf:"bytes,10,req,name=tt_channel_id,json=ttChannelId" json:"tt_channel_id"`
	DeviceId           string  `protobuf:"bytes,11,req,name=device_id,json=deviceId" json:"device_id"`
	GrantTime          uint32  `protobuf:"varint,12,req,name=grant_time,json=grantTime" json:"grant_time"`
	TotalAmount        float64 `protobuf:"fixed64,13,req,name=total_amount,json=totalAmount" json:"total_amount"`
	ConsumedAmount     float64 `protobuf:"fixed64,14,req,name=consumed_amount,json=consumedAmount" json:"consumed_amount"`
	TotalConsumeFee    float64 `protobuf:"fixed64,15,req,name=total_consume_fee,json=totalConsumeFee" json:"total_consume_fee"`
	CashConsumeFee     float64 `protobuf:"fixed64,16,req,name=cash_consume_fee,json=cashConsumeFee" json:"cash_consume_fee"`
	LastConsumeTime    uint64  `protobuf:"varint,17,req,name=last_consume_time,json=lastConsumeTime" json:"last_consume_time"`
	LastConsumeOrderId string  `protobuf:"bytes,18,req,name=last_consume_order_id,json=lastConsumeOrderId" json:"last_consume_order_id"`
	EffectiveDate      string  `protobuf:"bytes,19,req,name=effective_date,json=effectiveDate" json:"effective_date"`
	ExpiryDate         string  `protobuf:"bytes,20,req,name=expiry_date,json=expiryDate" json:"expiry_date"`
	Remark             string  `protobuf:"bytes,21,req,name=remark" json:"remark"`
}

func (m *GameVoucher) Reset()                    { *m = GameVoucher{} }
func (m *GameVoucher) String() string            { return proto.CompactTextString(m) }
func (*GameVoucher) ProtoMessage()               {}
func (*GameVoucher) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{2} }

func (m *GameVoucher) GetVoucherId() uint64 {
	if m != nil {
		return m.VoucherId
	}
	return 0
}

func (m *GameVoucher) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *GameVoucher) GetBizOrderId() string {
	if m != nil {
		return m.BizOrderId
	}
	return ""
}

func (m *GameVoucher) GetBizData() []byte {
	if m != nil {
		return m.BizData
	}
	return nil
}

func (m *GameVoucher) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GameVoucher) GetNumericAccount() string {
	if m != nil {
		return m.NumericAccount
	}
	return ""
}

func (m *GameVoucher) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GameVoucher) GetLyCpType() string {
	if m != nil {
		return m.LyCpType
	}
	return ""
}

func (m *GameVoucher) GetLyCpId() string {
	if m != nil {
		return m.LyCpId
	}
	return ""
}

func (m *GameVoucher) GetTtChannelId() string {
	if m != nil {
		return m.TtChannelId
	}
	return ""
}

func (m *GameVoucher) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GameVoucher) GetGrantTime() uint32 {
	if m != nil {
		return m.GrantTime
	}
	return 0
}

func (m *GameVoucher) GetTotalAmount() float64 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

func (m *GameVoucher) GetConsumedAmount() float64 {
	if m != nil {
		return m.ConsumedAmount
	}
	return 0
}

func (m *GameVoucher) GetTotalConsumeFee() float64 {
	if m != nil {
		return m.TotalConsumeFee
	}
	return 0
}

func (m *GameVoucher) GetCashConsumeFee() float64 {
	if m != nil {
		return m.CashConsumeFee
	}
	return 0
}

func (m *GameVoucher) GetLastConsumeTime() uint64 {
	if m != nil {
		return m.LastConsumeTime
	}
	return 0
}

func (m *GameVoucher) GetLastConsumeOrderId() string {
	if m != nil {
		return m.LastConsumeOrderId
	}
	return ""
}

func (m *GameVoucher) GetEffectiveDate() string {
	if m != nil {
		return m.EffectiveDate
	}
	return ""
}

func (m *GameVoucher) GetExpiryDate() string {
	if m != nil {
		return m.ExpiryDate
	}
	return ""
}

func (m *GameVoucher) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type VoucherSummaryFilter struct {
	BizId        uint32   `protobuf:"varint,1,opt,name=biz_id,json=bizId" json:"biz_id"`
	LyGameId     []uint32 `protobuf:"varint,2,rep,name=ly_game_id,json=lyGameId" json:"ly_game_id,omitempty"`
	LyCpType     []string `protobuf:"bytes,3,rep,name=ly_cp_type,json=lyCpType" json:"ly_cp_type,omitempty"`
	GrantTimeMin uint32   `protobuf:"varint,4,opt,name=grant_time_min,json=grantTimeMin" json:"grant_time_min"`
	GrantTimeMax uint32   `protobuf:"varint,5,opt,name=grant_time_max,json=grantTimeMax" json:"grant_time_max"`
}

func (m *VoucherSummaryFilter) Reset()                    { *m = VoucherSummaryFilter{} }
func (m *VoucherSummaryFilter) String() string            { return proto.CompactTextString(m) }
func (*VoucherSummaryFilter) ProtoMessage()               {}
func (*VoucherSummaryFilter) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{3} }

func (m *VoucherSummaryFilter) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *VoucherSummaryFilter) GetLyGameId() []uint32 {
	if m != nil {
		return m.LyGameId
	}
	return nil
}

func (m *VoucherSummaryFilter) GetLyCpType() []string {
	if m != nil {
		return m.LyCpType
	}
	return nil
}

func (m *VoucherSummaryFilter) GetGrantTimeMin() uint32 {
	if m != nil {
		return m.GrantTimeMin
	}
	return 0
}

func (m *VoucherSummaryFilter) GetGrantTimeMax() uint32 {
	if m != nil {
		return m.GrantTimeMax
	}
	return 0
}

type QueryGameVoucherSummaryReq struct {
	Filter *VoucherSummaryFilter `protobuf:"bytes,1,req,name=filter" json:"filter,omitempty"`
}

func (m *QueryGameVoucherSummaryReq) Reset()         { *m = QueryGameVoucherSummaryReq{} }
func (m *QueryGameVoucherSummaryReq) String() string { return proto.CompactTextString(m) }
func (*QueryGameVoucherSummaryReq) ProtoMessage()    {}
func (*QueryGameVoucherSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{4}
}

func (m *QueryGameVoucherSummaryReq) GetFilter() *VoucherSummaryFilter {
	if m != nil {
		return m.Filter
	}
	return nil
}

type GameVoucherSummary struct {
	// index
	BizId    uint32 `protobuf:"varint,1,req,name=biz_id,json=bizId" json:"biz_id"`
	LyGameId uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LyCpType string `protobuf:"bytes,3,req,name=ly_cp_type,json=lyCpType" json:"ly_cp_type"`
	// data
	GrantCount       uint32  `protobuf:"varint,4,opt,name=grant_count,json=grantCount" json:"grant_count"`
	UseCount         uint32  `protobuf:"varint,5,opt,name=use_count,json=useCount" json:"use_count"`
	FullUseCount     uint32  `protobuf:"varint,6,opt,name=full_use_count,json=fullUseCount" json:"full_use_count"`
	ExpireCount      uint32  `protobuf:"varint,7,opt,name=expire_count,json=expireCount" json:"expire_count"`
	FullExpireCount  uint32  `protobuf:"varint,8,opt,name=full_expire_count,json=fullExpireCount" json:"full_expire_count"`
	TotalConsumeFee  float64 `protobuf:"fixed64,9,opt,name=total_consume_fee,json=totalConsumeFee" json:"total_consume_fee"`
	CashConsumeFee   float64 `protobuf:"fixed64,10,opt,name=cash_consume_fee,json=cashConsumeFee" json:"cash_consume_fee"`
	TotalAmount      float64 `protobuf:"fixed64,11,opt,name=total_amount,json=totalAmount" json:"total_amount"`
	ConsumedAmount   float64 `protobuf:"fixed64,12,opt,name=consumed_amount,json=consumedAmount" json:"consumed_amount"`
	GrantUserCount   uint32  `protobuf:"varint,13,opt,name=grant_user_count,json=grantUserCount" json:"grant_user_count"`
	ConsumeUserCount uint32  `protobuf:"varint,14,opt,name=consume_user_count,json=consumeUserCount" json:"consume_user_count"`
	ExpiredAmount    float64 `protobuf:"fixed64,15,opt,name=expired_amount,json=expiredAmount" json:"expired_amount"`
}

func (m *GameVoucherSummary) Reset()                    { *m = GameVoucherSummary{} }
func (m *GameVoucherSummary) String() string            { return proto.CompactTextString(m) }
func (*GameVoucherSummary) ProtoMessage()               {}
func (*GameVoucherSummary) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{5} }

func (m *GameVoucherSummary) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *GameVoucherSummary) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GameVoucherSummary) GetLyCpType() string {
	if m != nil {
		return m.LyCpType
	}
	return ""
}

func (m *GameVoucherSummary) GetGrantCount() uint32 {
	if m != nil {
		return m.GrantCount
	}
	return 0
}

func (m *GameVoucherSummary) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

func (m *GameVoucherSummary) GetFullUseCount() uint32 {
	if m != nil {
		return m.FullUseCount
	}
	return 0
}

func (m *GameVoucherSummary) GetExpireCount() uint32 {
	if m != nil {
		return m.ExpireCount
	}
	return 0
}

func (m *GameVoucherSummary) GetFullExpireCount() uint32 {
	if m != nil {
		return m.FullExpireCount
	}
	return 0
}

func (m *GameVoucherSummary) GetTotalConsumeFee() float64 {
	if m != nil {
		return m.TotalConsumeFee
	}
	return 0
}

func (m *GameVoucherSummary) GetCashConsumeFee() float64 {
	if m != nil {
		return m.CashConsumeFee
	}
	return 0
}

func (m *GameVoucherSummary) GetTotalAmount() float64 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

func (m *GameVoucherSummary) GetConsumedAmount() float64 {
	if m != nil {
		return m.ConsumedAmount
	}
	return 0
}

func (m *GameVoucherSummary) GetGrantUserCount() uint32 {
	if m != nil {
		return m.GrantUserCount
	}
	return 0
}

func (m *GameVoucherSummary) GetConsumeUserCount() uint32 {
	if m != nil {
		return m.ConsumeUserCount
	}
	return 0
}

func (m *GameVoucherSummary) GetExpiredAmount() float64 {
	if m != nil {
		return m.ExpiredAmount
	}
	return 0
}

type GameVoucherSummaryList struct {
	GameVoucherSummaryList []*GameVoucherSummary `protobuf:"bytes,1,rep,name=game_voucher_summary_list,json=gameVoucherSummaryList" json:"game_voucher_summary_list,omitempty"`
}

func (m *GameVoucherSummaryList) Reset()         { *m = GameVoucherSummaryList{} }
func (m *GameVoucherSummaryList) String() string { return proto.CompactTextString(m) }
func (*GameVoucherSummaryList) ProtoMessage()    {}
func (*GameVoucherSummaryList) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{6}
}

func (m *GameVoucherSummaryList) GetGameVoucherSummaryList() []*GameVoucherSummary {
	if m != nil {
		return m.GameVoucherSummaryList
	}
	return nil
}

type VoucherListFilter struct {
	BizId        uint32 `protobuf:"varint,1,opt,name=biz_id,json=bizId" json:"biz_id"`
	LyGameId     uint32 `protobuf:"varint,2,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LyCpType     string `protobuf:"bytes,3,opt,name=ly_cp_type,json=lyCpType" json:"ly_cp_type"`
	Uid          uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
	GrantTimeMin uint32 `protobuf:"varint,5,opt,name=grant_time_min,json=grantTimeMin" json:"grant_time_min"`
	GrantTimeMax uint32 `protobuf:"varint,6,opt,name=grant_time_max,json=grantTimeMax" json:"grant_time_max"`
}

func (m *VoucherListFilter) Reset()                    { *m = VoucherListFilter{} }
func (m *VoucherListFilter) String() string            { return proto.CompactTextString(m) }
func (*VoucherListFilter) ProtoMessage()               {}
func (*VoucherListFilter) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{7} }

func (m *VoucherListFilter) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *VoucherListFilter) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *VoucherListFilter) GetLyCpType() string {
	if m != nil {
		return m.LyCpType
	}
	return ""
}

func (m *VoucherListFilter) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VoucherListFilter) GetGrantTimeMin() uint32 {
	if m != nil {
		return m.GrantTimeMin
	}
	return 0
}

func (m *VoucherListFilter) GetGrantTimeMax() uint32 {
	if m != nil {
		return m.GrantTimeMax
	}
	return 0
}

type QueryGameVoucherListReq struct {
	Filter     *VoucherListFilter `protobuf:"bytes,1,req,name=filter" json:"filter,omitempty"`
	StartIndex uint32             `protobuf:"varint,2,req,name=start_index,json=startIndex" json:"start_index"`
	Limit      uint32             `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *QueryGameVoucherListReq) Reset()         { *m = QueryGameVoucherListReq{} }
func (m *QueryGameVoucherListReq) String() string { return proto.CompactTextString(m) }
func (*QueryGameVoucherListReq) ProtoMessage()    {}
func (*QueryGameVoucherListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{8}
}

func (m *QueryGameVoucherListReq) GetFilter() *VoucherListFilter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *QueryGameVoucherListReq) GetStartIndex() uint32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *QueryGameVoucherListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GameVoucherList struct {
	VoucherList []*GameVoucher `protobuf:"bytes,1,rep,name=voucher_list,json=voucherList" json:"voucher_list,omitempty"`
}

func (m *GameVoucherList) Reset()                    { *m = GameVoucherList{} }
func (m *GameVoucherList) String() string            { return proto.CompactTextString(m) }
func (*GameVoucherList) ProtoMessage()               {}
func (*GameVoucherList) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{9} }

func (m *GameVoucherList) GetVoucherList() []*GameVoucher {
	if m != nil {
		return m.VoucherList
	}
	return nil
}

type VoucherRechargeInfo struct {
	Uid          uint32                        `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account      string                        `protobuf:"bytes,2,req,name=account" json:"account"`
	LyGameId     uint32                        `protobuf:"varint,3,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LyGameName   string                        `protobuf:"bytes,4,req,name=ly_game_name,json=lyGameName" json:"ly_game_name"`
	LyChannelId  uint32                        `protobuf:"varint,5,req,name=ly_channel_id,json=lyChannelId" json:"ly_channel_id"`
	LyCpId       string                        `protobuf:"bytes,6,req,name=ly_cp_id,json=lyCpId" json:"ly_cp_id"`
	RechargeInfo *VoucherRechargeInfo_Recharge `protobuf:"bytes,7,req,name=recharge_info,json=rechargeInfo" json:"recharge_info,omitempty"`
	VoucherInfo  *VoucherRechargeInfo_Voucher  `protobuf:"bytes,8,req,name=voucher_info,json=voucherInfo" json:"voucher_info,omitempty"`
}

func (m *VoucherRechargeInfo) Reset()                    { *m = VoucherRechargeInfo{} }
func (m *VoucherRechargeInfo) String() string            { return proto.CompactTextString(m) }
func (*VoucherRechargeInfo) ProtoMessage()               {}
func (*VoucherRechargeInfo) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{10} }

func (m *VoucherRechargeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VoucherRechargeInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *VoucherRechargeInfo) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *VoucherRechargeInfo) GetLyGameName() string {
	if m != nil {
		return m.LyGameName
	}
	return ""
}

func (m *VoucherRechargeInfo) GetLyChannelId() uint32 {
	if m != nil {
		return m.LyChannelId
	}
	return 0
}

func (m *VoucherRechargeInfo) GetLyCpId() string {
	if m != nil {
		return m.LyCpId
	}
	return ""
}

func (m *VoucherRechargeInfo) GetRechargeInfo() *VoucherRechargeInfo_Recharge {
	if m != nil {
		return m.RechargeInfo
	}
	return nil
}

func (m *VoucherRechargeInfo) GetVoucherInfo() *VoucherRechargeInfo_Voucher {
	if m != nil {
		return m.VoucherInfo
	}
	return nil
}

type VoucherRechargeInfo_Recharge struct {
	OrderId           string  `protobuf:"bytes,1,req,name=order_id,json=orderId" json:"order_id"`
	TotalFee          float64 `protobuf:"fixed64,2,req,name=total_fee,json=totalFee" json:"total_fee"`
	CashFee           float64 `protobuf:"fixed64,3,req,name=cash_fee,json=cashFee" json:"cash_fee"`
	RechargeTimestamp uint64  `protobuf:"varint,4,req,name=recharge_timestamp,json=rechargeTimestamp" json:"recharge_timestamp"`
}

func (m *VoucherRechargeInfo_Recharge) Reset()         { *m = VoucherRechargeInfo_Recharge{} }
func (m *VoucherRechargeInfo_Recharge) String() string { return proto.CompactTextString(m) }
func (*VoucherRechargeInfo_Recharge) ProtoMessage()    {}
func (*VoucherRechargeInfo_Recharge) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{10, 0}
}

func (m *VoucherRechargeInfo_Recharge) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *VoucherRechargeInfo_Recharge) GetTotalFee() float64 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *VoucherRechargeInfo_Recharge) GetCashFee() float64 {
	if m != nil {
		return m.CashFee
	}
	return 0
}

func (m *VoucherRechargeInfo_Recharge) GetRechargeTimestamp() uint64 {
	if m != nil {
		return m.RechargeTimestamp
	}
	return 0
}

type VoucherRechargeInfo_Voucher struct {
	Id              uint64  `protobuf:"varint,1,req,name=id" json:"id"`
	SourceVoucherId uint64  `protobuf:"varint,2,req,name=source_voucher_id,json=sourceVoucherId" json:"source_voucher_id"`
	ParentVoucherId uint64  `protobuf:"varint,3,req,name=parent_voucher_id,json=parentVoucherId" json:"parent_voucher_id"`
	Type            string  `protobuf:"bytes,4,req,name=type" json:"type"`
	Amount          float64 `protobuf:"fixed64,5,req,name=amount" json:"amount"`
	ActualAmount    float64 `protobuf:"fixed64,6,req,name=actual_amount,json=actualAmount" json:"actual_amount"`
	SpendingAmount  float64 `protobuf:"fixed64,7,req,name=spending_amount,json=spendingAmount" json:"spending_amount"`
	Status          string  `protobuf:"bytes,8,req,name=status" json:"status"`
}

func (m *VoucherRechargeInfo_Voucher) Reset()         { *m = VoucherRechargeInfo_Voucher{} }
func (m *VoucherRechargeInfo_Voucher) String() string { return proto.CompactTextString(m) }
func (*VoucherRechargeInfo_Voucher) ProtoMessage()    {}
func (*VoucherRechargeInfo_Voucher) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{10, 1}
}

func (m *VoucherRechargeInfo_Voucher) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *VoucherRechargeInfo_Voucher) GetSourceVoucherId() uint64 {
	if m != nil {
		return m.SourceVoucherId
	}
	return 0
}

func (m *VoucherRechargeInfo_Voucher) GetParentVoucherId() uint64 {
	if m != nil {
		return m.ParentVoucherId
	}
	return 0
}

func (m *VoucherRechargeInfo_Voucher) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *VoucherRechargeInfo_Voucher) GetAmount() float64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *VoucherRechargeInfo_Voucher) GetActualAmount() float64 {
	if m != nil {
		return m.ActualAmount
	}
	return 0
}

func (m *VoucherRechargeInfo_Voucher) GetSpendingAmount() float64 {
	if m != nil {
		return m.SpendingAmount
	}
	return 0
}

func (m *VoucherRechargeInfo_Voucher) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type GetGameVoucherReq struct {
	LyGameId  uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	VoucherId uint64 `protobuf:"varint,2,req,name=voucher_id,json=voucherId" json:"voucher_id"`
}

func (m *GetGameVoucherReq) Reset()                    { *m = GetGameVoucherReq{} }
func (m *GetGameVoucherReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameVoucherReq) ProtoMessage()               {}
func (*GetGameVoucherReq) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{11} }

func (m *GetGameVoucherReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GetGameVoucherReq) GetVoucherId() uint64 {
	if m != nil {
		return m.VoucherId
	}
	return 0
}

// 联运的代金券信息
type LYGameVoucher struct {
	Id              uint64  `protobuf:"varint,1,req,name=id" json:"id"`
	Type            string  `protobuf:"bytes,2,req,name=type" json:"type"`
	LyGameId        uint32  `protobuf:"varint,3,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	Uid             uint32  `protobuf:"varint,4,req,name=uid" json:"uid"`
	Amount          float64 `protobuf:"fixed64,5,req,name=amount" json:"amount"`
	ActualAmount    float64 `protobuf:"fixed64,6,req,name=actual_amount,json=actualAmount" json:"actual_amount"`
	SpendingAmount  float64 `protobuf:"fixed64,7,req,name=spending_amount,json=spendingAmount" json:"spending_amount"`
	OrderId         string  `protobuf:"bytes,8,req,name=order_id,json=orderId" json:"order_id"`
	Status          string  `protobuf:"bytes,9,req,name=status" json:"status"`
	ModifyTime      uint64  `protobuf:"varint,10,req,name=modify_time,json=modifyTime" json:"modify_time"`
	ActId           uint32  `protobuf:"varint,11,req,name=act_id,json=actId" json:"act_id"`
	ParentVoucherId uint32  `protobuf:"varint,12,req,name=parent_voucher_id,json=parentVoucherId" json:"parent_voucher_id"`
	SourceVoucherId uint32  `protobuf:"varint,13,req,name=source_voucher_id,json=sourceVoucherId" json:"source_voucher_id"`
}

func (m *LYGameVoucher) Reset()                    { *m = LYGameVoucher{} }
func (m *LYGameVoucher) String() string            { return proto.CompactTextString(m) }
func (*LYGameVoucher) ProtoMessage()               {}
func (*LYGameVoucher) Descriptor() ([]byte, []int) { return fileDescriptorGamevoucher, []int{12} }

func (m *LYGameVoucher) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LYGameVoucher) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *LYGameVoucher) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *LYGameVoucher) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LYGameVoucher) GetAmount() float64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *LYGameVoucher) GetActualAmount() float64 {
	if m != nil {
		return m.ActualAmount
	}
	return 0
}

func (m *LYGameVoucher) GetSpendingAmount() float64 {
	if m != nil {
		return m.SpendingAmount
	}
	return 0
}

func (m *LYGameVoucher) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *LYGameVoucher) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *LYGameVoucher) GetModifyTime() uint64 {
	if m != nil {
		return m.ModifyTime
	}
	return 0
}

func (m *LYGameVoucher) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *LYGameVoucher) GetParentVoucherId() uint32 {
	if m != nil {
		return m.ParentVoucherId
	}
	return 0
}

func (m *LYGameVoucher) GetSourceVoucherId() uint32 {
	if m != nil {
		return m.SourceVoucherId
	}
	return 0
}

type GetLYGameVoucherByOrderReq struct {
	LyOrderId string `protobuf:"bytes,1,req,name=ly_order_id,json=lyOrderId" json:"ly_order_id"`
}

func (m *GetLYGameVoucherByOrderReq) Reset()         { *m = GetLYGameVoucherByOrderReq{} }
func (m *GetLYGameVoucherByOrderReq) String() string { return proto.CompactTextString(m) }
func (*GetLYGameVoucherByOrderReq) ProtoMessage()    {}
func (*GetLYGameVoucherByOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{13}
}

func (m *GetLYGameVoucherByOrderReq) GetLyOrderId() string {
	if m != nil {
		return m.LyOrderId
	}
	return ""
}

type GetLYGameVoucherByOrderResp struct {
	Voucher *LYGameVoucher `protobuf:"bytes,1,opt,name=voucher" json:"voucher,omitempty"`
}

func (m *GetLYGameVoucherByOrderResp) Reset()         { *m = GetLYGameVoucherByOrderResp{} }
func (m *GetLYGameVoucherByOrderResp) String() string { return proto.CompactTextString(m) }
func (*GetLYGameVoucherByOrderResp) ProtoMessage()    {}
func (*GetLYGameVoucherByOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{14}
}

func (m *GetLYGameVoucherByOrderResp) GetVoucher() *LYGameVoucher {
	if m != nil {
		return m.Voucher
	}
	return nil
}

type GameVoucherRechargeSummary struct {
	BizId                 uint32  `protobuf:"varint,1,req,name=biz_id,json=bizId" json:"biz_id"`
	LyGameId              uint32  `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LyCpType              string  `protobuf:"bytes,3,req,name=ly_cp_type,json=lyCpType" json:"ly_cp_type"`
	RechargeOrderCount    uint32  `protobuf:"varint,4,req,name=recharge_order_count,json=rechargeOrderCount" json:"recharge_order_count"`
	RechargeUserCount     uint32  `protobuf:"varint,5,req,name=recharge_user_count,json=rechargeUserCount" json:"recharge_user_count"`
	RechargeTotalFee      float64 `protobuf:"fixed64,6,req,name=recharge_total_fee,json=rechargeTotalFee" json:"recharge_total_fee"`
	RechargeCashFee       float64 `protobuf:"fixed64,7,req,name=recharge_cash_fee,json=rechargeCashFee" json:"recharge_cash_fee"`
	VoucherSpendingAmount float64 `protobuf:"fixed64,8,req,name=voucher_spending_amount,json=voucherSpendingAmount" json:"voucher_spending_amount"`
}

func (m *GameVoucherRechargeSummary) Reset()         { *m = GameVoucherRechargeSummary{} }
func (m *GameVoucherRechargeSummary) String() string { return proto.CompactTextString(m) }
func (*GameVoucherRechargeSummary) ProtoMessage()    {}
func (*GameVoucherRechargeSummary) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{15}
}

func (m *GameVoucherRechargeSummary) GetBizId() uint32 {
	if m != nil {
		return m.BizId
	}
	return 0
}

func (m *GameVoucherRechargeSummary) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GameVoucherRechargeSummary) GetLyCpType() string {
	if m != nil {
		return m.LyCpType
	}
	return ""
}

func (m *GameVoucherRechargeSummary) GetRechargeOrderCount() uint32 {
	if m != nil {
		return m.RechargeOrderCount
	}
	return 0
}

func (m *GameVoucherRechargeSummary) GetRechargeUserCount() uint32 {
	if m != nil {
		return m.RechargeUserCount
	}
	return 0
}

func (m *GameVoucherRechargeSummary) GetRechargeTotalFee() float64 {
	if m != nil {
		return m.RechargeTotalFee
	}
	return 0
}

func (m *GameVoucherRechargeSummary) GetRechargeCashFee() float64 {
	if m != nil {
		return m.RechargeCashFee
	}
	return 0
}

func (m *GameVoucherRechargeSummary) GetVoucherSpendingAmount() float64 {
	if m != nil {
		return m.VoucherSpendingAmount
	}
	return 0
}

type GameVoucherRechargeSummaryList struct {
	SummaryList []*GameVoucherRechargeSummary `protobuf:"bytes,1,rep,name=summary_list,json=summaryList" json:"summary_list,omitempty"`
}

func (m *GameVoucherRechargeSummaryList) Reset()         { *m = GameVoucherRechargeSummaryList{} }
func (m *GameVoucherRechargeSummaryList) String() string { return proto.CompactTextString(m) }
func (*GameVoucherRechargeSummaryList) ProtoMessage()    {}
func (*GameVoucherRechargeSummaryList) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{16}
}

func (m *GameVoucherRechargeSummaryList) GetSummaryList() []*GameVoucherRechargeSummary {
	if m != nil {
		return m.SummaryList
	}
	return nil
}

type QueryGameVoucherRechargeSummaryReq struct {
	RechargeTimestampMin uint32 `protobuf:"varint,1,req,name=recharge_timestamp_min,json=rechargeTimestampMin" json:"recharge_timestamp_min"`
	RechargeTimestampMax uint32 `protobuf:"varint,2,req,name=recharge_timestamp_max,json=rechargeTimestampMax" json:"recharge_timestamp_max"`
}

func (m *QueryGameVoucherRechargeSummaryReq) Reset()         { *m = QueryGameVoucherRechargeSummaryReq{} }
func (m *QueryGameVoucherRechargeSummaryReq) String() string { return proto.CompactTextString(m) }
func (*QueryGameVoucherRechargeSummaryReq) ProtoMessage()    {}
func (*QueryGameVoucherRechargeSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamevoucher, []int{17}
}

func (m *QueryGameVoucherRechargeSummaryReq) GetRechargeTimestampMin() uint32 {
	if m != nil {
		return m.RechargeTimestampMin
	}
	return 0
}

func (m *QueryGameVoucherRechargeSummaryReq) GetRechargeTimestampMax() uint32 {
	if m != nil {
		return m.RechargeTimestampMax
	}
	return 0
}

func init() {
	proto.RegisterType((*GrantGameVoucherReq)(nil), "gamevoucher.GrantGameVoucherReq")
	proto.RegisterType((*GrantGameVoucherResp)(nil), "gamevoucher.GrantGameVoucherResp")
	proto.RegisterType((*GameVoucher)(nil), "gamevoucher.GameVoucher")
	proto.RegisterType((*VoucherSummaryFilter)(nil), "gamevoucher.VoucherSummaryFilter")
	proto.RegisterType((*QueryGameVoucherSummaryReq)(nil), "gamevoucher.QueryGameVoucherSummaryReq")
	proto.RegisterType((*GameVoucherSummary)(nil), "gamevoucher.GameVoucherSummary")
	proto.RegisterType((*GameVoucherSummaryList)(nil), "gamevoucher.GameVoucherSummaryList")
	proto.RegisterType((*VoucherListFilter)(nil), "gamevoucher.VoucherListFilter")
	proto.RegisterType((*QueryGameVoucherListReq)(nil), "gamevoucher.QueryGameVoucherListReq")
	proto.RegisterType((*GameVoucherList)(nil), "gamevoucher.GameVoucherList")
	proto.RegisterType((*VoucherRechargeInfo)(nil), "gamevoucher.VoucherRechargeInfo")
	proto.RegisterType((*VoucherRechargeInfo_Recharge)(nil), "gamevoucher.VoucherRechargeInfo.Recharge")
	proto.RegisterType((*VoucherRechargeInfo_Voucher)(nil), "gamevoucher.VoucherRechargeInfo.Voucher")
	proto.RegisterType((*GetGameVoucherReq)(nil), "gamevoucher.GetGameVoucherReq")
	proto.RegisterType((*LYGameVoucher)(nil), "gamevoucher.LYGameVoucher")
	proto.RegisterType((*GetLYGameVoucherByOrderReq)(nil), "gamevoucher.GetLYGameVoucherByOrderReq")
	proto.RegisterType((*GetLYGameVoucherByOrderResp)(nil), "gamevoucher.GetLYGameVoucherByOrderResp")
	proto.RegisterType((*GameVoucherRechargeSummary)(nil), "gamevoucher.GameVoucherRechargeSummary")
	proto.RegisterType((*GameVoucherRechargeSummaryList)(nil), "gamevoucher.GameVoucherRechargeSummaryList")
	proto.RegisterType((*QueryGameVoucherRechargeSummaryReq)(nil), "gamevoucher.QueryGameVoucherRechargeSummaryReq")
	proto.RegisterEnum("gamevoucher.VoucherBizId", VoucherBizId_name, VoucherBizId_value)
}
func (m *GrantGameVoucherReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrantGameVoucherReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.BizOrderId)))
	i += copy(dAtA[i:], m.BizOrderId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.EffectiveDate)))
	i += copy(dAtA[i:], m.EffectiveDate)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.ExpiryDate)))
	i += copy(dAtA[i:], m.ExpiryDate)
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.NumericAccount)))
	i += copy(dAtA[i:], m.NumericAccount)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpId)))
	i += copy(dAtA[i:], m.LyCpId)
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.TtChannelId)))
	i += copy(dAtA[i:], m.TtChannelId)
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyChannelId)))
	i += copy(dAtA[i:], m.LyChannelId)
	dAtA[i] = 0x9a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Remark)))
	i += copy(dAtA[i:], m.Remark)
	if m.BizData != nil {
		dAtA[i] = 0xa2
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.BizData)))
		i += copy(dAtA[i:], m.BizData)
	}
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LimitAmount))
	return i, nil
}

func (m *GrantGameVoucherResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrantGameVoucherResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GameVoucher) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameVoucher) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.VoucherId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.BizOrderId)))
	i += copy(dAtA[i:], m.BizOrderId)
	if m.BizData != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.BizData)))
		i += copy(dAtA[i:], m.BizData)
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.NumericAccount)))
	i += copy(dAtA[i:], m.NumericAccount)
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpType)))
	i += copy(dAtA[i:], m.LyCpType)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpId)))
	i += copy(dAtA[i:], m.LyCpId)
	dAtA[i] = 0x52
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.TtChannelId)))
	i += copy(dAtA[i:], m.TtChannelId)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x60
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantTime))
	dAtA[i] = 0x69
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.TotalAmount))))
	dAtA[i] = 0x71
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.ConsumedAmount))))
	dAtA[i] = 0x79
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.TotalConsumeFee))))
	dAtA[i] = 0x81
	i++
	dAtA[i] = 0x1
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.CashConsumeFee))))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LastConsumeTime))
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LastConsumeOrderId)))
	i += copy(dAtA[i:], m.LastConsumeOrderId)
	dAtA[i] = 0x9a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.EffectiveDate)))
	i += copy(dAtA[i:], m.EffectiveDate)
	dAtA[i] = 0xa2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.ExpiryDate)))
	i += copy(dAtA[i:], m.ExpiryDate)
	dAtA[i] = 0xaa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Remark)))
	i += copy(dAtA[i:], m.Remark)
	return i, nil
}

func (m *VoucherSummaryFilter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoucherSummaryFilter) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.BizId))
	if len(m.LyGameId) > 0 {
		for _, num := range m.LyGameId {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGamevoucher(dAtA, i, uint64(num))
		}
	}
	if len(m.LyCpType) > 0 {
		for _, s := range m.LyCpType {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantTimeMin))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantTimeMax))
	return i, nil
}

func (m *QueryGameVoucherSummaryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryGameVoucherSummaryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Filter == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(m.Filter.Size()))
		n1, err := m.Filter.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GameVoucherSummary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameVoucherSummary) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpType)))
	i += copy(dAtA[i:], m.LyCpType)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.UseCount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.FullUseCount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.ExpireCount))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.FullExpireCount))
	dAtA[i] = 0x49
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.TotalConsumeFee))))
	dAtA[i] = 0x51
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.CashConsumeFee))))
	dAtA[i] = 0x59
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.TotalAmount))))
	dAtA[i] = 0x61
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.ConsumedAmount))))
	dAtA[i] = 0x68
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantUserCount))
	dAtA[i] = 0x70
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.ConsumeUserCount))
	dAtA[i] = 0x79
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.ExpiredAmount))))
	return i, nil
}

func (m *GameVoucherSummaryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameVoucherSummaryList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameVoucherSummaryList) > 0 {
		for _, msg := range m.GameVoucherSummaryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamevoucher(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *VoucherListFilter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoucherListFilter) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpType)))
	i += copy(dAtA[i:], m.LyCpType)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantTimeMin))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.GrantTimeMax))
	return i, nil
}

func (m *QueryGameVoucherListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryGameVoucherListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Filter == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("filter")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(m.Filter.Size()))
		n2, err := m.Filter.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.StartIndex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GameVoucherList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameVoucherList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.VoucherList) > 0 {
		for _, msg := range m.VoucherList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamevoucher(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *VoucherRechargeInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoucherRechargeInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyGameName)))
	i += copy(dAtA[i:], m.LyGameName)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyChannelId))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpId)))
	i += copy(dAtA[i:], m.LyCpId)
	if m.RechargeInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("recharge_info")
	} else {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(m.RechargeInfo.Size()))
		n3, err := m.RechargeInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.VoucherInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("voucher_info")
	} else {
		dAtA[i] = 0x42
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(m.VoucherInfo.Size()))
		n4, err := m.VoucherInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *VoucherRechargeInfo_Recharge) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoucherRechargeInfo_Recharge) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x11
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.TotalFee))))
	dAtA[i] = 0x19
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.CashFee))))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.RechargeTimestamp))
	return i, nil
}

func (m *VoucherRechargeInfo_Voucher) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoucherRechargeInfo_Voucher) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.SourceVoucherId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.ParentVoucherId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Type)))
	i += copy(dAtA[i:], m.Type)
	dAtA[i] = 0x29
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.Amount))))
	dAtA[i] = 0x31
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.ActualAmount))))
	dAtA[i] = 0x39
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.SpendingAmount))))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Status)))
	i += copy(dAtA[i:], m.Status)
	return i, nil
}

func (m *GetGameVoucherReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameVoucherReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.VoucherId))
	return i, nil
}

func (m *LYGameVoucher) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LYGameVoucher) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Type)))
	i += copy(dAtA[i:], m.Type)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x29
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.Amount))))
	dAtA[i] = 0x31
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.ActualAmount))))
	dAtA[i] = 0x39
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.SpendingAmount))))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.Status)))
	i += copy(dAtA[i:], m.Status)
	dAtA[i] = 0x50
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.ModifyTime))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x60
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.ParentVoucherId))
	dAtA[i] = 0x68
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.SourceVoucherId))
	return i, nil
}

func (m *GetLYGameVoucherByOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLYGameVoucherByOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyOrderId)))
	i += copy(dAtA[i:], m.LyOrderId)
	return i, nil
}

func (m *GetLYGameVoucherByOrderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLYGameVoucherByOrderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Voucher != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamevoucher(dAtA, i, uint64(m.Voucher.Size()))
		n5, err := m.Voucher.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *GameVoucherRechargeSummary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameVoucherRechargeSummary) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.BizId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(len(m.LyCpType)))
	i += copy(dAtA[i:], m.LyCpType)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.RechargeOrderCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.RechargeUserCount))
	dAtA[i] = 0x31
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.RechargeTotalFee))))
	dAtA[i] = 0x39
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.RechargeCashFee))))
	dAtA[i] = 0x41
	i++
	i = encodeFixed64Gamevoucher(dAtA, i, uint64(math3.Float64bits(float64(m.VoucherSpendingAmount))))
	return i, nil
}

func (m *GameVoucherRechargeSummaryList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameVoucherRechargeSummaryList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SummaryList) > 0 {
		for _, msg := range m.SummaryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamevoucher(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *QueryGameVoucherRechargeSummaryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryGameVoucherRechargeSummaryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.RechargeTimestampMin))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamevoucher(dAtA, i, uint64(m.RechargeTimestampMax))
	return i, nil
}

func encodeFixed64Gamevoucher(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Gamevoucher(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGamevoucher(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GrantGameVoucherReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.BizId))
	l = len(m.BizOrderId)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.Uid))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	n += 1 + sovGamevoucher(uint64(m.Amount))
	l = len(m.EffectiveDate)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.ExpiryDate)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.NumericAccount)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.LyCpId)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.DeviceId)
	n += 2 + l + sovGamevoucher(uint64(l))
	l = len(m.TtChannelId)
	n += 2 + l + sovGamevoucher(uint64(l))
	l = len(m.LyChannelId)
	n += 2 + l + sovGamevoucher(uint64(l))
	l = len(m.Remark)
	n += 2 + l + sovGamevoucher(uint64(l))
	if m.BizData != nil {
		l = len(m.BizData)
		n += 2 + l + sovGamevoucher(uint64(l))
	}
	n += 2 + sovGamevoucher(uint64(m.LimitAmount))
	return n
}

func (m *GrantGameVoucherResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GameVoucher) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.VoucherId))
	n += 1 + sovGamevoucher(uint64(m.BizId))
	l = len(m.BizOrderId)
	n += 1 + l + sovGamevoucher(uint64(l))
	if m.BizData != nil {
		l = len(m.BizData)
		n += 1 + l + sovGamevoucher(uint64(l))
	}
	n += 1 + sovGamevoucher(uint64(m.Uid))
	l = len(m.NumericAccount)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	l = len(m.LyCpType)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.LyCpId)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.TtChannelId)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.DeviceId)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.GrantTime))
	n += 9
	n += 9
	n += 9
	n += 10
	n += 2 + sovGamevoucher(uint64(m.LastConsumeTime))
	l = len(m.LastConsumeOrderId)
	n += 2 + l + sovGamevoucher(uint64(l))
	l = len(m.EffectiveDate)
	n += 2 + l + sovGamevoucher(uint64(l))
	l = len(m.ExpiryDate)
	n += 2 + l + sovGamevoucher(uint64(l))
	l = len(m.Remark)
	n += 2 + l + sovGamevoucher(uint64(l))
	return n
}

func (m *VoucherSummaryFilter) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.BizId))
	if len(m.LyGameId) > 0 {
		for _, e := range m.LyGameId {
			n += 1 + sovGamevoucher(uint64(e))
		}
	}
	if len(m.LyCpType) > 0 {
		for _, s := range m.LyCpType {
			l = len(s)
			n += 1 + l + sovGamevoucher(uint64(l))
		}
	}
	n += 1 + sovGamevoucher(uint64(m.GrantTimeMin))
	n += 1 + sovGamevoucher(uint64(m.GrantTimeMax))
	return n
}

func (m *QueryGameVoucherSummaryReq) Size() (n int) {
	var l int
	_ = l
	if m.Filter != nil {
		l = m.Filter.Size()
		n += 1 + l + sovGamevoucher(uint64(l))
	}
	return n
}

func (m *GameVoucherSummary) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.BizId))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	l = len(m.LyCpType)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.GrantCount))
	n += 1 + sovGamevoucher(uint64(m.UseCount))
	n += 1 + sovGamevoucher(uint64(m.FullUseCount))
	n += 1 + sovGamevoucher(uint64(m.ExpireCount))
	n += 1 + sovGamevoucher(uint64(m.FullExpireCount))
	n += 9
	n += 9
	n += 9
	n += 9
	n += 1 + sovGamevoucher(uint64(m.GrantUserCount))
	n += 1 + sovGamevoucher(uint64(m.ConsumeUserCount))
	n += 9
	return n
}

func (m *GameVoucherSummaryList) Size() (n int) {
	var l int
	_ = l
	if len(m.GameVoucherSummaryList) > 0 {
		for _, e := range m.GameVoucherSummaryList {
			l = e.Size()
			n += 1 + l + sovGamevoucher(uint64(l))
		}
	}
	return n
}

func (m *VoucherListFilter) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.BizId))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	l = len(m.LyCpType)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.Uid))
	n += 1 + sovGamevoucher(uint64(m.GrantTimeMin))
	n += 1 + sovGamevoucher(uint64(m.GrantTimeMax))
	return n
}

func (m *QueryGameVoucherListReq) Size() (n int) {
	var l int
	_ = l
	if m.Filter != nil {
		l = m.Filter.Size()
		n += 1 + l + sovGamevoucher(uint64(l))
	}
	n += 1 + sovGamevoucher(uint64(m.StartIndex))
	n += 1 + sovGamevoucher(uint64(m.Limit))
	return n
}

func (m *GameVoucherList) Size() (n int) {
	var l int
	_ = l
	if len(m.VoucherList) > 0 {
		for _, e := range m.VoucherList {
			l = e.Size()
			n += 1 + l + sovGamevoucher(uint64(l))
		}
	}
	return n
}

func (m *VoucherRechargeInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	l = len(m.LyGameName)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.LyChannelId))
	l = len(m.LyCpId)
	n += 1 + l + sovGamevoucher(uint64(l))
	if m.RechargeInfo != nil {
		l = m.RechargeInfo.Size()
		n += 1 + l + sovGamevoucher(uint64(l))
	}
	if m.VoucherInfo != nil {
		l = m.VoucherInfo.Size()
		n += 1 + l + sovGamevoucher(uint64(l))
	}
	return n
}

func (m *VoucherRechargeInfo_Recharge) Size() (n int) {
	var l int
	_ = l
	l = len(m.OrderId)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 9
	n += 9
	n += 1 + sovGamevoucher(uint64(m.RechargeTimestamp))
	return n
}

func (m *VoucherRechargeInfo_Voucher) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.Id))
	n += 1 + sovGamevoucher(uint64(m.SourceVoucherId))
	n += 1 + sovGamevoucher(uint64(m.ParentVoucherId))
	l = len(m.Type)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 9
	n += 9
	n += 9
	l = len(m.Status)
	n += 1 + l + sovGamevoucher(uint64(l))
	return n
}

func (m *GetGameVoucherReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	n += 1 + sovGamevoucher(uint64(m.VoucherId))
	return n
}

func (m *LYGameVoucher) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.Id))
	l = len(m.Type)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	n += 1 + sovGamevoucher(uint64(m.Uid))
	n += 9
	n += 9
	n += 9
	l = len(m.OrderId)
	n += 1 + l + sovGamevoucher(uint64(l))
	l = len(m.Status)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.ModifyTime))
	n += 1 + sovGamevoucher(uint64(m.ActId))
	n += 1 + sovGamevoucher(uint64(m.ParentVoucherId))
	n += 1 + sovGamevoucher(uint64(m.SourceVoucherId))
	return n
}

func (m *GetLYGameVoucherByOrderReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.LyOrderId)
	n += 1 + l + sovGamevoucher(uint64(l))
	return n
}

func (m *GetLYGameVoucherByOrderResp) Size() (n int) {
	var l int
	_ = l
	if m.Voucher != nil {
		l = m.Voucher.Size()
		n += 1 + l + sovGamevoucher(uint64(l))
	}
	return n
}

func (m *GameVoucherRechargeSummary) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.BizId))
	n += 1 + sovGamevoucher(uint64(m.LyGameId))
	l = len(m.LyCpType)
	n += 1 + l + sovGamevoucher(uint64(l))
	n += 1 + sovGamevoucher(uint64(m.RechargeOrderCount))
	n += 1 + sovGamevoucher(uint64(m.RechargeUserCount))
	n += 9
	n += 9
	n += 9
	return n
}

func (m *GameVoucherRechargeSummaryList) Size() (n int) {
	var l int
	_ = l
	if len(m.SummaryList) > 0 {
		for _, e := range m.SummaryList {
			l = e.Size()
			n += 1 + l + sovGamevoucher(uint64(l))
		}
	}
	return n
}

func (m *QueryGameVoucherRechargeSummaryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamevoucher(uint64(m.RechargeTimestampMin))
	n += 1 + sovGamevoucher(uint64(m.RechargeTimestampMax))
	return n
}

func sovGamevoucher(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGamevoucher(x uint64) (n int) {
	return sovGamevoucher(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GrantGameVoucherReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GrantGameVoucherReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GrantGameVoucherReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizOrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizOrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectiveDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EffectiveDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpiryDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExpiryDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumericAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NumericAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TtChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TtChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Remark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizData = append(m.BizData[:0], dAtA[iNdEx:postIndex]...)
			if m.BizData == nil {
				m.BizData = []byte{}
			}
			iNdEx = postIndex
		case 21:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitAmount", wireType)
			}
			m.LimitAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_order_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("effective_date")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expiry_date")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("numeric_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrantGameVoucherResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GrantGameVoucherResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GrantGameVoucherResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameVoucher) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameVoucher: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameVoucher: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherId", wireType)
			}
			m.VoucherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoucherId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizOrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizOrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BizData = append(m.BizData[:0], dAtA[iNdEx:postIndex]...)
			if m.BizData == nil {
				m.BizData = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumericAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NumericAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TtChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TtChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantTime", wireType)
			}
			m.GrantTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 13:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.TotalAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000800)
		case 14:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumedAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.ConsumedAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00001000)
		case 15:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalConsumeFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.TotalConsumeFee = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00002000)
		case 16:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CashConsumeFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.CashConsumeFee = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00004000)
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastConsumeTime", wireType)
			}
			m.LastConsumeTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastConsumeTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00008000)
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastConsumeOrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LastConsumeOrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00010000)
		case 19:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectiveDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EffectiveDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00020000)
		case 20:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpiryDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExpiryDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00040000)
		case 21:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Remark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00080000)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_order_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("numeric_account")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_cp_type")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_cp_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tt_channel_id")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("grant_time")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_amount")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("consumed_amount")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_consume_fee")
	}
	if hasFields[0]&uint64(0x00004000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cash_consume_fee")
	}
	if hasFields[0]&uint64(0x00008000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_consume_time")
	}
	if hasFields[0]&uint64(0x00010000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_consume_order_id")
	}
	if hasFields[0]&uint64(0x00020000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("effective_date")
	}
	if hasFields[0]&uint64(0x00040000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expiry_date")
	}
	if hasFields[0]&uint64(0x00080000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remark")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoucherSummaryFilter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoucherSummaryFilter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoucherSummaryFilter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamevoucher
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LyGameId = append(m.LyGameId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamevoucher
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamevoucher
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamevoucher
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LyGameId = append(m.LyGameId, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpType = append(m.LyCpType, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantTimeMin", wireType)
			}
			m.GrantTimeMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantTimeMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantTimeMax", wireType)
			}
			m.GrantTimeMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantTimeMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryGameVoucherSummaryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueryGameVoucherSummaryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueryGameVoucherSummaryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Filter", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Filter == nil {
				m.Filter = &VoucherSummaryFilter{}
			}
			if err := m.Filter.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("filter")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameVoucherSummary) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameVoucherSummary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameVoucherSummary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantCount", wireType)
			}
			m.GrantCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseCount", wireType)
			}
			m.UseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FullUseCount", wireType)
			}
			m.FullUseCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FullUseCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireCount", wireType)
			}
			m.ExpireCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FullExpireCount", wireType)
			}
			m.FullExpireCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FullExpireCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalConsumeFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.TotalConsumeFee = float64(math4.Float64frombits(v))
		case 10:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CashConsumeFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.CashConsumeFee = float64(math4.Float64frombits(v))
		case 11:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.TotalAmount = float64(math4.Float64frombits(v))
		case 12:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumedAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.ConsumedAmount = float64(math4.Float64frombits(v))
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantUserCount", wireType)
			}
			m.GrantUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeUserCount", wireType)
			}
			m.ConsumeUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpiredAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.ExpiredAmount = float64(math4.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_cp_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameVoucherSummaryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameVoucherSummaryList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameVoucherSummaryList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameVoucherSummaryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameVoucherSummaryList = append(m.GameVoucherSummaryList, &GameVoucherSummary{})
			if err := m.GameVoucherSummaryList[len(m.GameVoucherSummaryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoucherListFilter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoucherListFilter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoucherListFilter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantTimeMin", wireType)
			}
			m.GrantTimeMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantTimeMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrantTimeMax", wireType)
			}
			m.GrantTimeMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrantTimeMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryGameVoucherListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueryGameVoucherListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueryGameVoucherListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Filter", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Filter == nil {
				m.Filter = &VoucherListFilter{}
			}
			if err := m.Filter.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIndex", wireType)
			}
			m.StartIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("filter")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameVoucherList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameVoucherList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameVoucherList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherList = append(m.VoucherList, &GameVoucher{})
			if err := m.VoucherList[len(m.VoucherList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoucherRechargeInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoucherRechargeInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoucherRechargeInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyGameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyChannelId", wireType)
			}
			m.LyChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RechargeInfo == nil {
				m.RechargeInfo = &VoucherRechargeInfo_Recharge{}
			}
			if err := m.RechargeInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VoucherInfo == nil {
				m.VoucherInfo = &VoucherRechargeInfo_Voucher{}
			}
			if err := m.VoucherInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_channel_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_cp_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_info")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoucherRechargeInfo_Recharge) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Recharge: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Recharge: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.TotalFee = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CashFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.CashFee = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeTimestamp", wireType)
			}
			m.RechargeTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeTimestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_fee")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cash_fee")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoucherRechargeInfo_Voucher) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Voucher: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Voucher: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceVoucherId", wireType)
			}
			m.SourceVoucherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceVoucherId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentVoucherId", wireType)
			}
			m.ParentVoucherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentVoucherId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Amount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActualAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.ActualAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpendingAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.SpendingAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_voucher_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("parent_voucher_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("actual_amount")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("spending_amount")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameVoucherReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameVoucherReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameVoucherReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherId", wireType)
			}
			m.VoucherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoucherId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LYGameVoucher) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LYGameVoucher: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LYGameVoucher: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Amount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActualAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.ActualAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpendingAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.SpendingAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ModifyTime", wireType)
			}
			m.ModifyTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ModifyTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentVoucherId", wireType)
			}
			m.ParentVoucherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentVoucherId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceVoucherId", wireType)
			}
			m.SourceVoucherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceVoucherId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("actual_amount")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("spending_amount")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("modify_time")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_id")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("parent_voucher_id")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_voucher_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLYGameVoucherByOrderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLYGameVoucherByOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLYGameVoucherByOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyOrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyOrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLYGameVoucherByOrderResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLYGameVoucherByOrderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLYGameVoucherByOrderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Voucher", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Voucher == nil {
				m.Voucher = &LYGameVoucher{}
			}
			if err := m.Voucher.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameVoucherRechargeSummary) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameVoucherRechargeSummary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameVoucherRechargeSummary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BizId", wireType)
			}
			m.BizId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BizId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyCpType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LyCpType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeOrderCount", wireType)
			}
			m.RechargeOrderCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeOrderCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeUserCount", wireType)
			}
			m.RechargeUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeTotalFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.RechargeTotalFee = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeCashFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.RechargeCashFee = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherSpendingAmount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.VoucherSpendingAmount = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("biz_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_cp_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_order_count")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_user_count")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_total_fee")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_cash_fee")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_spending_amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameVoucherRechargeSummaryList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameVoucherRechargeSummaryList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameVoucherRechargeSummaryList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SummaryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamevoucher
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SummaryList = append(m.SummaryList, &GameVoucherRechargeSummary{})
			if err := m.SummaryList[len(m.SummaryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryGameVoucherRechargeSummaryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QueryGameVoucherRechargeSummaryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QueryGameVoucherRechargeSummaryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeTimestampMin", wireType)
			}
			m.RechargeTimestampMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeTimestampMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeTimestampMax", wireType)
			}
			m.RechargeTimestampMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeTimestampMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamevoucher(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamevoucher
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_timestamp_min")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_timestamp_max")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGamevoucher(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGamevoucher
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGamevoucher
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGamevoucher
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGamevoucher
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGamevoucher(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGamevoucher = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGamevoucher   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/gamelotto/voucher/gamevoucher.proto", fileDescriptorGamevoucher)
}

var fileDescriptorGamevoucher = []byte{
	// 2155 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x4f, 0x6f, 0x1b, 0xc7,
	0x15, 0xcf, 0x72, 0x25, 0x8a, 0x7c, 0x24, 0x25, 0x6a, 0x24, 0xcb, 0x2c, 0xad, 0xca, 0xab, 0xb5,
	0x63, 0x33, 0x4e, 0x29, 0x19, 0x6a, 0x90, 0xa2, 0x2c, 0x41, 0x20, 0x92, 0x13, 0x41, 0x89, 0xeb,
	0xa6, 0xb2, 0x93, 0xa2, 0x45, 0x00, 0x62, 0xb5, 0x3b, 0xa4, 0x17, 0xde, 0x7f, 0xd9, 0x9d, 0x15,
	0x44, 0xf5, 0x52, 0xa0, 0x97, 0xa2, 0x97, 0x16, 0x6d, 0x73, 0xeb, 0x51, 0x40, 0x3f, 0x40, 0x81,
	0x1e, 0x7b, 0x6a, 0xd1, 0x1c, 0x73, 0x2e, 0xd0, 0x22, 0x70, 0x2f, 0xfa, 0x0a, 0xbd, 0x15, 0x33,
	0xb3, 0xb3, 0x9c, 0x5d, 0x2e, 0x2d, 0xe5, 0x90, 0xe6, 0x26, 0xbd, 0x3f, 0xc3, 0x37, 0xef, 0xfd,
	0xde, 0xef, 0xbd, 0x59, 0xf8, 0x4e, 0x84, 0xc3, 0x53, 0xdb, 0xc4, 0xd1, 0xee, 0xd8, 0x70, 0xb1,
	0xe3, 0x13, 0xe2, 0xef, 0x9e, 0xfa, 0xb1, 0xf9, 0x1c, 0x87, 0x4c, 0x92, 0xfc, 0xbd, 0x13, 0x84,
	0x3e, 0xf1, 0x51, 0x4d, 0x12, 0xb5, 0xef, 0x9a, 0xbe, 0xeb, 0xfa, 0xde, 0x2e, 0x71, 0x4e, 0x03,
	0xdb, 0x7c, 0xe1, 0xe0, 0xdd, 0xe8, 0xc5, 0x49, 0x6c, 0x3b, 0xc4, 0xf6, 0xc8, 0x24, 0xc0, 0xdc,
	0x45, 0xff, 0xcb, 0x02, 0xac, 0x1d, 0x86, 0x86, 0x47, 0x0e, 0x0d, 0x17, 0x7f, 0xcc, 0x5d, 0x8f,
	0xf1, 0xa7, 0xe8, 0x16, 0x94, 0x4f, 0xec, 0xf3, 0xa1, 0x6d, 0xb5, 0x14, 0xad, 0xd4, 0x69, 0xec,
	0x2f, 0x7c, 0xfe, 0xef, 0xdb, 0xaf, 0x1d, 0x2f, 0x9e, 0xd8, 0xe7, 0x47, 0x16, 0xba, 0x07, 0x75,
	0xaa, 0xf4, 0x43, 0x0b, 0x87, 0xd4, 0xa4, 0xa4, 0x95, 0x3a, 0xd5, 0xc4, 0x04, 0x4e, 0xec, 0xf3,
	0x1f, 0x51, 0xc5, 0x91, 0x85, 0x36, 0x40, 0x8d, 0x6d, 0xab, 0xa5, 0x4a, 0x27, 0x50, 0x01, 0xd2,
	0x01, 0x9c, 0xc9, 0x90, 0x06, 0x4b, 0xbd, 0x17, 0x24, 0x75, 0xc5, 0x99, 0xd0, 0x40, 0x8e, 0x2c,
	0xb4, 0x09, 0x65, 0xc3, 0xf5, 0x63, 0x8f, 0xb4, 0x16, 0x25, 0x7d, 0x22, 0x43, 0x6f, 0xc2, 0x32,
	0x1e, 0x8d, 0xb0, 0x49, 0xec, 0x53, 0x3c, 0xb4, 0x0c, 0x82, 0x5b, 0x65, 0x29, 0x86, 0x46, 0xaa,
	0x7b, 0x64, 0x10, 0x8c, 0x5e, 0x87, 0x1a, 0x3e, 0x0b, 0xec, 0x70, 0xc2, 0x2d, 0x97, 0xe4, 0x68,
	0xb9, 0x82, 0x99, 0x75, 0x61, 0xc5, 0x8b, 0x5d, 0x1c, 0xda, 0xe6, 0xd0, 0x30, 0x4d, 0xf6, 0xd3,
	0x15, 0xc9, 0x74, 0x39, 0x51, 0xbe, 0xc3, 0x75, 0x68, 0x0b, 0x2a, 0xce, 0x64, 0x68, 0x06, 0xf4,
	0x0a, 0x55, 0x4d, 0x49, 0xed, 0xca, 0xce, 0xe4, 0x20, 0x38, 0xb2, 0xd0, 0x36, 0x54, 0x2d, 0x4c,
	0x6b, 0x47, 0x0d, 0x9a, 0x92, 0x41, 0x85, 0x8b, 0x8f, 0x2c, 0xd4, 0x81, 0x06, 0x21, 0x43, 0xf3,
	0xb9, 0xe1, 0x79, 0xd8, 0xa1, 0x66, 0xab, 0x92, 0x59, 0x8d, 0x90, 0x03, 0xae, 0xe1, 0x96, 0xf4,
	0xc7, 0xa6, 0x96, 0x48, 0xb6, 0x74, 0x26, 0x53, 0xcb, 0x4d, 0x28, 0x87, 0xd8, 0x35, 0xc2, 0x17,
	0xad, 0x35, 0x39, 0x28, 0x2e, 0x43, 0xb7, 0xa1, 0x42, 0x2b, 0x67, 0x19, 0xc4, 0x68, 0xad, 0x6b,
	0x4a, 0xa7, 0x9e, 0xe8, 0x97, 0x4e, 0xec, 0xf3, 0x47, 0x06, 0x31, 0xd0, 0x7d, 0xa8, 0x3b, 0xb6,
	0x6b, 0x93, 0x61, 0x92, 0xfc, 0x1b, 0x9a, 0x92, 0x26, 0xbf, 0xc6, 0x34, 0xef, 0x30, 0x85, 0xbe,
	0x01, 0xeb, 0xb3, 0xb8, 0x89, 0x02, 0xfd, 0x8b, 0x32, 0xd4, 0x24, 0x19, 0xba, 0x03, 0x90, 0x20,
	0x52, 0x80, 0x69, 0x21, 0x39, 0xae, 0x9a, 0xc8, 0x8f, 0x2c, 0x09, 0x6d, 0xa5, 0xab, 0xd1, 0xa6,
	0xce, 0x41, 0x9b, 0x7c, 0xb7, 0x85, 0xa2, 0xbb, 0x25, 0x70, 0x5c, 0xcc, 0xc3, 0xb1, 0xa0, 0xf0,
	0xe5, 0x57, 0x14, 0x3e, 0x8b, 0xde, 0xa5, 0x42, 0xf4, 0x72, 0x1b, 0x33, 0x18, 0xd2, 0x56, 0xcb,
	0xc0, 0xa8, 0x42, 0xe1, 0xf1, 0x6c, 0x12, 0xe0, 0x1c, 0x80, 0x4a, 0x33, 0x00, 0x9a, 0x41, 0x07,
	0x48, 0x46, 0x19, 0x74, 0x64, 0xa0, 0x56, 0x93, 0x7f, 0x2c, 0x85, 0xda, 0x1d, 0x80, 0x31, 0x2d,
	0xd7, 0x90, 0xd8, 0x2e, 0x6e, 0xd5, 0xa5, 0xa0, 0xab, 0x4c, 0xfe, 0xcc, 0x76, 0x31, 0x2d, 0x3e,
	0xf1, 0x89, 0xe1, 0x88, 0xe2, 0x37, 0xb4, 0x52, 0x47, 0x49, 0x7f, 0x90, 0x6a, 0x78, 0xf1, 0x69,
	0xc6, 0x4c, 0xdf, 0x8b, 0x62, 0x17, 0x5b, 0xc2, 0x76, 0x59, 0xb2, 0x5d, 0x16, 0xca, 0xc4, 0xfc,
	0x21, 0xac, 0xf2, 0x73, 0x13, 0xf9, 0x70, 0x84, 0x71, 0x6b, 0x45, 0x72, 0x58, 0x61, 0xea, 0x03,
	0xae, 0x7d, 0x0f, 0x63, 0xb4, 0x03, 0x4d, 0xd3, 0x88, 0x9e, 0x67, 0x1c, 0x9a, 0x99, 0x5f, 0x30,
	0xa2, 0xe7, 0x92, 0xfd, 0x43, 0x58, 0x75, 0x8c, 0x88, 0xa4, 0xf6, 0xec, 0x96, 0xab, 0x12, 0xd8,
	0x56, 0xa8, 0x3a, 0x71, 0x60, 0x77, 0xfd, 0x1e, 0xdc, 0xc8, 0x78, 0xa4, 0xf0, 0x42, 0x52, 0xfe,
	0x90, 0xe4, 0x25, 0x60, 0x36, 0x4b, 0x3d, 0x6b, 0xd7, 0xa6, 0x9e, 0xf5, 0x39, 0xd4, 0x33, 0x6d,
	0xda, 0x1b, 0x32, 0x10, 0xb8, 0x4c, 0xff, 0x87, 0x02, 0xeb, 0x49, 0x3b, 0x3d, 0x8d, 0x5d, 0xd7,
	0x08, 0x27, 0xef, 0xd9, 0x0e, 0xc1, 0x61, 0x86, 0xa4, 0x95, 0x7c, 0xdb, 0x6c, 0x66, 0x60, 0x5a,
	0xd2, 0xd4, 0x4e, 0x23, 0x43, 0xaf, 0x32, 0x40, 0x55, 0x4d, 0xed, 0x54, 0x25, 0x68, 0x3e, 0x80,
	0xe5, 0x29, 0x5a, 0x86, 0xae, 0xed, 0xb1, 0x86, 0x12, 0x3f, 0x50, 0x4f, 0x11, 0xf3, 0x43, 0xdb,
	0xcb, 0xdb, 0x1a, 0x67, 0xad, 0xc5, 0x62, 0x5b, 0xe3, 0x4c, 0xff, 0x09, 0xb4, 0x7f, 0x1c, 0xe3,
	0x70, 0x22, 0x11, 0x44, 0x72, 0x23, 0x3a, 0x73, 0xbe, 0x0f, 0xe5, 0x11, 0xbb, 0x18, 0xa3, 0x89,
	0xda, 0xde, 0xf6, 0x8e, 0x3c, 0xe2, 0x8a, 0x32, 0x70, 0x9c, 0x38, 0xe8, 0x9f, 0x2d, 0x02, 0x9a,
	0x3d, 0xf4, 0xd5, 0x53, 0x4c, 0xcf, 0x25, 0xe8, 0xea, 0x3e, 0x56, 0x0b, 0xfb, 0xf8, 0x75, 0xa8,
	0xf1, 0x04, 0x70, 0xea, 0x90, 0x33, 0xc5, 0x7b, 0xee, 0x80, 0x35, 0xc1, 0x36, 0x54, 0xe3, 0x08,
	0x27, 0x46, 0x72, 0x8a, 0x2a, 0x71, 0x84, 0xb9, 0xc9, 0x03, 0x58, 0x1e, 0xc5, 0x8e, 0x33, 0x9c,
	0xda, 0x95, 0xe5, 0x54, 0x52, 0xdd, 0x47, 0xc2, 0xf6, 0x3e, 0xd4, 0x19, 0x80, 0x84, 0xe5, 0x92,
	0x4c, 0xd4, 0x5c, 0x73, 0x20, 0x9a, 0x8f, 0x1d, 0x9a, 0xb1, 0xae, 0x48, 0xd6, 0x2b, 0x54, 0xfd,
	0x6e, 0xd6, 0x63, 0xb6, 0x5d, 0xe9, 0x88, 0xfb, 0x4a, 0xed, 0x0a, 0x92, 0x43, 0xbe, 0x5d, 0xf3,
	0x44, 0x53, 0x93, 0x6c, 0xaf, 0x22, 0x9a, 0x7a, 0xe6, 0xdc, 0x2c, 0xd1, 0xec, 0x40, 0x93, 0x97,
	0x22, 0x8e, 0x70, 0x98, 0x5c, 0xb5, 0x21, 0x5d, 0x95, 0x23, 0xf5, 0xa3, 0x08, 0x87, 0xfc, 0xa6,
	0x7b, 0x80, 0x44, 0xc8, 0x92, 0xc7, 0xb2, 0xe4, 0xd1, 0x4c, 0xf4, 0x53, 0x1f, 0xda, 0xff, 0x2c,
	0x59, 0x69, 0x44, 0x2b, 0x52, 0x44, 0x8d, 0x44, 0x97, 0x4c, 0x49, 0x02, 0x1b, 0xb3, 0xb0, 0x7c,
	0x6c, 0x47, 0x04, 0xfd, 0x0c, 0xbe, 0xc5, 0xa0, 0x27, 0x86, 0x63, 0xc4, 0x75, 0x43, 0xc7, 0x8e,
	0x48, 0x4b, 0xd1, 0xd4, 0x4e, 0x6d, 0xef, 0x76, 0x06, 0xff, 0x05, 0x3d, 0xb3, 0x31, 0x2e, 0x3c,
	0x5b, 0xbf, 0x54, 0x60, 0x35, 0x11, 0xd3, 0xff, 0xaf, 0xc3, 0x16, 0xf9, 0x66, 0x50, 0xae, 0xd1,
	0x0c, 0x4a, 0x41, 0x33, 0x24, 0x33, 0x56, 0x6e, 0x02, 0x36, 0x63, 0x67, 0x19, 0x65, 0xf1, 0x2b,
	0x30, 0x4a, 0x79, 0x2e, 0xa3, 0xfc, 0x41, 0x81, 0x9b, 0x79, 0x4a, 0xa1, 0x77, 0xa6, 0x7c, 0xf2,
	0x76, 0x8e, 0x4f, 0xb6, 0x8a, 0xf8, 0x64, 0x9a, 0x20, 0x41, 0x26, 0xb4, 0xa1, 0x23, 0x62, 0x84,
	0x64, 0x68, 0x7b, 0x16, 0x3e, 0xcb, 0x30, 0x03, 0x30, 0xc5, 0x11, 0x95, 0xa3, 0x36, 0x2c, 0xb2,
	0x85, 0x28, 0xb3, 0xdf, 0x72, 0x91, 0xfe, 0x04, 0x56, 0x72, 0x01, 0xa1, 0x1f, 0x40, 0x5d, 0xd4,
	0x5a, 0xaa, 0x71, 0x6b, 0x5e, 0x8d, 0x8f, 0x6b, 0xa7, 0x53, 0x67, 0xfd, 0x5f, 0x65, 0x58, 0x4b,
	0xb7, 0x2c, 0xf3, 0xb9, 0x11, 0x8e, 0xf1, 0x91, 0x37, 0xf2, 0x45, 0xba, 0x95, 0xfc, 0x4a, 0xb3,
	0x05, 0x4b, 0x62, 0x95, 0x91, 0x97, 0x73, 0x21, 0xcc, 0x95, 0x5b, 0x2d, 0xe4, 0xbe, 0x7b, 0x50,
	0x17, 0x36, 0x9e, 0xe1, 0x62, 0xb6, 0xa7, 0xa7, 0xc3, 0x8b, 0x5b, 0x3d, 0x31, 0x5c, 0x3c, 0xbb,
	0x9b, 0xca, 0x0b, 0x56, 0x66, 0x37, 0x95, 0x37, 0x9e, 0x72, 0xc1, 0xc6, 0xf3, 0x04, 0x1a, 0x61,
	0x72, 0xbb, 0xa1, 0xed, 0x8d, 0x7c, 0xb6, 0x5c, 0xd5, 0xf6, 0xde, 0x28, 0xaa, 0x9b, 0x9c, 0x86,
	0x1d, 0xf1, 0xcf, 0x71, 0x3d, 0x94, 0xb3, 0xf3, 0xc1, 0x34, 0xe5, 0xec, 0xb8, 0x0a, 0x3b, 0xae,
	0x73, 0xe5, 0x71, 0xf9, 0x12, 0x50, 0x61, 0xfb, 0x42, 0x81, 0x8a, 0xb0, 0xa2, 0xbb, 0x66, 0xba,
	0x30, 0x28, 0x72, 0x82, 0xfd, 0x64, 0x4b, 0xd8, 0x86, 0x2a, 0x67, 0x38, 0x4a, 0x85, 0x25, 0x69,
	0x73, 0xa9, 0x30, 0x31, 0x25, 0xc1, 0xdb, 0x50, 0x61, 0xa4, 0x49, 0x2d, 0x54, 0xc9, 0x62, 0x89,
	0x4a, 0xa9, 0xc1, 0x77, 0x01, 0xa5, 0xe9, 0xa0, 0xad, 0x10, 0x11, 0xc3, 0x0d, 0x58, 0x19, 0xc4,
	0x56, 0xb3, 0x2a, 0xf4, 0xcf, 0x84, 0xba, 0xfd, 0xe7, 0x12, 0x2c, 0x89, 0xdd, 0x7b, 0x1d, 0x4a,
	0xb9, 0x9d, 0xbb, 0x64, 0x5b, 0x94, 0xde, 0x23, 0x3f, 0x0e, 0xcd, 0x29, 0xf7, 0x24, 0xe3, 0x2f,
	0xdd, 0x95, 0xb8, 0xfa, 0xe3, 0x74, 0x3d, 0x7f, 0x08, 0xab, 0x81, 0x11, 0x62, 0x8f, 0xc8, 0x1e,
	0xaa, 0xec, 0xc1, 0xd5, 0x53, 0x8f, 0x16, 0x2c, 0x30, 0x92, 0x90, 0x31, 0xc3, 0x24, 0xb9, 0x77,
	0x9d, 0x92, 0x7b, 0xd7, 0xbd, 0x01, 0x0d, 0xc3, 0x24, 0xf1, 0x74, 0x32, 0x94, 0x25, 0xa3, 0x3a,
	0x57, 0x4d, 0x47, 0x43, 0x14, 0x60, 0xcf, 0xb2, 0xbd, 0xb1, 0x30, 0x5e, 0x92, 0x37, 0x44, 0xa1,
	0x4c, 0xcc, 0x37, 0xa1, 0x1c, 0x11, 0x83, 0xc4, 0x51, 0x66, 0x1b, 0x4f, 0x64, 0xfa, 0x27, 0xb0,
	0x7a, 0x88, 0xf3, 0x6f, 0xe0, 0x6c, 0x93, 0x28, 0x85, 0x4d, 0x92, 0x7d, 0xde, 0x94, 0x0a, 0x9f,
	0x37, 0xfa, 0x3f, 0x55, 0x68, 0x3c, 0xfe, 0xa9, 0xfc, 0x2a, 0x2a, 0xae, 0x8c, 0xc8, 0x5a, 0x69,
	0x26, 0x6b, 0xd7, 0xe9, 0xd7, 0x94, 0x7a, 0x73, 0x5c, 0xf0, 0x4d, 0x65, 0x5c, 0xee, 0x91, 0x4a,
	0x51, 0x8f, 0x4c, 0x4b, 0x52, 0x9d, 0x2d, 0x09, 0x65, 0x61, 0xd7, 0xb7, 0xec, 0xd1, 0x84, 0x2f,
	0xf3, 0x20, 0xe5, 0x0a, 0xb8, 0x82, 0xed, 0xf1, 0xb7, 0xa0, 0x6c, 0x98, 0x44, 0x3c, 0x7c, 0x52,
	0x1a, 0x36, 0x4c, 0x32, 0x0f, 0xb8, 0xf2, 0xe3, 0x67, 0x06, 0xb8, 0x85, 0xcd, 0xd1, 0x90, 0x3d,
	0x72, 0xcd, 0xa1, 0xef, 0x43, 0xfb, 0x10, 0x93, 0x4c, 0x79, 0xf7, 0x27, 0xec, 0xb1, 0x40, 0x31,
	0x74, 0x17, 0x6a, 0xce, 0x64, 0x58, 0xc8, 0x15, 0x55, 0x67, 0x92, 0xbc, 0x29, 0xf4, 0xa7, 0x70,
	0x6b, 0xee, 0x19, 0x51, 0x80, 0xde, 0x82, 0xa5, 0x24, 0x1a, 0x36, 0xba, 0x6b, 0x7b, 0xed, 0x0c,
	0x85, 0x65, 0xfc, 0x8e, 0x85, 0xa9, 0xfe, 0x7b, 0x15, 0xda, 0x19, 0x44, 0x73, 0xaa, 0xf8, 0xbf,
	0xee, 0xc6, 0x6f, 0xc3, 0x7a, 0x4a, 0x61, 0x3c, 0x09, 0x62, 0x49, 0x9e, 0x9e, 0x98, 0x92, 0x1c,
	0xbb, 0x30, 0x5f, 0xb2, 0xde, 0x82, 0xb5, 0xd4, 0x4f, 0xda, 0xcc, 0xe4, 0xc9, 0x92, 0x72, 0x5f,
	0x66, 0x9d, 0x9b, 0x12, 0x66, 0xca, 0xbe, 0x32, 0xa0, 0x9b, 0x29, 0x61, 0x0a, 0x16, 0x7e, 0x08,
	0xe9, 0x41, 0xc3, 0x94, 0x8e, 0x65, 0x58, 0xaf, 0x08, 0xf5, 0x41, 0x42, 0xcb, 0x7d, 0xb8, 0x99,
	0x2e, 0x6d, 0xb9, 0x76, 0xa8, 0x48, 0x7e, 0x37, 0x12, 0xa3, 0xa7, 0x99, 0xae, 0xd0, 0x1d, 0xd8,
	0x9a, 0x5f, 0x14, 0xb6, 0x28, 0xbc, 0x0f, 0xf5, 0x82, 0x65, 0xf0, 0xfe, 0xdc, 0x45, 0x21, 0x7b,
	0xc4, 0x71, 0x2d, 0x92, 0x36, 0xc1, 0x3f, 0x2a, 0xa0, 0xe7, 0xd7, 0xa3, 0xbc, 0x03, 0xfe, 0x14,
	0xf5, 0x60, 0x63, 0x76, 0xd2, 0xb0, 0x2d, 0x4d, 0xc6, 0xc6, 0xfa, 0xcc, 0xb4, 0xa1, 0xdb, 0xda,
	0x1c, 0x5f, 0x23, 0xbb, 0x38, 0x15, 0xf8, 0x1a, 0x67, 0x0f, 0x8e, 0xa0, 0x2e, 0xe0, 0xce, 0x60,
	0xb7, 0x0c, 0xf0, 0xd8, 0x1f, 0xdb, 0xde, 0x63, 0x9f, 0x10, 0xbf, 0xa9, 0xa0, 0x55, 0x68, 0x88,
	0x68, 0xb9, 0xa8, 0x84, 0x36, 0x00, 0x1d, 0x86, 0x18, 0x7b, 0x38, 0x3c, 0xb4, 0x47, 0xe4, 0x43,
	0xc3, 0x7c, 0x61, 0x8c, 0x71, 0x53, 0xdd, 0xfb, 0x2f, 0x80, 0xfc, 0xf9, 0x13, 0xfd, 0x55, 0x81,
	0x66, 0xfe, 0x03, 0x15, 0xd2, 0xb2, 0x49, 0x9c, 0xfd, 0xee, 0xd9, 0xde, 0xbe, 0xc2, 0x22, 0x0a,
	0x74, 0xfc, 0x8b, 0x8b, 0x4b, 0x55, 0xf9, 0xf5, 0xc5, 0xa5, 0x5a, 0x3f, 0xe9, 0xf9, 0xbd, 0xb8,
	0x37, 0xee, 0x19, 0xbd, 0xa0, 0xf7, 0xbb, 0x8b, 0x4b, 0xf5, 0xfd, 0xee, 0x89, 0xd6, 0xe7, 0x5d,
	0x35, 0xd0, 0xba, 0xbe, 0xd6, 0x17, 0x8d, 0x3f, 0xd0, 0xba, 0xb1, 0xd6, 0x67, 0x48, 0x66, 0xff,
	0x8c, 0xb5, 0x7e, 0xd2, 0x5c, 0x03, 0xad, 0x6b, 0x68, 0x7d, 0x8e, 0x9f, 0x81, 0xd6, 0x0d, 0xb4,
	0x3e, 0xdb, 0x8f, 0x06, 0xe8, 0x14, 0x6e, 0x1c, 0xe3, 0xc0, 0x0f, 0x49, 0xae, 0x6c, 0xb9, 0x4b,
	0x14, 0xec, 0x2f, 0xed, 0xcd, 0x9d, 0xf4, 0xa3, 0xef, 0xce, 0xd3, 0x0f, 0xf6, 0xf9, 0x47, 0xdf,
	0x77, 0xdd, 0x80, 0x4c, 0x86, 0x1f, 0xee, 0xeb, 0xdf, 0xa6, 0xf1, 0x97, 0x68, 0xfc, 0xa5, 0x98,
	0x45, 0x5d, 0x97, 0x63, 0x43, 0x7f, 0x53, 0x60, 0xe3, 0x91, 0x1d, 0x62, 0x93, 0x7c, 0x8d, 0xe9,
	0x53, 0xbf, 0xf6, 0xf4, 0xfd, 0x1c, 0x96, 0xb3, 0x13, 0x1d, 0x65, 0xd7, 0xff, 0x99, 0x71, 0xdf,
	0x9e, 0xbb, 0x8a, 0xeb, 0xbb, 0x34, 0xe4, 0x05, 0x1a, 0xf2, 0xc2, 0xb8, 0xe7, 0xb3, 0x50, 0x37,
	0xb3, 0x31, 0xf8, 0x5a, 0x7f, 0x3a, 0x2f, 0x06, 0xe8, 0x37, 0x0a, 0xdc, 0x9c, 0x43, 0xe8, 0xe8,
	0x7e, 0x3e, 0x8c, 0x39, 0xa3, 0xa3, 0xdd, 0xb9, 0x9e, 0x61, 0x14, 0xe8, 0xdb, 0x34, 0xbe, 0x45,
	0x56, 0x51, 0x1e, 0x5d, 0x93, 0x06, 0x24, 0xcd, 0x9d, 0x01, 0xfa, 0x7b, 0xc1, 0x3b, 0x49, 0x4c,
	0x82, 0x6c, 0x44, 0xf3, 0x3f, 0xd0, 0xb4, 0xef, 0x5c, 0xf1, 0x20, 0x65, 0x94, 0xf3, 0x09, 0x0d,
	0xc6, 0xa2, 0xc1, 0xc0, 0x79, 0x6f, 0xdc, 0x0b, 0x7a, 0x27, 0x3d, 0xcc, 0x82, 0x3a, 0xe8, 0x9e,
	0x4b, 0xd5, 0x9d, 0xa6, 0x8f, 0xb1, 0x5c, 0x5a, 0x3b, 0x3a, 0x4c, 0x84, 0x84, 0xa2, 0x01, 0x8f,
	0x6d, 0x6f, 0xa0, 0x75, 0xb1, 0xd6, 0xc7, 0x9e, 0x35, 0x40, 0x5f, 0x2a, 0xb0, 0x5e, 0xf4, 0xde,
	0x43, 0x77, 0x5f, 0x79, 0x89, 0xe4, 0x49, 0xd8, 0xde, 0x9c, 0x77, 0x03, 0x16, 0xfa, 0x2f, 0x15,
	0x1a, 0x3b, 0xa6, 0xb1, 0x37, 0x79, 0xec, 0x31, 0x8b, 0x3e, 0xea, 0x39, 0xec, 0x06, 0xd6, 0x9c,
	0x1b, 0x64, 0x82, 0x4f, 0xb0, 0xca, 0xa4, 0x27, 0x5a, 0x9f, 0xbf, 0x64, 0xa5, 0x6b, 0x70, 0x01,
	0xbd, 0x8c, 0xd6, 0x8d, 0xb4, 0x3e, 0x7b, 0x50, 0x0e, 0xb4, 0xae, 0xa3, 0xf5, 0xd9, 0xd3, 0x71,
	0x80, 0xfe, 0xa4, 0xc0, 0xed, 0x2b, 0x38, 0x1b, 0xed, 0xbe, 0xf2, 0xb6, 0xb3, 0x0c, 0xdf, 0x7e,
	0xf3, 0x9a, 0xe3, 0x83, 0xe5, 0xe1, 0x1e, 0x4d, 0xc3, 0x88, 0xe1, 0x5d, 0x14, 0x6f, 0xad, 0xa0,
	0x18, 0xed, 0xf2, 0xaf, 0x2e, 0x2e, 0xd5, 0xcf, 0xce, 0xf6, 0x9b, 0x9f, 0xbf, 0xdc, 0x52, 0xbe,
	0x78, 0xb9, 0xa5, 0x7c, 0xf9, 0x72, 0x4b, 0xf9, 0xed, 0x7f, 0xb6, 0x5e, 0xfb, 0x5f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x53, 0xe3, 0x59, 0xdc, 0xb8, 0x1a, 0x00, 0x00,
}
