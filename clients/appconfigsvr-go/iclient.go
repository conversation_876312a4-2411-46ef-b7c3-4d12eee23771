// Code generated by quicksilver-cli. DO NOT EDIT.
package appconfigsvr_go

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/appconfigsvr-go"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	BatchGetAppAuditSw(ctx context.Context, in *pb.BatchGetAppAuditSwReq) (*pb.BatchGetAppAuditSwResp,protocol.ServerError)
	GetAccountIsolationSwConfig(ctx context.Context, in *pb.GetAccountIsolationSwConfigReq) (*pb.GetAccountIsolationSwConfigResp,protocol.ServerError)
	GetAppAuditSw(ctx context.Context, in *pb.GetAppAuditSwReq) (*pb.GetAppAuditSwResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
