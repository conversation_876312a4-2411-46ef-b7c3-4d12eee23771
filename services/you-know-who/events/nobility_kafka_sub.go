package events

//const (
//	topicNobility = "nobility_event"
//)
//
//type KafkaNobilitySubscriber struct {
//	KafkaSub *event.KafkaSub
//	handler  EventHandler
//}
//
//type EventHandler interface {
//	// ProcessEventUpgrade(ctx context.Context, uid uint32) (error, bool)
//	// ProcessEventDegrade(ctx context.Context, uid uint32) (error, bool)
//	ProcessEventChannel(ctx context.Context, uid, channelId uint32) (error, bool)
//}
//
//func NewNobilityKafkaSubscriber(clientId, groupId string, topics, brokers []string, handler EventHandler) (*KafkaNobilitySubscriber, error) {
//	kfkConfig := sarama.NewConfig()
//	kfkConfig.ClientID = clientId
//	kfkConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
//	kfkConfig.Consumer.Return.Errors = true
//
//	kafkaSub, err := event.NewKafkaSub(topicNobility, brokers, groupId, topics, kfkConfig)
//	if err != nil {
//		log.Errorf("Failed to create kafka-subscriber %+v", err)
//		return nil, err
//	}
//
//	sub := &KafkaNobilitySubscriber{
//		KafkaSub: kafkaSub,
//		handler:  handler,
//	}
//	sub.KafkaSub.SetMessageProcessor(sub.handlerEvent)
//	return sub, nil
//}
//
//func (s *KafkaNobilitySubscriber) Close() {
//	s.KafkaSub.Stop()
//}
//
//func (s *KafkaNobilitySubscriber) handlerEvent(msg *sarama.ConsumerMessage) (error, bool) {
//	switch msg.Topic {
//	case topicNobility:
//		return s.handlerNobilityEvent(msg)
//	}
//	return nil, false
//}
//
//func (s *KafkaNobilitySubscriber) handlerNobilityEvent(msg *sarama.ConsumerMessage) (error, bool) {
//	// 设置超时时间
//	ctx, cancelFuc := context.WithTimeout(context.Background(), 10*time.Second)
//	defer cancelFuc()
//
//	// 判断是否由新服务执行该任务
//	if conf.GetSvrConf().StartSettlementSvr {
//		log.DebugWithCtx(ctx, "Start handlerNobilityEvent with new settlement svr")
//		return nil, false
//	}
//
//	nobilityEv := kafkanobility.NobilityInfoChange{}
//	err := proto.Unmarshal(msg.Value, &nobilityEv)
//	if err != nil {
//		log.ErrorWithCtx(ctx, "Failed to proto.Unmarshal %+v", err)
//		return err, false
//	}
//	log.InfoWithCtx(ctx, "handlerEvent topic(%s) partition(%d) offset(%d),event:%s", msg.Topic, msg.Partition, msg.Offset, nobilityEv.String())
//
//	if nobilityEv.Uid == 0 {
//		log.ErrorWithCtx(ctx, "handlerNobilityEvent continue msg, uid is 0! ")
//		return nil, false
//	}
//
//	// 判断是否升降级请求，如果不是升降级请求直接跳过
//	if nobilityEv.Level == nobilityEv.OldLevel {
//		return nil, true
//	}
//
//	// 如果是升级消息，并且级别大于6级
//	level, _ := conf.GetMinNbLevel()
//	if nobilityEv.OldLevel < nobilityEv.Level && int(nobilityEv.Level) >= level {
//		return s.handler.ProcessEventUpgrade(ctx, nobilityEv.Uid)
//	}
//	// 如果是降级消息，并且降级等级小于6级，原本等级大于6级
//	//if nobilityEv.OldLevel > nobilityEv.Level && int(nobilityEv.Level) < level && int(nobilityEv.OldLevel) >= level {
//	//	return s.handler.ProcessEventDegrade(ctx, nobilityEv.Uid)
//	//}
//
//	return nil, false
//}
