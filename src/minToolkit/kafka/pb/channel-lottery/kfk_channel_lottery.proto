syntax = "proto3";

package kfk_channel_lottery;

// 事件类型
enum LotteryEventType {
  Unknown = 0;
  BeginLottery = 1;   // 开始抽奖
  BreakLottery = 2;   // 中断抽奖
}

message GiftInfo {
  uint32 gift_id = 1;
  string gift_name = 2;
  string gift_img = 3;
  uint32 gift_price = 4;
  uint32 gift_price_type = 5;
}

// 转转中奖事件
message LotteryEvent {
  uint32 event_type = 1;        // see LotteryEventType
  uint32 lottery_id = 2;        // 抽奖场次id
  uint32 sponsor_uid = 3;       // 发起人uid
  uint32 channel_id = 4;
  uint32 lottery_award_cnt = 5; // 奖品数量
  GiftInfo gift_info = 6;       // 当event_type为BreakLottery时，gift_info为空
  int64 begin_time = 7;
  int64 end_time = 8;
  uint32 gift_send_type = 9;    // see channel-lottery.proto LotteryGiftSendType 礼物送出方式 ， 0为旧版送出方式
}