package mocks

import (
	"github.com/golang/mock/gomock"
	mockbackpack "golang.52tt.com/clients/mocks/backpack"
	mockbackpackSender "golang.52tt.com/clients/mocks/backpack-sender"
	mockimapi "golang.52tt.com/clients/mocks/im-api"
	mockPush "golang.52tt.com/clients/mocks/push-notification/v2"
	mockuserprofile "golang.52tt.com/clients/mocks/user-profile-api"
	mockPresent "golang.52tt.com/clients/mocks/userpresent"
	"golang.52tt.com/services/tt-rev/glory-magic/internal/rpc"
)

var (
	MockBc     *MockIBusinessConfManager
	MockCache  *MockICache
	MockStore  *MockIStore
	MockRpcCli *rpc.Client

	MockPushCli           *mockPush.MockIClient
	MockUserProfileCli    *mockuserprofile.MockIClient
	MockImApiClient       *mockimapi.MockIClient
	MockPresentCli        *mockPresent.MockIClient
	MockBackpackCli       *mockbackpack.MockIClient
	MockBackpackSenderCli *mockbackpackSender.MockIClient
)

func TestPretreatment(ctrl *gomock.Controller) {
	MockBc = NewMockIBusinessConfManager(ctrl)
	MockStore = NewMockIStore(ctrl)
	MockCache = NewMockICache(ctrl)

	MockBackpackCli = mockbackpack.NewMockIClient(ctrl)
	MockBackpackSenderCli = mockbackpackSender.NewMockIClient(ctrl)
	MockPresentCli = mockPresent.NewMockIClient(ctrl)
	MockUserProfileCli = mockuserprofile.NewMockIClient(ctrl)

	MockRpcCli = &rpc.Client{
		PushCli:           MockPushCli,
		ImApiClient:       MockImApiClient,
		UserProfileCli:    MockUserProfileCli,
		BackpackCli:       MockBackpackCli,
		BackpackSenderCli: MockBackpackSenderCli,
		PresentCli:        MockPresentCli,
	}
}
