// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/interact-proxy-logic/interact-proxy-logic.proto

package interact_proxy_logic // import "golang.52tt.com/protocol/services/logicsvr-go/interact-proxy-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import interact_proxy_logic "golang.52tt.com/protocol/app/interact-proxy-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// InteractProxyLogicClient is the client API for InteractProxyLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type InteractProxyLogicClient interface {
	ReportCommonInvitationResult(ctx context.Context, in *interact_proxy_logic.ReportCommonInvitationResultReq, opts ...grpc.CallOption) (*interact_proxy_logic.ReportCommonInvitationResultResp, error)
}

type interactProxyLogicClient struct {
	cc *grpc.ClientConn
}

func NewInteractProxyLogicClient(cc *grpc.ClientConn) InteractProxyLogicClient {
	return &interactProxyLogicClient{cc}
}

func (c *interactProxyLogicClient) ReportCommonInvitationResult(ctx context.Context, in *interact_proxy_logic.ReportCommonInvitationResultReq, opts ...grpc.CallOption) (*interact_proxy_logic.ReportCommonInvitationResultResp, error) {
	out := new(interact_proxy_logic.ReportCommonInvitationResultResp)
	err := c.cc.Invoke(ctx, "/logic.InteractProxyLogic/ReportCommonInvitationResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InteractProxyLogicServer is the server API for InteractProxyLogic service.
type InteractProxyLogicServer interface {
	ReportCommonInvitationResult(context.Context, *interact_proxy_logic.ReportCommonInvitationResultReq) (*interact_proxy_logic.ReportCommonInvitationResultResp, error)
}

func RegisterInteractProxyLogicServer(s *grpc.Server, srv InteractProxyLogicServer) {
	s.RegisterService(&_InteractProxyLogic_serviceDesc, srv)
}

func _InteractProxyLogic_ReportCommonInvitationResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(interact_proxy_logic.ReportCommonInvitationResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InteractProxyLogicServer).ReportCommonInvitationResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.InteractProxyLogic/ReportCommonInvitationResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InteractProxyLogicServer).ReportCommonInvitationResult(ctx, req.(*interact_proxy_logic.ReportCommonInvitationResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _InteractProxyLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.InteractProxyLogic",
	HandlerType: (*InteractProxyLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReportCommonInvitationResult",
			Handler:    _InteractProxyLogic_ReportCommonInvitationResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/interact-proxy-logic/interact-proxy-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/interact-proxy-logic/interact-proxy-logic.proto", fileDescriptor_interact_proxy_logic_b4b4c3c90ad6d9b1)
}

var fileDescriptor_interact_proxy_logic_b4b4c3c90ad6d9b1 = []byte{
	// 238 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xb2, 0xce, 0xc9, 0x4f, 0xcf,
	0x4c, 0x2e, 0x2e, 0x2b, 0xd2, 0x4d, 0xcf, 0xd7, 0xcf, 0xcc, 0x2b, 0x49, 0x2d, 0x4a, 0x4c, 0x2e,
	0xd1, 0x2d, 0x28, 0xca, 0xaf, 0xa8, 0xd4, 0x05, 0x4b, 0x61, 0x15, 0xd4, 0x2b, 0x28, 0xca, 0x2f,
	0xc9, 0x17, 0x62, 0x05, 0x73, 0xa4, 0x34, 0x91, 0xcd, 0x48, 0x4f, 0x2c, 0x49, 0x2d, 0x4f, 0xac,
	0xd4, 0xcf, 0x2f, 0x28, 0xc9, 0xcc, 0xcf, 0x2b, 0x86, 0xd1, 0x10, 0x1d, 0x52, 0xf2, 0x89, 0x05,
	0x05, 0x58, 0x4d, 0x8c, 0x87, 0x28, 0x30, 0xba, 0xc4, 0xc8, 0x25, 0xe4, 0x09, 0x95, 0x0f, 0x00,
	0x49, 0xfb, 0x80, 0x64, 0x85, 0xe6, 0x31, 0x72, 0xc9, 0x04, 0xa5, 0x16, 0xe4, 0x17, 0x95, 0x38,
	0xe7, 0xe7, 0xe6, 0xe6, 0xe7, 0x79, 0xe6, 0x95, 0x65, 0x96, 0x24, 0x82, 0x4c, 0x0e, 0x4a, 0x2d,
	0x2e, 0xcd, 0x29, 0x11, 0xb2, 0xd0, 0x4b, 0x4f, 0xd4, 0x83, 0x19, 0x1c, 0x0f, 0x36, 0x38, 0x1e,
	0xe2, 0x54, 0x7c, 0xda, 0x82, 0x52, 0x0b, 0xa5, 0x2c, 0xc9, 0xd4, 0x59, 0x5c, 0xa0, 0xc4, 0xf9,
	0xe9, 0xfc, 0x4e, 0x3d, 0x16, 0x8e, 0x4b, 0x33, 0x99, 0xa4, 0xa4, 0x7f, 0x9d, 0xdf, 0xa9, 0x27,
	0xc6, 0x25, 0x82, 0xcd, 0x6f, 0x4e, 0x2e, 0x51, 0x4e, 0xe9, 0xf9, 0x39, 0x89, 0x79, 0xe9, 0x7a,
	0xa6, 0x46, 0x25, 0x25, 0x7a, 0xc9, 0xf9, 0xb9, 0xfa, 0x60, 0xdf, 0x26, 0xe7, 0xe7, 0xe8, 0x17,
	0xa7, 0x16, 0x95, 0x65, 0x26, 0xa7, 0x16, 0xeb, 0x13, 0x8a, 0x88, 0x24, 0x36, 0xb0, 0x1e, 0x63,
	0x40, 0x00, 0x00, 0x00, 0xff, 0xff, 0x10, 0x72, 0xd7, 0x07, 0xb3, 0x01, 0x00, 0x00,
}
