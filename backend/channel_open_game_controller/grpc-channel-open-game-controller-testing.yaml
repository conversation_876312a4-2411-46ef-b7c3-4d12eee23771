apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-channel-open-game-controller-logic
spec:
  gateways:
  - istio-ingress/apiv2-gateway
  - istio-ingress/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic/
    rewrite:
      uri: /logic.ChannelOpenGameControllerLogic/
    delegate:
       name: channel-open-game-controller-logic-delegator-80
       namespace: quicksilver


