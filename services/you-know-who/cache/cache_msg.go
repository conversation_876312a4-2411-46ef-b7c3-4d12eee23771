package cache

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	pb "golang.52tt.com/protocol/services/youknowwho"
	"strconv"
)


func weightKey(uid,channelId uint32) string {
	return fmt.Sprintf("ykw_show_up_msg_weight_%v_%v", uid, channelId)
}
func countKey(uid, channelId uint32) string {
	return fmt.Sprintf("ykw_show_up_msg_count_%v_%v", uid, channelId)
}
func contentKey(uid, channelId uint32) string {
	return fmt.Sprintf("ykw_show_up_msg_content_%v_%v", uid, channelId)
}

func (c *YouKnowWhoCache) SendShowUpMsg(ctx context.Context, msg *pb.ShowUpMsg, uid, channelId uint32) error {
	bin, err := proto.Marshal(msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendShowUpMsg Marshal msg +%v err %+v", msg, err)
		return err
	}
	pipe := c.redisClient.Pipeline()
	field := fmt.Sprintf("%v", msg.GetSendUid())
	pipe.ZAdd(weightKey(uid, channelId), redis.Z{
		Score:  float64(msg.Weight),
		Member: field,
	})
	pipe.HIncrBy(countKey(uid, channelId), field, 1)
	pipe.HSet(contentKey(uid, channelId), field, bin)
	pipe.Exec()

	return err
}

func (c *YouKnowWhoCache) GetShowUpMsgList(ctx context.Context, uid, channelId uint32) ([]*pb.ShowUpMsg, uint32, error) {

	msgList := make([]*pb.ShowUpMsg, 0, 100)
	uidList, err := c.redisClient.ZRevRangeWithScores(weightKey(uid, channelId), 0, 99).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GetShowUpMsgList ZRangeWithScores err:%v", err)
		return msgList, 0, err
	}

	files := make([]string, 0,100)
	for _, m := range uidList {
		if nil == m.Member || redis.Nil == m.Member {
			continue
		}
		files = append(files, m.Member.(string))
	}

	if len(files) == 0 {
		return msgList, 0, nil
	}

	senderCnt := c.redisClient.HLen(contentKey(uid, channelId)).Val()

	contentList, err := c.redisClient.HMGet(contentKey(uid, channelId), files...).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GetShowUpMsgList HMGet err:%v", err)
		return msgList, 0, err
	}

	countList, err := c.redisClient.HMGet(countKey(uid, channelId), files...).Result()
	if nil != err {
		log.ErrorWithCtx(ctx, "GetShowUpMsgList HMGet err:%v", err)
		return msgList, 0, err
	}

	if len(countList ) != len(contentList) {
		log.ErrorWithCtx(ctx, "GetShowUpMsgList len(countList ) != len(contentList) uid %v channelId %v", uid, channelId)
		return msgList, 0, nil
	}

	countNumList := make([]uint32, 0 , 100)
	for _, item := range countList {
		var count int64 = 1
		if nil != item && redis.Nil != item {
			count, _ = strconv.ParseInt( item.(string), 10, 32)
		}
		countNumList = append(countNumList, uint32(count))
	}

	for idx, content := range contentList {
		if nil == content || redis.Nil == content {
			continue
		}
		str := content.(string)
		msg := &pb.ShowUpMsg{}
		err := proto.Unmarshal([]byte(str), msg)
		if nil != err {
			log.ErrorWithCtx(ctx, "GetShowUpMsgList Unmarshal err:%v", err)
			continue
		}
		msg.Count = countNumList[idx] //用于发消息次数
		msgList = append(msgList, msg)
	}

	return msgList, uint32(senderCnt), nil
}

func (c *YouKnowWhoCache) ClearShowUpMsgList(ctx context.Context, uid, channelId uint32) error {
	err := c.redisClient.Del(weightKey(uid, channelId), contentKey(uid, channelId), countKey(uid, channelId)).Err()
	if nil != err {
		log.ErrorWithCtx(ctx, "ClearShowUpMsgList Del  uid %v channelId %v err:%v", uid, channelId, err)
	}
	return err
}
