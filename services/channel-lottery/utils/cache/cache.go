package cache

import (
	"errors"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
)

var (
	ErrNotFound = errors.New("not found")
	ErrItemBusy = errors.New("item busy")
)

type AutoLock struct {
	cache   *Cache
	key     string
	locked  bool
	begin   time.Time
	timeout time.Duration
	t       time.Time
}

func NewAutoLock(cache *Cache) *AutoLock {
	return &AutoLock{
		cache: cache,
	}
}

func (s *AutoLock) _lock(key string, timeout time.Duration) bool {
	s.t = time.Now()
	s.timeout = timeout
	s.key = key

	ok, err := s.cache.Lock(s.key, strconv.FormatInt(s.t.Unix(), 10), timeout)
	if err != nil {
		log.Errorf("Lock %s fail: %v", key, err)
		return false
	}
	return ok
}

func (s *AutoLock) Lock(key string, timeout time.Duration) bool {
	s.begin = time.Now()
	s.locked = s._lock(key, timeout)
	// log.Infof("[%s]Lock takes %v get %v", s.id, time.Since(s.begin), s.locked)
	return s.locked
}

func (s *AutoLock) LockWithRetry(key string, timeout time.Duration, try int, retryInterval time.Duration) bool {
	s.begin = time.Now()
	for i := 0; i < try; i++ {
		s.locked = s._lock(key, timeout)
		if !s.locked {
			time.Sleep(retryInterval + time.Millisecond*time.Duration(i))
		} else {
			// log.Infof("[%s]Lock takes %v get %v", s.id, time.Since(s.begin), s.locked)
			return true
		}
	}

	// log.Infof("[%s]Lock takes %v get %v", s.id, time.Since(s.begin), s.locked)
	return false
}

func (s *AutoLock) Unlock() {
	if s.locked {
		if time.Since(s.t)+50*time.Millisecond < s.timeout {
			s.cache.Unlock(s.key)
		}
	}
}

type Cache struct {
	Cmder redis.Cmdable
}

func NewCache(cfg *config.RedisConfig) (*Cache, error) {
	return &Cache{
		Cmder: redis.NewClient(&redis.Options{
			Network:            cfg.Protocol,
			Addr:               cfg.Addr(),
			PoolSize:           cfg.PoolSize,
			IdleCheckFrequency: cfg.IdleCheckFrequency(),
			DB:                 cfg.DB,
		}),
	}, nil
}

func (s *Cache) GetRedisCli() *redis.Client {
	return s.Cmder.(*redis.Client)
}

func (s *Cache) Lock(key, id string, duration time.Duration) (bool, error) {
	return s.Cmder.SetNX(key, id, duration).Result()
}

func (s *Cache) LockTry(key, id string, duration time.Duration, retry int) bool {
	for i := 0; i < retry; i++ {
		if ok, _ := s.Lock(key, id, duration); !ok {
			time.Sleep(time.Millisecond * time.Duration(i+1))
		} else {
			return true
		}
	}
	return false
}

func (s *Cache) Lockf(key, id string, duration time.Duration, do func() error) (bool, error) {
	ok, err := s.Cmder.SetNX(key, id, duration).Result()
	if err != nil || !ok {
		return ok, err
	}

	err = do()

	_ = s.Cmder.Del(key)
	return true, err
}

// LockfEx duration must bigger than 2*time.second
func (s *Cache) LockfEx(key, id string, duration time.Duration, do func() error) (bool, error) {
	if duration < 2*time.Second {
		return false, errors.New("invalid param")
	}

	ok, err := s.Cmder.SetNX(key, id, duration).Result()
	if err != nil || !ok {
		return ok, err
	}

	sw := sync.WaitGroup{}
	sw.Add(1)
	stopCh := make(chan bool)
	go func() {
		dur := duration - time.Second

		for {
			select {
			case <-stopCh:
				sw.Done()
				return
			case <-time.After(dur):
				_, e := s.Cmder.Expire(key, duration).Result()
				if e != nil {
					if e == redis.Nil {
						return
					} else {
						dur = time.Millisecond * 200
					}
				} else {
					dur = duration - time.Second
				}
			}
		}
	}()

	err = do()

	close(stopCh)
	sw.Wait()

	_ = s.Cmder.Del(key)
	return true, err
}

func (s *Cache) Locking(key string) (ok bool, id string, err error) {
	return ResultWithExist(s.Cmder.Get(key).Result())
}

func (s *Cache) Unlock(key string) {
	_, err := s.Cmder.Del(key).Result()
	if err != nil {
		log.Debugf("Unlock err:%+v, key:%s", err.Error(), key)
	}
}

func ResultWithExist(value string, err error) (bool, string, error) {
	if nil == err {
		return true, value, nil
	} else {
		if err == redis.Nil {
			return false, value, nil
		}
		return false, value, err
	}
}

func ResultOK(ok string, err error) (bool, error) {
	if ok == "OK" {
		return true, err
	}
	return false, err
}

func ResultCount(ok int64, err error) (bool, error) {
	if ok > 0 {
		return true, err
	}
	return false, err
}

func DayOff(at time.Time, off int) string {
	return at.AddDate(0, 0, off).Format("20060102")
}
