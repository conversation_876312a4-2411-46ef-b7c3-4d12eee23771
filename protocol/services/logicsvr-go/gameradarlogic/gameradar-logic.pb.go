// Code generated by protoc-gen-go. DO NOT EDIT.
// source: gameradar-logic.proto

package gameradarlogic // import "golang.52tt.com/protocol/services/logicsvr-go/gameradarlogic"

import (
	fmt "fmt"

	proto "github.com/golang/protobuf/proto"

	math "math"

	gameradarlogic "golang.52tt.com/protocol/app/gameradarlogic"

	_ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

	context "golang.org/x/net/context"

	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GameradarLogicClient is the client API for GameradarLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GameradarLogicClient interface {
	InvitePlay(ctx context.Context, in *gameradarlogic.InvitePlayReq, opts ...grpc.CallOption) (*gameradarlogic.InvitePlayRsp, error)
	InviteSuc(ctx context.Context, in *gameradarlogic.InviteSucReq, opts ...grpc.CallOption) (*gameradarlogic.InviteSucRsp, error)
	RadarDisplay(ctx context.Context, in *gameradarlogic.RadarDisplayReq, opts ...grpc.CallOption) (*gameradarlogic.RadarDisplayRsp, error)
	GetPlaymates(ctx context.Context, in *gameradarlogic.GetPlaymatesReq, opts ...grpc.CallOption) (*gameradarlogic.GetPlaymatesResp, error)
	GetPlaymateTabList(ctx context.Context, in *gameradarlogic.GetPlaymateTabListReq, opts ...grpc.CallOption) (*gameradarlogic.GetPlaymateTabListResp, error)
	ListPlaymateTabBlocks(ctx context.Context, in *gameradarlogic.ListPlaymateTabBlocksReq, opts ...grpc.CallOption) (*gameradarlogic.ListPlaymateTabBlocksResp, error)
	GetPlaymateNum(ctx context.Context, in *gameradarlogic.GetPlaymateNumReq, opts ...grpc.CallOption) (*gameradarlogic.GetPlaymateNumResp, error)
	// -------获取用户雷达状态-------
	GetUserRadarStatus(ctx context.Context, in *gameradarlogic.GetUserRadarStatusReq, opts ...grpc.CallOption) (*gameradarlogic.GetUserRadarStatusResp, error)
	// -------获取雷达配置-------
	GetRadarConfig(ctx context.Context, in *gameradarlogic.GetRadarConfigReq, opts ...grpc.CallOption) (*gameradarlogic.GetRadarConfigResp, error)
	// -------开启雷达-------
	StartRadar(ctx context.Context, in *gameradarlogic.StartRadarReq, opts ...grpc.CallOption) (*gameradarlogic.StartRadarResp, error)
	// -------关闭雷达-------
	StopRadar(ctx context.Context, in *gameradarlogic.StopRadarReq, opts ...grpc.CallOption) (*gameradarlogic.StopRadarResp, error)
	// -------拿推荐数-------
	GetRecommendNum(ctx context.Context, in *gameradarlogic.GetRecommendNumReq, opts ...grpc.CallOption) (*gameradarlogic.GetRecommendNumResp, error)
}

type gameradarLogicClient struct {
	cc *grpc.ClientConn
}

func NewGameradarLogicClient(cc *grpc.ClientConn) GameradarLogicClient {
	return &gameradarLogicClient{cc}
}

func (c *gameradarLogicClient) InvitePlay(ctx context.Context, in *gameradarlogic.InvitePlayReq, opts ...grpc.CallOption) (*gameradarlogic.InvitePlayRsp, error) {
	out := new(gameradarlogic.InvitePlayRsp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/InvitePlay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) InviteSuc(ctx context.Context, in *gameradarlogic.InviteSucReq, opts ...grpc.CallOption) (*gameradarlogic.InviteSucRsp, error) {
	out := new(gameradarlogic.InviteSucRsp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/InviteSuc", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) RadarDisplay(ctx context.Context, in *gameradarlogic.RadarDisplayReq, opts ...grpc.CallOption) (*gameradarlogic.RadarDisplayRsp, error) {
	out := new(gameradarlogic.RadarDisplayRsp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/RadarDisplay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) GetPlaymates(ctx context.Context, in *gameradarlogic.GetPlaymatesReq, opts ...grpc.CallOption) (*gameradarlogic.GetPlaymatesResp, error) {
	out := new(gameradarlogic.GetPlaymatesResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/GetPlaymates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) GetPlaymateTabList(ctx context.Context, in *gameradarlogic.GetPlaymateTabListReq, opts ...grpc.CallOption) (*gameradarlogic.GetPlaymateTabListResp, error) {
	out := new(gameradarlogic.GetPlaymateTabListResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/GetPlaymateTabList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) ListPlaymateTabBlocks(ctx context.Context, in *gameradarlogic.ListPlaymateTabBlocksReq, opts ...grpc.CallOption) (*gameradarlogic.ListPlaymateTabBlocksResp, error) {
	out := new(gameradarlogic.ListPlaymateTabBlocksResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/ListPlaymateTabBlocks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) GetPlaymateNum(ctx context.Context, in *gameradarlogic.GetPlaymateNumReq, opts ...grpc.CallOption) (*gameradarlogic.GetPlaymateNumResp, error) {
	out := new(gameradarlogic.GetPlaymateNumResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/GetPlaymateNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) GetUserRadarStatus(ctx context.Context, in *gameradarlogic.GetUserRadarStatusReq, opts ...grpc.CallOption) (*gameradarlogic.GetUserRadarStatusResp, error) {
	out := new(gameradarlogic.GetUserRadarStatusResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/GetUserRadarStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) GetRadarConfig(ctx context.Context, in *gameradarlogic.GetRadarConfigReq, opts ...grpc.CallOption) (*gameradarlogic.GetRadarConfigResp, error) {
	out := new(gameradarlogic.GetRadarConfigResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/GetRadarConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) StartRadar(ctx context.Context, in *gameradarlogic.StartRadarReq, opts ...grpc.CallOption) (*gameradarlogic.StartRadarResp, error) {
	out := new(gameradarlogic.StartRadarResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/StartRadar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) StopRadar(ctx context.Context, in *gameradarlogic.StopRadarReq, opts ...grpc.CallOption) (*gameradarlogic.StopRadarResp, error) {
	out := new(gameradarlogic.StopRadarResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/StopRadar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameradarLogicClient) GetRecommendNum(ctx context.Context, in *gameradarlogic.GetRecommendNumReq, opts ...grpc.CallOption) (*gameradarlogic.GetRecommendNumResp, error) {
	out := new(gameradarlogic.GetRecommendNumResp)
	err := c.cc.Invoke(ctx, "/logic.GameradarLogic/GetRecommendNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameradarLogicServer is the server API for GameradarLogic service.
type GameradarLogicServer interface {
	InvitePlay(context.Context, *gameradarlogic.InvitePlayReq) (*gameradarlogic.InvitePlayRsp, error)
	InviteSuc(context.Context, *gameradarlogic.InviteSucReq) (*gameradarlogic.InviteSucRsp, error)
	RadarDisplay(context.Context, *gameradarlogic.RadarDisplayReq) (*gameradarlogic.RadarDisplayRsp, error)
	GetPlaymates(context.Context, *gameradarlogic.GetPlaymatesReq) (*gameradarlogic.GetPlaymatesResp, error)
	GetPlaymateTabList(context.Context, *gameradarlogic.GetPlaymateTabListReq) (*gameradarlogic.GetPlaymateTabListResp, error)
	ListPlaymateTabBlocks(context.Context, *gameradarlogic.ListPlaymateTabBlocksReq) (*gameradarlogic.ListPlaymateTabBlocksResp, error)
	GetPlaymateNum(context.Context, *gameradarlogic.GetPlaymateNumReq) (*gameradarlogic.GetPlaymateNumResp, error)
	// -------获取用户雷达状态-------
	GetUserRadarStatus(context.Context, *gameradarlogic.GetUserRadarStatusReq) (*gameradarlogic.GetUserRadarStatusResp, error)
	// -------获取雷达配置-------
	GetRadarConfig(context.Context, *gameradarlogic.GetRadarConfigReq) (*gameradarlogic.GetRadarConfigResp, error)
	// -------开启雷达-------
	StartRadar(context.Context, *gameradarlogic.StartRadarReq) (*gameradarlogic.StartRadarResp, error)
	// -------关闭雷达-------
	StopRadar(context.Context, *gameradarlogic.StopRadarReq) (*gameradarlogic.StopRadarResp, error)
	// -------拿推荐数-------
	GetRecommendNum(context.Context, *gameradarlogic.GetRecommendNumReq) (*gameradarlogic.GetRecommendNumResp, error)
}

func RegisterGameradarLogicServer(s *grpc.Server, srv GameradarLogicServer) {
	s.RegisterService(&_GameradarLogic_serviceDesc, srv)
}

func _GameradarLogic_InvitePlay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.InvitePlayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).InvitePlay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/InvitePlay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).InvitePlay(ctx, req.(*gameradarlogic.InvitePlayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_InviteSuc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.InviteSucReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).InviteSuc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/InviteSuc",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).InviteSuc(ctx, req.(*gameradarlogic.InviteSucReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_RadarDisplay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.RadarDisplayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).RadarDisplay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/RadarDisplay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).RadarDisplay(ctx, req.(*gameradarlogic.RadarDisplayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_GetPlaymates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.GetPlaymatesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).GetPlaymates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/GetPlaymates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).GetPlaymates(ctx, req.(*gameradarlogic.GetPlaymatesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_GetPlaymateTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.GetPlaymateTabListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).GetPlaymateTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/GetPlaymateTabList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).GetPlaymateTabList(ctx, req.(*gameradarlogic.GetPlaymateTabListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_ListPlaymateTabBlocks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.ListPlaymateTabBlocksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).ListPlaymateTabBlocks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/ListPlaymateTabBlocks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).ListPlaymateTabBlocks(ctx, req.(*gameradarlogic.ListPlaymateTabBlocksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_GetPlaymateNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.GetPlaymateNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).GetPlaymateNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/GetPlaymateNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).GetPlaymateNum(ctx, req.(*gameradarlogic.GetPlaymateNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_GetUserRadarStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.GetUserRadarStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).GetUserRadarStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/GetUserRadarStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).GetUserRadarStatus(ctx, req.(*gameradarlogic.GetUserRadarStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_GetRadarConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.GetRadarConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).GetRadarConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/GetRadarConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).GetRadarConfig(ctx, req.(*gameradarlogic.GetRadarConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_StartRadar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.StartRadarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).StartRadar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/StartRadar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).StartRadar(ctx, req.(*gameradarlogic.StartRadarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_StopRadar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.StopRadarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).StopRadar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/StopRadar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).StopRadar(ctx, req.(*gameradarlogic.StopRadarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameradarLogic_GetRecommendNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gameradarlogic.GetRecommendNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameradarLogicServer).GetRecommendNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.GameradarLogic/GetRecommendNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameradarLogicServer).GetRecommendNum(ctx, req.(*gameradarlogic.GetRecommendNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GameradarLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.GameradarLogic",
	HandlerType: (*GameradarLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InvitePlay",
			Handler:    _GameradarLogic_InvitePlay_Handler,
		},
		{
			MethodName: "InviteSuc",
			Handler:    _GameradarLogic_InviteSuc_Handler,
		},
		{
			MethodName: "RadarDisplay",
			Handler:    _GameradarLogic_RadarDisplay_Handler,
		},
		{
			MethodName: "GetPlaymates",
			Handler:    _GameradarLogic_GetPlaymates_Handler,
		},
		{
			MethodName: "GetPlaymateTabList",
			Handler:    _GameradarLogic_GetPlaymateTabList_Handler,
		},
		{
			MethodName: "ListPlaymateTabBlocks",
			Handler:    _GameradarLogic_ListPlaymateTabBlocks_Handler,
		},
		{
			MethodName: "GetPlaymateNum",
			Handler:    _GameradarLogic_GetPlaymateNum_Handler,
		},
		{
			MethodName: "GetUserRadarStatus",
			Handler:    _GameradarLogic_GetUserRadarStatus_Handler,
		},
		{
			MethodName: "GetRadarConfig",
			Handler:    _GameradarLogic_GetRadarConfig_Handler,
		},
		{
			MethodName: "StartRadar",
			Handler:    _GameradarLogic_StartRadar_Handler,
		},
		{
			MethodName: "StopRadar",
			Handler:    _GameradarLogic_StopRadar_Handler,
		},
		{
			MethodName: "GetRecommendNum",
			Handler:    _GameradarLogic_GetRecommendNum_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gameradar-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/gameradar-logic/gameradar-logic.proto", fileDescriptor_gameradar_logic_2cff6cf1dbe4a07b)
}

var fileDescriptor_gameradar_logic_2cff6cf1dbe4a07b = []byte{
	// 477 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0x5d, 0xab, 0xd3, 0x30,
	0x18, 0xc7, 0x09, 0xa8, 0xd8, 0x70, 0x38, 0xc2, 0x03, 0xde, 0xf4, 0xe6, 0xe8, 0xf1, 0x05, 0x0f,
	0x7a, 0x5a, 0x3c, 0xe2, 0x9d, 0x78, 0x31, 0x85, 0x21, 0x8c, 0x21, 0xab, 0x82, 0x6f, 0x20, 0x59,
	0x16, 0x6b, 0x59, 0xdb, 0xd4, 0x26, 0x9d, 0xec, 0xc2, 0x0f, 0xe6, 0xa7, 0xf0, 0x3b, 0xf8, 0xfe,
	0x12, 0x10, 0xf4, 0x0b, 0x48, 0xda, 0x6d, 0xc9, 0x66, 0xdf, 0xae, 0x46, 0xf2, 0xfc, 0x9e, 0xff,
	0x2f, 0x4f, 0xb6, 0x05, 0xdf, 0x8c, 0x79, 0x18, 0x51, 0xb1, 0xc8, 0x8f, 0x43, 0xee, 0x87, 0x24,
	0x61, 0x39, 0x99, 0x91, 0xfc, 0xb8, 0xdc, 0xdd, 0x5d, 0x7b, 0x59, 0xce, 0x25, 0x87, 0xd3, 0xe5,
	0xc2, 0x3d, 0xda, 0xee, 0x94, 0xec, 0x2d, 0x59, 0xfa, 0x3c, 0x93, 0x11, 0x4f, 0xc5, 0xfa, 0xb3,
	0xea, 0x70, 0x5d, 0x92, 0x65, 0xbb, 0x61, 0x2f, 0xab, 0xda, 0xc9, 0x5f, 0x07, 0xef, 0x0f, 0xd7,
	0xa5, 0x91, 0xae, 0xc0, 0x73, 0x8c, 0x1f, 0xa4, 0x8b, 0x48, 0xb2, 0x87, 0x31, 0x59, 0xc2, 0x05,
	0x2f, 0x24, 0xde, 0xa6, 0xb9, 0x3a, 0x88, 0x29, 0x4f, 0xd8, 0x1b, 0xb7, 0x83, 0x10, 0xd9, 0xa1,
	0xf3, 0xe7, 0xc3, 0x7b, 0xef, 0xd4, 0xd9, 0x8f, 0x0a, 0xc1, 0x13, 0xec, 0x54, 0xb5, 0xa0, 0xa0,
	0x70, 0xd0, 0xd8, 0x19, 0x14, 0x54, 0x47, 0xb7, 0x03, 0x26, 0xf9, 0x93, 0x42, 0x40, 0xf0, 0xde,
	0x44, 0x53, 0xf7, 0x23, 0x91, 0xe9, 0x83, 0x1f, 0xd6, 0xf4, 0xda, 0x80, 0xce, 0xef, 0x64, 0x8c,
	0xe2, 0xb3, 0x42, 0x30, 0xc5, 0x7b, 0x43, 0x26, 0xf5, 0x54, 0x09, 0x91, 0x4c, 0xd4, 0x2a, 0x6c,
	0x40, 0x2b, 0x2e, 0x75, 0x32, 0xc6, 0xf1, 0x45, 0x21, 0x90, 0x18, 0xac, 0xf2, 0x23, 0x32, 0x1d,
	0x45, 0x42, 0xc2, 0xb5, 0xf6, 0x94, 0x15, 0xa6, 0x7d, 0x47, 0x3d, 0x49, 0x63, 0xfd, 0xaa, 0x10,
	0xbc, 0xc3, 0xe7, 0xf5, 0xb6, 0x45, 0x0d, 0x62, 0x4e, 0xe7, 0x02, 0xae, 0xd7, 0xc4, 0xd5, 0x92,
	0xda, 0x7d, 0xa3, 0x3f, 0x6c, 0xf4, 0xdf, 0x14, 0x82, 0xd7, 0x78, 0xdf, 0x3a, 0xe3, 0xb8, 0x48,
	0xe0, 0x72, 0xfb, 0x18, 0xe3, 0x22, 0xd1, 0xc2, 0x2b, 0x3d, 0x28, 0x63, 0xfa, 0xbd, 0xb9, 0xde,
	0xc7, 0x82, 0xe5, 0xe5, 0xf7, 0x1c, 0x48, 0x22, 0x0b, 0xd1, 0x74, 0xbd, 0x3b, 0x58, 0xcb, 0xf5,
	0xfe, 0x47, 0x1a, 0xeb, 0xf7, 0xcd, 0x7c, 0x25, 0x70, 0x8f, 0xa7, 0xaf, 0xa2, 0xb0, 0x69, 0x3e,
	0x0b, 0x69, 0x99, 0x6f, 0x8b, 0x32, 0xa6, 0x1f, 0x0a, 0xc1, 0x0b, 0x8c, 0x03, 0x49, 0xf2, 0x0a,
	0xa9, 0xfd, 0xf3, 0x9a, 0xb2, 0x36, 0x5c, 0xec, 0x20, 0x4c, 0xfa, 0x4f, 0x85, 0xe0, 0x29, 0x76,
	0x02, 0xc9, 0xb3, 0x2a, 0xfc, 0xa0, 0xb6, 0x75, 0x55, 0x6d, 0x7a, 0x18, 0x2c, 0xc0, 0x44, 0xff,
	0x52, 0x08, 0xe6, 0xf8, 0x9c, 0x9e, 0x8c, 0x51, 0x9e, 0x24, 0x2c, 0x9d, 0xe9, 0xdf, 0x40, 0xd3,
	0xf4, 0x16, 0xa3, 0x35, 0x57, 0xfb, 0x60, 0x46, 0xa6, 0x14, 0x1a, 0xdc, 0x7d, 0x76, 0x27, 0xe4,
	0x31, 0x49, 0x43, 0xef, 0xf6, 0x89, 0x94, 0x1e, 0xe5, 0x89, 0x5f, 0x3e, 0x87, 0x94, 0xc7, 0xbe,
	0x60, 0xf9, 0x22, 0xa2, 0x4c, 0xf8, 0xb5, 0x4f, 0x73, 0xb9, 0x39, 0x3d, 0x53, 0xd2, 0xb7, 0xfe,
	0x05, 0x00, 0x00, 0xff, 0xff, 0x71, 0x7f, 0x02, 0x4b, 0xbf, 0x05, 0x00, 0x00,
}
