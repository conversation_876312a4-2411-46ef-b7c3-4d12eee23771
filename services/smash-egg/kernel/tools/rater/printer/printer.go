package printer

import (
	"fmt"
	"time"
)

type Printer struct {
	count int
	current int
}

func (s *Printer) Begin(count int)  {
	s.count = count
	s.current = 0

	for i := 0; i < count-1; i++ {
		fmt.Println(i)
	}

	time.Sleep(1)
}

func (s *Printer) Print(line int, text string) {
	s.current = switchLine(s.current, line)

	printLine(text)
}

func (s *Printer) End()  {
	for i := 0; i < s.count-1; i++ {
		fmt.Println()
	}
}

func switchLine(from, to int) int {
	if from == to {
		return to
	}

	upLine(from)
	downLine(to)
	return to
}

func upLine(step int) {
	if 0 == step {
		return
	}
	fmt.Printf("\033[%dA", step)
	return
}

func downLine(step int) {
	if 0 == step {
		return
	}
	fmt.Printf("\033[%dB", step)
	return
}

func printLine(text string) {
	fmt.Printf("\033[K%s\r", text)
}
