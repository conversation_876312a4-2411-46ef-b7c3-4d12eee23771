package businsess_ai_partner

import (
	"context"

	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/rcmd/business_ai_partner"
	"google.golang.org/grpc"
)

type Client struct {
	pb.BusinessAIPartnerClient
}

func NewClient(opts ...grpc.DialOption) (*Client, error) {
	cli := new(Client)

	var err error
	cli.BusinessAIPartnerClient, err = pb.NewClient(context.Background(), opts...)

	return cli, err
}

func (c *Client) GetPartnerInfo(ctx context.Context, uid, partnerId uint32) (*pb.GetPartnerInfoResp, error) {
	resp, err := c.BusinessAIPartnerClient.GetPartnerInfo(ctx, &pb.GetPartnerInfoReq{Uid: uid, PartnerId: partnerId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) RoleSwitch(ctx context.Context, in *pb.RoleSwitchReq) error {
	_, err := c.BusinessAIPartnerClient.RoleSwitch(ctx, in)
	return protocol.ToServerError(err)
}
