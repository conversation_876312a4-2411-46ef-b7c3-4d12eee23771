// Code generated by MockGen. DO NOT EDIT.
// Source: E:\quicksilver\clients\usertag\iclient.go

// Package usertag is a generated GoMock package.
package usertag

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	userTagSvr "golang.52tt.com/protocol/services/userTagSvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatGetSimpleGameTag mocks base method.
func (m *MockIClient) BatGetSimpleGameTag(ctx context.Context, uids []uint32, games []string) (map[uint32]map[string]*userTagSvr.SimpleGameTagExt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetSimpleGameTag", ctx, uids, games)
	ret0, _ := ret[0].(map[uint32]map[string]*userTagSvr.SimpleGameTagExt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetSimpleGameTag indicates an expected call of BatGetSimpleGameTag.
func (mr *MockIClientMockRecorder) BatGetSimpleGameTag(ctx, uids, games interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetSimpleGameTag", reflect.TypeOf((*MockIClient)(nil).BatGetSimpleGameTag), ctx, uids, games)
}

// BatGetUserTag mocks base method.
func (m *MockIClient) BatGetUserTag(ctx context.Context, uin uint32, userId []uint32) (map[uint32][]*userTagSvr.UserTagBase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetUserTag", ctx, uin, userId)
	ret0, _ := ret[0].(map[uint32][]*userTagSvr.UserTagBase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetUserTag indicates an expected call of BatGetUserTag.
func (mr *MockIClientMockRecorder) BatGetUserTag(ctx, uin, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetUserTag", reflect.TypeOf((*MockIClient)(nil).BatGetUserTag), ctx, uin, userId)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetOptPersonalTagClassifyList mocks base method.
func (m *MockIClient) GetOptPersonalTagClassifyList(ctx context.Context, uin uint32) ([]*userTagSvr.UserOptPersonalTagClassify, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptPersonalTagClassifyList", ctx, uin)
	ret0, _ := ret[0].([]*userTagSvr.UserOptPersonalTagClassify)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOptPersonalTagClassifyList indicates an expected call of GetOptPersonalTagClassifyList.
func (mr *MockIClientMockRecorder) GetOptPersonalTagClassifyList(ctx, uin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptPersonalTagClassifyList", reflect.TypeOf((*MockIClient)(nil).GetOptPersonalTagClassifyList), ctx, uin)
}

// GetSimpleGame mocks base method.
func (m *MockIClient) GetSimpleGame(ctx context.Context, uids []uint32, gameName string) (*userTagSvr.GetSimpleGameTagResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSimpleGame", ctx, uids, gameName)
	ret0, _ := ret[0].(*userTagSvr.GetSimpleGameTagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSimpleGame indicates an expected call of GetSimpleGame.
func (mr *MockIClientMockRecorder) GetSimpleGame(ctx, uids, gameName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSimpleGame", reflect.TypeOf((*MockIClient)(nil).GetSimpleGame), ctx, uids, gameName)
}

// GetTagConfigList mocks base method.
func (m *MockIClient) GetTagConfigList(ctx context.Context, uin, tagType uint32) ([]*userTagSvr.UserTagBase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTagConfigList", ctx, uin, tagType)
	ret0, _ := ret[0].([]*userTagSvr.UserTagBase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTagConfigList indicates an expected call of GetTagConfigList.
func (mr *MockIClientMockRecorder) GetTagConfigList(ctx, uin, tagType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTagConfigList", reflect.TypeOf((*MockIClient)(nil).GetTagConfigList), ctx, uin, tagType)
}

// GetUserTag mocks base method.
func (m *MockIClient) GetUserTag(ctx context.Context, uin uint32) ([]*userTagSvr.UserTagBase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTag", ctx, uin)
	ret0, _ := ret[0].([]*userTagSvr.UserTagBase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTag indicates an expected call of GetUserTag.
func (mr *MockIClientMockRecorder) GetUserTag(ctx, uin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTag", reflect.TypeOf((*MockIClient)(nil).GetUserTag), ctx, uin)
}

// NotifyChannelPlayChange mocks base method.
func (m *MockIClient) NotifyChannelPlayChange(ctx context.Context, uin uint32, tabName string, tabType, channelId uint32) (*userTagSvr.NotifyChannelPlayChangeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyChannelPlayChange", ctx, uin, tabName, tabType, channelId)
	ret0, _ := ret[0].(*userTagSvr.NotifyChannelPlayChangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotifyChannelPlayChange indicates an expected call of NotifyChannelPlayChange.
func (mr *MockIClientMockRecorder) NotifyChannelPlayChange(ctx, uin, tabName, tabType, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyChannelPlayChange", reflect.TypeOf((*MockIClient)(nil).NotifyChannelPlayChange), ctx, uin, tabName, tabType, channelId)
}

// SetUserGameScreenShot mocks base method.
func (m *MockIClient) SetUserGameScreenShot(ctx context.Context, in *userTagSvr.SetUserGameScreenShotReq) (*userTagSvr.SetUserGameScreenShotResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserGameScreenShot", ctx, in)
	ret0, _ := ret[0].(*userTagSvr.SetUserGameScreenShotResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserGameScreenShot indicates an expected call of SetUserGameScreenShot.
func (mr *MockIClientMockRecorder) SetUserGameScreenShot(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserGameScreenShot", reflect.TypeOf((*MockIClient)(nil).SetUserGameScreenShot), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
