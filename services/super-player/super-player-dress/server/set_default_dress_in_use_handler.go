package server

import (
	"context"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	pb "golang.52tt.com/protocol/services/superplayerdress"
	"golang.52tt.com/services/notify"
	"golang.52tt.com/services/super-player/super-player-dress/conf"

	"golang.52tt.com/services/super-player/super-player-dress/store"
)

func (svr *SuperPlayerDress) SetDefaultDressInUse(ctx context.Context, in *pb.SetDefaultDressInUseReq) (*pb.SetDefaultDressInUseResp, error) {
	var out pb.SetDefaultDressInUseResp
	var err error
	nowTs := uint64(time.Now().Unix())

	for i := 0; i < 1; i++ {
		var setDefault bool
		for _, dressType := range pb.DressType_value {
			if dressType == int32(pb.DressType_DRESS_TYPE_UNSPECIFIC) ||
				dressType == int32(pb.DressType_DRESS_TYPE_CHAT_BACKGROUND) {
				continue
			}

			list, err := svr.GetStore().GetDressAllConfigList(ctx, uint32(dressType))
			if err != nil {
				continue
			}

			for _, dressCfg := range list {
				if dressCfg.IsDefault == 1 && dressCfg.BeginTs <= nowTs && dressCfg.EndTs >= nowTs {
					_, err = svr.GetStore().GetDressInUse(ctx, uint32(dressType), in.Uid)
					if err != nil && err.Error() == "record not found" {
						dress := &store.DressInUse{
							ID:         uint64(in.GetUid()),
							DressType:  uint32(dressType),
							DressId:    dressCfg.ID,
							CreateTime: time.Now(),
						}
						err = svr.GetStore().SetDressInUse(ctx, dress)
						svr.SetDressInUseChan(dress)
						log.InfoWithCtx(ctx, "SetDefaultDressInUse uid:%d dress type:%d, dress id:%d, err:%v", in.GetUid(), dressType, dressCfg.ID, err)
						setDefault = true
					}
				}
			}
		}

		if setDefault {
			highlight := "装扮中心>"
			svr.SendIMMsgWithJumpUrl(uint32(in.GetUid()), "恭喜成为超级玩家！\n"+
				"已为您自动佩戴专属房间装扮、聊天气泡和特别关心装扮，快去看看吧～查看更多装扮，点击这里进入"+highlight, highlight)
		}
	}
	return &out, err
}

func (svr *SuperPlayerDress) SendIMMsgWithJumpUrl(uid uint32, content, highlight string) error {
	subCtx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	// 获取用户在线平台信息
	onlineInfo, err := svr.userOlCli.GetLastMobileOnlineInfo(subCtx, uid)
	if err != nil {
		log.ErrorWithCtx(subCtx, "SendIMMsgWithJumpUrl GetLastMultiOnlineInfo failed uid:%d err:%v", uid, err)
		return err
	}

	if onlineInfo.GetUid() != uid {
		log.InfoWithCtx(subCtx, "SendIMMsgWithJumpUrl user no onLineInfo uid:%d info:%v", uid, onlineInfo)
		return nil
	}

	_, os, _ := protocol.UnPackTerminalType(onlineInfo.TerminalType)
	var imPushConf conf.ImPushConf
	for _, pushConf := range svr.sc.ImPushConfs {
		if pushConf.MarketId == onlineInfo.MarketId && uint32(os) == pushConf.Os {
			imPushConf = *pushConf
		}
	}

	if imPushConf.WebUrl == "" {
		log.InfoWithCtx(subCtx, "SendImMsg user no push conf uid:%d marketId:%d", uid, onlineInfo.MarketId)
		return nil
	}

	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextHlUrl: &apiPB.ImTextWithHighlightUrl{
				Content:    content,
				Hightlight: highlight,
				Url:        imPushConf.WebUrl,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: imPushConf.AppPlatform,
		AppName:     imPushConf.AppName,
	}

	err = svr.apiCenterClient.SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(subCtx, "SendIMMsgWithJumpUrl fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.DebugfWithCtx(subCtx, "SendIMMsgWithJumpUrl done. uid:%d, content:%s, highlight:%s, jumpUrl:%s, Msg:%+v", uid, content, highlight, imPushConf.WebUrl, msg)
	return nil
}
