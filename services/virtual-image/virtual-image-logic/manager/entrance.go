package manager

import (
    "context"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    channelPb "golang.52tt.com/protocol/app/channel"
    pb "golang.52tt.com/protocol/app/virtual_image_logic"
    channel_go "golang.52tt.com/protocol/services/channel-go"
    virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
)

func (m *VirtualImageLogicMgr) CheckGameEnable(ctx context.Context, uid uint32) (bool, error) {
    return m.dyconfig.GetGameEnable(uid), nil
}

func (m *VirtualImageLogicMgr) CheckChannelEntrance(ctx context.Context, uid, cid uint32) (bool, error) {
    channelResp, err := m.channelCli.GetChannelSimpleInfo(ctx, &channel_go.GetChannelSimpleInfoReq{
        OpUid:     uid,
        ChannelId: cid,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckChannelEntrance GetChannelSimpleInfo failed. uid:%d, cid:%d, err:%v", uid, cid, err)
        return false, err
    }

    channelType := channelResp.GetChannelSimple().GetChannelType()
    // 仅pgc、ugc和临时房房间需要处理
    if channelType != uint32(channelPb.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) &&
        channelType != uint32(channelPb.ChannelType_RADIO_LIVE_CHANNEL_TYPE) &&
        channelType != uint32(channelPb.ChannelType_USER_CHANNEL_TYPE) &&
        channelType != uint32(channelPb.ChannelType_TEMP_KH_CHANNEL_TYPE) {

        return false, nil
    }

    return true, nil
}

func (m *VirtualImageLogicMgr) GetNoticeCfg(ctx context.Context) (*pb.ChannelNoticeCfg, error) {
    cfgResp, err := m.resourceCli.GetNoticeCfgCache(ctx, &virtual_image_resource.GetNoticeCfgCacheRequest{})
    if err != nil {
        log.ErrorWithCtx(ctx, "GetNoticeCfg GetNoticeCfgCache failed. err:%v", err)
        return nil, err
    }

    cfg := cfgResp.GetCfg()
    out := &pb.ChannelNoticeCfg{
        BeginTime:            cfg.GetBeginTime(),
        EndTime:              cfg.GetEndTime(),
        PublicContent:        cfg.GetPublicContent(),
        PublicContentColor:   cfg.GetPublicContentColor(),
        PublicContentJumpUrl: cfg.GetPublicContentJumpUrl(),
        FloatContent:         cfg.GetFloatContent(),
        FloatContentDuration: cfg.GetFloatContentDuration(),
        HasRedDot:            cfg.GetHasRedDot(),
    }

    return out, nil
}
