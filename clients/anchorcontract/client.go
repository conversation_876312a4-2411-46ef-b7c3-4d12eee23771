package anchorcontract

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"strconv"

	"golang.52tt.com/pkg/tracing"
	tracingGrpc "golang.52tt.com/pkg/tracing/grpc" //
	PB "golang.52tt.com/protocol/services/anchorcontractsvr"
)

const (
	serviceName = "anchorcontract"
)

type Client struct {
	client.BaseClient
}

func NewClient(dopts ...grpc.DialOption) *Client {
	return newClient(dopts...)
}

func NewTracedClient(tracer tracing.Tracer) *Client {
	tracerInt := tracingGrpc.TracedUnaryClientInterceptor(
		tracing.LogPayloads(true),
		tracing.UsingTracer(tracer),
	)

	return newClient(grpc.WithUnaryInterceptor(tracerInt))
}

func newClient(dopts ...grpc.DialOption) *Client {

	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return PB.NewAnchorcontractClient(cc)
		}, dopts...),
	}
}
func (c *Client) typedStub() PB.AnchorcontractClient { return c.Stub().(PB.AnchorcontractClient) }

func (c *Client) GetUserContractInfo(ctx context.Context, uin, targetUid uint32) (*PB.GetUserContractInfoResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetUserContractInfo(ctx, &PB.GetUserContractInfoReq{ActorUid: targetUid})
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetGuildRenewAbleList(ctx context.Context, uin, guildId, begin, limit uint32) (*PB.GetGuildRenewAbleListResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetGuildRenewAbleList(ctx, &PB.GetGuildRenewAbleListReq{
		GuildId: guildId,
		Begin:   begin,
		Limit:   limit,
	})
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetGuildActor(ctx context.Context, uin, guildId, begin, limit uint32) (*PB.GetGuildActorResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetGuildActor(ctx, &PB.GetGuildActorReq{
		GuildId: guildId,
		Begin:   begin,
		Limit:   limit,
	})
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetGuildContractSum(ctx context.Context, uin, guildId uint32) (*PB.GetGuildContractSumResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetGuildContractSum(ctx, &PB.GetGuildContractSumReq{
		GuildId: guildId,
	})
	return resp, protocol.ToServerError(err)

}

func (c *Client) GetGuildLiveActor(ctx context.Context, uin, guildId uint32) (*PB.GetGuildLiveActorResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().GetGuildLiveActor(ctx, &PB.GetGuildLiveActorReq{GuildId: guildId})
	return resp, protocol.ToServerError(err)

}

func (c *Client) SetContractLiveActorFlag(ctx context.Context, uid, flag uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uid))))
	_, err := c.typedStub().SetContractLiveActorFlag(ctx, &PB.SetContractLiveActorFlagReq{Uid: uid, Flag: flag})
	return protocol.ToServerError(err)
}

func (c *Client) BatchGetCancelContractCnt(ctx context.Context, uin uint32, uids []uint32) (*PB.BatchGetCancelContractCntResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().BatchGetCancelContractCnt(ctx, &PB.BatchGetCancelContractCntReq{Uids: uids})
	return resp, protocol.ToServerError(err)

}

func (c *Client) SearchActor(ctx context.Context, uin, guildId, signType uint32, keyword string) (*PB.SearchActorResp, error) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(uin))))
	resp, err := c.typedStub().SearchActor(ctx, &PB.SearchActorReq{
		GuildId: guildId,
		Keyword: keyword,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetAllGuild(ctx context.Context, begin, limit uint32) ([]*PB.GuildContractInfo, protocol.ServerError) {
	resp, err := c.typedStub().GetAllGuild(ctx, &PB.GetAllGuildReq{
		Begin: begin,
		Limit: limit,
	})
	return resp.GetGuildInfoList(), protocol.ToServerError(err)
}

func (c *Client) GetGuildLiveActorUidList(ctx context.Context, guildId, begin, limit uint32) ([]uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetGuildLiveActorUidList(ctx, &PB.GetGuildLiveActorUidListReq{
		GuildId: guildId,
		Begin:   begin,
		Limit:   limit,
	})
	return resp.GetActorList(), protocol.ToServerError(err)
}

func (c *Client) GetSignContractChangeLog(ctx context.Context, uid uint32) (*PB.GetSignContractChangeLogResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSignContractChangeLog(ctx, &PB.GetSignContractChangeLogReq{
		Uid: uid,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserInGuildMonthScore(ctx context.Context, uid uint32, guildId uint32, timestamp uint32) (*PB.GetUserInGuildMonthScoreResp, protocol.ServerError) {
	resp, err := c.typedStub().GetUserInGuildMonthScore(ctx, &PB.GetUserInGuildMonthScoreReq{
		Uid:       uid,
		GuildId:   guildId,
		Timestamp: timestamp,
	})
	return resp, protocol.ToServerError(err)
}
