package friendship

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/ugc/friendship"
)

const (
	serviceName = "ugc-friendship"
)

type Sort = pb.LoadMore_Sort

const (
	SortAscending  Sort = pb.LoadMore_ASC
	SortDescending Sort = pb.LoadMore_DESC
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewFriendshipClient(cc)
		}, dopts...),
	}, nil
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
		grpcClient.UnaryClientInterceptor,
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.FriendshipClient { return c.Stub().(pb.FriendshipClient) }

// 关注
func (c *Client) FollowUserSimple(ctx context.Context, fromUid, toUid uint32, sequence uint64) (bool, bool, protocol.ServerError) {
	r, err := c.typedStub().FollowUser(ctx, &pb.FollowUserReq{
		Uid: fromUid, FollowingUid: toUid, SequenceId: sequence,
	})
	return r.GetAffected(), r.GetIsFirstTimeFollow(), protocol.ToServerError(err)
}

func (c *Client) FollowUser(ctx context.Context, in *pb.FollowUserReq) (bool, bool, protocol.ServerError) {
	r, err := c.typedStub().FollowUser(ctx, in)
	return r.GetAffected(), r.GetIsFirstTimeFollow(), protocol.ToServerError(err)
}

// 取消关注(软删除)
func (c *Client) UnfollowUser(ctx context.Context, fromUid, toUid uint32, sequence uint64) (bool, protocol.ServerError) {
	r, err := c.typedStub().UnfollowUser(ctx, &pb.UnfollowUserReq{
		Uid: fromUid, FollowingUid: toUid, SequenceId: sequence,
	})
	return r.GetAffected(), protocol.ToServerError(err)
}

// 彻底删除关注关系(仅供测试)
func (c *Client) HardDeleteFollowing(ctx context.Context, fromUid, toUid uint32) (bool, protocol.ServerError) {
	r, err := c.typedStub().UnfollowUser(ctx, &pb.UnfollowUserReq{
		Uid: fromUid, FollowingUid: toUid, HardDelete: true,
	})
	return r.GetAffected(), protocol.ToServerError(err)
}

// 按照id排序(a.k.a. 创建时间)
func NewLoadMoreByID(sort Sort, lastID string) *pb.LoadMore {
	return &pb.LoadMore{
		Sort: sort,
		OrderBy: &pb.LoadMore_IdKey{
			IdKey: &pb.LoadMore_IDKey{LastObjectId: lastID},
		},
	}
}

// 按sequenceId排序, 只可以用于拉取关注列表
func NewLoadMoreBySequenceId(sort Sort, lastSeq uint64) *pb.LoadMore {
	return &pb.LoadMore{
		Sort: sort,
		OrderBy: &pb.LoadMore_SequenceIdKey_{SequenceIdKey: &pb.LoadMore_SequenceIdKey{
			LastSequenceId: lastSeq,
		}},
	}
}

// 获取关注列表
func (c *Client) GetUserFollowingList(ctx context.Context, userID uint32, includingDropped bool, loadMore *pb.LoadMore, count uint32) ([]*pb.Following, *pb.LoadMore, protocol.ServerError) {
	return c.getFollowingList(ctx, &pb.GetFollowingListReq{
		RequestType:      &pb.GetFollowingListReq_FollowedByUid{FollowedByUid: userID},
		IncludingDropped: includingDropped,
		LoadMore:         loadMore,
		Count:            count,
	})
}

// 获取粉丝列表
func (c *Client) GetUserFollowerList(ctx context.Context, userID uint32, includingDropped bool, loadMore *pb.LoadMore, count uint32) ([]*pb.Following, *pb.LoadMore, protocol.ServerError) {
	return c.getFollowingList(ctx, &pb.GetFollowingListReq{
		RequestType:      &pb.GetFollowingListReq_FollowingUid{FollowingUid: userID},
		IncludingDropped: includingDropped,
		LoadMore:         loadMore,
		Count:            count,
	})
}

func (c *Client) getFollowingList(ctx context.Context, in *pb.GetFollowingListReq) ([]*pb.Following, *pb.LoadMore, protocol.ServerError) {
	r, err := c.typedStub().GetFollowingList(ctx, in)
	return r.GetFollowingList(), r.GetLoadMore(), protocol.ToServerError(err)
}

// 获取关注粉丝数
func (c *Client) GetUserCounts(ctx context.Context, userID uint32) (uint32 /*followingCount*/, uint32 /*followerCount*/, protocol.ServerError) {
	r, err := c.typedStub().GetUserCounts(ctx, &pb.GetUserCountsReq{Uid: userID})
	return r.GetUserCounts().GetFollowingCount(), r.GetUserCounts().GetFollowerCount(), protocol.ToServerError(err)
}

// 获取关注粉丝数(批量)
func (c *Client) BatchGetUserCounts(ctx context.Context, userIDList []uint32) (map[uint32]*pb.Counts, protocol.ServerError) {
	r, err := c.typedStub().BatchGetUserCounts(ctx, &pb.BatchGetUserCountsReq{UidList: userIDList})
	return r.GetUserCountsMap(), protocol.ToServerError(err)
}

// 获取双向关注状态
func (c *Client) GetBiFollowing(ctx context.Context, uidA, uidB uint32, allowCache ...bool) (*pb.Following, *pb.Following, protocol.ServerError) {
	req := &pb.GetBiFollowingReq{UidA: uidA, UidB: uidB}
	if len(allowCache) > 0 {
		req.AllowCache = allowCache[0]
	}
	r, err := c.typedStub().GetBiFollowing(ctx, req)
	return r.GetAToB(), r.GetBToA(), protocol.ToServerError(err)
}

func (c *Client) IsFriend(ctx context.Context, uidA, uidB uint32, allowCache ...bool) (bool, protocol.ServerError) {
	req := &pb.GetBiFollowingReq{UidA: uidA, UidB: uidB}
	if len(allowCache) > 0 {
		req.AllowCache = allowCache[0]
	}
	r, err := c.typedStub().GetBiFollowing(ctx, req)
	a2b, b2a := r.GetAToB(), r.GetBToA()
	if a2b != nil && !a2b.Dropped && b2a != nil && !b2a.Dropped {
		return true, nil
	}
	return false, protocol.ToServerError(err)
}

// 批量获取双向关注状态
func (c *Client) BatchGetBiFollowing(ctx context.Context, uid uint32, testUidSet []uint32, testFollowings, testFollowers bool) (map[uint32]*pb.Following, map[uint32]*pb.Following, protocol.ServerError) {
	r, err := c.typedStub().BatchGetBiFollowing(ctx, &pb.BatchGetBiFollowingReq{
		Uid:            uid,
		TestUidSet:     testUidSet,
		TestFollowings: testFollowings,
		TestFollowers:  testFollowers,
	})
	return r.GetFollowingsMap(), r.GetFollowersMap(), protocol.ToServerError(err)
}

//
//func (c *Client) GetFriendsBySeq(ctx context.Context, uid uint32, seq uint64) ([]*pb.GetFriendsBySeqResp_FriendSeqInfo, protocol.ServerError) {
//	r, err := c.typedStub().GetFriendsBySeq(ctx, &pb.GetFriendsBySeqReq{
//		Uid:            uid,
//		SequenceId:     seq,
//	})
//	return r.GetFriends(), protocol.ToServerError(err)
//}
//
//func (c *Client) UpdateFriendSeq(ctx context.Context, uid, member uint32, seq uint64, del bool) (protocol.ServerError) {
//	_, err := c.typedStub().UpdateFriendSeq(ctx, &pb.UpdateFriendSeqReq{
//        Del:            del,
//		Uid:            uid,
//        Member:         member,
//		SequenceId:     seq,
//	})
//
//	return protocol.ToServerError(err)
//}
//
//func (c *Client) GetFriendRemark(ctx context.Context, uid, member uint32) (string, protocol.ServerError) {
//    remark := ""
//	out, err := c.typedStub(). GetFriendsRemark(ctx, &pb.GetFriendsRemarkReq{
//        Uid:uid,
//        Member:[]uint32{member},
//    })
//
//    if err != nil {
//        return remark, protocol.ToServerError(err)
//    }
//
//    if t, ok := out.Field[member]; ok {
//        remark = t
//    }
//    return remark, protocol.ToServerError(err)
//}
//
//func (c *Client) NotifyAllMyFriends(ctx context.Context, uid uint32) protocol.ServerError {
//    _, err := c.typedStub().NotifyAllMyFriends(ctx, &pb.NotifyAllMyFriendsReq{Uid:uid})
//    return protocol.ToServerError(err)
//}

func (c *Client) GetShowUserFollow(ctx context.Context, uid uint32) (bool, protocol.ServerError) {
	resp, err := c.typedStub().GetShowUserFollow(ctx, &pb.GetShowUserFollowReq{
		Uid: uid,
	})
	// 默认不显示
	show := false
	if err == nil {
		show = resp.Show
	}
	return show, protocol.ToServerError(err)
}

func (c *Client) SetShowUserFollow(ctx context.Context, uid uint32, show bool) protocol.ServerError {
	_, err := c.typedStub().SetShowUserFollow(ctx, &pb.SetShowUserFollowReq{
		Uid:  uid,
		Show: show,
	})
	return protocol.ToServerError(err)
}

// 获取新好友表的好友信息
func (c *Client) GetOneFriendInfo(ctx context.Context, uid, friendID uint32) (*pb.FriendInfo, protocol.ServerError) {
	resp, err := c.typedStub().GetOneFriendInfo(ctx, &pb.GetOneFriendInfoReq{
		Uid:       uid,
		FriendUid: friendID,
	})
	return resp.GetFriendInfo(), protocol.ToServerError(err)
}

func (c *Client) GetAllFriendInfo(ctx context.Context, uid uint32, valid bool) ([]*pb.FriendInfo, protocol.ServerError) {
	resp, err := c.typedStub().GetAllFriendInfo(ctx, &pb.GetAllFriendInfoReq{
		Uid:         uid,
		IsOnlyValid: valid,
	})
	return resp.GetFriends(), protocol.ToServerError(err)
}

// 获取新好友表的好友数量
func (c *Client) GetFriendCount(ctx context.Context, uid, choice uint32) (uint32, protocol.ServerError) {
	resp, err := c.typedStub().GetFriendCount(ctx, &pb.GetFriendCountReq{
		Uid:    uid,
		Choice: choice,
	})
	var count uint32
	count = 0
	if err == nil {
		count = resp.Count
	}
	return count, protocol.ToServerError(err)
}

// 分页获取好友
func (c *Client) GetFriendByPage(ctx context.Context, uid, page, pageCount uint32) ([]*pb.FriendInfo, protocol.ServerError) {
	resp, err := c.typedStub().GetFriendByPage(ctx, &pb.GetFriendByPageReq{
		Uid:       uid,
		Page:      page,
		PageCount: pageCount,
	})
	return resp.GetFriends(), protocol.ToServerError(err)
}

// 批量获取缓存里的关注关系
func (c *Client) BatchGetBiFollowingWithCache(ctx context.Context, uid uint32, uidList []uint32, isNeedFollowing bool, isNeedFollower bool) (map[uint32]bool, map[uint32]bool, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetBiFollowingWithCache(ctx, &pb.BatchGetBiFollowingWithCacheReq{
		Uid:           uid,
		ToUids:        uidList,
		NeedFollowing: isNeedFollowing,
		NeedFollower:  isNeedFollower,
	})

	if nil != err {
		return make(map[uint32]bool), make(map[uint32]bool), protocol.ToServerError(err)
	}

	return resp.GetFollowingMap(), resp.GetFollowerMap(), nil
}

func (c *Client) GetSupervisionFansCount(ctx context.Context, uid uint32) (uint32, error) {
	resp, err := c.typedStub().GetSupervisionFansCount(ctx, &pb.GetSupervisionFansCountReq{Uid: uid})

	if err != nil {
		return 0, err
	}

	return resp.GetCount(), nil
}

// 更新好友信息
func (c *Client) UpdateFriendInfo(ctx context.Context, in *pb.UpdateFriendInfoReq) (*pb.UpdateFriendInfoResp, error) {
	return c.typedStub().UpdateFriendInfo(ctx, in)
}
func (c *Client) UpdateMultiFriendInfo(ctx context.Context, in *pb.UpdateMultiFriendInfoReq) (*pb.UpdateMultiFriendInfoResp, error) {
	return c.typedStub().UpdateMultiFriendInfo(ctx, in)
}

func (c *Client) BatchGetFriendInfo(ctx context.Context, in *pb.BatchGetFriendInfoReq) (*pb.BatchGetFriendInfoResp, error) {
	return c.typedStub().BatchGetFriendInfo(ctx, in)
}
