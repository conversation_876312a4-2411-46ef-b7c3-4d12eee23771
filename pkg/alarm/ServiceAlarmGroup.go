package alarm

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"path/filepath"
	"strings"
	"sync"

	"github.com/fsnotify/fsnotify"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
)

type ServiceAlarmGroup struct {
	mtx              sync.RWMutex
	AlarmTokenConfig AlarmTokenConfig    `json:"AlarmTokenConfig"`
	Fallback         string              `json:"Fallback"`
	DisableLabel     map[string]struct{} `json:"DisableLabel"`
	Dict             map[string]string   `json:"AlarmDict"`
}

func (s *ServiceAlarmGroup) Start(confCenterPath string) (err error) {
	err = s.loadConf(confCenterPath)
	if nil != err {
		log.Errorf("load conf failed, confPath: %s, err: %s", confCenterPath, err)
		return err
	}

	watch(context.Background(), filepath.Dir(confCenterPath), fsnotify.Create|fsnotify.Write, func(file string) (string, bool) {
		if file == confCenterPath {
			return confCenterPath, false
		}
		return "", true
	}, s.loadConf)

	return nil
}

func (s ServiceAlarmGroup) IsDisable(env, svr, label string) bool {
	s.mtx.RLock()
	defer s.mtx.RUnlock()

	if _, ok := s.DisableLabel[strings.Join([]string{env, svr, label}, "/")]; ok {
		return true
	}
	return false
}

func (s ServiceAlarmGroup) getSvcAlarmGroup(svc string) string {
	s.mtx.RLock()
	defer s.mtx.RUnlock()

	if v, ok := s.Dict[svc]; ok {
		return v
	}
	return s.Fallback

}

func (s *ServiceAlarmGroup) loadConf(path string) (err error) {
	defer func() {
		if sEx := recover(); nil != sEx {
			log.Errorf("load filePath: %s, catch ex: %s", path, sEx)
			err = sEx.(error)
		}
	}()

	buffer, err := ioutil.ReadFile(path)
	if err != nil {
		log.Errorf("serviceAlarmGroup read file:%s failed, err: %s", path, err)
		return err
	}

	if 0 != len(buffer) {
		s.mtx.Lock()
		defer s.mtx.Unlock()

		tmpConf := ServiceAlarmGroup{}
		err = json.Unmarshal(buffer, &tmpConf)
		if nil != err {
			log.Errorf("load conf: %s failed, str: %s", path, string(buffer))
		} else {
			log.Infof("serviceAlarmGroup %+v", tmpConf)
			s.AlarmTokenConfig = tmpConf.AlarmTokenConfig
			if s.AlarmTokenConfig.DurationMinute == 0 {
				s.AlarmTokenConfig.DurationMinute = 3
			}
			s.Fallback = tmpConf.Fallback
			s.Dict = tmpConf.Dict
		}
	} else {
		log.Errorf("serviceAlarmGroup read file: %s empty", path)
		return protocol.NewServerError(status.ErrKeyNotFound, status.MessageFromCode(status.ErrKeyNotFound))
	}

	return nil
}
