package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/channel-live-logic"
	"golang.52tt.com/protocol/common/status"
	mgrPb "golang.52tt.com/protocol/services/channellivemgr"
)

func (s *ChannelLiveLogic_) GetVirtualLiveChannelSecret(ctx context.Context, in *pb.GetVirtualLiveChannelSecretRequest) (*pb.GetVirtualLiveChannelSecretResponse, error) {
	out := &pb.GetVirtualLiveChannelSecretResponse{}
	serviceInfo, _ := protogrpc.ServiceInfoFromContext(ctx)
	uid := serviceInfo.UserID

	// 查询是否有虚拟主播权限
	virtualResp, err := s.channelLiveMgrCli.CheckHasVirtualAnchorPer(ctx, &mgrPb.CheckHasVirtualAnchorPerReq{Uid: uid})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualLiveChannelSecret CheckHasVirtualAnchorPer fail uid:%d err:%v", uid, err)
		return out, err
	}
	if !virtualResp.GetHasPer() {
		log.ErrorWithCtx(ctx, "GetVirtualLiveChannelSecret no virtual per fail uid:%d err:%v", uid, err)
		return out, protocol.NewExactServerError(nil, status.ErrRequestParamInvalid, "没有虚拟开播权限")
	}

	secretRsp, err := s.channelLiveMgrCli.GetVirtualLiveChannelSecret(ctx, &mgrPb.GetVirtualLiveChannelSecretReq{
		AnchorUid: uid,
		ChannelId: in.ChannelId,
		Refresh:   in.Refresh,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetVirtualLiveChannelSecret GetVirtualLiveChannelSecret fail uid:%d, in:%s err:%v", uid, in.String(), err)
		return out, err
	}
	log.InfoWithCtx(ctx, "GetVirtualLiveChannelSecret uid:%d, in:%s, out:%s", uid, in.String(), out.String())
	out.ChannelSecret = secretRsp.ChannelSecret
	return out, nil
}
