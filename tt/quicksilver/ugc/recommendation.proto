syntax = "proto3";

// buf:lint:ignore DIRECTORY_SAME_PACKAGE
package ugc.recommendation;

option go_package = "golang.52tt.com/protocol/services/ugc/recommendation";

service UgcRecommendation {
    // 用户浏览记录
    rpc AddUserReadHistory (AddUserReadHistoryReq) returns (AddUserReadHistoryResp);

    // 加入推荐池
    rpc AddPostsInto (AddPostsIntoReq) returns (AddPostsIntoResp);

    // 从推荐池移除
    rpc RemovePosts (RemovePostsReq) returns (RemovePostsResp);

    // 从推荐池里面取数据
    rpc GetPostsWithFilter (GetPostsWithFilterReq) returns (GetPostsWithFilterResp);

}

message AddUserReadHistoryReq {
    uint32 user_id = 1;
    repeated string post_id_list = 2;
}

message AddUserReadHistoryResp {

}

message RecommendationPostInfo {
    string post_id = 1;
    string topic_id = 2;
    uint32 score = 3;
    string hint = 4;
}

message AddPostsIntoReq {
    repeated RecommendationPostInfo post_info_list = 1;
}

message AddPostsIntoResp {

}

message RemovePostsReq {
    repeated string post_id_list = 1;
}

message RemovePostsResp {

}

message Filter {
    enum Method {
        RANDOM = 0;
        TIME = 1;
        TAGS = 2;
        FORCE = 3;
    }
    Method method = 1;
    repeated string exception_post_id_list = 2;
}

message GetPostsWithFilterReq {
    uint32 user_id = 1;
    Filter filter = 2;
    uint32 count = 3;
}

message GetPostsWithFilterResp {
    repeated RecommendationPostInfo post_list = 1;
}
