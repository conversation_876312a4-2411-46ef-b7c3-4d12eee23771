package store

import (
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/services/channel-live/channel-live-pk/internal/model/data"
	"time"
)

func (s *Store) AddMultiPk(data *data.MultiPkData) (uint32, error) {
	err := s.mysqlDb.Create(data).Error
	if err != nil {
		return 0, err
	}

	return data.PkId, err
}

func (s *Store) GetAnchorMultiPkList(uid, limit uint32, tm time.Time) ([]*data.MultiPkData, error) {
	dataList := make([]*data.MultiPkData, 0)

	err := s.mysqlDb.Model(&data.MultiPkData{}).Where("(uid_a = ? or uid_b = ? or uid_c = ? or uid_d = ?) and create_tm >= ?", uid, uid,
		uid, uid, tm).Order("create_tm desc").Limit(limit).Find(&dataList).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return dataList, nil
		}
		return nil, err
	}
	return dataList, nil
}

func (s *Store) UpdateMultiPkType(pkId, pkType uint32) error {
	sql := fmt.Sprintf("UPDATE %s SET pk_type = ? WHERE pk_id = ?", data.MultiPkDataTb)

	err := s.mysqlDb.Exec(sql, pkType, pkId).Error

	return err
}
