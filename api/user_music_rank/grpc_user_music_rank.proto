// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.user_music_rank;



import "user_music_rank/user-music-rank-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/user_music_rank;user_music_rank";

service UserMusicRankLogic {
    option (ga.api.extension.logic_service_name) = "user-music-rank-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.UserMusicRankLogic/";
    rpc GetMusicRankUserInfo(ga.user_music_rank.BatchMusicRankUserInfoReq) returns (ga.user_music_rank.BatchMusicRankUserInfoResp) {
        option (ga.api.extension.command) = {
             id: 31600
        };
    }
    rpc GetUserMusicRankDialog(ga.user_music_rank.GetUserMusicRankDialogReq) returns (ga.user_music_rank.GetUserMusicRankDialogResp) {
        option (ga.api.extension.command) = {
             id: 31601
        };
    }
    rpc ListUserMusicRankSingerScore(ga.user_music_rank.ListUserMusicRankSingerScoreReq) returns (ga.user_music_rank.ListUserMusicRankSingerScoreResp) {
        option (ga.api.extension.command) = {
             id: 31602
        };
    }
    rpc GetUserMusicRankSingerScoreDetail(ga.user_music_rank.GetUserMusicRankSingerScoreDetailReq) returns (ga.user_music_rank.GetUserMusicRankSingerScoreDetailResp) {
        option (ga.api.extension.command) = {
             id: 31603
        };
    }
    rpc UserMusicRankDialogConfirm(ga.user_music_rank.UserMusicRankDialogConfirmReq) returns (ga.user_music_rank.UserMusicRankDialogConfirmResp) {
        option (ga.api.extension.command) = {
             id: 31604
        };
    }
}

