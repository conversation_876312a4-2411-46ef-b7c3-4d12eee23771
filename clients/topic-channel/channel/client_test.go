package channel_test

import (
	"context"
	. "github.com/onsi/ginkgo"
	"github.com/onsi/ginkgo/reporters"
	. "github.com/onsi/gomega"
	. "golang.52tt.com/clients/topic-channel/channel"
	tcpb "golang.52tt.com/protocol/services/topic_channel/channel"
	"google.golang.org/grpc"
	"testing"
	"time"
)

func TestChannel(t *testing.T) {
	RegisterFailHandler(Fail)
	junitReporter := reporters.NewJUnitReporter("junit.xml")
	RunSpecsWithDefaultAndCustomReporters(t, "topic-channel", []Reporter{junitReporter})
}

var _ = Describe("topic-channel", func() {
	var client *Client
	// channel服务连接
	client, _ = NewClient(grpc.WithBlock(), grpc.WithAuthority("topic-channel.52tt.local"))
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	var aChannelId uint32 = 10201953
	var errChannelId uint32 = 111
	// 发布房间
	_, err := client.AddChannel(ctx, &tcpb.AddChannelReq{
		Channel: &tcpb.ChannelInfo{
			Id:                 aChannelId,
			TabId:              1,
			CreateTime:         0,
			MaleCount:          0,
			FemaleCount:        0,
			IsRecommendChannel: false,
			Creator:            0,
			TotalCount:         0,
			ScanParam:          "",
			BlockOptions:       nil,
			IsPrivate:          false,
			OnMicCount:         0,
			Name:               "",
			DisplayType:        nil,
			WantFresh:          false,
			ReleaseIp:          "",
			ShowGeoInfo:        false,
		},
		IsChange: false,
	})
	Context("发布房间", func() {
		It("发布房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
	})

	_, err = client.AddChannel(ctx, &tcpb.AddChannelReq{
		Channel: &tcpb.ChannelInfo{
			Id:                 errChannelId,
			TabId:              1,
			CreateTime:         0,
			MaleCount:          0,
			FemaleCount:        0,
			IsRecommendChannel: false,
			Creator:            0,
			TotalCount:         0,
			ScanParam:          "",
			BlockOptions:       nil,
			IsPrivate:          false,
			OnMicCount:         0,
			Name:               "",
			DisplayType:        nil,
			WantFresh:          false,
			ReleaseIp:          "",
			ShowGeoInfo:        false,
		},
		IsChange: false,
	})
	Context("发布房间", func() {
		It("发布房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
	})

	_, err = client.UpdateChannelInfo(ctx, &tcpb.UpdateChannelInfoReq{
		Id:          aChannelId,
		TabId:       1,
		MaleCount:   22,
		FemaleCount: 33,
		TotalCount:  55,
		OnMicCount:  1,
		Creator:     0,
	})
	Context("修改房间", func() {
		It("修改房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
	})

	channelInfoResp, gerr := client.GetChannelByIds(ctx, &tcpb.GetChannelByIdsReq{Ids: []uint32{aChannelId}, ReturnAll: true})
	Context("获取房间", func() {
		It("获取房间接口失败", func() {
			Expect(gerr).ShouldNot(HaveOccurred())
		})
		It("房间发布但未显示", func() {
			Expect(isDisplayChannel(channelInfoResp.Info[0].DisplayType)).To(Equal(false))
		})
		It("房间人数不等于修改人数", func() {
			Expect(channelInfoResp.Info[0].TotalCount).Should(Equal(uint32(55)))
		})
	})

	dismissResp, err := client.DismissChannel(ctx, &tcpb.DismissChannelReq{ChannelId: aChannelId})
	Context("解散房间", func() {
		It("解散房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
		It("解散房间失败", func() {
			Expect(dismissResp.Dismiss).Should(Equal(true))
		})
	})
	channelInfoResp, gerr = client.GetChannelByIds(ctx, &tcpb.GetChannelByIdsReq{Ids: []uint32{aChannelId}, ReturnAll: true})
	Context("获取房间", func() {
		It("获取房间接口失败", func() {
			Expect(gerr).ShouldNot(HaveOccurred())
		})
		It("解散房间但而然显示", func() {
			Expect(isDisplayChannel(channelInfoResp.Info[0].DisplayType)).To(Equal(false))
		})
	})

	_, err = client.FreezeChannel(ctx, &tcpb.FreezeChannelReq{ChannelIdList: []uint32{aChannelId}, FreezeTime: int64(120)})
	Context("冻结房间", func() {
		It("冻结房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})

		channelFreezeResp, ferr := client.GetChannelFreezeInfo(ctx, &tcpb.GetChannelFreezeInfoReq{ChannelId: aChannelId})
		Context("获取房间冻结信息", func() {
			It("获取房间冻结信息失败", func() {
				Expect(ferr).ShouldNot(HaveOccurred())
			})
			It("冻结房间失败", func() {
				Expect(channelFreezeResp.FreezeTime).ShouldNot(Equal(int64(0)))
			})
		})
	})

	_, err = client.UnfreezeChannel(ctx, &tcpb.UnfreezeChannelReq{ChannelIdList: []uint32{aChannelId}})
	Context("解冻房间", func() {
		It("解冻房间接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
		channelFreezeResp, ferr := client.GetChannelFreezeInfo(ctx, &tcpb.GetChannelFreezeInfoReq{ChannelId: aChannelId})
		Context("获取房间冻结信息", func() {
			It("获取房间冻结信息失败", func() {
				Expect(ferr).ShouldNot(HaveOccurred())
			})
			It("解冻房间失败", func() {
				Expect(channelFreezeResp.FreezeTime).Should(Equal(int64(0)))
			})
		})
	})

	pmResp, err := client.GetChannelPlayModel(ctx, &tcpb.GetChannelPlayModelReq{ChannelId: aChannelId})
	Context("获取房间玩法", func() {
		It("获取房间玩法接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
		It("获取房间玩法错误", func() {
			Expect(pmResp.TabId).ShouldNot(Equal(1))
		})
	})

	_, err = client.SetExtraHistory(ctx, &tcpb.SetExtraHistoryReq{Key: "client-test", Value: "1", ExpireAfter: 5})
	Context("设置历史信息", func() {
		It("设置历史信息接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
	})
	GetExtraHistoryResp, err := client.GetExtraHistory(ctx, &tcpb.GetExtraHistoryReq{Key: "client-test"})
	Context("获取历史信息", func() {
		It("获取历史信息接口失败", func() {
			Expect(err).ShouldNot(HaveOccurred())
		})
		It("获取历史信息不等于设置历史信息", func() {
			Expect(GetExtraHistoryResp.Value).To(Equal("1"))
		})
	})
})

func isDisplayChannel(displayType []tcpb.ChannelDisplayType) bool {
	for _, v := range displayType {
		if v == tcpb.ChannelDisplayType_DISPLAY_AT_MAIN_PAGE {
			//仅展示发布的
			return true
		}
	}
	return false
}
