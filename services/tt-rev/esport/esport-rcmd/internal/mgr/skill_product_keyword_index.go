package mgr

import (
    "context"
    "fmt"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/protocol/app/esport_logic"
    esport_skill "golang.52tt.com/protocol/services/esport-skill"
    "golang.52tt.com/protocol/services/esport_hall"
    comctx "golang.52tt.com/services/tt-rev/common/ctx"
    "golang.52tt.com/services/tt-rev/esport/common"
    "golang.52tt.com/services/tt-rev/esport/common/collection/transform"
    "golang.52tt.com/services/tt-rev/esport/esport-rcmd/internal/store"
    "hash/fnv"
    "strconv"
    "strings"
    "time"
)

func genKeywordKey(skillId, propertyType uint32, propertyName, tag string) string {
    propertyNameHash := hash2Uint32(propertyName)
    tagHash := hash2Uint32(tag)
    return fmt.Sprintf("%d_%d_%d_%d", skillId, propertyType, propertyNameHash, tagHash)
}

func hash2Uint32(str string) uint32 {
    h := fnv.New32a()
    h.Write([]byte(str))
    hash := h.Sum32()
    return hash
}

func (m *Mgr) UpdateSkillProductKeywordIndex(ctx context.Context) {
    st := time.Now()
    ctx, cancel := comctx.WithTimeout(5*time.Minute)
    defer cancel()
    pageSize := int64(1000)
    pageNum := int64(0)

    ver := strconv.Itoa(int(time.Now().Unix()))
    for {
        spList, err := m.store.GetEnableSkillProduct(ctx, pageNum, pageSize)
        if err != nil {
            log.ErrorWithCtx(ctx, "GetEnableSkillProduct error: %v", err)
            return
        }

        if len(spList) == 0 {
            break
        }

        err = m.updateKeywordIndex(ctx, ver, spList)
        if err != nil {
            log.ErrorWithCtx(ctx, "updateKeywordIndex error: %v", err)
            return
        }
        pageNum ++
    }

    err := m.cache.SetUpKeywordIndexVer(ctx, ver)
    if err != nil {
        log.ErrorWithCtx(ctx, "SetUpKeywordIndexVer error: %v", err)
        return
    }
    log.InfoWithCtx(ctx, "UpdateSkillProductKeywordIndex cost: %+v", time.Since(st))
}

func (m *Mgr) updateKeywordIndex(ctx context.Context, ver string, spList []*store.SkillProduct) error {
    keywordUidMap := make(map[string][]uint32)

    // 更新音色/特色标签uid映射
    coachUidList := transform.FlatMap(spList, func(item *store.SkillProduct) []uint32 {
        return []uint32{item.Uid}
    })
    specialLabelResp, err := m.rpcCli.ESportSkillCli.BatchGetUserSpecialLabel(ctx, &esport_skill.BatchGetUserSpecialLabelRequest{
        UidList: coachUidList,
    })
    if err != nil {
        log.ErrorWithCtx(ctx, "BatchGetUserSpecialLabel error: %v", err)
        return err
    }
    for k, v := range specialLabelResp.GetUserSpecialLabelMap() {
        for _, gameLabel := range v.GetGameList() {
            for _, item := range gameLabel.GetLabelList() {
                propertyName := "选择音色"
                if item.LabelType == esport_skill.SpecialLabelType_SPECIAL_LABEL_TYPE_SPECIAL {
                    propertyName = "游戏风格"
                }
                key := genKeywordKey(gameLabel.GetGameId(), uint32(esport_hall.GameProperty_PROPERTY_TYPE_LABEL), propertyName, item.GetLabelName())
                keywordUidMap[key] = append(keywordUidMap[key], k)
            }
        }
    }

    for _, item := range spList {
        // 记录知名选手uid映射
        if m.localCache.IsFamousPlayer(item.SkillId, item.Uid) {
            key := genKeywordKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_FAMUOS_PLAYER), "技术经验", "知名选手")
            keywordUidMap[key] = append(keywordUidMap[key], item.Uid)
        }

        // 记录性别uid映射
        gender := "女"
        if m.localCache.GetUserSex(item.Uid) == 1 {
            gender = "男"
        }
        key := genKeywordKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_GENDER), "性别", gender)
        keywordUidMap[key] = append(keywordUidMap[key], item.Uid)
        // 记录价格区间uid映射
        priceSearchList := m.bc.GetConfig().SkillProductSearchPrice[strconv.Itoa(int(item.SkillId))]
        for _, ps := range priceSearchList {
            minPrice, maxPrice := splitSearchPriceCfg(ps)
            itemPrice := m.localCache.GetCoachGamePrice(item.SkillId, item.Uid)
            if itemPrice >= minPrice && itemPrice <= maxPrice {
                key := genKeywordKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_PRICE), "价格", ps)
                keywordUidMap[key] = append(keywordUidMap[key], item.Uid)
                break
            }
        }

        // 开启首单
        if m.localCache.IsFirstRoundCoach(item.SkillId, item.Uid) {
            key := genKeywordKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_CUSTOM), common.FirstRoundPropertyName, common.FirstRoundPropertyNameVal)
            keywordUidMap[key] = append(keywordUidMap[key], item.Uid)
        }

        // 记录tag uid映射
        for _, prop := range item.SkillProperty {
            if common.IsGranteeWinSection(prop.GetSectionName()) && !item.GuaranteeWin { // 报应开关没打开, 不建立包赢搜索索引
                continue
            }

            for _, tag := range prop.GetItemList() {
                key := genKeywordKey(item.SkillId, uint32(esport_logic.GameProperty_PROPERTY_TYPE_CUSTOM), prop.GetSectionName(), tag)
                keywordUidMap[key] = append(keywordUidMap[key], item.Uid)
            }
        }
    }

    // 更新缓存
    cErr := m.cache.UpdateSkillProductKeywordIndex(ctx, ver, keywordUidMap)
    if cErr != nil {
        log.WarnWithCtx(ctx, "UpdateEachGameTopCoachList UpdateSkillProductKeywordIndex, err: %v", cErr)
    }
    return nil
}

func splitSearchPriceCfg(str string) (uint32, uint32) {
    str = strings.ReplaceAll(str, "豆", "")
    arr := strings.Split(str, "-")
    if len(arr) != 2 {
        return 0, 0
    }
    minPrice, _ := strconv.Atoi(arr[0])
    maxPrice, _ := strconv.Atoi(arr[1])
    return uint32(minPrice), uint32(maxPrice)
}

func (m *Mgr) SearchByKeyword(ctx context.Context, skillId uint32, gameProperty []*esport_hall.GameProperty) (map[uint32]bool, error) {
    if len(gameProperty) == 0 {
        return nil, nil
    }

    searchKey := make([]string, 0, 8)
    for _, prop := range gameProperty {
        for _, item := range prop.GetValList() {
            searchKey = append(searchKey, genKeywordKey(skillId, prop.GetPropertyType(), prop.GetName(), item.GetName()))
        }
    }

    keywordUidMap, err := m.cache.SearchKeywordUidMap(ctx, searchKey)
    if err != nil {
        log.ErrorWithCtx(ctx, "GetKeywordUid, skillId: %d, gameProperty: %+v, err: %v", skillId, gameProperty, err)
        return nil, err
    }
    log.InfoWithCtx(ctx, "SearchByKeyword, skillId: %d, gameProperty: %+v, keywordUidMap: %+v", skillId, gameProperty, keywordUidMap)

    usableUidMap := make(map[uint32]bool)
    for i, prop := range gameProperty {
        if len(prop.GetValList()) == 0 {
            continue
        }
        thisPropUsableUid := make([]uint32, 0, 8)
        for _, item := range prop.GetValList() {
            key := genKeywordKey(skillId, prop.GetPropertyType(), prop.GetName(), item.GetName())
            thisPropUsableUid = append(thisPropUsableUid, keywordUidMap[key]...) // 同类筛选并集
        }

        // 不同类筛选交集
        // 第一个筛选类全要
        if i == 0 {
            for _, uid := range thisPropUsableUid {
                usableUidMap[uid] = true
            }
            continue
        }
        thisPropUsableUidMap := make(map[uint32]bool)
        for _, uid := range thisPropUsableUid {
            thisPropUsableUidMap[uid] = true
        }

        for uid, usable := range usableUidMap {
            usableUidMap[uid] = usable && thisPropUsableUidMap[uid] // 一旦某个筛选类不包含此uid, 则标记为false
        }
    }

    return  usableUidMap, nil
}
