package main

import (
	"context"
	"fmt"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/clients/channel-live-mgr"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	liveMgrPb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-stats/cache"
	"golang.52tt.com/services/channel-live-stats/manager"
	"golang.52tt.com/services/channel-live-stats/mysql"
	"google.golang.org/grpc"
	"time"
)

func main() {
	UpdateAnchorActiveLiveCnt()
}

func ExportLiveAnchor() {
	ctx := context.Background()

	statsMysqlConf := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channel_live_stats",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	mysqlDb, err := gorm.Open("mysql", statsMysqlConf.ConnectionString())
	if err != nil {
		log.Errorf("gorm.Open failed err:%v", err)
		return
	}

	RedisConf := &config.RedisConfig{
		Host:         "************",
		Port:         6379,
		Protocol:     "tcp",
		PingInterval: 300,
		PoolSize:     1,
	}
	redisClient := RedisConf.NewDefualtGoRedisClient()

	cacheClient := cache.NewStatsCache(redisClient, nil)
	statsMysqlStore := mysql.NewMysql(mysqlDb, mysqlDb)

	liveMgrCli, err := channellivemgr.NewClient(grpc.WithBlock())
	if err != nil {
		log.Errorf("ExportLiveAnchor channellivemgr.NewClient failed err:%v", err)
		return
	}

	liveMgrResp, err := liveMgrCli.BatchGetAllChannelLive(ctx, &liveMgrPb.BatchGetAllChannelLiveReq{})
	if err != nil {
		log.Errorf("ExportLiveAnchor channellivemgr.NewClient failed err:%v", err)
		return
	}

	nowTm := time.Now()
	for _, liveChannel := range liveMgrResp.GetLiveChannelList() {
		if liveChannel.AnchorUid == 317511025 {
			continue
		}

		liveStatsResp, err := liveMgrCli.GetChannelLiveStatus(ctx, liveMgrPb.GetChannelLiveStatusReq{
			Uid:       liveChannel.GetAnchorUid(),
			ChannelId: liveChannel.GetChannelId(),
		})
		if err != nil {
			log.Errorf("ExportLiveAnchor GetChannelLiveStatus failed liveChannel:%+v err:%+v", liveChannel, err)
			continue
		}

		if liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetStatus() == liveMgrPb.EnumChannelLiveStatus_CLOSE {
			log.Errorf("ExportLiveAnchor live channel is close liveChannel:%+v liveStatsResp:%+v", liveChannel, liveStatsResp)
			continue
		}

		if liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime() <= uint32(nowTm.Unix()-7*24*3600) {
			log.Infof("ExportLiveAnchor invalid live channel liveChannel:%+v liveStatsResp:%+v now:%+v", liveChannel, liveStatsResp, nowTm)
			continue
		}

		anchorUid := liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetUid()
		endTime := nowTm
		beginTime := time.Unix(int64(liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime()), 0)

		if beginTime.IsZero() || beginTime.After(endTime) {
			log.Infof("ExportLiveAnchor invalid tm liveStatsResp:%+v, begin:%+v end:%+v", liveStatsResp, beginTime, endTime)
			continue
		}

		info, _, mysqlErr := statsMysqlStore.GetAnchorBaseInfo(ctx, anchorUid)
		if mysqlErr != nil {
			log.ErrorWithCtx(ctx, "ExportLiveAnchor GetAnchorBaseInfo failed  anchorUid:%d channelId:%d ts:%d err(%v)",
				anchorUid, liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelId(), liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime(), mysqlErr)
			continue
		}

		signGuildId := info.SignGuildId

		beginDate := mysql.GetDateTime(beginTime)
		endDate := mysql.GetDateTime(endTime)

		if !endDate.Equal(beginDate) { // 跨天了
			end := endTime
			begin := endDate
			i := 0
			for {
				log.Infof("ExportLiveAnchor kuatian anchorUid:%d incr:%d begin:%v end:%v", anchorUid, uint32(end.Sub(begin).Minutes()), begin)
				// 增加每天直播时长
				statsMysqlStore.IncrAnchorDailyLiveTime(ctx, nil, anchorUid, signGuildId, uint32(end.Sub(begin).Minutes()), 0, begin)
				statsMysqlStore.IncrAnchorMonthlyLiveTime(ctx, nil, anchorUid, signGuildId, uint32(end.Sub(begin).Minutes()), 0, mysql.GetYearMonth(begin))

				_, err := cacheClient.IncrLiveTsById(int64(liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelLiveId()), int64(end.Sub(begin).Minutes()))
				if err != nil {
					log.ErrorWithCtx(ctx, "ExportLiveAnchor failed to IncrLiveTsById anchorUid:%d channelId:%d ts:%d err(%v)",
						anchorUid, liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelId(), liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime(), err)
				}

				if end.Sub(begin) >= time.Hour {
					// 记录有效天，,增加有效时长
					UpdateDailyRecord(ctx, statsMysqlStore, anchorUid, uint32(end.Sub(begin).Minutes()), signGuildId, begin)
				}

				end = begin
				begin = begin.AddDate(0, 0, -1)

				if begin.Before(beginTime) {
					begin = beginTime
				}

				if i > 3 || !end.After(begin) {
					break
				}

				i++
			}
		} else {
			log.Infof("ExportLiveAnchor no kua kuatian anchorUid:%d incr:%d begin:%v end:%v", anchorUid, uint32(endTime.Sub(beginTime).Minutes()), endTime)
			// 增加每天直播时长
			statsMysqlStore.IncrAnchorDailyLiveTime(ctx, nil, anchorUid, signGuildId, uint32(endTime.Sub(beginTime).Minutes()), 0, endTime)
			statsMysqlStore.IncrAnchorMonthlyLiveTime(ctx, nil, anchorUid, signGuildId, uint32(endTime.Sub(beginTime).Minutes()), 0, mysql.GetYearMonth(endTime))

			_, err := cacheClient.IncrLiveTsById(int64(liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelLiveId()), int64(endTime.Sub(beginTime).Minutes()))
			if err != nil {
				log.ErrorWithCtx(ctx, "ExportLiveAnchor failed to IncrLiveTsById anchorUid:%d channelId:%d ts:%d err(%v)",
					anchorUid, liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelId(), liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime(), err)
			}

			if endTime.Sub(beginTime) >= time.Hour {
				// 记录有效天,增加有效时长
				UpdateDailyRecord(ctx, statsMysqlStore, anchorUid, uint32(endTime.Sub(beginTime).Minutes()), signGuildId, endTime)
			}

		}

		cacheErr := cacheClient.SetAnchorLiveInfo(anchorUid, &cache.LiveInfo{
			AnchorUid:     anchorUid,
			ChannelLiveId: int64(liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelLiveId()),
			LiveTs:        int64(liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime()),
			ChannelId:     liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelId(),
		})
		if cacheErr != nil {
			log.ErrorWithCtx(ctx, "ExportLiveAnchor SetAnchorLiveInfo failed anchorUid:%d channelId:%d ts:%d err(%v)",
				anchorUid, liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelId(), liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime(), cacheErr)
			continue
		}

		// 加到开播房间列表
		cacheErr = cacheClient.AddRunningChannel(anchorUid, anchorUid%manager.RunningChPoorCnt, nowTm.Unix())
		if cacheErr != nil {
			log.ErrorWithCtx(ctx, "ExportLiveAnchor AddRunningChannel failed anchorUid:%d channelId:%d ts:%d err(%v)",
				anchorUid, liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetChannelId(), liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetBeginTime(), cacheErr)
			continue
		}

		log.Infof("ExportLiveAnchor success liveChannel:%v", liveChannel)
	}

}

func UpdateDailyRecord(ctx context.Context, statsMysqlStore *mysql.Store, anchorUid, incrValidMinutes, signGuildId uint32, t time.Time) {
	log.Infof("UpdateDailyRecord  anchorUid:%d incr:%d begin:%v", anchorUid, incrValidMinutes, t)
	// 日记录
	hasChange, err := statsMysqlStore.UpdateAnchorDailyRecordValidDay(ctx, nil, anchorUid, signGuildId, 1, t)
	if err != nil {
		log.Errorf("UpdateDailyRecord UpdateAnchorDailyRecordValidDay %d %d t:%v", anchorUid, incrValidMinutes, t)
		return
	}

	_ = statsMysqlStore.IncrAnchorDailyValidTime(ctx, nil, anchorUid, signGuildId, incrValidMinutes, 0, t)
	_ = statsMysqlStore.IncrAnchorMonthlyValidTime(ctx, nil, anchorUid, signGuildId, incrValidMinutes, 0, mysql.GetYearMonth(t))

	if !hasChange {
		// 没有变化(即已经记录过有效天了)，返回
		return
	}

	// 月记录
	err = statsMysqlStore.IncrAnchorMonthlyRecordValidDay(ctx, nil, anchorUid, signGuildId, 1, mysql.GetYearMonth(t))
	if err != nil {
		log.Errorf("UpdateDailyRecord IncrAnchorMonthlyRecordValidDay %d %d t:%v", anchorUid, incrValidMinutes, t)
		return
	}

	fmt.Printf("uid:%v, signGuildId:%v, incrValidMinutes:%v t:%v\n", anchorUid, signGuildId, incrValidMinutes, t)
	return
}

func UpdateAnchorActiveLiveCnt() {
	statsMysqlConf := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channel_live_stats",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	mysqlDb, err := gorm.Open("mysql", statsMysqlConf.ConnectionString())
	if err != nil {
		log.Errorf("gorm.Open failed err:%v", err)
		return
	}

	RedisConf := &config.RedisConfig{
		Host:         "************",
		Port:         6379,
		Protocol:     "tcp",
		PingInterval: 300,
		PoolSize:     1,
	}
	redisClient := RedisConf.NewDefualtGoRedisClient()

	cacheClient := cache.NewStatsCache(redisClient, nil)
	statsMysqlStore := mysql.NewMysql(mysqlDb, mysqlDb)

	nowTm := time.Unix(1685606790, 0)
	date := mysql.GetDateTime(nowTm)
	ctx := context.Background()

	var offset, limit uint32 = 0, 500
	out := make([]*mysql.AnchorDailyLiveRecord, 0)
	for {
		tmpOut := make([]*mysql.AnchorDailyLiveRecord, 0)
		err := mysqlDb.Table(mysql.TblAnchorDailyRecord).Select("uid, date, anchor_income, channel_fee, live_valid_minutes, day_live_valid, day_active_fans,"+
			"day_sp_fans, fans_send_fee, day_follow_cnt, new_fans_cnt, live_minutes, agent_uid, knight_income").
			Where("date = ?", date).Offset(offset).Limit(limit).Scan(&tmpOut).Error
		if err != nil {
			log.Errorf("UpdateAnchorActiveLiveCnt select failed offset:%d limit:%d err:%v", offset, limit, err)
			return
		}

		out = append(out, tmpOut...)

		if len(tmpOut) < int(limit) {
			break
		}

		offset = offset + limit
	}

	uidList := []uint32{107440981, 285277557, 310965667, 42529689, 291148527, 300720141, 317560621, 214827338, 54155291, 306594066, 284826398, 78737543, 279415102, 291516137,
		214197145, 249109888, 311975031, 121164156, 315692504, 310867087, 315674090, 306012185, 318455454, 85056738, 312023477, 316874556, 178299149, 291939033, 312956375,
		182578704, 212287172, 259498762, 316394392, 310801391, 253420207, 276308593, 296222252, 308677456, 257483207, 309273900, 208886985, 231488373, 298721286, 244516933,
		313936731, 318425486, 266288057, 309426193, 315332374, 193682212, 284219716, 223499458, 279172414, 656865, 209155387, 284747125, 305567894, 124930916, 12040000, 316938295,
		255608676, 159970397, 289419259, 317897661, 300202747, 317498021, 318408782, 152349377, 295525871, 81592266, 313602866, 317552256, 310265319, 297332963, 202328551,
		203649747, 261789763, 150035843, 238989140, 283103929, 109250, 268622587, 255216749, 306773950, 202203605, 271966752, 86290785, 737627, 316417819, 153364717, 252373555,
		314206368, 286131733, 314579677, 306988246, 215073587, 314498876, 720559, 309093733, 275579806, 310352954, 255371982, 316049702, 216646076, 316427918, 308470085,
		317451046, 318083842, 152222123, 313842630, 315921201, 243016872, 218543042, 315399776, 316565295, 164608785, 313499947, 104481025, 218658145, 317473916, 285574176,
		126623626, 287537451, 316479275, 310965667, 278568631, 284582113, 105362964, 316264516, 317184772, 311451358, 157918356, 83821, 314003987, 207371410, 313658513,
		221313414, 314216105, 297984481, 294707022, 294707022, 272315267, 105351231, 281780807, 208005350, 316988893, 67827721, 316433311, 313452976, 260434349, 261953646,
		279590924, 255721789, 313365369, 305523640, 299815728, 258570305, 317444192, 164426615, 306713215, 281864129, 46192011, 204603216, 125617, 316242440, 318666826,
		256761481, 306981059, 178969861, 178969861, 276499496, 311402502, 279008795, 300700238, 162498545, 264261491, 313579263, 207954451, 310684899, 240557233, 296811822,
		216528520, 152780117, 310170004, 292025008, 289221031, 311232182, 317604324, 773416, 48962516, 296160279, 99791715, 288649037, 299096980, 313797909, 318286190,
		311048713, 317408400, 305965936, 257506037, 308668405, 312698317, 75149350, 313037670, 260700410, 265904225, 277867621, 309087656, 267802429}

	mapUid2IsProc := make(map[uint32]bool, 0)
	for _, uid := range uidList {
		mapUid2IsProc[uid] = true
	}

	mapUid2Min := make(map[uint32]uint32, 0)
	for _, info := range out {
		mapUid2Min[info.Uid] += info.LiveValidMinutes
	}

	for uid, min := range mapUid2Min {
		if !mapUid2IsProc[uid] {
			continue
		}

		if min >= mysql.LiveActiveAnchorMinutes {
			info, _, mysqlErr := statsMysqlStore.GetAnchorBaseInfo(ctx, uid)
			if mysqlErr != nil {
				log.ErrorWithCtx(ctx, "UpdateAnchorActiveLiveCnt failed to GetAnchorBaseInfo anchorUid:%d  err(%v)", uid, mysqlErr)
				continue
			}

			newLiveValidMin, err := cacheClient.IncrValidLiveTsByAnchor(uid, int64(min), nowTm)
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateAnchorActiveLiveCnt failed to IncrValidLiveTsByAnchor anchorUid:%d min:%d err(%v)", uid, min, err)
				continue
			}

			if newLiveValidMin-int64(min) >= mysql.LiveActiveAnchorMinutes {
				log.Infof("UpdateAnchorActiveLiveCnt no need proc  anchorUid:%d min:%d", uid, min)
				continue
			}

			sql := fmt.Sprintf("UPDATE %s set live_active_cnt =live_active_cnt+1"+
				" where sign_guild_id = ? and yearmonth = ? and uid = ?", mysql.TblAnchorMonthlyRecord)
			err = mysqlDb.Exec(sql, info.SignGuildId, mysql.GetYearMonth(nowTm), uid).Error
			if err != nil {
				log.ErrorWithCtx(ctx, "UpdateAnchorActiveLiveCnt failed uid:%d err:%v",
					uid, err)
				continue
			}

			log.Infof("UpdateAnchorActiveLiveCnt success uid:%d min:%d", uid, min)
		}
	}

}

func getAllRunningCh(statsCache *cache.StatsCache, index uint32) ([]uint32, map[uint32]int64, error) {
	var offset int64
	var limit int64 = 200
	uidList := make([]uint32, 0)
	mapUid2Ts := make(map[uint32]int64, 0)
	for {
		tmpMapUid2Ts, cnt, err := statsCache.GetRunningChannelList(offset, offset+limit-1, index)
		if err != nil {
			log.Errorf("getAllRunningCh GetRunningChannelList failed %d %d %d err:%v", offset, limit, index, err)
			return nil, nil, err
		}

		for k, v := range tmpMapUid2Ts {
			uidList = append(uidList, k)
			mapUid2Ts[k] = v
		}

		if cnt < uint32(limit) {
			break
		}

		offset += limit
	}

	log.Debugf("getAllRunningCh end cidList:%v mapCid2Ts:%v", uidList, mapUid2Ts)
	return uidList, mapUid2Ts, nil
}

func ClearInvalidAnchor() {
	ctx := context.Background()

	RedisConf := &config.RedisConfig{
		Host:         "************",
		Port:         6379,
		Protocol:     "tcp",
		PingInterval: 300,
		PoolSize:     1,
	}
	redisClient := RedisConf.NewDefualtGoRedisClient()
	cacheClient := cache.NewStatsCache(redisClient, nil)

	liveMgrCli, err := channellivemgr.NewClient(grpc.WithBlock())
	if err != nil {
		log.Errorf("ClearInvalidAnchor channellivemgr.NewClient failed err:%v", err)
		return
	}

	for i := 0; i < manager.RunningChPoorCnt; i++ {
		uidList, _, err := getAllRunningCh(cacheClient, uint32(i))
		if err != nil {
			log.ErrorWithCtx(ctx, "ClearInvalidAnchor getAllRunningCh failed %d err:%v", i, err)
			return
		}

		for _, uid := range uidList {
			liveStatsResp, err := liveMgrCli.GetChannelLiveStatus(ctx, liveMgrPb.GetChannelLiveStatusReq{
				Uid: uid,
			})
			if err != nil {
				log.Errorf("ClearInvalidAnchor GetChannelLiveStatus failed uid:%+v err:%+v", uid, err)
				continue
			}

			if liveStatsResp.GetChannelLiveInfo().GetChannelLiveStatus().GetStatus() == liveMgrPb.EnumChannelLiveStatus_CLOSE {
				log.Infof("ClearInvalidAnchor live channel is close uid:%+v liveStatsResp:%+v", uid, liveStatsResp)
				err := cacheClient.DelRunningChannel(uid, uid%manager.RunningChPoorCnt)
				if err != nil {
					log.Errorf("ClearInvalidAnchor DelRunningChannel failed uid:%+v err:%+v", uid, err)
				}
			}
		}
	}

	log.Infof("ClearInvalidAnchor end ")
}

func FixAnchorLiveTs() {
	statsMysqlConf := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channel_live_stats",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	mysqlDb, err := gorm.Open("mysql", statsMysqlConf.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}
	statsMysqlStore := mysql.NewMysql(mysqlDb, mysqlDb)

	liveMysqlConf := &config.MysqlConfig{
		Host:         "*************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channellive",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}
	liveDb, err := gorm.Open("mysql", liveMysqlConf.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	type Tmp struct {
		Uid   uint32
		Begin uint32
		End   uint32
	}

	ctx := context.Background()

	dateEnd := time.Unix(1685127600, 0)
	dateBegin := time.Unix(1685030400, 0)

	uidList := []uint32{269278139, 276708101, 193765419, 279855705, 316753364, 21203936, 300466848, 281195658, 316718707, 103183686, 101627371, 302962137, 318283459,
		172814602, 230967494, 316115743, 301883504, 315079903, 278951792, 162145716, 313963918, 307941698, 303145328, 308080404, 314693541, 207954451, 177745395, 11407046,
		310765006, 173100732}

	mapUid2ValidTs := make(map[string]uint32, 0)
	for _, uid := range uidList {
		list := make([]*Tmp, 0)
		err = liveDb.Table("tblChannelLiveRecord").Select("uid,unix_timestamp(begin_time) as begin,unix_timestamp(end_time) as end").
			Where("uid = ? and end_time >= ? and end_time < ? and (unix_timestamp(end_time)-unix_timestamp(begin_time)) >= 3600", uid, dateBegin, dateEnd).
			Scan(&list).Error
		if err != nil {
			log.Errorf("FixAnchorLiveTs Select failed uid:%d err:%v", uid, err)
			return
		}

		log.Infof("FixAnchorLiveTs list:%+v", list)

		for _, m := range list {
			anchorUid := m.Uid
			endTime := time.Unix(int64(m.End), 0)
			beginTime := time.Unix(int64(m.Begin), 0)

			if beginTime.IsZero() || beginTime.After(endTime) {
				continue
			}

			beginDate := mysql.GetDateTime(beginTime)
			endDate := mysql.GetDateTime(endTime)

			if !endDate.Equal(beginDate) { // 跨天了
				end := endTime
				begin := endDate
				i := 0
				for {
					if end.Sub(begin) >= time.Hour {
						// 记录有效天，,增加有效时长
						mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", anchorUid, begin.Year(), begin.Month(), begin.Day())] += uint32(end.Sub(begin).Minutes())
					}

					end = begin
					begin = begin.AddDate(0, 0, -1)

					if begin.Before(beginTime) {
						begin = beginTime
					}

					if i > 3 || !end.After(begin) {
						break
					}

					i++
				}
			} else if endTime.Sub(beginTime) >= time.Hour {
				// 记录有效天,增加有效时长
				mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", anchorUid, endTime.Year(), endTime.Month(), endTime.Day())] += uint32(endTime.Sub(beginTime).Minutes())
			}
		}
	}

	for _, uid := range uidList {
		date := mysql.GetDateTime(dateBegin)
		monthDate := mysql.GetYearMonth(dateBegin)

		tmpOut := make([]*mysql.AnchorDailyLiveRecord, 0)
		err := mysqlDb.Table(mysql.TblAnchorDailyRecord).Select("uid, date, anchor_income, channel_fee, live_valid_minutes, day_live_valid, day_active_fans,"+
			"day_sp_fans, fans_send_fee, day_follow_cnt, new_fans_cnt, live_minutes, agent_uid, knight_income").
			Where("date = ? AND uid = ?", date, uid).Scan(&tmpOut).Error
		if err != nil {
			log.Errorf("FixAnchorLiveTs Select failed uid:%d err:%v", uid, err)
			return
		}

		var validTs uint32
		for _, record := range tmpOut {
			validTs += record.LiveValidMinutes
		}

		info, _, err := statsMysqlStore.GetAnchorBaseInfo(ctx, uid)
		if err != nil {
			fmt.Printf("err:%v", err)
			return
		}

		if mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())] < 60 &&
			validTs >= 60 {
			log.Infof("FixAnchorLiveTs need fix valid ts uid:%d ts1:%d ts2:%d",
				uid, mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())], validTs)

			query := fmt.Sprintf("UPDATE %s set live_valid_minutes = ?, day_live_valid = 0 "+
				"where uid = ? and sign_guild_id = ? and date = ?", mysql.TblAnchorDailyRecord)

			err = mysqlDb.Exec(query, mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())],
				uid, info.SignGuildId, date).Error
			if err != nil {
				log.Errorf("FixAnchorLiveTs mysqlDb.Exec uid:%d err:%v", uid, err)
				return
			}

			query2 := fmt.Sprintf("UPDATE %s set day_live_valid_cnt = if(day_live_valid_cnt>=1,day_live_valid_cnt-1, day_live_valid_cnt),"+
				"live_valid_minutes=if(live_valid_minutes>=?, live_valid_minutes-?, live_valid_minutes) "+
				"where uid = ? and sign_guild_id = ? and yearmonth = ?", mysql.TblAnchorMonthlyRecord)

			err = mysqlDb.Exec(query2, mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())],
				validTs-mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())],
				uid, info.SignGuildId, monthDate).Error
			if err != nil {
				log.Errorf("FixAnchorLiveTs mysqlDb.Exec uid:%d err:%v", uid, err)
				return
			}
		}

		if mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())] < 120 &&
			validTs >= 120 {
			log.Infof("FixAnchorLiveTs need fix active ts uid:%d ts1:%d ts2:%d",
				uid, mapUid2ValidTs[fmt.Sprintf("%d_%04d%02d%02d", uid, dateBegin.Year(), dateBegin.Month(), dateBegin.Day())], validTs)

			query2 := fmt.Sprintf("UPDATE %s set live_active_cnt = if(live_active_cnt>=1,live_active_cnt-1, live_active_cnt) "+
				"where uid = ? and sign_guild_id = ? and yearmonth = ?", mysql.TblAnchorMonthlyRecord)

			err = mysqlDb.Exec(query2, uid, info.SignGuildId, monthDate).Error
			if err != nil {
				log.Errorf("FixAnchorLiveTs mysqlDb.Exec uid:%d err:%v", uid, err)
				return
			}
		}
	}

}

func ReleaseLock() {
	RedisConf := &config.RedisConfig{
		Host:         "************",
		Port:         6379,
		Protocol:     "tcp",
		PingInterval: 300,
		PoolSize:     1,
	}
	redisClient := RedisConf.NewDefualtGoRedisClient()
	cacheClient := cache.NewStatsCache(redisClient, nil)

	for i := 0; i < manager.RunningChPoorCnt; i++ {
		lockName := fmt.Sprintf("%s_%d", manager.LiveTsProcLock, i)
		err := cacheClient.ReleaseLock(lockName)
		if err != nil {
			log.Errorf("failed to ReleaseLock, lockName:%s, err:%v", lockName, err)
		}
	}

}
