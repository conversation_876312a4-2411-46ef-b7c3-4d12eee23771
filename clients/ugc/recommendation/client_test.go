/**
 * Author: Orange
 * Date: 18-12-10
 */

package recommendation

import (
	"context"
	"testing"

	"gitlab.ttyuyin.com/golang/gudetama/log"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/ugc/recommendation"
)

var (
	cli *Client
)

func init() {
	var err error
	cli, err = NewClient(grpc.WithBlock())
	if err != nil {
		log.Fatalln(err)
	}
}

func TestClient_AddPostsInto(t *testing.T) {
	resp, err := cli.AddPostsInto(context.Background(), &pb.AddPostsIntoReq{
		PostInfoList: []*pb.RecommendationPostInfo{
			{PostId: "5c0521adef57053fb3a6af13", TopicId: ""},
		},
	})

	if err != nil {
		t.Fatal(err)
	}

	t.Log(resp)
}

func TestClient_AddUserReadHistory(t *testing.T) {
	resp, err := cli.AddUserReadHistory(context.Background(), &pb.AddUserReadHistoryReq{
		UserId:     1966030,
		PostIdList: []string{"5c0521adef57053fb3a6af13"},
	})

	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_GetPostsWithFilter(t *testing.T) {
	resp, err := cli.GetPostsWithFilter(context.Background(), &pb.GetPostsWithFilterReq{
		UserId: 1966030,
		Filter: &pb.Filter{Method: pb.Filter_TIME},
		Count:  100,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_RemovePosts(t *testing.T) {
	resp, err := cli.RemovePosts(context.Background(),
		"5c0521adef57053fb3a6af13",
	)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestClient_GetPostsByTime(t *testing.T) {
	resp, err := cli.GetPostsWithFilter(context.Background(), &pb.GetPostsWithFilterReq{
		UserId: 1966033,
		Filter: &pb.Filter{Method: pb.Filter_TIME},
		Count:  100,
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Log(resp)
}
