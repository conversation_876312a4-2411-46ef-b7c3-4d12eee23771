package mongo

//
//import (
//	"context"
//	"fmt"
//	"golang.52tt.com/protocol/services/molebeat"
//	"golang.52tt.com/services/molebeat/conf"
//	"testing"
//)
//
//var mFailDao *MongoDao
//
//func init() {
//	sc := &conf.ServiceConfigT{}
//	err := sc.Parse("../molebeat.json")
//	if err != nil {
//		return
//	}
//	m, err := NewMongoDao(sc.GetLotteryRecordMongoConfig())
//
//	if err != nil {
//		fmt.Println(err)
//	}
//	err = m.CreateLotteryRecordIndexes()
//
//	if err != nil {
//		fmt.Println(err)
//	}
//
//	mDao = m
//}
//
//func TestFail(t *testing.T) {
//	ctx := context.Background()
//	mDao.ReloadMoleLotteryInfo(ctx, 0, 0)
//	lMap := mDao.GetMoleLotteryCache()
//	for _, l := range lMap {
//		item := l.Find(1000)
//		err := mDao.LotteryRecod(ctx, item.PoolInfo, 1, 1, 1)
//		fmt.Println(err)
//		break
//	}
//	award := &molebeat.LotteryAwardAsync{
//		Uid:                      1,
//		LotteryType:              2,
//		SourceId:                 3,
//		SourceKey:                "4",
//		EffectSecond:             5,
//		LotteryItemPrice:         6,
//		LotteryItemRealPriceSort: 7,
//		ChannelId:                8,
//		Gameid:                   9,
//		Name:                     "10",
//		CntType:                  11,
//		AwardKey:                 "12",
//	}
//	err := mDao.LotteryRecodFail(ctx, award.Gameid, award.Uid, award.ChannelId, "test", award)
//	fmt.Println("LotteryRecodFail", err)
//	records, err := mDao.GetLotteryFailRecod(ctx)
//	fmt.Println("GetLotteryFailRecod", records, err)
//	err = mDao.LotteryRecodFailFix(ctx, award.Gameid, award.Uid, award.ChannelId, award.CntType, award.AwardKey)
//	fmt.Println("LotteryRecodFailFix", err)
//
//}
