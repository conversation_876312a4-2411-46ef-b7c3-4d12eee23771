package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/services/demo/echo"
	"golang.52tt.com/services/tt-rev/channel-dating-game-async/internal/conf"

	pb "golang.52tt.com/protocol/services/channel-dating-game-async"

	"golang.52tt.com/services/tt-rev/channel-dating-game-async/internal/server"

	// use server startup
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {
	var (
		svr *server.Server
		cfg = &conf.StartConfig{}
		err error
	)

	// config file support yaml & json, default channel-dating-game.json/yaml
	if err := startup.New("channel-dating-game", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				kafka.InitEventLinkSubWithGrpcSvr(s)
				if svr, err = server.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterChannelDatingGameAsyncServer(s, svr)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
