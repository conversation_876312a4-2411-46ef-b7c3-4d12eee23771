// Code generated by protoc-gen-go. DO NOT EDIT.
// source: silent-user-audit/silent-user-audit.proto

package silent_user_audit // import "golang.52tt.com/protocol/services/silent-user-audit"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetAuditRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAuditRecordReq) Reset()         { *m = GetAuditRecordReq{} }
func (m *GetAuditRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetAuditRecordReq) ProtoMessage()    {}
func (*GetAuditRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_silent_user_audit_45f8d2c58f6249fd, []int{0}
}
func (m *GetAuditRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuditRecordReq.Unmarshal(m, b)
}
func (m *GetAuditRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuditRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetAuditRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuditRecordReq.Merge(dst, src)
}
func (m *GetAuditRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetAuditRecordReq.Size(m)
}
func (m *GetAuditRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuditRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuditRecordReq proto.InternalMessageInfo

func (m *GetAuditRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAuditRecordResp struct {
	Time                 uint32   `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	Result               uint32   `protobuf:"varint,2,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAuditRecordResp) Reset()         { *m = GetAuditRecordResp{} }
func (m *GetAuditRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetAuditRecordResp) ProtoMessage()    {}
func (*GetAuditRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_silent_user_audit_45f8d2c58f6249fd, []int{1}
}
func (m *GetAuditRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuditRecordResp.Unmarshal(m, b)
}
func (m *GetAuditRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuditRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetAuditRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuditRecordResp.Merge(dst, src)
}
func (m *GetAuditRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetAuditRecordResp.Size(m)
}
func (m *GetAuditRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuditRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuditRecordResp proto.InternalMessageInfo

func (m *GetAuditRecordResp) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *GetAuditRecordResp) GetResult() uint32 {
	if m != nil {
		return m.Result
	}
	return 0
}

func init() {
	proto.RegisterType((*GetAuditRecordReq)(nil), "silent_user_audit.GetAuditRecordReq")
	proto.RegisterType((*GetAuditRecordResp)(nil), "silent_user_audit.GetAuditRecordResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SilentUserAuditClient is the client API for SilentUserAudit service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SilentUserAuditClient interface {
	GetAuditRecord(ctx context.Context, in *GetAuditRecordReq, opts ...grpc.CallOption) (*GetAuditRecordResp, error)
}

type silentUserAuditClient struct {
	cc *grpc.ClientConn
}

func NewSilentUserAuditClient(cc *grpc.ClientConn) SilentUserAuditClient {
	return &silentUserAuditClient{cc}
}

func (c *silentUserAuditClient) GetAuditRecord(ctx context.Context, in *GetAuditRecordReq, opts ...grpc.CallOption) (*GetAuditRecordResp, error) {
	out := new(GetAuditRecordResp)
	err := c.cc.Invoke(ctx, "/silent_user_audit.SilentUserAudit/GetAuditRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SilentUserAuditServer is the server API for SilentUserAudit service.
type SilentUserAuditServer interface {
	GetAuditRecord(context.Context, *GetAuditRecordReq) (*GetAuditRecordResp, error)
}

func RegisterSilentUserAuditServer(s *grpc.Server, srv SilentUserAuditServer) {
	s.RegisterService(&_SilentUserAudit_serviceDesc, srv)
}

func _SilentUserAudit_GetAuditRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuditRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SilentUserAuditServer).GetAuditRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/silent_user_audit.SilentUserAudit/GetAuditRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SilentUserAuditServer).GetAuditRecord(ctx, req.(*GetAuditRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SilentUserAudit_serviceDesc = grpc.ServiceDesc{
	ServiceName: "silent_user_audit.SilentUserAudit",
	HandlerType: (*SilentUserAuditServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAuditRecord",
			Handler:    _SilentUserAudit_GetAuditRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "silent-user-audit/silent-user-audit.proto",
}

func init() {
	proto.RegisterFile("silent-user-audit/silent-user-audit.proto", fileDescriptor_silent_user_audit_45f8d2c58f6249fd)
}

var fileDescriptor_silent_user_audit_45f8d2c58f6249fd = []byte{
	// 210 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0x2c, 0xce, 0xcc, 0x49,
	0xcd, 0x2b, 0xd1, 0x2d, 0x2d, 0x4e, 0x2d, 0xd2, 0x4d, 0x2c, 0x4d, 0xc9, 0x2c, 0xd1, 0xc7, 0x10,
	0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x12, 0x84, 0x48, 0xc4, 0x83, 0x24, 0xe2, 0xc1, 0x12,
	0x4a, 0xaa, 0x5c, 0x82, 0xee, 0xa9, 0x25, 0x8e, 0x20, 0x76, 0x50, 0x6a, 0x72, 0x7e, 0x51, 0x4a,
	0x50, 0x6a, 0xa1, 0x90, 0x00, 0x17, 0x73, 0x69, 0x66, 0x8a, 0x04, 0xa3, 0x02, 0xa3, 0x06, 0x6f,
	0x10, 0x88, 0xa9, 0xe4, 0xc0, 0x25, 0x84, 0xae, 0xac, 0xb8, 0x40, 0x48, 0x88, 0x8b, 0xa5, 0x24,
	0x33, 0x37, 0x15, 0xaa, 0x10, 0xcc, 0x16, 0x12, 0xe3, 0x62, 0x2b, 0x4a, 0x2d, 0x2e, 0xcd, 0x29,
	0x91, 0x60, 0x02, 0x8b, 0x42, 0x79, 0x46, 0x45, 0x5c, 0xfc, 0xc1, 0x60, 0xdb, 0x43, 0x8b, 0x53,
	0x8b, 0xc0, 0x06, 0x09, 0xc5, 0x73, 0xf1, 0xa1, 0x1a, 0x2a, 0xa4, 0xa2, 0x87, 0xe1, 0x42, 0x3d,
	0x0c, 0xe7, 0x49, 0xa9, 0x12, 0xa1, 0xaa, 0xb8, 0x40, 0x89, 0xc1, 0xc9, 0x34, 0xca, 0x38, 0x3d,
	0x3f, 0x27, 0x31, 0x2f, 0x5d, 0xcf, 0xd4, 0xa8, 0xa4, 0x44, 0x2f, 0x39, 0x3f, 0x57, 0x1f, 0x1c,
	0x10, 0xc9, 0xf9, 0x39, 0xfa, 0xc5, 0xa9, 0x45, 0x65, 0x99, 0xc9, 0xa9, 0xc5, 0x98, 0x81, 0x95,
	0xc4, 0x06, 0x56, 0x64, 0x0c, 0x08, 0x00, 0x00, 0xff, 0xff, 0xbe, 0xab, 0x39, 0x49, 0x5a, 0x01,
	0x00, 0x00,
}
