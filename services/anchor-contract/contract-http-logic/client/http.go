package client

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"golang.52tt.com/pkg/log"
)

func GetUserLoginCnt(ctx context.Context, uid uint32) (uint32, error) {
	log.DebugWithCtx(ctx, "GetUserLoginCnt begin uid:%d", uid)

	type ReqData struct{
		AppId  string `json:"appId"`
		EntityId string `json:"entityId"`
		EntityDomain string `json:"entityDomain"`
		LabelNames []string  `json:"labelNames"`
	}

	type ReqClient struct{
		Caller  string `json:"caller"`
	}

	postReq := &struct {
		Id     int64   `json:"id"`
		Data   ReqData `json:"data"`
		Client ReqClient `json:"client"`
		Sign   string `json:"sign"`
	}{}

	httpConf := TTRevenueDyCfg.Get().LabelHttpConf

	postReq.Id = time.Now().Unix()
	postReq.Data.AppId = httpConf.AppId
	postReq.Data.EntityDomain = httpConf.EntityDomain
	postReq.Data.EntityId = fmt.Sprintf("%d", uid)
	postReq.Data.LabelNames = []string{"tt_user_login_base_dcnt_365d"}
	postReq.Client.Caller = httpConf.Caller
	postReq.Sign = httpConf.Sign

	httpCli := &http.Client{
		Timeout: time.Duration(httpConf.TimeOutMs) * time.Millisecond,
	}

	postBodeByte, err := json.Marshal(postReq)
	if err != nil {
		log.Errorf("GetUserLoginCnt json.Marshal failed req:%v err:%v", postReq, err)
		return 0, err
	}

	postResp, err := httpCli.Post(httpConf.Url, "application/json", bytes.NewReader(postBodeByte))
	if err != nil {
		log.Errorf("GetUserLoginCnt httpCli.Post failed req:%v httpConf:%v err:%v", postReq, httpConf, err)
		return 0, err
	}

	if postResp.StatusCode != http.StatusOK {
		log.Errorf("GetUserLoginCnt http code no 200 req:%v byte:%v code:%d", postReq, string(postBodeByte), postResp.StatusCode)
		return 0,  protocol.NewExactServerError(nil, status.ErrExternalSystemFailed)
	}

	defer postResp.Body.Close()
	respBody, err := ioutil.ReadAll(postResp.Body)
	if err != nil {
		log.Errorf("GetUserLoginCnt ioutil.ReadAll failed req:%v err:%v", postReq, err)
		return 0, err
	}

	type RespData struct {
		EntityId string `json:"entityId"`
		LoginCntTag string `json:"tt_user_login_base_dcnt_365d"`
	}

	respResult := &struct {
		Id int64 `json:"id"`
		Status  int `json:"status"`
		Message string `json:"message"`
		Data RespData `json:"data"`
	}{}

	err = json.Unmarshal(respBody, respResult)
	if err != nil {
		log.Errorf("GetUserLoginCnt json.Unmarshal(respBody, respResult) failed req:%v respBody:%v err:%+v", postReq, respBody, err)
		return 0, err
	}

	if respResult.Status != 200 {
		log.Errorf("GetUserLoginCnt failed req:%v result:%v", postReq, respResult)
		return 0,  errors.New(respResult.Message)
	}

	cnt, err := strconv.Atoi(respResult.Data.LoginCntTag)
	if err != nil {
		log.Errorf("GetUserLoginCnt  strconv.Atoi failed req:%v respResult:%v", postReq, respResult)
		return 0, err
	}

	log.Debugf("GetUserLoginCnt end uid:%d result:%v cnt:%d", uid, respResult, cnt)
	return uint32(cnt), nil
}

