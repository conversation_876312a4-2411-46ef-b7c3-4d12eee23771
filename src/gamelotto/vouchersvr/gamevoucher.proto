syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package gamevoucher;

// 业务类别, 用于区分进行统计
enum VoucherBizId {
    LoginLotto = 1;             // 登录抽奖
    RechargeLotto = 2;          // 充值抽奖
    GreenerGiftPackage = 3;     // 新手大礼包
}

message GrantGameVoucherReq {
    required uint32 biz_id = 1;
    required string biz_order_id = 2;
    required uint32 uid = 3;
    required uint32 ly_game_id = 4;
    required uint32 amount = 5;
    required string effective_date = 6;
    required string expiry_date = 7;
    required string numeric_account = 8;
    optional string ly_cp_id = 9;

    // 额外信息
    optional string device_id = 16;
    optional string tt_channel_id = 17;
    optional string ly_channel_id = 18;
    optional string remark = 19;
    optional bytes biz_data = 20;
    optional uint32 limit_amount = 21;
}

message GrantGameVoucherResp {
}

message GameVoucher {
    required uint64 voucher_id = 1;       // 代金券ID
    required uint32 biz_id = 2;
    required string biz_order_id = 3;
    optional bytes biz_data = 4;
    required uint32 uid = 5;              // 代金券所有者UID
    required string numeric_account = 6;  // 数字帐号
    required uint32 ly_game_id = 7;
    required string ly_cp_type = 8;       // 联运渠道   S/T/Y/C
    required string ly_cp_id = 9;         //
    required string tt_channel_id = 10;   // TT渠道
    required string device_id = 11;               // 设备ID
    required uint32 grant_time = 12;              // 获取时间
    required double total_amount = 13;            // 该代金券面额
    required double consumed_amount = 14;         // 该代金券已经消费额度
    required double total_consume_fee = 15;       // 使用代金券时 累计总消费额（包括代金券和其他货币）
    required double cash_consume_fee = 16;        // 使用代金券时 累计现金消费额
    required uint64 last_consume_time = 17;       // 最后消费时间
    required string last_consume_order_id = 18;
    required string effective_date = 19;          // 生效日期
    required string expiry_date = 20;             // 过期日期
    required string remark = 21;
}

message VoucherSummaryFilter {
    optional uint32 biz_id = 1;
    repeated uint32 ly_game_id = 2;
    repeated string ly_cp_type = 3;         // T|S|Y|G|C

    optional uint32 grant_time_min = 4;
    optional uint32 grant_time_max = 5;
}

message QueryGameVoucherSummaryReq {
    required VoucherSummaryFilter filter = 1;
}

message GameVoucherSummary {
    // index
    required uint32 biz_id = 1;
    required uint32 ly_game_id = 2;
    required string ly_cp_type = 3;

    // data
    optional uint32 grant_count = 4;         // 总的发放张数
    optional uint32 use_count = 5;           // 总的使用张数
    optional uint32 full_use_count = 6;      // 完全使用张数
    optional uint32 expire_count = 7;        // 代金券过期张数
    optional uint32 full_expire_count = 8;   // 代金券一次没用就过期张数

    optional double total_consume_fee = 9;   // 使用代金券时 累计总消费额（包括代金券和其他货币）
    optional double cash_consume_fee = 10;   // 使用代金券时 累计现金消费额
    optional double total_amount = 11;       // 代金券总发放面额
    optional double consumed_amount = 12;    // 代金券已经被消费的额度
    optional uint32 grant_user_count = 13;   // 领取代金券的用户数
    optional uint32 consume_user_count = 14; // 使用领取代金券的用户数
    optional double expired_amount = 15;     // 代金券的总过期额度
}

message GameVoucherSummaryList {
    repeated GameVoucherSummary game_voucher_summary_list = 1;
}

message VoucherListFilter {
    optional uint32 biz_id = 1;
    optional uint32 ly_game_id = 2;
    optional string ly_cp_type = 3;         // T|S|Y|G|C

    optional uint32 uid = 4;
    optional uint32 grant_time_min = 5;
    optional uint32 grant_time_max = 6;
}

message QueryGameVoucherListReq {
    required VoucherListFilter filter = 1;
    required uint32 start_index = 2;
    required uint32 limit = 3;
}

message GameVoucherList {
    repeated GameVoucher voucher_list = 1;
}

message VoucherRechargeInfo {
    message Recharge {
        required string order_id = 1;
        required double total_fee = 2;
        required double cash_fee = 3;
        required uint64 recharge_timestamp = 4;
    }

    message Voucher {
        required uint64 id = 1;
        required uint64 source_voucher_id = 2;
        required uint64 parent_voucher_id = 3;
        required string type = 4;
        required double amount = 5;
        required double actual_amount = 6;
        required double spending_amount = 7;
        required string status = 8;
    }

    required uint32 uid = 1;
    required string account = 2;
    required uint32 ly_game_id = 3;
    required string ly_game_name =4;
    required uint32 ly_channel_id = 5;
    required string ly_cp_id = 6;
    required Recharge recharge_info = 7;
    required Voucher voucher_info = 8;
}

message GetGameVoucherReq {
    required uint32 ly_game_id = 1;
    required uint64 voucher_id = 2;
}

// 联运的代金券信息
message LYGameVoucher {
    required uint64 id = 1;
    required string type = 2;
    required uint32 ly_game_id = 3;
    required uint32 uid = 4;
    required double amount = 5;
    required double actual_amount = 6;      // 代金券的实际金额
    required double spending_amount = 7;
    required string order_id = 8;
    required string status = 9;
    required uint64 modify_time = 10;
    required uint32 act_id = 11;
    required uint32 parent_voucher_id = 12; // 父代金卷ID
    required uint32 source_voucher_id = 13; // 祖先代金卷ID
}

message GetLYGameVoucherByOrderReq {
    required string ly_order_id = 1;        // 联运订单号
}

message GetLYGameVoucherByOrderResp {
    optional LYGameVoucher voucher = 1;
}


message GameVoucherRechargeSummary {
    required uint32 biz_id = 1;
    required uint32 ly_game_id = 2;
    required string ly_cp_type = 3;

    required uint32 recharge_order_count = 4;
    required uint32 recharge_user_count = 5;
    required double recharge_total_fee = 6;
    required double recharge_cash_fee = 7;
    required double voucher_spending_amount = 8;
}

message GameVoucherRechargeSummaryList {
    repeated GameVoucherRechargeSummary summary_list = 1;
}

message QueryGameVoucherRechargeSummaryReq {
    required uint32 recharge_timestamp_min = 1;
    required uint32 recharge_timestamp_max = 2;
}

service gamevoucher {

    option( tlvpickle.Magic ) = 15381;

    // 发放代金券
    rpc GrantGameVoucher( GrantGameVoucherReq ) returns( GrantGameVoucherResp ) {
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "b:o:u:g:a:p:";
        option( tlvpickle.Usage ) = "-b <biz_id> -o <order_id> -u <user_id> -g <game_id> -a <amount> -p <cp_id>";
    }

    // 上报带有代金券的充值数据
    rpc ReportVoucherRecharge( VoucherRechargeInfo ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <user_id>";
    }

    // 直接发放代金券(Not async)
    rpc DirectGrantGameVoucher( GrantGameVoucherReq ) returns( GrantGameVoucherResp ) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "b:o:u:g:a:p:";
        option( tlvpickle.Usage ) = "-b <biz_id> -o <order_id> -u <user_id> -g <game_id> -a <amount> -p <cp_id>";
    }

    // 获取代金券详情
    rpc GetGameVoucher( GetGameVoucherReq ) returns( GameVoucher ) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "g:o:";
        option( tlvpickle.Usage ) = "-g <game_id> -o <voucher_id>";
    }

    // 获取联运代金券信息
    rpc GetLYGameVoucherByOrder( GetLYGameVoucherByOrderReq ) returns( GetLYGameVoucherByOrderResp ) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "o:";
        option( tlvpickle.Usage ) = "-o <ly_order_id>";
    }

    // 查代金券发放总览(统计数据)
    rpc QueryGameVoucherSummary( QueryGameVoucherSummaryReq ) returns( GameVoucherSummaryList ) {
        option( tlvpickle.CmdID ) = 100;
        option( tlvpickle.OptString ) = "z:g:p:b:e:";
        option( tlvpickle.Usage ) = "-z <biz_id> -g <game_id_list> -p <cp_type_list> -b <begin> -e <end>";
    }

    // 查询代金券明细列表
    rpc QueryGameVoucherList( QueryGameVoucherListReq ) returns( GameVoucherList ) {
        option( tlvpickle.CmdID ) = 101;
        option( tlvpickle.OptString ) = "z:g:p:u:b:e:s:l:";
        option( tlvpickle.Usage ) = "-z <biz_id> -g <game_id> -p <cp_type> -u <uid> -b <grant_begin> -e <grant_end> -s <start> -l <limit>";
    }

    // 查询代金券消费统计
    rpc QueryGameVoucherRechargeSummary( QueryGameVoucherRechargeSummaryReq ) returns( GameVoucherRechargeSummaryList ) {
        option( tlvpickle.CmdID ) = 102;
        option( tlvpickle.OptString ) = "b:e:";
        option( tlvpickle.Usage ) = "-b <begin> -e <end>";
    }
}
