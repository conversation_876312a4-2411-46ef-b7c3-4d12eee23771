// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.music_list_channel;

import "channel_play/channel-play_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/music_list_channel;music_list_channel";

service MusicListChannelLogic {
    option (ga.api.extension.logic_service_name) = "music-list-channel-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.MusicListChannelLogic/";

    rpc HomePageMixChannelList(ga.channel_play.ListTopicChannelReq) returns (ga.channel_play.ListTopicChannelResp) {
        option (ga.api.extension.command) = {
            id: 31534
        };
    }

}


