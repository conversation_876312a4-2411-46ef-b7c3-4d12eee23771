package rpc

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	account_go "golang.52tt.com/clients/account-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	award_center "golang.52tt.com/clients/award-center"
	backpackBase "golang.52tt.com/clients/backpack-base"
	backpackSender "golang.52tt.com/clients/backpack-sender"
	"golang.52tt.com/clients/channelol"
	headimage "golang.52tt.com/clients/headimage"
	im_api "golang.52tt.com/clients/im-api"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	reconcilepresent "golang.52tt.com/clients/reconcile-v2-svr/reconcile-present"
	revenueApiGo "golang.52tt.com/clients/revenue-api-go"
	userol "golang.52tt.com/clients/user-online"
	userprofileapi "golang.52tt.com/clients/user-profile-api"
	userpresent "golang.52tt.com/clients/userpresent-go"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	present_go_logic "golang.52tt.com/protocol/app/present-go-logic"
	pushPb "golang.52tt.com/protocol/app/push"
	syncPB "golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	backpackBasePb "golang.52tt.com/protocol/services/backpack-base"
	im_api2 "golang.52tt.com/protocol/services/im-api"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	publicnotice "golang.52tt.com/protocol/services/public-notice"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	revenue_api_go "golang.52tt.com/protocol/services/revenue-api-go"
	userpresentpb "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/notify"
	"google.golang.org/grpc"
	"time"
)

type IRpcClients interface {
	BatchGetRevenueAwardInfosByIds(ctx context.Context, awardInfoList []*revenue_api_go.SearchAwardInfo) (map[string]*revenue_api_go.RevenueAwardInfo, error)
	GetAwardCenter() award_center.IClient
	GetAwardMapKey(awardType revenue_api_go.RevenueAwardType, awardID string) string
	GetBackpackSender() backpackSender.IClient
	GetPackageItemConfigById(ctx context.Context, bgID uint32) ([]*backpackBasePb.PackageItemCfg, error)
	GetPackageFirstItemInfoById(ctx context.Context, bgID uint32) (string, string, string, uint32, bool, error)
	GetPresentConfigList(ctx context.Context) ([]*userpresentpb.StPresentItemConfig, error)
	SendImMsg(ctx context.Context, uid uint32, content, hlight, url string) error
	GetUserMap(ctx context.Context, uidList []uint32) (map[uint32]*account_go.User, error)
	PresentSetUnlockNotify(ctx context.Context, uid uint32, msg *present_go_logic.PresentSetUnlockMsg) error
	BatchGetHeadImageMd5(ctx context.Context, uid uint32, accountList []string) (map[string]string, error)
	PushGetAllBreakingNews(ctx context.Context, uid, newsId uint32, setName string, setId uint32) error
	PushEmperorBreakingNews(ctx context.Context, uid, targetUid, newsId, giftWorth, channelId uint32, setName, icon, jumpUrl string) error
	SendUnlockIm(ctx context.Context, uid uint32, levelName, presentName, expire string)
	SendUnlockImLong(ctx context.Context, uid uint32, levelName, presentName string)
	SendGetAllIm(ctx context.Context, uid uint32, setName, expire string)
	SendGetAllImLong(ctx context.Context, uid uint32, setName string)
	CheckBreakingNewValid(ctx context.Context, newsId uint32) (bool, error)
}

type Clients struct {
	HeadImageCli        headimage.IClient
	PushCli             *pushclient.Client
	UserProfileCli      userprofileapi.IClient
	ReconcilePresentCli reconcilepresent.IClient
	PublicNoticeCli     publicNoticePb.PublicNoticeClient
	ChannelOlCli        channelol.IClient
	UserPresentCli      userpresent.IClient
	RevenueApiGoCli     revenueApiGo.IClient
	BackpackBaseCli     backpackBase.IClient
	AwardCenterCli      award_center.IClient
	BackpackSenderCli   backpackSender.IClient
	apiClient           apicenter.IClient
	userOlCli           userol.IClient
	accountCli          account_go.IClient
	imApiCli            im_api.IClient
}

func NewClients() IRpcClients {
	opts := []grpc.DialOption{grpc.WithBlock()}
	pushCli, _ := pushclient.NewClient(opts...)
	publicNoticeCli, _ := publicnotice.NewClient(context.Background(), opts...)
	return &Clients{
		PushCli:             pushCli,
		HeadImageCli:        headimage.NewIClient(opts...),
		UserProfileCli:      userprofileapi.NewIClient(opts...),
		ReconcilePresentCli: reconcilepresent.NewIClient(opts...),
		PublicNoticeCli:     publicNoticeCli,
		ChannelOlCli:        channelol.NewIClient(opts...),
		UserPresentCli:      userpresent.NewClient(opts...),
		RevenueApiGoCli:     revenueApiGo.NewIClient(opts...),
		BackpackBaseCli:     backpackBase.NewIClient(opts...),
		AwardCenterCli:      award_center.NewIClient(opts...),
		BackpackSenderCli:   backpackSender.NewIClient(opts...),
		apiClient:           apicenter.NewClient(opts...),
		userOlCli:           userol.NewIClient(opts...),
		accountCli:          account_go.NewIClient(opts...),
		imApiCli:            im_api.NewIClient(opts...),
	}
}

func (c *Clients) GetPresentConfigList(ctx context.Context) ([]*userpresentpb.StPresentItemConfig, error) {
	r, err := c.UserPresentCli.GetPresentConfigList(ctx, &userpresentpb.GetPresentConfigListReq{})
	if err != nil {
		log.ErrorWithCtx(nil, "GetPresentConfigList err:%s", err)
		return nil, err
	}
	return r.GetItemList(), nil
}

func (c *Clients) GetAwardMapKey(awardType revenue_api_go.RevenueAwardType, awardID string) string {
	return fmt.Sprintf("%+v_%+v", awardType, awardID)
}

func (c *Clients) BatchGetRevenueAwardInfosByIds(ctx context.Context, awardInfoList []*revenue_api_go.SearchAwardInfo) (map[string]*revenue_api_go.RevenueAwardInfo, error) {
	resp, err := c.RevenueApiGoCli.BatchGetRevenueAwardInfosByIds(ctx, &revenue_api_go.BatchGetRevenueAwardInfosByIdsReq{
		AwardTypeList: awardInfoList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetRevenueAwardInfosByIds err:%s", err)
		return nil, err
	}
	result := make(map[string]*revenue_api_go.RevenueAwardInfo)
	for _, i := range resp.GetAwardInfos() {
		info := i
		if info == nil {
			continue
		}
		result[c.GetAwardMapKey(info.GetAwardType(), info.GetAwardId())] = info
	}
	return result, nil
}

func (c *Clients) GetPackageItemConfigById(ctx context.Context, bgID uint32) ([]*backpackBasePb.PackageItemCfg, error) {
	resp, err := c.BackpackBaseCli.GetPackageItemCfg(ctx, bgID)
	if err != nil {
		log.ErrorWithCtx(nil, "GetPackageCfgById err:%s", err)
		return nil, err
	}
	return resp.GetItemCfgList(), nil
}

func (c *Clients) GetPackageFirstItemInfoById(ctx context.Context, bgID uint32) (string, string, string, uint32,
	bool, error) {
	resp, err := c.BackpackBaseCli.GetPackageItemCfg(ctx, bgID)
	if err != nil {
		log.ErrorWithCtx(nil, "GetPackageCfgById err:%s", err)
		return "", "", "", 0, false, err
	}
	firstItem := resp.GetItemCfgList()[0]
	var price uint32
	var priceType string
	var name, pic string
	var isFragment bool
	switch backpackBasePb.PackageItemType(firstItem.GetItemType()) {
	case backpackBasePb.PackageItemType_BACKPACK_PRESENT:
		respPresentConfig, err := c.UserPresentCli.GetPresentConfigById(ctx, firstItem.GetSourceId())
		if err != nil {
			log.ErrorWithCtx(nil, "GetPresentConfigById err:%s", err)
			return name, pic, priceType, 0, isFragment, err
		}
		price = respPresentConfig.GetItemConfig().GetPrice()
		if respPresentConfig.GetItemConfig().GetPriceType() == uint32(userpresentpb.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) {
			priceType = "红钻"
		} else {
			priceType = "豆"
		}
		name = respPresentConfig.GetItemConfig().GetName()
		pic = respPresentConfig.GetItemConfig().GetIconUrl()
	case backpackBasePb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT:
		respPackageItemCfg, svrErr := c.BackpackBaseCli.GetItemCfg(ctx, &backpackBasePb.GetItemCfgReq{
			ItemType:         uint32(backpackBasePb.PackageItemType_BACKPACK_LOTTERY_FRAGMENT),
			ItemSourceIdList: []uint32{firstItem.GetSourceId()},
		})
		if svrErr != nil {
			log.ErrorWithCtx(nil, "GetPackageItemCfg err:%s", err)
			return name, pic, priceType, 0, isFragment, svrErr
		}
		itemConfig := &backpackBasePb.LotteryFragmentCfg{}
		err := proto.Unmarshal(respPackageItemCfg.GetItemCfgList()[0], itemConfig)
		if err != nil {
			log.ErrorWithCtx(ctx, "Unmarshal failed, err:%v", err)
			return name, pic, priceType, 0, isFragment, err
		}
		isFragment = true
		price = itemConfig.GetFragmentPrice()
		priceType = "豆"
		name = itemConfig.GetFragmentName()
		pic = itemConfig.GetFragmentUrl()
	default:
		price = 0
		priceType = "豆"
		name = "未知"
		pic = ""
	}

	return name, pic, priceType, price, isFragment, nil
}

func (c *Clients) GetAwardCenter() award_center.IClient {
	return c.AwardCenterCli
}

func (c *Clients) GetBackpackSender() backpackSender.IClient {
	return c.BackpackSenderCli
}

// SendImMsg 推送IM消息
func (c *Clients) SendImMsg(ctx context.Context, uid uint32, content, hlight, url string) error {
	log.DebugWithCtx(ctx, "SendImMsg uid:%d content:%s hlight:%s", uid, content, hlight)

	// 获取用户在线平台信息
	onlineInfo, err := c.userOlCli.GetLastMobileOnlineInfo(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg GetLastMultiOnlineInfo failed uid:%d err:%v", uid, err)
		return err
	}

	if onlineInfo.GetUid() != uid {
		log.InfoWithCtx(ctx, "SendImMsg user no onLineInfo uid:%d info:%v", uid, onlineInfo)
		return nil
	}

	platform, _, _ := protocol.UnPackTerminalType(onlineInfo.TerminalType)

	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
		ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = apiPB.Platform_name[int32(platform)]
	msg.AppName = marketid_helper.GetAppName(onlineInfo.MarketId)

	log.InfoWithCtx(ctx, "SendImMsg uid:%d content:%s hlight:%s", uid, content, hlight)
	err = c.apiClient.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendImMsg fail uid:%d err:%v", uid, err)
	}

	notify.NotifySync(uid, syncPB.SyncReq_IM_MSG)
	totalPre, ok := notify.NotifySync(uid, syncPB.SyncReq_IM_MSG)

	log.DebugWithCtx(ctx, "SendImMsg NotifySync uid:%d totalPre:%v ok:%v AppPlatform %v AppName %v", uid, totalPre, ok, msg.AppPlatform, msg.AppName)

	return nil
}

func (c *Clients) PresentSetUnlockNotify(ctx context.Context, uid uint32, msg *present_go_logic.PresentSetUnlockMsg) error {
	log.DebugWithCtx(ctx, "PresentSetUnlockNotify uid:%d msg:%+v", uid, msg)

	userMsgContent, _ := proto.Marshal(msg)
	pushMessage := pushPb.PushMessage{Cmd: uint32(pushPb.PushMessage_PRESENT_SET_UNLOCK_PUSH), Content: userMsgContent}

	pushMessageContent, _ := proto.Marshal(&pushMessage)
	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		AppId:              uint32(protocol.TT),
		TerminalTypePolicy: pushclient.DefaultPolicy,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageContent,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 86400,
		},
	}

	err := c.PushCli.PushToUsers(ctx, []uint32{uid}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentSetUnlockNotify PushToUsers err:%s", err)
		return err
	}

	return nil
}

func (c *Clients) GetUserMap(ctx context.Context, uidList []uint32) (map[uint32]*account_go.User, error) {
	users, err := c.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetUserMap err:%s", err)
		return nil, err
	}
	return users, nil
}

func (c *Clients) BatchGetHeadImageMd5(ctx context.Context, uid uint32, accountList []string) (map[string]string, error) {
	headImages, err := c.HeadImageCli.BatchGetHeadImageMd5(ctx, uid, accountList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetHeadImageMd5 err:%s", err)
		return nil, err
	}
	return headImages, nil
}

func (c *Clients) PushGetAllBreakingNews(ctx context.Context, uid, newsId uint32, setName string, setId uint32) error {
	user, err := c.accountCli.GetUserByUid(ctx, uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushGetAllBreakingNews failed, err:[%+v], uid:[%d], newsId:[%d]", err, uid, newsId)
		return err
	}

	if user == nil || user.GetUid() == 0 {
		log.ErrorWithCtx(ctx, "PushUpgradeDiamondBreaking err, user is nil or uid is 0, user:[%+v]", user)
		return nil
	}

	if newsId == 0 {
		log.ErrorWithCtx(ctx, "PushUpgradeDiamondBreaking err, newsId=0, newsId:[%+v], uid:[%d]", newsId, user.GetUid())
		return nil
	}

	breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: user.GetUid(),
		FromUserInfo: &publicNoticePb.UserInfo{
			Account: user.GetUsername(),
			Nick:    user.GetNickname(),
		},
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS),
		},
		JumpUrl: fmt.Sprintf("tt://m.52tt.com/suitgift_popupview?suitId=%d", setId),
	}
	req := &publicNoticePb.PushBreakingNewsReq{
		BreakingCmdType:    publicNoticePb.PushBreakingNewsReq_COMMON_BREAKING_EVENT_V3,
		CommonBreakingNews: breakingNewsMessage,
	}
	req.RichTextNews = &publicNoticePb.RichTextNews{
		NewsId:             newsId,
		CustomBizLevelName: setName,
	}

	_, sErr := c.PublicNoticeCli.PushBreakingNews(ctx, req)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "PushGetAllBreakingNews failed, req:[%+v], err:[%+v], uid:[%d], newsId:[%d]", req, err, user.GetUid(), newsId)
		return sErr
	} else {
		log.InfoWithCtx(ctx, "PushGetAllBreakingNews succ, req:[%+v], uid:[%d], newsId:[%d]", req, user.GetUid(), newsId)
	}
	return nil
}

func (c *Clients) PushEmperorBreakingNews(ctx context.Context, uid, targetUid, newsId, giftWorth, channelId uint32, setName, icon, jumpUrl string) error {
	if uid == 0 {
		log.ErrorWithCtx(ctx, "PushUpgradeDiamondBreaking err,uid is 0, user:[%+v]", uid)
		return nil
	}

	if newsId == 0 {
		log.ErrorWithCtx(ctx, "PushUpgradeDiamondBreaking err, newsId=0, newsId:[%+v], uid:[%d]", newsId, uid)
		return nil
	}

	breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid:   uid,
		TargetUid: targetUid,
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS),
		},
		ChannelId: channelId,
		PresentNewsBaseOpt: &publicNoticePb.PresentBreakingNewsBaseOpt{
			GiftName:    setName,
			GiftIconUrl: icon,
			GiftWorth:   giftWorth,
		},
		JumpUrl: jumpUrl,
	}
	req := &publicNoticePb.PushBreakingNewsReq{
		BreakingCmdType:    publicNoticePb.PushBreakingNewsReq_COMMON_BREAKING_EVENT_V3,
		CommonBreakingNews: breakingNewsMessage,
	}
	req.RichTextNews = &publicNoticePb.RichTextNews{
		NewsId: newsId,
	}

	_, err := c.PublicNoticeCli.PushBreakingNews(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushEmperorBreakingNews failed, req:[%+v], err:[%+v], uid:[%d], newsId:[%d]", req, err, uid, newsId)
		return err
	} else {
		log.InfoWithCtx(ctx, "PushEmperorBreakingNews succ, req:[%+v], uid:[%d], targetUid:%d, newsId:[%d]", req, uid, targetUid, newsId)
	}
	return nil
}

// SendUnlockIm 发送解锁IM
func (c *Clients) SendUnlockIm(ctx context.Context, uid uint32, levelName, presentName, expire string) {
	_, err := c.imApiCli.SendTTAssistantText(ctx, &im_api2.SendTTAssistantTextReq{
		ToUid: uid,
		Text: &im_api2.Text{
			Content: fmt.Sprintf("【礼物套装】恭喜你！解锁%s新礼物%s！请在%s前，前往礼物架赠送~", levelName, presentName, expire),
		},
		Opt:       nil,
		Namespace: "",
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SendUnlockIm uid %d err:%s", uid, err)
		return
	}

	return
}

// SendUnlockImLong 发送解锁IM
func (c *Clients) SendUnlockImLong(ctx context.Context, uid uint32, levelName, presentName string) {
	_, err := c.imApiCli.SendTTAssistantText(ctx, &im_api2.SendTTAssistantTextReq{
		ToUid: uid,
		Text: &im_api2.Text{
			Content: fmt.Sprintf("【礼物套装】恭喜你！解锁%s新礼物%s！快去赠送吧~", levelName, presentName),
		},
		Opt:       nil,
		Namespace: "",
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SendUnlockImLong uid %d err:%s", uid, err)
		return
	}

	return
}

// SendGetAllIm 发送解锁IM
func (c *Clients) SendGetAllIm(ctx context.Context, uid uint32, setName, expire string) {

	_, err := c.imApiCli.SendTTAssistantText(ctx, &im_api2.SendTTAssistantTextReq{
		ToUid: uid,
		Text: &im_api2.Text{
			Content: fmt.Sprintf("【礼物套装】恭喜你！集齐%s全部礼物！请在%s前，前往礼物架赠送~", setName, expire),
		},
		Opt:       nil,
		Namespace: "",
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SendGetAllIm uid %d err:%s", uid, err)
		return
	}

	return
}

// SendGetAllImLong 发送解锁IM
func (c *Clients) SendGetAllImLong(ctx context.Context, uid uint32, setName string) {

	_, err := c.imApiCli.SendTTAssistantText(ctx, &im_api2.SendTTAssistantTextReq{
		ToUid: uid,
		Text: &im_api2.Text{
			Content: fmt.Sprintf("【礼物套装】恭喜你！集齐%s全部礼物！快去赠送吧~", setName),
		},
		Opt:       nil,
		Namespace: "",
	})

	if err != nil {
		log.ErrorWithCtx(ctx, "SendGetAllIm uid %d err:%s", uid, err)
		return
	}

	return
}

func (c *Clients) CheckBreakingNewValid(ctx context.Context, newsId uint32) (bool, error) {
	resp, err := c.PublicNoticeCli.BatchGetBreakingNewsConfig(ctx, &publicNoticePb.BatchGetBreakingNewsConfigReq{SearchNewsId: newsId})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckBreakingNewValid err:%v, newsId:%d", err, newsId)
		return false, err
	}
	for _, i := range resp.GetBreakingNewsConfigList() {
		if i.GetNewsId() == newsId {
			return true, nil
		}
	}
	log.DebugWithCtx(ctx, "CheckBreakingNewValid newsId:%d not found", newsId)
	return false, nil
}
