// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mystery-place-push/mystery-place-push.proto

package mystery_place_push // import "golang.52tt.com/protocol/services/mystery-place-push"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "github.com/envoyproxy/protoc-gen-validate/validate"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BindGroupsReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupIds             []uint32 `protobuf:"varint,2,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindGroupsReq) Reset()         { *m = BindGroupsReq{} }
func (m *BindGroupsReq) String() string { return proto.CompactTextString(m) }
func (*BindGroupsReq) ProtoMessage()    {}
func (*BindGroupsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{0}
}
func (m *BindGroupsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindGroupsReq.Unmarshal(m, b)
}
func (m *BindGroupsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindGroupsReq.Marshal(b, m, deterministic)
}
func (dst *BindGroupsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindGroupsReq.Merge(dst, src)
}
func (m *BindGroupsReq) XXX_Size() int {
	return xxx_messageInfo_BindGroupsReq.Size(m)
}
func (m *BindGroupsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindGroupsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindGroupsReq proto.InternalMessageInfo

func (m *BindGroupsReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BindGroupsReq) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

type BindGroupsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindGroupsResp) Reset()         { *m = BindGroupsResp{} }
func (m *BindGroupsResp) String() string { return proto.CompactTextString(m) }
func (*BindGroupsResp) ProtoMessage()    {}
func (*BindGroupsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{1}
}
func (m *BindGroupsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindGroupsResp.Unmarshal(m, b)
}
func (m *BindGroupsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindGroupsResp.Marshal(b, m, deterministic)
}
func (dst *BindGroupsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindGroupsResp.Merge(dst, src)
}
func (m *BindGroupsResp) XXX_Size() int {
	return xxx_messageInfo_BindGroupsResp.Size(m)
}
func (m *BindGroupsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BindGroupsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BindGroupsResp proto.InternalMessageInfo

type UnbindGroupsReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GroupIds             []uint32 `protobuf:"varint,2,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnbindGroupsReq) Reset()         { *m = UnbindGroupsReq{} }
func (m *UnbindGroupsReq) String() string { return proto.CompactTextString(m) }
func (*UnbindGroupsReq) ProtoMessage()    {}
func (*UnbindGroupsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{2}
}
func (m *UnbindGroupsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnbindGroupsReq.Unmarshal(m, b)
}
func (m *UnbindGroupsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnbindGroupsReq.Marshal(b, m, deterministic)
}
func (dst *UnbindGroupsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnbindGroupsReq.Merge(dst, src)
}
func (m *UnbindGroupsReq) XXX_Size() int {
	return xxx_messageInfo_UnbindGroupsReq.Size(m)
}
func (m *UnbindGroupsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnbindGroupsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnbindGroupsReq proto.InternalMessageInfo

func (m *UnbindGroupsReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UnbindGroupsReq) GetGroupIds() []uint32 {
	if m != nil {
		return m.GroupIds
	}
	return nil
}

type UnbindGroupsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnbindGroupsResp) Reset()         { *m = UnbindGroupsResp{} }
func (m *UnbindGroupsResp) String() string { return proto.CompactTextString(m) }
func (*UnbindGroupsResp) ProtoMessage()    {}
func (*UnbindGroupsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{3}
}
func (m *UnbindGroupsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnbindGroupsResp.Unmarshal(m, b)
}
func (m *UnbindGroupsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnbindGroupsResp.Marshal(b, m, deterministic)
}
func (dst *UnbindGroupsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnbindGroupsResp.Merge(dst, src)
}
func (m *UnbindGroupsResp) XXX_Size() int {
	return xxx_messageInfo_UnbindGroupsResp.Size(m)
}
func (m *UnbindGroupsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnbindGroupsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnbindGroupsResp proto.InternalMessageInfo

type GetGroupListReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGroupListReq) Reset()         { *m = GetGroupListReq{} }
func (m *GetGroupListReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupListReq) ProtoMessage()    {}
func (*GetGroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{4}
}
func (m *GetGroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListReq.Unmarshal(m, b)
}
func (m *GetGroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListReq.Marshal(b, m, deterministic)
}
func (dst *GetGroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListReq.Merge(dst, src)
}
func (m *GetGroupListReq) XXX_Size() int {
	return xxx_messageInfo_GetGroupListReq.Size(m)
}
func (m *GetGroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListReq proto.InternalMessageInfo

func (m *GetGroupListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetGroupListResp struct {
	Groups               []*GroupInfo `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupListResp) Reset()         { *m = GetGroupListResp{} }
func (m *GetGroupListResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupListResp) ProtoMessage()    {}
func (*GetGroupListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{5}
}
func (m *GetGroupListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupListResp.Unmarshal(m, b)
}
func (m *GetGroupListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupListResp.Marshal(b, m, deterministic)
}
func (dst *GetGroupListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupListResp.Merge(dst, src)
}
func (m *GetGroupListResp) XXX_Size() int {
	return xxx_messageInfo_GetGroupListResp.Size(m)
}
func (m *GetGroupListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupListResp proto.InternalMessageInfo

func (m *GetGroupListResp) GetGroups() []*GroupInfo {
	if m != nil {
		return m.Groups
	}
	return nil
}

type GroupInfo struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	OwnerId              uint32   `protobuf:"varint,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	MemberCount          uint32   `protobuf:"varint,4,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	MemberLimit          uint32   `protobuf:"varint,5,opt,name=member_limit,json=memberLimit,proto3" json:"member_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupInfo) Reset()         { *m = GroupInfo{} }
func (m *GroupInfo) String() string { return proto.CompactTextString(m) }
func (*GroupInfo) ProtoMessage()    {}
func (*GroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{6}
}
func (m *GroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupInfo.Unmarshal(m, b)
}
func (m *GroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupInfo.Marshal(b, m, deterministic)
}
func (dst *GroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupInfo.Merge(dst, src)
}
func (m *GroupInfo) XXX_Size() int {
	return xxx_messageInfo_GroupInfo.Size(m)
}
func (m *GroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GroupInfo proto.InternalMessageInfo

func (m *GroupInfo) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupInfo) GetOwnerId() uint32 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *GroupInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GroupInfo) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *GroupInfo) GetMemberLimit() uint32 {
	if m != nil {
		return m.MemberLimit
	}
	return 0
}

type StartUserRecallReq struct {
	WhiteList            []uint32 `protobuf:"varint,1,rep,packed,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartUserRecallReq) Reset()         { *m = StartUserRecallReq{} }
func (m *StartUserRecallReq) String() string { return proto.CompactTextString(m) }
func (*StartUserRecallReq) ProtoMessage()    {}
func (*StartUserRecallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{7}
}
func (m *StartUserRecallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartUserRecallReq.Unmarshal(m, b)
}
func (m *StartUserRecallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartUserRecallReq.Marshal(b, m, deterministic)
}
func (dst *StartUserRecallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartUserRecallReq.Merge(dst, src)
}
func (m *StartUserRecallReq) XXX_Size() int {
	return xxx_messageInfo_StartUserRecallReq.Size(m)
}
func (m *StartUserRecallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartUserRecallReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartUserRecallReq proto.InternalMessageInfo

func (m *StartUserRecallReq) GetWhiteList() []uint32 {
	if m != nil {
		return m.WhiteList
	}
	return nil
}

type StartUserRecallResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartUserRecallResp) Reset()         { *m = StartUserRecallResp{} }
func (m *StartUserRecallResp) String() string { return proto.CompactTextString(m) }
func (*StartUserRecallResp) ProtoMessage()    {}
func (*StartUserRecallResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_push_0e59fb5a9578000e, []int{8}
}
func (m *StartUserRecallResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartUserRecallResp.Unmarshal(m, b)
}
func (m *StartUserRecallResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartUserRecallResp.Marshal(b, m, deterministic)
}
func (dst *StartUserRecallResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartUserRecallResp.Merge(dst, src)
}
func (m *StartUserRecallResp) XXX_Size() int {
	return xxx_messageInfo_StartUserRecallResp.Size(m)
}
func (m *StartUserRecallResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartUserRecallResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartUserRecallResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BindGroupsReq)(nil), "mystery_place_push.BindGroupsReq")
	proto.RegisterType((*BindGroupsResp)(nil), "mystery_place_push.BindGroupsResp")
	proto.RegisterType((*UnbindGroupsReq)(nil), "mystery_place_push.UnbindGroupsReq")
	proto.RegisterType((*UnbindGroupsResp)(nil), "mystery_place_push.UnbindGroupsResp")
	proto.RegisterType((*GetGroupListReq)(nil), "mystery_place_push.GetGroupListReq")
	proto.RegisterType((*GetGroupListResp)(nil), "mystery_place_push.GetGroupListResp")
	proto.RegisterType((*GroupInfo)(nil), "mystery_place_push.GroupInfo")
	proto.RegisterType((*StartUserRecallReq)(nil), "mystery_place_push.StartUserRecallReq")
	proto.RegisterType((*StartUserRecallResp)(nil), "mystery_place_push.StartUserRecallResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MysteryPushClient is the client API for MysteryPush service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MysteryPushClient interface {
	// 手动触发新用户召回
	StartUserRecall(ctx context.Context, in *StartUserRecallReq, opts ...grpc.CallOption) (*StartUserRecallResp, error)
	// 绑定群id
	BindGroups(ctx context.Context, in *BindGroupsReq, opts ...grpc.CallOption) (*BindGroupsResp, error)
	// 解绑群id
	UnbindGroups(ctx context.Context, in *UnbindGroupsReq, opts ...grpc.CallOption) (*UnbindGroupsResp, error)
	// 获取绑定的群信息
	GetGroupList(ctx context.Context, in *GetGroupListReq, opts ...grpc.CallOption) (*GetGroupListResp, error)
}

type mysteryPushClient struct {
	cc *grpc.ClientConn
}

func NewMysteryPushClient(cc *grpc.ClientConn) MysteryPushClient {
	return &mysteryPushClient{cc}
}

func (c *mysteryPushClient) StartUserRecall(ctx context.Context, in *StartUserRecallReq, opts ...grpc.CallOption) (*StartUserRecallResp, error) {
	out := new(StartUserRecallResp)
	err := c.cc.Invoke(ctx, "/mystery_place_push.MysteryPush/StartUserRecall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPushClient) BindGroups(ctx context.Context, in *BindGroupsReq, opts ...grpc.CallOption) (*BindGroupsResp, error) {
	out := new(BindGroupsResp)
	err := c.cc.Invoke(ctx, "/mystery_place_push.MysteryPush/BindGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPushClient) UnbindGroups(ctx context.Context, in *UnbindGroupsReq, opts ...grpc.CallOption) (*UnbindGroupsResp, error) {
	out := new(UnbindGroupsResp)
	err := c.cc.Invoke(ctx, "/mystery_place_push.MysteryPush/UnbindGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPushClient) GetGroupList(ctx context.Context, in *GetGroupListReq, opts ...grpc.CallOption) (*GetGroupListResp, error) {
	out := new(GetGroupListResp)
	err := c.cc.Invoke(ctx, "/mystery_place_push.MysteryPush/GetGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MysteryPushServer is the server API for MysteryPush service.
type MysteryPushServer interface {
	// 手动触发新用户召回
	StartUserRecall(context.Context, *StartUserRecallReq) (*StartUserRecallResp, error)
	// 绑定群id
	BindGroups(context.Context, *BindGroupsReq) (*BindGroupsResp, error)
	// 解绑群id
	UnbindGroups(context.Context, *UnbindGroupsReq) (*UnbindGroupsResp, error)
	// 获取绑定的群信息
	GetGroupList(context.Context, *GetGroupListReq) (*GetGroupListResp, error)
}

func RegisterMysteryPushServer(s *grpc.Server, srv MysteryPushServer) {
	s.RegisterService(&_MysteryPush_serviceDesc, srv)
}

func _MysteryPush_StartUserRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartUserRecallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPushServer).StartUserRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place_push.MysteryPush/StartUserRecall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPushServer).StartUserRecall(ctx, req.(*StartUserRecallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPush_BindGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindGroupsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPushServer).BindGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place_push.MysteryPush/BindGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPushServer).BindGroups(ctx, req.(*BindGroupsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPush_UnbindGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnbindGroupsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPushServer).UnbindGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place_push.MysteryPush/UnbindGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPushServer).UnbindGroups(ctx, req.(*UnbindGroupsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPush_GetGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPushServer).GetGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mystery_place_push.MysteryPush/GetGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPushServer).GetGroupList(ctx, req.(*GetGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MysteryPush_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mystery_place_push.MysteryPush",
	HandlerType: (*MysteryPushServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartUserRecall",
			Handler:    _MysteryPush_StartUserRecall_Handler,
		},
		{
			MethodName: "BindGroups",
			Handler:    _MysteryPush_BindGroups_Handler,
		},
		{
			MethodName: "UnbindGroups",
			Handler:    _MysteryPush_UnbindGroups_Handler,
		},
		{
			MethodName: "GetGroupList",
			Handler:    _MysteryPush_GetGroupList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mystery-place-push/mystery-place-push.proto",
}

func init() {
	proto.RegisterFile("mystery-place-push/mystery-place-push.proto", fileDescriptor_mystery_place_push_0e59fb5a9578000e)
}

var fileDescriptor_mystery_place_push_0e59fb5a9578000e = []byte{
	// 480 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0xcb, 0x6a, 0xdb, 0x40,
	0x14, 0xb5, 0x6c, 0xc7, 0x8f, 0xeb, 0xb8, 0x36, 0x53, 0x4a, 0x55, 0x43, 0x40, 0x99, 0xbe, 0x4c,
	0x4b, 0x64, 0xb0, 0x9b, 0x7e, 0x80, 0xba, 0x08, 0x82, 0x14, 0x8a, 0x8a, 0xa1, 0xa4, 0x0b, 0x21,
	0x4b, 0x53, 0x7b, 0x40, 0x8f, 0xa9, 0xee, 0x38, 0x21, 0xfb, 0x7e, 0x41, 0xb7, 0xfd, 0xd3, 0xac,
	0xca, 0x8c, 0xec, 0x56, 0xb6, 0x05, 0xc9, 0x26, 0xbb, 0xf1, 0x79, 0xdd, 0x99, 0xcb, 0xb1, 0xe0,
	0x7d, 0x72, 0x8b, 0x92, 0xe5, 0xb7, 0x67, 0x22, 0x0e, 0x42, 0x76, 0x26, 0xd6, 0xb8, 0x9a, 0x1c,
	0x42, 0xb6, 0xc8, 0x33, 0x99, 0x11, 0xb2, 0x61, 0x7c, 0xcd, 0xf8, 0x8a, 0x19, 0x3d, 0xbf, 0x0e,
	0x62, 0x1e, 0x05, 0x92, 0x4d, 0xb6, 0x87, 0x42, 0x4c, 0xbf, 0x41, 0xdf, 0xe1, 0x69, 0x74, 0x91,
	0x67, 0x6b, 0x81, 0x1e, 0xfb, 0x49, 0x2c, 0x68, 0x2f, 0x83, 0x84, 0xf9, 0x3c, 0x32, 0x0d, 0xcb,
	0x18, 0xf7, 0x9d, 0xf6, 0x9d, 0xd3, 0x7c, 0x57, 0xb7, 0x6a, 0x5e, 0x4b, 0xe1, 0x6e, 0x44, 0x5e,
	0x43, 0x77, 0xa9, 0xe4, 0x3e, 0x8f, 0xd0, 0xac, 0x5b, 0x8d, 0x71, 0xdf, 0xe9, 0xdc, 0x39, 0x47,
	0xbf, 0x8d, 0x7a, 0xc7, 0xf0, 0x3a, 0x9a, 0x72, 0x23, 0xa4, 0x43, 0x78, 0x52, 0x4e, 0x46, 0x41,
	0xaf, 0x60, 0x30, 0x4f, 0x17, 0x8f, 0x33, 0x8d, 0xc0, 0x70, 0x37, 0x1b, 0x05, 0x9d, 0xc1, 0xe0,
	0x82, 0x49, 0x0d, 0x5c, 0x72, 0x94, 0x0f, 0x9a, 0x47, 0x5d, 0x18, 0xee, 0x9a, 0x50, 0x90, 0x73,
	0x68, 0xe9, 0x41, 0x68, 0x1a, 0x56, 0x63, 0xdc, 0x9b, 0x9e, 0xd8, 0x87, 0x2b, 0xb6, 0xb5, 0xc5,
	0x4d, 0x7f, 0x64, 0xde, 0x46, 0x4c, 0xff, 0x18, 0xd0, 0xfd, 0x87, 0x92, 0x17, 0xd0, 0xd9, 0x3e,
	0xa4, 0x98, 0xed, 0xb5, 0x37, 0xb7, 0x57, 0x54, 0x76, 0x93, 0xb2, 0x5c, 0x51, 0xf5, 0x82, 0xd2,
	0xbf, 0xdd, 0x88, 0x10, 0x68, 0xa6, 0x41, 0xc2, 0xcc, 0x86, 0x65, 0x8c, 0xbb, 0x9e, 0x3e, 0x93,
	0x53, 0x38, 0x4e, 0x58, 0xb2, 0x60, 0xb9, 0x1f, 0x66, 0xeb, 0x54, 0x9a, 0x4d, 0x6d, 0xe9, 0x15,
	0xd8, 0x27, 0x05, 0x95, 0x24, 0x31, 0x4f, 0xb8, 0x34, 0x8f, 0xca, 0x92, 0x4b, 0x05, 0xd1, 0x19,
	0x90, 0xaf, 0x32, 0xc8, 0xe5, 0x1c, 0x59, 0xee, 0xb1, 0x30, 0x88, 0x63, 0xb5, 0xa0, 0x13, 0x80,
	0x9b, 0x15, 0x97, 0xcc, 0x8f, 0x39, 0x4a, 0xfd, 0xdc, 0xbe, 0xd7, 0xd5, 0x88, 0xda, 0x06, 0x7d,
	0x06, 0x4f, 0x0f, 0x4c, 0x28, 0xa6, 0xbf, 0x1a, 0xd0, 0xfb, 0x5c, 0xac, 0xe4, 0xcb, 0x1a, 0x57,
	0x24, 0x82, 0xc1, 0x9e, 0x8c, 0xbc, 0xa9, 0xda, 0xd9, 0xe1, 0x05, 0x46, 0x6f, 0x1f, 0xa4, 0x43,
	0x41, 0x6b, 0x64, 0x0e, 0xf0, 0xbf, 0x61, 0xe4, 0xb4, 0xca, 0xb8, 0xd3, 0xed, 0x11, 0xbd, 0x4f,
	0xa2, 0x63, 0xbf, 0xc3, 0x71, 0xb9, 0x4a, 0xe4, 0x65, 0x95, 0x6b, 0xaf, 0xc8, 0xa3, 0x57, 0xf7,
	0x8b, 0xb6, 0xe1, 0xe5, 0x7a, 0x55, 0x87, 0xef, 0xb5, 0xb6, 0x3a, 0x7c, 0xbf, 0xa5, 0xb4, 0xe6,
	0x7c, 0xbc, 0xfa, 0xb0, 0xcc, 0xe2, 0x20, 0x5d, 0xda, 0xe7, 0x53, 0x29, 0xed, 0x30, 0x4b, 0x26,
	0xfa, 0x5f, 0x1e, 0x66, 0xf1, 0x04, 0x59, 0x7e, 0xcd, 0x43, 0x86, 0x15, 0xdf, 0x8d, 0x45, 0x4b,
	0xab, 0x66, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0xd1, 0x1e, 0x9b, 0x3d, 0x67, 0x04, 0x00, 0x00,
}
