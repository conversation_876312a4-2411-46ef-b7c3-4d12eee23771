package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/pkg/log"
	channelPB "golang.52tt.com/protocol/app/channel"
	kfk_revenue_ext_game "golang.52tt.com/protocol/services/minToolkit/kafka/pb/revenue-ext-game"
	"golang.52tt.com/services/channel-live-stats/manager"
	//"golang.52tt.com/services/ugc/common/event"
	"time"
)

type RevenueExtGameSub struct {
	subscriber.Subscriber
	mgr        *manager.ChannelLiveStatsManager
	topic      string
	channelCli *channel.Client
}

func NewRevenueExtGameSub(clientId, groupId string, topics, brokers []string, mgr *manager.ChannelLiveStatsManager) (*RevenueExtGameSub, error) {
	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	/*
	kafkaSub, err := event.NewKafkaSub(topics[0], brokers, groupId, topics, conf)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	kafkaSub.SetIsMetrics()
	 */

	kafkaSub, err := kafka.NewSubscriber(brokers, conf)
	if err != nil {
		return nil, err
	}

	channelCli := channel.NewClient()

	sub := &RevenueExtGameSub{
		Subscriber:   kafkaSub,
		mgr:        mgr,
		topic:      topics[0],
		channelCli: channelCli,
	}

	err = sub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		return nil, err
	}

	//sub.SetMessageProcessor(sub.handlerEvent)
	return sub, nil
}

func (s *RevenueExtGameSub) Close() {
	s.Subscriber.Stop()
}

func (s *RevenueExtGameSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case s.topic:
		return s.handlerExtGameEvent(msg)
	}
	return nil, false
}

func (s *RevenueExtGameSub) handlerExtGameEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	gameEvent := &kfk_revenue_ext_game.ExtGameMountEvent{}
	err := proto.Unmarshal(msg.Value, gameEvent)
	if err != nil {
		log.Errorf(" handlerExtGameEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	chResp, err := s.channelCli.GetChannelSimpleInfo(ctx, gameEvent.GetUid(), gameEvent.GetChannelId())
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerExtGameEvent GetChannelSimpleInfo failed ev:%v err:%v", gameEvent, err)
		return nil, false
	}

	if chResp.GetChannelType() != uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		log.DebugWithCtx(ctx, "handlerExtGameEvent invalid channel type ev:%v chResp:%v", gameEvent, chResp)
		return nil, false
	}

	switch kfk_revenue_ext_game.MountEventType(gameEvent.GetEventType()) {
	case kfk_revenue_ext_game.MountEventType_MountGame:
		s.mgr.HandleMountGameEv(ctx, gameEvent.GetUid(), gameEvent.GetChannelId(), gameEvent.GetEventTime())
	case kfk_revenue_ext_game.MountEventType_UnmountGame:
		s.mgr.HandleUnmountGameEv(ctx, gameEvent.GetUid(), gameEvent.GetChannelId(), gameEvent.GetEventTime())
	default:
		log.DebugWithCtx(ctx, "handlerExtGameEvent no need proc ev:%v", gameEvent)
		return nil, false
	}

	log.Debugf("handlerExtGameEvent %+v", gameEvent)

	return nil, false
}
