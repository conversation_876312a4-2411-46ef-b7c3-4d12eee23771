package server

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	presentExtraMock "golang.52tt.com/clients/mocks/present-extra-conf"
	present_extra_conf "golang.52tt.com/clients/present-extra-conf"
	userpresent_go "golang.52tt.com/clients/userpresent-go"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	"golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/services/present-go-logic/internal/conf"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"testing"
)

func TestPresentGoLogic__GetPresentEffectTimeDetail(t *testing.T) {
	ctx := context.Background()
	ctx = protogrpc.WithServiceInfo(ctx, &protogrpc.ServiceInfo{UserID: 2202538})

	ctl := gomock.NewController(t)
	defer ctl.Finish()
	presentExtraCli := presentExtraMock.NewMockIClient(ctl)
	presentExtraCli.EXPECT().GetPresentEffectTimeDetail(gomock.Any(), gomock.Any()).Return(&presentextraconf.GetPresentEffectTimeDetailResp{
		GiftId: 1, NowCount: 10, LevelInfo: []*presentextraconf.PresentEffectTimeLevelInfo{{Level: 1, LevelDayCount: 1, LevelSendCount: 1}},
	}, nil)

	client.PresentExtraConfigCli = presentExtraCli

	type fields struct {
		presentCli          userpresent_go.PresentClientWrapper
		businessConf        *conf.BusinessConfManager
		sendPresentConf     *conf.SendPresentConfManager
		constellationUrl    string
		presentExtraConfCli present_extra_conf.IClient
		presentFloatCache   []*pb.PresentFloatLayer
		presentFlashCache   []*pb.PresentFlashEffect
		flashConfigCache    []*pb.FlashEffectConfig
		lastUpdateTime      uint32
	}
	type args struct {
		c   context.Context
		req *pb.GetPresentEffectTimeDetailReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pb.GetPresentEffectTimeDetailResp
		wantErr bool
	}{
		{
			name:   "test",
			fields: fields{},
			args: args{
				c:   ctx,
				req: &pb.GetPresentEffectTimeDetailReq{GiftId: 1},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &PresentGoLogic_{
				businessConf:        tt.fields.businessConf,
				sendPresentConf:     tt.fields.sendPresentConf,
				constellationUrl:    tt.fields.constellationUrl,
				presentExtraConfCli: tt.fields.presentExtraConfCli,
				presentFlashCache:   tt.fields.presentFlashCache,
				flashConfigCache:    tt.fields.flashConfigCache,
				lastUpdateTime:      tt.fields.lastUpdateTime,
			}
			got, err := s.GetPresentEffectTimeDetail(tt.args.c, tt.args.req)
			fmt.Println(got, err)
		})
	}
}
