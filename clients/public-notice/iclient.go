package public_notice

import (
	"context"
 	public_notice "golang.52tt.com/protocol/services/public-notice"
 	"google.golang.org/grpc"
)

type IClient interface {
	PushBreakingNews(ctx context.Context, req *public_notice.PushBreakingNewsReq, opts ...grpc.CallOption) (*public_notice.PushBreakingNewsResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _  := NewClient(dopts...)
	return cli
}
