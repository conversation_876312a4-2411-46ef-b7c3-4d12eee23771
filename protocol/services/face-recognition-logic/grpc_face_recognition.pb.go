// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/face_recognition/grpc_face_recognition.proto

package face_recognition_logic // import "golang.52tt.com/protocol/services/face-recognition-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import auth "golang.52tt.com/protocol/app/auth"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// FaceRecognitionLogicClient is the client API for FaceRecognitionLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type FaceRecognitionLogicClient interface {
	GetFaceRecognitionProvider(ctx context.Context, in *auth.GetFaceRecognitionProviderReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionProviderResp, error)
	GetFaceRecognitionCertifyId(ctx context.Context, in *auth.GetFaceRecognitionCertifyIdReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionCertifyIdResp, error)
	GetFaceRecognitionResult(ctx context.Context, in *auth.GetFaceRecognitionResultReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionResultResp, error)
	GetFaceRecognitionProviderNoAuth(ctx context.Context, in *auth.GetFaceRecognitionProviderReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionProviderResp, error)
	GetFaceRecognitionCertifyIdNoAuth(ctx context.Context, in *auth.GetFaceRecognitionCertifyIdReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionCertifyIdResp, error)
	GetFaceRecognitionResultNoAuth(ctx context.Context, in *auth.GetFaceRecognitionResultReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionResultResp, error)
}

type faceRecognitionLogicClient struct {
	cc *grpc.ClientConn
}

func NewFaceRecognitionLogicClient(cc *grpc.ClientConn) FaceRecognitionLogicClient {
	return &faceRecognitionLogicClient{cc}
}

func (c *faceRecognitionLogicClient) GetFaceRecognitionProvider(ctx context.Context, in *auth.GetFaceRecognitionProviderReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionProviderResp, error) {
	out := new(auth.GetFaceRecognitionProviderResp)
	err := c.cc.Invoke(ctx, "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionProvider", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faceRecognitionLogicClient) GetFaceRecognitionCertifyId(ctx context.Context, in *auth.GetFaceRecognitionCertifyIdReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionCertifyIdResp, error) {
	out := new(auth.GetFaceRecognitionCertifyIdResp)
	err := c.cc.Invoke(ctx, "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionCertifyId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faceRecognitionLogicClient) GetFaceRecognitionResult(ctx context.Context, in *auth.GetFaceRecognitionResultReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionResultResp, error) {
	out := new(auth.GetFaceRecognitionResultResp)
	err := c.cc.Invoke(ctx, "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faceRecognitionLogicClient) GetFaceRecognitionProviderNoAuth(ctx context.Context, in *auth.GetFaceRecognitionProviderReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionProviderResp, error) {
	out := new(auth.GetFaceRecognitionProviderResp)
	err := c.cc.Invoke(ctx, "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionProviderNoAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faceRecognitionLogicClient) GetFaceRecognitionCertifyIdNoAuth(ctx context.Context, in *auth.GetFaceRecognitionCertifyIdReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionCertifyIdResp, error) {
	out := new(auth.GetFaceRecognitionCertifyIdResp)
	err := c.cc.Invoke(ctx, "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionCertifyIdNoAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *faceRecognitionLogicClient) GetFaceRecognitionResultNoAuth(ctx context.Context, in *auth.GetFaceRecognitionResultReq, opts ...grpc.CallOption) (*auth.GetFaceRecognitionResultResp, error) {
	out := new(auth.GetFaceRecognitionResultResp)
	err := c.cc.Invoke(ctx, "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionResultNoAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FaceRecognitionLogicServer is the server API for FaceRecognitionLogic service.
type FaceRecognitionLogicServer interface {
	GetFaceRecognitionProvider(context.Context, *auth.GetFaceRecognitionProviderReq) (*auth.GetFaceRecognitionProviderResp, error)
	GetFaceRecognitionCertifyId(context.Context, *auth.GetFaceRecognitionCertifyIdReq) (*auth.GetFaceRecognitionCertifyIdResp, error)
	GetFaceRecognitionResult(context.Context, *auth.GetFaceRecognitionResultReq) (*auth.GetFaceRecognitionResultResp, error)
	GetFaceRecognitionProviderNoAuth(context.Context, *auth.GetFaceRecognitionProviderReq) (*auth.GetFaceRecognitionProviderResp, error)
	GetFaceRecognitionCertifyIdNoAuth(context.Context, *auth.GetFaceRecognitionCertifyIdReq) (*auth.GetFaceRecognitionCertifyIdResp, error)
	GetFaceRecognitionResultNoAuth(context.Context, *auth.GetFaceRecognitionResultReq) (*auth.GetFaceRecognitionResultResp, error)
}

func RegisterFaceRecognitionLogicServer(s *grpc.Server, srv FaceRecognitionLogicServer) {
	s.RegisterService(&_FaceRecognitionLogic_serviceDesc, srv)
}

func _FaceRecognitionLogic_GetFaceRecognitionProvider_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetFaceRecognitionProviderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionProvider(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionProvider",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionProvider(ctx, req.(*auth.GetFaceRecognitionProviderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaceRecognitionLogic_GetFaceRecognitionCertifyId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetFaceRecognitionCertifyIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionCertifyId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionCertifyId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionCertifyId(ctx, req.(*auth.GetFaceRecognitionCertifyIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaceRecognitionLogic_GetFaceRecognitionResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetFaceRecognitionResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionResult(ctx, req.(*auth.GetFaceRecognitionResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaceRecognitionLogic_GetFaceRecognitionProviderNoAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetFaceRecognitionProviderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionProviderNoAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionProviderNoAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionProviderNoAuth(ctx, req.(*auth.GetFaceRecognitionProviderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaceRecognitionLogic_GetFaceRecognitionCertifyIdNoAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetFaceRecognitionCertifyIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionCertifyIdNoAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionCertifyIdNoAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionCertifyIdNoAuth(ctx, req.(*auth.GetFaceRecognitionCertifyIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _FaceRecognitionLogic_GetFaceRecognitionResultNoAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(auth.GetFaceRecognitionResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionResultNoAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.face_recognition.FaceRecognitionLogic/GetFaceRecognitionResultNoAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FaceRecognitionLogicServer).GetFaceRecognitionResultNoAuth(ctx, req.(*auth.GetFaceRecognitionResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _FaceRecognitionLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.face_recognition.FaceRecognitionLogic",
	HandlerType: (*FaceRecognitionLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFaceRecognitionProvider",
			Handler:    _FaceRecognitionLogic_GetFaceRecognitionProvider_Handler,
		},
		{
			MethodName: "GetFaceRecognitionCertifyId",
			Handler:    _FaceRecognitionLogic_GetFaceRecognitionCertifyId_Handler,
		},
		{
			MethodName: "GetFaceRecognitionResult",
			Handler:    _FaceRecognitionLogic_GetFaceRecognitionResult_Handler,
		},
		{
			MethodName: "GetFaceRecognitionProviderNoAuth",
			Handler:    _FaceRecognitionLogic_GetFaceRecognitionProviderNoAuth_Handler,
		},
		{
			MethodName: "GetFaceRecognitionCertifyIdNoAuth",
			Handler:    _FaceRecognitionLogic_GetFaceRecognitionCertifyIdNoAuth_Handler,
		},
		{
			MethodName: "GetFaceRecognitionResultNoAuth",
			Handler:    _FaceRecognitionLogic_GetFaceRecognitionResultNoAuth_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/face_recognition/grpc_face_recognition.proto",
}

func init() {
	proto.RegisterFile("api/face_recognition/grpc_face_recognition.proto", fileDescriptor_grpc_face_recognition_f7158ac42f347119)
}

var fileDescriptor_grpc_face_recognition_f7158ac42f347119 = []byte{
	// 332 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x93, 0xcf, 0x4a, 0x03, 0x31,
	0x18, 0xc4, 0xd1, 0x62, 0x91, 0x1c, 0x83, 0x68, 0x59, 0x51, 0xdb, 0x7a, 0x6e, 0x56, 0x2a, 0x9e,
	0x3c, 0x69, 0x41, 0x11, 0x44, 0xcb, 0x1e, 0xbd, 0x94, 0x98, 0xa6, 0x69, 0x60, 0xbb, 0xdf, 0x36,
	0xfb, 0x6d, 0xad, 0x37, 0xf1, 0xe8, 0x63, 0xf8, 0x2c, 0xfe, 0xa9, 0x6f, 0x25, 0xc9, 0x96, 0x55,
	0xda, 0x6e, 0x7b, 0xe9, 0x2d, 0x64, 0x26, 0xf3, 0x83, 0x21, 0x43, 0x4e, 0x78, 0xac, 0xfd, 0x1e,
	0x17, 0xb2, 0x63, 0xa4, 0x00, 0x15, 0x69, 0xd4, 0x10, 0xf9, 0xca, 0xc4, 0xa2, 0x33, 0x7b, 0xcb,
	0x62, 0x03, 0x08, 0x74, 0x4f, 0x71, 0xc6, 0x63, 0xcd, 0x66, 0x65, 0xef, 0xc0, 0x46, 0xc9, 0x31,
	0xca, 0x28, 0xb1, 0x19, 0xf9, 0x29, 0x7b, 0xe7, 0x11, 0x9e, 0x62, 0x3f, 0x3b, 0x37, 0x27, 0x5b,
	0x64, 0xe7, 0x8a, 0x0b, 0x19, 0xfc, 0x3d, 0xbf, 0x05, 0xa5, 0x05, 0x05, 0xe2, 0x5d, 0x4b, 0x9c,
	0x91, 0xda, 0x06, 0x46, 0xba, 0x2b, 0x0d, 0xad, 0x31, 0xc5, 0x59, 0xb1, 0x1e, 0xc8, 0xa1, 0x57,
	0x5f, 0x65, 0x49, 0xe2, 0x3a, 0x79, 0x7d, 0xa9, 0x96, 0xb7, 0x3f, 0xde, 0x4a, 0x95, 0x0d, 0x6a,
	0xc8, 0xfe, 0xbc, 0xbb, 0x25, 0x0d, 0xea, 0xde, 0xf3, 0x4d, 0x97, 0x16, 0xc4, 0xe5, 0x06, 0x8b,
	0x3c, 0x5e, 0xe9, 0xc9, 0x99, 0x9f, 0x8e, 0xa9, 0x49, 0x65, 0xde, 0x1e, 0xc8, 0x24, 0x0d, 0x91,
	0x1e, 0x2d, 0x0e, 0xcb, 0x54, 0x4b, 0xab, 0x2e, 0x37, 0xe4, 0xa8, 0x2f, 0x87, 0x4a, 0x49, 0xb5,
	0xb8, 0x8c, 0x3b, 0xb8, 0x48, 0xb1, 0xbf, 0xde, 0x56, 0xbf, 0x1d, 0x76, 0x4c, 0x6a, 0x4b, 0x0a,
	0x99, 0x72, 0xd7, 0xdc, 0xed, 0xc4, 0x91, 0x81, 0x1c, 0x16, 0x95, 0x33, 0xc5, 0xae, 0xad, 0xe1,
	0x1f, 0x0b, 0xbc, 0x04, 0xb2, 0x2b, 0x60, 0xc0, 0x86, 0xe9, 0x13, 0x8f, 0x18, 0x62, 0xf6, 0xc1,
	0xed, 0x40, 0x1e, 0xee, 0x15, 0x84, 0x3c, 0x52, 0xec, 0xac, 0x89, 0xc8, 0x04, 0x0c, 0x7c, 0x27,
	0x09, 0x08, 0xfd, 0x44, 0x9a, 0x91, 0x16, 0x32, 0x71, 0xc3, 0x6b, 0xfc, 0xdb, 0x50, 0x23, 0xb4,
	0x2b, 0x38, 0x5f, 0x7c, 0xfd, 0xbe, 0x59, 0x0a, 0xda, 0xad, 0xc7, 0xb2, 0x8b, 0x39, 0xfd, 0x0d,
	0x00, 0x00, 0xff, 0xff, 0xf2, 0xcb, 0xd2, 0x01, 0xba, 0x03, 0x00, 0x00,
}
