package commission

import (
	"golang.52tt.com/pkg/log"
	"golang.org/x/net/context"
	"testing"
)

var (
	cli Client

	urlPrefix = "http://testing-tbean.ttyuyin.com/ttc/"
	// urlPrefix = "http://10.110.213.246:80/ttc/"

	// cli = NewClient(urlPrefix, "ttintegral", "c3083ec2c17fe7063fb491cd5332d1ab")
	// cli = NewClient(urlPrefix, "ttguild", "086b5d491dd4e7a6f108e6d6a2aeb946")
	// cli = NewClient(urlPrefix", "happy", "f1adc00e058d0a745f38c0444157e621")
	// 娱乐房佣金
	ttVoiceCli     = NewClient(urlPrefix, "ttvoice", "2875bd107767d6e64a035a8f17bb46d2")
	ttVoiceTaskCli = NewClient(urlPrefix, "ttvoicetask", "rHvmfhw2v7fn5DQNK3gCdR0ZQVjUInVA")
	// 语音基础佣金
	yuyinBaseCli = NewClient(urlPrefix, "yuyinbase", "34ea760afbedc44df59f872ae8179ca4")
	// 语音额外奖励佣金
	yuyinTaskCli = NewClient(urlPrefix, "yuyintask", "d974a9c06ab3e8f72cdc86a65870b14c")

	giftScoreCli = NewClient(urlPrefix, "exchangeguildpresent", "hO8oM8jA7aZ2nY4rI7dJ7dI4uL2aQ5yB")

	knightClient = NewClient(urlPrefix, "ttknight", "l9i1HDEQ1lslXT5hejKNlunhwUb66yW5")

	esportComCli   = NewClient(urlPrefix, "esportcommission", "clao6s9rxzwibvlwfe9krq93uxs0")
	esportScoreCli = NewClient(urlPrefix, "exchangeguildesport", "jkq80nvummb5pfl8kaawkoiwroez")
)

func init() {
	log.SetLevel(log.DebugLevel)
	cli = ttVoiceCli
}

func TestClient_Settlement1(t *testing.T) {
	r, err := cli.Settlement(context.Background(), 2412015, 30000, "20240001202", "Test 结算于:20220412 前的房间送礼主播金钻收益")
	if err != nil {
		t.Error("Settlement error:", err)
	} else {
		t.Log("Settlement OK", r)
	}
}

func TestClient_Balance(t *testing.T) {
	balance, total, err := cli.GetBalance(context.Background(), 2630242)
	if err != nil {
		t.Error("Balance error:", err)
	} else {
		t.Logf("Balance %d %d", balance, total)
	}
}

func TestClient_Encashment(t *testing.T) {
	err := cli.Encashment(context.Background(), &EncashInfo{
		Uid:         2630242,
		Amount:      30000,
		Remark:      "Test_Encashment",
		AcType:      AcTypePublic,
		RemitAmount: 30000,
		Remitter:    "广州趣丸网络科技有限公司",
		Receiver:    "广州趣丸网络科技有限公司",
		OrderId:     "",
		CreateTime:  "",
	})
	if err != nil {
		t.Error("Balance error:", err)
	} else {
		t.Log("Encashment OK")
	}
}

//func TestClient_SettlementAndEncashment(t *testing.T) {
//	err := cli.SettlementAndEncashment(context.Background(), &SettlementAndEncashmentRequest{
//		Uid:          2196173,
//		Amount:       10000,
//		Date:         "2019-w-48",
//		Remark:       "Test Settlement_Encashment",
//		EncashAcType: 0,
//		RemitAmount:  0,
//		Remitter:     "",
//		Receiver:     "",
//	})
//	if err != nil {
//
//		if apiErr, ok := err.(APIError); ok {
//			t.Error("SettlementAndEncashment error:", apiErr.Code(), apiErr.Message())
//			return
//		}
//
//		t.Error("SettlementAndEncashment error:", err)
//	} else {
//		t.Log("SettlementAndEncashment OK")
//	}
//}

func TestClient_GetEncashment(t *testing.T) {
	resp, err := cli.GetEncashment(context.Background(), 2424451)
	if err != nil {
		t.Error("GetEncashment error:", err)
	} else {
		t.Logf("GetEncashment total %d", resp.Total)
		for _, r := range resp.Rows {
			t.Logf("GetEncashment row %+v", r)
		}
	}
}

func TestClient_GetIdentifyInfo(t *testing.T) {
	resp, err := cli.GetIdentifyInfo(context.Background(), 2180521)
	if err != nil {
		t.Fatal(err)
	} else {
		t.Log(resp)
	}
}

func TestClient_CheckEncashBlock(t *testing.T) {
	err := cli.CheckEncashBlock(context.Background(), 2199041)
	if err != nil {
		t.Fatal(err)
	}
}

func TestClient_QueryEncashment(t *testing.T) {
	resp, err := cli.QueryEncashment(context.Background(), 4, 2197152, 1, 10, "2022-03-01 12:04:23", "2022-04-02 10:13:54")
	if err != nil {
		t.Error("QueryEncashment error:", err)
	} else {
		t.Logf("QueryEncashment total %d", resp.Total)
		for _, r := range resp.Rows {
			t.Logf("QueryEncashment row %+v", r)
		}
	}
}
