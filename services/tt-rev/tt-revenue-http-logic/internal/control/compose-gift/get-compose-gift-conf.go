package compose_gift

import (
	"context"
	"encoding/json"
	"golang.52tt.com/services/tt-rev/tt-revenue-http-logic/internal/models/gen-go"
	"net/http"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	backpackPB "golang.52tt.com/protocol/services/backpacksvr"
	"golang.52tt.com/protocol/services/userpresent"
)

func (m *giftComposeMgr) GetComposeGiftConfHandle(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*3)
	defer cancel()

	var in api.GetComposeGiftConfListReq
	e := json.Unmarshal(authInfo.Body, &in)
	if e != nil {
		log.Errorf("GetComposeGiftConfHandle failed to parse request body [%s], err %+v", string(authInfo.Body), e)
		web.ServeBadReq(w)
		return
	}

	out := &api.GetComposeGiftConfListResp{}
	uid := authInfo.UserID
	log.Debugf("GetComposeGiftConfHandle uid:%v, %+v", uid, in)

	conversionResp, err := m.conversionCli.GetAllComposeGiftConf(ctx, uid)
	if err != nil {
		log.Errorf("GetComposeGiftConfHandle fail to GetAllComposeGiftConf uid(%d) %+v err(%+v)", uid, in, err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Message(), nil)
		return
	}

	confList := conversionResp.GetConfList()
	if len(confList) == 0 {
		log.Debugf("GetComposeGiftConfHandle composeGiftConfList is empty. uid(%d) %+v", uid, in)
		_ = web.ServeAPIJson(w, out)
		return
	}

	giftIdList := make([]uint32, 0, len(confList))
	for _, conf := range confList {
		giftIdList = append(giftIdList, conf.GetGiftId())
	}

	// 获取礼物配置
	presentResp, err := m.presentCli.GetPresentConfigByIdList(ctx, uid, giftIdList,
		uint32(userpresent.ConfigListTypeBitMap_CONFIG_NOT_DELETED))
	if err != nil {
		log.Errorf("GetComposeGiftConfHandle fail to GetPresentConfigByIdList uid(%d), %+v err(%+v)", uid, in, err)
		_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Message(), nil)
		return
	}

	mapGiftConf := make(map[uint32]*userpresent.StPresentItemConfig)
	for _, itemConf := range presentResp.GetItemList() {
		mapGiftConf[itemConf.GetItemId()] = itemConf
	}

	composeConfList := make([]*api.ComposeGiftConf, 0, len(confList))
	for _, conf := range confList {
		giftConf, ok := mapGiftConf[conf.GetGiftId()]
		if !ok {
			continue
		}

		packageResp, err := m.backpackCli.GetPackageItemCfg(ctx, uid, &backpackPB.GetPackageItemCfgReq{BgId: conf.GetBgId()})
		if err != nil {
			log.Errorf("GetComposeGiftConfHandle fail to GetPackageItemCfg uid(%d), %+v err(%+v)", uid, in, err)
			_ = web.ServeAPICodeJson(w, int32(err.Code()), err.Message(), nil)
			return
		}

		// 礼物合成包裹中只能是1个礼物
		if len(packageResp.GetItemCfgList()) != 1 || packageResp.GetItemCfgList()[0].GetItemCount() != 1 {
			continue
		}

		expireTs := uint32(0)
		for _, item := range packageResp.GetItemCfgList() {
			expireTs = item.GetFinTime()
			if item.GetDynamicFinTime() != 0 {
				expireTs = uint32(time.Now().Unix() + int64(item.GetDynamicFinTime()))
			} else if item.GetMonths() > 0 {
				now := time.Now()
				t := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
				expireTs = uint32(t.AddDate(0, int(item.GetMonths()), 0).Unix())
			} else {
				log.Debugf("GetComposeGiftConfHandle get no expireTs")
			}
		}
		if expireTs != 0 && expireTs < uint32(time.Now().Unix()) {
			continue
		}

		composeConfList = append(composeConfList, &api.ComposeGiftConf{
			SyntheticId:    conf.GetId(),
			SyntheticType:  conf.GetGiftType(),
			SyntheticName:  giftConf.GetName(),
			SyntheticUrl:   giftConf.GetIconUrl(),
			SyntheticPrice: giftConf.GetPrice(),
			Index:          conf.GetSortId(),
			UpTime:         conf.GetUpTime(),
			DownTime:       conf.GetDownTime(),
			FinTime:        expireTs,
		})
	}

	out.Configs = composeConfList

	log.Debugf("GetComposeGiftConfHandle uid(%d) in(%v), out(%+v)", uid, in, out)
	_ = web.ServeAPIJson(w, out)
}
