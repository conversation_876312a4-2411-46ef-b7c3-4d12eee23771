package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/logic"          // logic startup
	bizFilter "gitlab.ttyuyin.com/bizFund/bizFund/pkg/foundation/grpc/logic/filter" // logic 业务拦截器

	pb "golang.52tt.com/protocol/app/api/tt_rev_common_logic"

	"golang.52tt.com/services/tt-rev-common-logic/internal"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/logic"
)

func main() {
	var (
		svr *internal.Server
		cfg = &internal.StartConfig{}
		err error
	)

	// config file support yaml & json, default tt-rev-common-logic.json/yaml
	if err := logic.NewLogic("tt-rev-common-logic", cfg).
		AddGrpcServer(logic.NewBuildOption().
			AddExtraUnaryInterceptor(bizFilter.FilterInterceptor()). // 添加 logic 业务过滤器
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if svr, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterTTRevCommonLogicServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
