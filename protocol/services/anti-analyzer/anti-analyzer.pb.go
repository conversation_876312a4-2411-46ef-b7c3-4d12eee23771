// Code generated by protoc-gen-go. DO NOT EDIT.
// source: anti-analyzer/anti-analyzer.proto

package anti_analyzer // import "golang.52tt.com/protocol/services/anti-analyzer"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AntiAnalyzerClient is the client API for AntiAnalyzer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AntiAnalyzerClient interface {
}

type antiAnalyzerClient struct {
	cc *grpc.ClientConn
}

func NewAntiAnalyzerClient(cc *grpc.ClientConn) AntiAnalyzerClient {
	return &antiAnalyzerClient{cc}
}

// AntiAnalyzerServer is the server API for AntiAnalyzer service.
type AntiAnalyzerServer interface {
}

func RegisterAntiAnalyzerServer(s *grpc.Server, srv AntiAnalyzerServer) {
	s.RegisterService(&_AntiAnalyzer_serviceDesc, srv)
}

var _AntiAnalyzer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "anti_analyzer.AntiAnalyzer",
	HandlerType: (*AntiAnalyzerServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "anti-analyzer/anti-analyzer.proto",
}

func init() {
	proto.RegisterFile("anti-analyzer/anti-analyzer.proto", fileDescriptor_anti_analyzer_a32962a412958778)
}

var fileDescriptor_anti_analyzer_a32962a412958778 = []byte{
	// 107 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x4c, 0xcc, 0x2b, 0xc9,
	0xd4, 0x4d, 0xcc, 0x4b, 0xcc, 0xa9, 0xac, 0x4a, 0x2d, 0xd2, 0x47, 0xe1, 0xe9, 0x15, 0x14, 0xe5,
	0x97, 0xe4, 0x0b, 0xf1, 0x82, 0x04, 0xe3, 0x61, 0x82, 0x46, 0x7c, 0x5c, 0x3c, 0x8e, 0x79, 0x25,
	0x99, 0x8e, 0x50, 0xbe, 0x93, 0x61, 0x94, 0x7e, 0x7a, 0x7e, 0x4e, 0x62, 0x5e, 0xba, 0x9e, 0xa9,
	0x51, 0x49, 0x89, 0x5e, 0x72, 0x7e, 0xae, 0x3e, 0x58, 0x5f, 0x72, 0x7e, 0x8e, 0x7e, 0x71, 0x6a,
	0x51, 0x59, 0x66, 0x72, 0x6a, 0x31, 0xaa, 0xb9, 0x49, 0x6c, 0x60, 0x05, 0xc6, 0x80, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x6b, 0x61, 0xe2, 0x5b, 0x7d, 0x00, 0x00, 0x00,
}
