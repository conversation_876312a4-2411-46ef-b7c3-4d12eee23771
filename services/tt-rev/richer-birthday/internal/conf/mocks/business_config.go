// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/richer-birthday/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/tt-rev/richer-birthday/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// CheckBlessGiftId mocks base method.
func (m *MockIBusinessConfManager) CheckBlessGiftId(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBlessGiftId", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckBlessGiftId indicates an expected call of CheckBlessGiftId.
func (mr *MockIBusinessConfManagerMockRecorder) CheckBlessGiftId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBlessGiftId", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckBlessGiftId), arg0)
}

// CheckIfInRicherWhiteList mocks base method.
func (m *MockIBusinessConfManager) CheckIfInRicherWhiteList(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfInRicherWhiteList", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckIfInRicherWhiteList indicates an expected call of CheckIfInRicherWhiteList.
func (mr *MockIBusinessConfManagerMockRecorder) CheckIfInRicherWhiteList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfInRicherWhiteList", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckIfInRicherWhiteList), arg0)
}

// CheckRicherGiftId mocks base method.
func (m *MockIBusinessConfManager) CheckRicherGiftId(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRicherGiftId", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckRicherGiftId indicates an expected call of CheckRicherGiftId.
func (mr *MockIBusinessConfManagerMockRecorder) CheckRicherGiftId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRicherGiftId", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckRicherGiftId), arg0)
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetAwardCenterAppId mocks base method.
func (m *MockIBusinessConfManager) GetAwardCenterAppId() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAwardCenterAppId")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetAwardCenterAppId indicates an expected call of GetAwardCenterAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetAwardCenterAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAwardCenterAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetAwardCenterAppId))
}

// GetBirthdayAwards mocks base method.
func (m *MockIBusinessConfManager) GetBirthdayAwards() []*conf.AwardCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBirthdayAwards")
	ret0, _ := ret[0].([]*conf.AwardCfg)
	return ret0
}

// GetBirthdayAwards indicates an expected call of GetBirthdayAwards.
func (mr *MockIBusinessConfManagerMockRecorder) GetBirthdayAwards() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBirthdayAwards", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBirthdayAwards))
}

// GetBlessGiftIds mocks base method.
func (m *MockIBusinessConfManager) GetBlessGiftIds() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlessGiftIds")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetBlessGiftIds indicates an expected call of GetBlessGiftIds.
func (mr *MockIBusinessConfManagerMockRecorder) GetBlessGiftIds() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlessGiftIds", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBlessGiftIds))
}

// GetBlessTexts mocks base method.
func (m *MockIBusinessConfManager) GetBlessTexts() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlessTexts")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetBlessTexts indicates an expected call of GetBlessTexts.
func (mr *MockIBusinessConfManagerMockRecorder) GetBlessTexts() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlessTexts", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBlessTexts))
}

// GetModifyBirthdayTestMode mocks base method.
func (m *MockIBusinessConfManager) GetModifyBirthdayTestMode() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetModifyBirthdayTestMode")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetModifyBirthdayTestMode indicates an expected call of GetModifyBirthdayTestMode.
func (mr *MockIBusinessConfManagerMockRecorder) GetModifyBirthdayTestMode() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetModifyBirthdayTestMode", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetModifyBirthdayTestMode))
}

// GetRicherGiftIds mocks base method.
func (m *MockIBusinessConfManager) GetRicherGiftIds() []uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRicherGiftIds")
	ret0, _ := ret[0].([]uint32)
	return ret0
}

// GetRicherGiftIds indicates an expected call of GetRicherGiftIds.
func (mr *MockIBusinessConfManagerMockRecorder) GetRicherGiftIds() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRicherGiftIds", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRicherGiftIds))
}

// GetRicherNobilityMinLevel mocks base method.
func (m *MockIBusinessConfManager) GetRicherNobilityMinLevel() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRicherNobilityMinLevel")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetRicherNobilityMinLevel indicates an expected call of GetRicherNobilityMinLevel.
func (mr *MockIBusinessConfManagerMockRecorder) GetRicherNobilityMinLevel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRicherNobilityMinLevel", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRicherNobilityMinLevel))
}

// GetRicherWealthMinLevel mocks base method.
func (m *MockIBusinessConfManager) GetRicherWealthMinLevel() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRicherWealthMinLevel")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetRicherWealthMinLevel indicates an expected call of GetRicherWealthMinLevel.
func (mr *MockIBusinessConfManagerMockRecorder) GetRicherWealthMinLevel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRicherWealthMinLevel", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRicherWealthMinLevel))
}

// GetTTAssistantUrl mocks base method.
func (m *MockIBusinessConfManager) GetTTAssistantUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTTAssistantUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTTAssistantUrl indicates an expected call of GetTTAssistantUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetTTAssistantUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTTAssistantUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetTTAssistantUrl))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
