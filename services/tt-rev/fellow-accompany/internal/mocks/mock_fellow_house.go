// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/fellow_house (interfaces: FellowHouseClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	fellow_house "golang.52tt.com/protocol/services/fellow_house"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockFellowHouseClient is a mock of FellowHouseClient interface.
type MockFellowHouseClient struct {
	ctrl     *gomock.Controller
	recorder *MockFellowHouseClientMockRecorder
}

// MockFellowHouseClientMockRecorder is the mock recorder for MockFellowHouseClient.
type MockFellowHouseClientMockRecorder struct {
	mock *MockFellowHouseClient
}

// NewMockFellowHouseClient creates a new mock instance.
func NewMockFellowHouseClient(ctrl *gomock.Controller) *MockFellowHouseClient {
	mock := &MockFellowHouseClient{ctrl: ctrl}
	mock.recorder = &MockFellowHouseClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFellowHouseClient) EXPECT() *MockFellowHouseClientMockRecorder {
	return m.recorder
}

// BuyFellowHouse mocks base method.
func (m *MockFellowHouseClient) BuyFellowHouse(arg0 context.Context, arg1 *fellow_house.BuyFellowHouseReq, arg2 ...grpc.CallOption) (*fellow_house.BuyFellowHouseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BuyFellowHouse", varargs...)
	ret0, _ := ret[0].(*fellow_house.BuyFellowHouseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BuyFellowHouse indicates an expected call of BuyFellowHouse.
func (mr *MockFellowHouseClientMockRecorder) BuyFellowHouse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BuyFellowHouse", reflect.TypeOf((*MockFellowHouseClient)(nil).BuyFellowHouse), varargs...)
}

// DeleteFellowHouseProduct mocks base method.
func (m *MockFellowHouseClient) DeleteFellowHouseProduct(arg0 context.Context, arg1 *fellow_house.DeleteFellowHouseProductReq, arg2 ...grpc.CallOption) (*fellow_house.DeleteFellowHouseProductResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteFellowHouseProduct", varargs...)
	ret0, _ := ret[0].(*fellow_house.DeleteFellowHouseProductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteFellowHouseProduct indicates an expected call of DeleteFellowHouseProduct.
func (mr *MockFellowHouseClientMockRecorder) DeleteFellowHouseProduct(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFellowHouseProduct", reflect.TypeOf((*MockFellowHouseClient)(nil).DeleteFellowHouseProduct), varargs...)
}

// GetConsumeOrderIds mocks base method.
func (m *MockFellowHouseClient) GetConsumeOrderIds(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq, arg2 ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeOrderIds indicates an expected call of GetConsumeOrderIds.
func (mr *MockFellowHouseClientMockRecorder) GetConsumeOrderIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeOrderIds", reflect.TypeOf((*MockFellowHouseClient)(nil).GetConsumeOrderIds), varargs...)
}

// GetConsumeTotalCount mocks base method.
func (m *MockFellowHouseClient) GetConsumeTotalCount(arg0 context.Context, arg1 *reconcile_v2.TimeRangeReq, arg2 ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsumeTotalCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsumeTotalCount indicates an expected call of GetConsumeTotalCount.
func (mr *MockFellowHouseClientMockRecorder) GetConsumeTotalCount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsumeTotalCount", reflect.TypeOf((*MockFellowHouseClient)(nil).GetConsumeTotalCount), varargs...)
}

// GetFellowHouseInuseListByCps mocks base method.
func (m *MockFellowHouseClient) GetFellowHouseInuseListByCps(arg0 context.Context, arg1 *fellow_house.GetFellowHouseInuseListByCpsReq, arg2 ...grpc.CallOption) (*fellow_house.GetFellowHouseInuseListByCpsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowHouseInuseListByCps", varargs...)
	ret0, _ := ret[0].(*fellow_house.GetFellowHouseInuseListByCpsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowHouseInuseListByCps indicates an expected call of GetFellowHouseInuseListByCps.
func (mr *MockFellowHouseClientMockRecorder) GetFellowHouseInuseListByCps(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowHouseInuseListByCps", reflect.TypeOf((*MockFellowHouseClient)(nil).GetFellowHouseInuseListByCps), varargs...)
}

// GetFellowHouseProductById mocks base method.
func (m *MockFellowHouseClient) GetFellowHouseProductById(arg0 context.Context, arg1 *fellow_house.GetFellowHouseProductByIdReq, arg2 ...grpc.CallOption) (*fellow_house.GetFellowHouseProductByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowHouseProductById", varargs...)
	ret0, _ := ret[0].(*fellow_house.GetFellowHouseProductByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowHouseProductById indicates an expected call of GetFellowHouseProductById.
func (mr *MockFellowHouseClientMockRecorder) GetFellowHouseProductById(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowHouseProductById", reflect.TypeOf((*MockFellowHouseClient)(nil).GetFellowHouseProductById), varargs...)
}

// GetFellowHouseProductByIds mocks base method.
func (m *MockFellowHouseClient) GetFellowHouseProductByIds(arg0 context.Context, arg1 *fellow_house.GetFellowHouseProductByIdsReq, arg2 ...grpc.CallOption) (*fellow_house.GetFellowHouseProductByIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowHouseProductByIds", varargs...)
	ret0, _ := ret[0].(*fellow_house.GetFellowHouseProductByIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowHouseProductByIds indicates an expected call of GetFellowHouseProductByIds.
func (mr *MockFellowHouseClientMockRecorder) GetFellowHouseProductByIds(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowHouseProductByIds", reflect.TypeOf((*MockFellowHouseClient)(nil).GetFellowHouseProductByIds), varargs...)
}

// GetFellowHouseProductList mocks base method.
func (m *MockFellowHouseClient) GetFellowHouseProductList(arg0 context.Context, arg1 *fellow_house.GetFellowHouseProductListReq, arg2 ...grpc.CallOption) (*fellow_house.GetFellowHouseProductListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFellowHouseProductList", varargs...)
	ret0, _ := ret[0].(*fellow_house.GetFellowHouseProductListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFellowHouseProductList indicates an expected call of GetFellowHouseProductList.
func (mr *MockFellowHouseClientMockRecorder) GetFellowHouseProductList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFellowHouseProductList", reflect.TypeOf((*MockFellowHouseClient)(nil).GetFellowHouseProductList), varargs...)
}

// GetUserFellowHouseInuseList mocks base method.
func (m *MockFellowHouseClient) GetUserFellowHouseInuseList(arg0 context.Context, arg1 *fellow_house.GetUserFellowHouseInuseListReq, arg2 ...grpc.CallOption) (*fellow_house.GetUserFellowHouseInuseListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserFellowHouseInuseList", varargs...)
	ret0, _ := ret[0].(*fellow_house.GetUserFellowHouseInuseListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFellowHouseInuseList indicates an expected call of GetUserFellowHouseInuseList.
func (mr *MockFellowHouseClientMockRecorder) GetUserFellowHouseInuseList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFellowHouseInuseList", reflect.TypeOf((*MockFellowHouseClient)(nil).GetUserFellowHouseInuseList), varargs...)
}

// GetUserFellowHouseList mocks base method.
func (m *MockFellowHouseClient) GetUserFellowHouseList(arg0 context.Context, arg1 *fellow_house.GetUserFellowHouseListReq, arg2 ...grpc.CallOption) (*fellow_house.GetUserFellowHouseListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserFellowHouseList", varargs...)
	ret0, _ := ret[0].(*fellow_house.GetUserFellowHouseListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFellowHouseList indicates an expected call of GetUserFellowHouseList.
func (mr *MockFellowHouseClientMockRecorder) GetUserFellowHouseList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFellowHouseList", reflect.TypeOf((*MockFellowHouseClient)(nil).GetUserFellowHouseList), varargs...)
}

// GiveFellowHouse mocks base method.
func (m *MockFellowHouseClient) GiveFellowHouse(arg0 context.Context, arg1 *fellow_house.GiveFellowHouseReq, arg2 ...grpc.CallOption) (*fellow_house.GiveFellowHouseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GiveFellowHouse", varargs...)
	ret0, _ := ret[0].(*fellow_house.GiveFellowHouseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GiveFellowHouse indicates an expected call of GiveFellowHouse.
func (mr *MockFellowHouseClientMockRecorder) GiveFellowHouse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveFellowHouse", reflect.TypeOf((*MockFellowHouseClient)(nil).GiveFellowHouse), varargs...)
}

// SearchFellowHouseProduct mocks base method.
func (m *MockFellowHouseClient) SearchFellowHouseProduct(arg0 context.Context, arg1 *fellow_house.SearchFellowHouseProductReq, arg2 ...grpc.CallOption) (*fellow_house.SearchFellowHouseProductResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchFellowHouseProduct", varargs...)
	ret0, _ := ret[0].(*fellow_house.SearchFellowHouseProductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchFellowHouseProduct indicates an expected call of SearchFellowHouseProduct.
func (mr *MockFellowHouseClientMockRecorder) SearchFellowHouseProduct(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchFellowHouseProduct", reflect.TypeOf((*MockFellowHouseClient)(nil).SearchFellowHouseProduct), varargs...)
}

// SetUserFellowHouseInuse mocks base method.
func (m *MockFellowHouseClient) SetUserFellowHouseInuse(arg0 context.Context, arg1 *fellow_house.SetUserFellowHouseInuseReq, arg2 ...grpc.CallOption) (*fellow_house.SetUserFellowHouseInuseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserFellowHouseInuse", varargs...)
	ret0, _ := ret[0].(*fellow_house.SetUserFellowHouseInuseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserFellowHouseInuse indicates an expected call of SetUserFellowHouseInuse.
func (mr *MockFellowHouseClientMockRecorder) SetUserFellowHouseInuse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFellowHouseInuse", reflect.TypeOf((*MockFellowHouseClient)(nil).SetUserFellowHouseInuse), varargs...)
}

// UpdateFellowHouseProduct mocks base method.
func (m *MockFellowHouseClient) UpdateFellowHouseProduct(arg0 context.Context, arg1 *fellow_house.UpdateFellowHouseProductReq, arg2 ...grpc.CallOption) (*fellow_house.UpdateFellowHouseProductResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateFellowHouseProduct", varargs...)
	ret0, _ := ret[0].(*fellow_house.UpdateFellowHouseProductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFellowHouseProduct indicates an expected call of UpdateFellowHouseProduct.
func (mr *MockFellowHouseClientMockRecorder) UpdateFellowHouseProduct(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFellowHouseProduct", reflect.TypeOf((*MockFellowHouseClient)(nil).UpdateFellowHouseProduct), varargs...)
}
