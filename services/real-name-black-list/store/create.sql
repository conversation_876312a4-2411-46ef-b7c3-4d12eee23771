
CREATE TABLE IF NOT EXISTS real_name_black_list (
  id VARCHAR(64)  NOT NULL COMMENT '身份证号',
  end_time INT UNSIGNED NOT NULL COMMENT '结束时间戳',
  begin_time INT UNSIGNED NOT NULL COMMENT '开始时间戳',
  reason VARCHAR(64) COMMENT '封禁原因',
  uid INT UNSIGNED NOT NULL COMMENT 'uid',
  PRIMARY KEY (id,uid)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT='实名黑名单';


CREATE TABLE IF NOT EXISTS suspected_black_list (
  id VARCHAR(64) NOT NULL COMMENT '身份证号',
  note_time INT UNSIGNED NOT NULL COMMENT '记录时间',
  ttid VARCHAR(64)  COMMENT 'TTID',
  nickname VARCHAR(64) COMMENT '用户昵称',
  device_id VARCHAR(64) COMMENT '设备号',
  uid INT UNSIGNED NOT NULL COMMENT 'UID',
  PRIMARY KEY (id,note_time)
) ENGINE = INNODB DEFAULT CHARSET = utf8 COMMENT='疑似黑名单记录';