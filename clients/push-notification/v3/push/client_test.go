package pushV3

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"testing"
	"time"

	"golang.52tt.com/pkg/log"
	pushServer "golang.52tt.com/protocol/services/push-notification/v3"
	"google.golang.org/grpc"
)

var testClient *Client

func init() {
	log.SetLevel(log.DebugLevel)
	var err error
	testClient, err = NewClient(grpc.WithBlock())
	if err != nil {
		panic(err)
	}
}

func TestPushExample(t *testing.T) {

	newTaskResp, err := testClient.NewTask(context.Background(), &pushServer.NewTaskReq{
		Notification: &pushServer.Notification{
			JumpUrl: "tt://navigation/UgcPostDetail/1/5c9b2e705dfcac14edcab990",
			Extra: map[string]string{
				"push_type": "0", "opt_user": "0",
			},
			Labels: []string{"aaaa", "bbb", "cccc"},
		},
		UidList: []uint32{2212659, 2196581, 1959278},
		//通知标题，假设可以查到用户昵称为abc，用户会显示标题为Hi~abc，否则使用备选显示标题为通知
		NotificationTitle: []*pushServer.NotificationText{
			{Text: []*pushServer.NotificationElement{{Text: "Hi~"}, {Type: pushServer.ElementType_NICKNAME}}},
			{Text: []*pushServer.NotificationElement{{Text: "通知"}}}, //备选
		},
		//通知标题，假设可以查到用户昵称为abc，用户会显示内容为恭喜您，abc，获得了奥斯卡金像奖，否则使用备选显示内容为恭喜您获得了奥斯卡金像奖
		NotificationContent: []*pushServer.NotificationText{
			{Text: []*pushServer.NotificationElement{{Text: "恭喜您，"}, {Type: pushServer.ElementType_NICKNAME}, {Text: "，获得了奥斯卡金像奖"}}},
			{Text: []*pushServer.NotificationElement{{Text: "恭喜您获得了奥斯卡金像奖"}}}, //备选
		},
	})
	if err != nil {
		t.Error(err)
		return
	}

	fmt.Println("task id", newTaskResp.TaskId)
}

func TestPushFastOne(t *testing.T) {
	var n = rand.New(rand.NewSource(time.Now().UnixNano())).Intn(10000)
	newTaskResp, err := testClient.NewTask(context.Background(), &pushServer.NewTaskReq{
		Notification: &pushServer.Notification{
			Title:   "证券时报快讯 " + time.Now().Format("2006-01-02 15:04:05"),
			Content: "TT股价将要突破" + strconv.Itoa(n+1) + "元大关，三日涨幅达29.5%，成为国内机构评价最优质蓝筹股！",
			JumpUrl: "tt://navigation/UgcPostDetail/1/5c9b2e705dfcac14edcab990",
			Extra: map[string]string{
				"push_type": "0", "opt_user": "0",
			},
		},
		UidList: []uint32{2198917, 2215133, 2199914, 2211906, 2207529, 2227112, 2203994, 2206594, 2224501, 2200078, 2212459, 2205359, 2207877, 2201960, 2206284, 2211906, 2199795, 500001},
		NotificationTitle: []*pushServer.NotificationText{
			{Text: []*pushServer.NotificationElement{{Text: "时报标题快讯，"}, {Type: pushServer.ElementType_NICKNAME}, {Type: pushServer.ElementType_LBS}}},
			{Text: []*pushServer.NotificationElement{{Text: "时报标题备选"}}},
		},
		NotificationContent: []*pushServer.NotificationText{
			{Text: []*pushServer.NotificationElement{{Text: "时报内容，"}, {Type: pushServer.ElementType_NICKNAME}, {Type: pushServer.ElementType_LBS}}},
			{Text: []*pushServer.NotificationElement{{Text: "时报内容备选"}}},
		},
	})
	if err != nil {
		t.Error(err)
		return
	}

	fmt.Println("task id", newTaskResp.TaskId)
}

func TestPushDirectTask(t *testing.T) {
	var n = rand.New(rand.NewSource(time.Now().UnixNano())).Intn(10000)
	newTaskResp, err := testClient.SendPushTask(context.Background(), &pushServer.SendPushTaskReq{
		Notification: &pushServer.Notification{
			Title:   "证券时报快讯 " + time.Now().Format("2006-01-02 15:04:05"),
			Content: "TT股价将要突破" + strconv.Itoa(n+1) + "元大关，三日涨幅达29.5%，成为国内机构评价最优质蓝筹股！",
			JumpUrl: "tt://navigation/UgcPostDetail/1/5c9b2e705dfcac14edcab990",
			Extra:   map[string]string{},
		},
		UidList: []uint32{2215133, 2199914, 2211906, 2207529, 2227112, 2203994, 2206594, 2224501, 2200078, 2212459, 2205359, 2207877, 2201960, 2206284, 2211906, 2199795, 500001},
	})
	if err != nil {
		t.Error(err)
		return
	}

	fmt.Println("task id", newTaskResp.TaskId)
}

// go test -test.run TestSendPushTask
func TestSendPushTask(t *testing.T) {

	p := &pushServer.SendPushTaskReq{
		Notification: &pushServer.Notification{
			Title:   "asdasd",
			Content: "asdasd",
			JumpUrl: "tt://navigation/UgcPostDetail/1/5c9b2e705dfcac14edcab990",
			Extra:   map[string]string{},
		},
		UidList: []uint32{2186719},
	}

	testClient.AddExtParams(p.Notification, pushServer.PushType_BeVisitorCount, pushServer.OptUser_Human)

	newTaskResp, err := testClient.SendPushTask(context.Background(), p)
	if err != nil {
		t.Error(err)
		return
	}

	fmt.Println("task id", newTaskResp.TaskId)
}

func TestNewPushTask(t *testing.T) {
	var n = rand.New(rand.NewSource(time.Now().UnixNano())).Intn(10000)
	newTaskResp, err := testClient.NewTask(context.Background(), &pushServer.NewTaskReq{
		Notification: &pushServer.Notification{
			Title:   "证券时报快讯 " + time.Now().Format("2006-01-02 15:04:05"),
			Content: "TT股价将要突破" + strconv.Itoa(n+1) + "元大关，三日涨幅达29.5%，成为国内机构评价最优质蓝筹股！",
			JumpUrl: "tt://navigation/UgcPostDetail/1/5c9b2e705dfcac14edcab990",
			Extra:   map[string]string{},
		},
	})
	if err != nil {
		t.Error(err)
		return
	}

	fmt.Println("task id", newTaskResp.TaskId)

	for i := 0; i < 5000; i++ {
		var uidList []uint32
		for n := 1; n < 30000; n++ {
			uidList = append(uidList, uint32(i*500000+n))
		}
		_, err = testClient.InitPartOfTask(context.Background(), &pushServer.InitPartOfTaskReq{
			TaskId:   newTaskResp.TaskId,
			UidList:  uidList,
			Sequence: uint32(i),
		})
		if err != nil {
			t.Error(err)
			return
		}
		fmt.Println("part", i)
	}

	_, err = testClient.SaveTask(context.Background(), &pushServer.SaveTaskReq{
		TaskId: newTaskResp.TaskId,
		PushAt: uint64(time.Now().Unix()),
	})
	if err != nil {
		t.Error(err)
		return
	}

}