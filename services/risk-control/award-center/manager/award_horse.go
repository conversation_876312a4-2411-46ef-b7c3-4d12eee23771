package manager

import (
	"context"
	channel_personalization "golang.52tt.com/clients/channel-personalization"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/risk-control/award-center"
	"golang.52tt.com/services/risk-control/award-center/mysql"
	"time"
)

type AwardHorse struct {
	Cli *channel_personalization.Client
}

func (a *AwardHorse) GenAwardExtraJsonContent(req *pb.AwardReq) (string, error) {
	return "", nil
}

func (a *AwardHorse) Award(ctx context.Context, order *mysql.AwardOrder) error {
	decoration := &channel_personalization.Decoration{
		Id:   order.GiftId,
		Type: channel_personalization.ChannelEnterSpecialEffectType,
		Detail: &channel_personalization.DecorationDetail{
			ChannelEnterSpecialEffect: &channel_personalization.ChannelEnterSpecialEffect{},
		},
	}

	ud := &channel_personalization.UserDecoration{
		OrderId:       order.OrderId,
		Uid:           order.TargetUid,
		Decoration:    decoration,
		EffectBegin:   uint64(time.Now().Unix()),
		EffectEnd:     uint64(uint32(time.Now().Unix()) + calcAwardDuration(order)),
		GrantAt:       uint64(time.Now().Unix()),
		GrantReason:   "add",
		GrantOperator: "add-center",
	}

	err := a.Cli.GrantDecorationV2WithOrderInfo(ctx, ud, true, order.OutsideTime.Unix(), order.SourceType)
	if err != nil {
		log.Errorf("AwardHorse GrantDecoration fail err:%v uid:%v", err, order.TargetUid)
		return err
	}

	log.Infof("AwardHorse Award order %+v", order)
	return nil
}

func (a *AwardHorse) AutoWear(ctx context.Context, order *mysql.AwardOrder) error {
    ty := uint32(channel_personalization.ChannelEnterSpecialEffectType)
    _, err := a.Cli.ActivateUserDecoration(ctx, order.TargetUid, ty, order.GiftId)
    if err != nil {
        log.Errorf("AwardHorse AutoWear ActivateUserDecoration fail err:%v uid:%v", err, order.TargetUid)
        return err
    }

    log.Infof("AwardHorse AutoWear order %+v", order)
    return nil
}

func (a *AwardHorse) RollbackAward(ctx context.Context, order *mysql.AwardOrder) error {
	decoration := &channel_personalization.Decoration{
		Id:   order.GiftId,
		Type: channel_personalization.ChannelEnterSpecialEffectType,
		Detail: &channel_personalization.DecorationDetail{
			ChannelEnterSpecialEffect: &channel_personalization.ChannelEnterSpecialEffect{},
		},
	}

	ud := &channel_personalization.UserDecoration{
		OrderId:       order.OrderId,
		Uid:           order.TargetUid,
		Decoration:    decoration,
		EffectBegin:   uint64(time.Now().Unix()),
		EffectEnd:     uint64(uint32(time.Now().Unix()) - order.HoldingDay*24*3600),
		GrantAt:       uint64(time.Now().Unix()),
		GrantReason:   "rollback",
		GrantOperator: "award-center",
	}

	err := a.Cli.GrantDecorationV2(ctx, ud, true)
	if err != nil {
		log.Errorf("AwardHorse RollbackAward GrantDecoration fail err:%v uid:%v", err, order.TargetUid)
		return err
	}

	log.Infof("AwardHorse RollbackAward order %+v", order)
	return nil
}

func (a *AwardHorse) IsRetryableError(err error) bool {

	if e, ok := err.(protocol.ServerError); ok {
		switch e.Code() {
		case status.ErrChannelPslGrantingOrderIdDuplicate:
			return false
		}
	}
	return true
}
