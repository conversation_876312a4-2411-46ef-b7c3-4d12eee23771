// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/find_friends/iclient.go

// Package FindFriends is a generated GoMock package.
package FindFriends

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	FindFriends "golang.52tt.com/clients/find_friends"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	FindFriends0 "golang.52tt.com/protocol/services/find_friends"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddAFKLiked mocks base method.
func (m *MockIClient) AddAFKLiked(ctx context.Context, userID uint32) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAFKLiked", ctx, userID)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddAFKLiked indicates an expected call of AddAFKLiked.
func (mr *MockIClientMockRecorder) AddAFKLiked(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAFKLiked", reflect.TypeOf((*MockIClient)(nil).AddAFKLiked), ctx, userID)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// CancelQuickMatch mocks base method.
func (m *MockIClient) CancelQuickMatch(ctx context.Context, userID uint32, sessionID string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelQuickMatch", ctx, userID, sessionID)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// CancelQuickMatch indicates an expected call of CancelQuickMatch.
func (mr *MockIClientMockRecorder) CancelQuickMatch(ctx, userID, sessionID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelQuickMatch", reflect.TypeOf((*MockIClient)(nil).CancelQuickMatch), ctx, userID, sessionID)
}

// CheckBeenLiked mocks base method.
func (m *MockIClient) CheckBeenLiked(ctx context.Context, userID uint32, testUserIDs []uint32) ([]uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckBeenLiked", ctx, userID, testUserIDs)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckBeenLiked indicates an expected call of CheckBeenLiked.
func (mr *MockIClientMockRecorder) CheckBeenLiked(ctx, userID, testUserIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckBeenLiked", reflect.TypeOf((*MockIClient)(nil).CheckBeenLiked), ctx, userID, testUserIDs)
}

// CheckLiked mocks base method.
func (m *MockIClient) CheckLiked(ctx context.Context, userID, targetUserID uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLiked", ctx, userID, targetUserID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CheckLiked indicates an expected call of CheckLiked.
func (mr *MockIClientMockRecorder) CheckLiked(ctx, userID, targetUserID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLiked", reflect.TypeOf((*MockIClient)(nil).CheckLiked), ctx, userID, targetUserID)
}

// ClearAFKLiked mocks base method.
func (m *MockIClient) ClearAFKLiked(ctx context.Context, userID uint32) (bool, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearAFKLiked", ctx, userID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ClearAFKLiked indicates an expected call of ClearAFKLiked.
func (mr *MockIClientMockRecorder) ClearAFKLiked(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearAFKLiked", reflect.TypeOf((*MockIClient)(nil).ClearAFKLiked), ctx, userID)
}

// ClearMutualLikesMe mocks base method.
func (m *MockIClient) ClearMutualLikesMe(ctx context.Context, userID_A, userID_B uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearMutualLikesMe", ctx, userID_A, userID_B)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ClearMutualLikesMe indicates an expected call of ClearMutualLikesMe.
func (mr *MockIClientMockRecorder) ClearMutualLikesMe(ctx, userID_A, userID_B interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearMutualLikesMe", reflect.TypeOf((*MockIClient)(nil).ClearMutualLikesMe), ctx, userID_A, userID_B)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// CreateOrUpdateUser mocks base method.
func (m *MockIClient) CreateOrUpdateUser(ctx context.Context, userInfo *FindFriends0.UserInfo, clearPlayingGames bool) (*FindFriends0.UserInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateUser", ctx, userInfo, clearPlayingGames)
	ret0, _ := ret[0].(*FindFriends0.UserInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// CreateOrUpdateUser indicates an expected call of CreateOrUpdateUser.
func (mr *MockIClientMockRecorder) CreateOrUpdateUser(ctx, userInfo, clearPlayingGames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateUser", reflect.TypeOf((*MockIClient)(nil).CreateOrUpdateUser), ctx, userInfo, clearPlayingGames)
}

// GetFreeLikeQuota mocks base method.
func (m *MockIClient) GetFreeLikeQuota(ctx context.Context, userID uint32, t time.Time) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFreeLikeQuota", ctx, userID, t)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetFreeLikeQuota indicates an expected call of GetFreeLikeQuota.
func (mr *MockIClientMockRecorder) GetFreeLikeQuota(ctx, userID, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFreeLikeQuota", reflect.TypeOf((*MockIClient)(nil).GetFreeLikeQuota), ctx, userID, t)
}

// GetQuickMatchStatistics mocks base method.
func (m *MockIClient) GetQuickMatchStatistics(ctx context.Context, beginTime, endTime time.Time) (uint32, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQuickMatchStatistics", ctx, beginTime, endTime)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GetQuickMatchStatistics indicates an expected call of GetQuickMatchStatistics.
func (mr *MockIClientMockRecorder) GetQuickMatchStatistics(ctx, beginTime, endTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickMatchStatistics", reflect.TypeOf((*MockIClient)(nil).GetQuickMatchStatistics), ctx, beginTime, endTime)
}

// GetRecommendUsers mocks base method.
func (m *MockIClient) GetRecommendUsers(ctx context.Context, userID, count uint32, sessionID string, filterUserIDs []uint32, genderFilter FindFriends.GenderFilter, location *FindFriends0.Location) ([]*FindFriends0.UserInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendUsers", ctx, userID, count, sessionID, filterUserIDs, genderFilter, location)
	ret0, _ := ret[0].([]*FindFriends0.UserInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetRecommendUsers indicates an expected call of GetRecommendUsers.
func (mr *MockIClientMockRecorder) GetRecommendUsers(ctx, userID, count, sessionID, filterUserIDs, genderFilter, location interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendUsers", reflect.TypeOf((*MockIClient)(nil).GetRecommendUsers), ctx, userID, count, sessionID, filterUserIDs, genderFilter, location)
}

// GetTodayLikedCount mocks base method.
func (m *MockIClient) GetTodayLikedCount(ctx context.Context, userID uint32, t time.Time) (uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTodayLikedCount", ctx, userID, t)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetTodayLikedCount indicates an expected call of GetTodayLikedCount.
func (mr *MockIClientMockRecorder) GetTodayLikedCount(ctx, userID, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTodayLikedCount", reflect.TypeOf((*MockIClient)(nil).GetTodayLikedCount), ctx, userID, t)
}

// GetUser mocks base method.
func (m *MockIClient) GetUser(ctx context.Context, userID uint32) (*FindFriends0.UserInfo, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", ctx, userID)
	ret0, _ := ret[0].(*FindFriends0.UserInfo)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockIClientMockRecorder) GetUser(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockIClient)(nil).GetUser), ctx, userID)
}

// GrantFreeLikeQuota mocks base method.
func (m *MockIClient) GrantFreeLikeQuota(ctx context.Context, userID, quota uint32, t time.Time) (bool, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrantFreeLikeQuota", ctx, userID, quota, t)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// GrantFreeLikeQuota indicates an expected call of GrantFreeLikeQuota.
func (mr *MockIClientMockRecorder) GrantFreeLikeQuota(ctx, userID, quota, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantFreeLikeQuota", reflect.TypeOf((*MockIClient)(nil).GrantFreeLikeQuota), ctx, userID, quota, t)
}

// Like mocks base method.
func (m *MockIClient) Like(ctx context.Context, userID uint32, throws []uint32, like uint32) (*FindFriends0.LikeResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Like", ctx, userID, throws, like)
	ret0, _ := ret[0].(*FindFriends0.LikeResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// Like indicates an expected call of Like.
func (mr *MockIClientMockRecorder) Like(ctx, userID, throws, like interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Like", reflect.TypeOf((*MockIClient)(nil).Like), ctx, userID, throws, like)
}

// PunishQuickMatchDeserter mocks base method.
func (m *MockIClient) PunishQuickMatchDeserter(ctx context.Context, userID, channelID, onlineSeconds uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PunishQuickMatchDeserter", ctx, userID, channelID, onlineSeconds)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// PunishQuickMatchDeserter indicates an expected call of PunishQuickMatchDeserter.
func (mr *MockIClientMockRecorder) PunishQuickMatchDeserter(ctx, userID, channelID, onlineSeconds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PunishQuickMatchDeserter", reflect.TypeOf((*MockIClient)(nil).PunishQuickMatchDeserter), ctx, userID, channelID, onlineSeconds)
}

// QuickMatchKeepAlive mocks base method.
func (m *MockIClient) QuickMatchKeepAlive(ctx context.Context, userID uint32, sessionID string) (bool, uint32, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuickMatchKeepAlive", ctx, userID, sessionID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(protocol.ServerError)
	return ret0, ret1, ret2
}

// QuickMatchKeepAlive indicates an expected call of QuickMatchKeepAlive.
func (mr *MockIClientMockRecorder) QuickMatchKeepAlive(ctx, userID, sessionID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuickMatchKeepAlive", reflect.TypeOf((*MockIClient)(nil).QuickMatchKeepAlive), ctx, userID, sessionID)
}

// ReportActive mocks base method.
func (m *MockIClient) ReportActive(ctx context.Context, userID uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportActive", ctx, userID)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ReportActive indicates an expected call of ReportActive.
func (mr *MockIClientMockRecorder) ReportActive(ctx, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportActive", reflect.TypeOf((*MockIClient)(nil).ReportActive), ctx, userID)
}

// StartQuickMatch mocks base method.
func (m *MockIClient) StartQuickMatch(ctx context.Context, userID, userGender uint32, gameName string, options map[string]uint32, expiration time.Duration) (string, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartQuickMatch", ctx, userID, userGender, gameName, options, expiration)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartQuickMatch indicates an expected call of StartQuickMatch.
func (mr *MockIClientMockRecorder) StartQuickMatch(ctx, userID, userGender, gameName, options, expiration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartQuickMatch", reflect.TypeOf((*MockIClient)(nil).StartQuickMatch), ctx, userID, userGender, gameName, options, expiration)
}

// StartQuickMatchNew mocks base method.
func (m *MockIClient) StartQuickMatchNew(ctx context.Context, userID, userGender uint32, games []*FindFriends0.QuickMatchGame, expiration time.Duration, supplementChannelId, supplementNum uint32) (*FindFriends0.StartQuickMatchResponse, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartQuickMatchNew", ctx, userID, userGender, games, expiration, supplementChannelId, supplementNum)
	ret0, _ := ret[0].(*FindFriends0.StartQuickMatchResponse)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// StartQuickMatchNew indicates an expected call of StartQuickMatchNew.
func (mr *MockIClientMockRecorder) StartQuickMatchNew(ctx, userID, userGender, games, expiration, supplementChannelId, supplementNum interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartQuickMatchNew", reflect.TypeOf((*MockIClient)(nil).StartQuickMatchNew), ctx, userID, userGender, games, expiration, supplementChannelId, supplementNum)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UpdatePhoto mocks base method.
func (m *MockIClient) UpdatePhoto(ctx context.Context, userID, index uint32, photoURL string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePhoto", ctx, userID, index, photoURL)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// UpdatePhoto indicates an expected call of UpdatePhoto.
func (mr *MockIClientMockRecorder) UpdatePhoto(ctx, userID, index, photoURL interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePhoto", reflect.TypeOf((*MockIClient)(nil).UpdatePhoto), ctx, userID, index, photoURL)
}
