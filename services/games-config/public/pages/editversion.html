<div class="fanbox">
    <h3>添加/修改记录</h3>
    <form id="add_form">
        <table width="100%" cellpadding="0" cellspacing="0" class="post_table">
            <tr>
                <td align="right">gameId：</td>
                <td><input type="text" class="input2" id="gameId" name="gameId" maxlength="30" /></td>
            </tr>
            <tr>
                <td align="right">version：</td>
                <td><input type="text" class="input2" id="version" name="version" maxlength="30" /></td>
            </tr>
            <tr>
                <td align="right">engineVersion：</td>
                <td><input type="text" class="input2" id="engineVersion" name="engineVersion" maxlength="30" /></td>
            </tr>
            <tr>
                <td width="30%" align="right"><label>engineType：</label></td>
                <td><select id="engineType" maxlength="30">
                    </select></td>
            </tr>
            <tr>
                <td align="right">packagePath：</td>
                <td><input type="text" class="input2" id="packagePath" name="packagePath" maxlength="200" /></td>
            </tr>
            <tr>
                <td align="right">picUrl：</td>
                <td><input type="text" class="input2" id="picUrl" name="picUrl" maxlength="200" /></td>
            </tr>
            <tr>
                <td align="right">gameUrl：</td>
                <td><input type="text" class="input2" id="gameUrl" name="gameUrl" maxlength="400" /></td>
            </tr>
            <tr>
                <td align="right">resUrl：</td>
                <td><input type="text" class="input2" id="resUrl" name="resUrl" maxlength="400" /></td>
            </tr>
            <tr>
                <td align="right">extraProperty：</td>
                <td><input type="text" class="input2" id="extraProperty" name="extraProperty" maxlength="200000" /></td>
            </tr>
            <tr>
                <td align="right">packageMD5：</td>
                <td><input type="text" class="input2" id="packageMD5" name="packageMD5" maxlength="200" /></td>
            </tr>
            <tr>
                <td align="right">resMD5：</td>
                <td><input type="text" class="input2" id="resMD5" name="resMD5" maxlength="200" /></td>
            </tr>
             <tr>
                <td align="right">stateTemplate：</td>
                <td><input type="text" class="input2" id="stateTemplate" name="stateTemplate" maxlength="200" /></td>
            </tr>
            <tr>
                <td height="60">&nbsp;</td>
                <td><input type="submit" class="btn" value="提交" /> <input type="button" class="btn" value="取消"
                        onclick="$.fancybox.close();" /></td>
            </tr>
        </table>
    </form>
</div>
<script src="../js/common/jquery.form.js" type="text/javascript"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $("#add_form").height(480);
        $("#add_form").css({ 'overflow': 'hidden' });
        $("#add_form").find('table').hide();
        setTimeout("show()", 100);
        setMasterParams()

        $('#add_form').submit(function () {
            $.ajax({
                url: baseurl + "config/editVersionList",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                type: 'POST',
                data: form2JsonString(),
                beforeSubmit: validate,
                success: function (msg) {
                    if (msg.code == 0) {
                        $.fancybox.close();
                        $().message("成功添加/修改");
                    } else {
                        alert(msg.result);
                    }
                }
            })
            return false;
        });
    });

    function show() {
        var gr = $("#list").jqGrid('getGridParam', 'selrow');
        if (gr != null) {
            var rowData = $("#list").jqGrid('getRowData', gr);
            if (versionActionType == 2) {
                $('#gameId').val(rowData.gameid).attr("disabled", "disabled");
                $('#version').val(rowData.version).attr("disabled", "disabled");
                $('#engineVersion').val(rowData.engineversion);
                console.log("rowData.enginetype: ", rowData.enginetype)
                // if (rowData.enginetype == 1) {
                //     $('#engineType').val("COCOS") 
                // } else if (rowData.enginetype == 2) {
                //     $('#engineType').val("WEB")  
                // }
                $('#engineType').val(rowData.enginetype);
                $('#packagePath').val(rowData.packagepath);
                $('#picUrl').val(rowData.picurl);
                $('#gameUrl').val(rowData.gameurl);
                $('#resUrl').val(rowData.resurl);
                $('#extraProperty').val(rowData.extraproperty);
                $('#packageMD5').val(rowData.packagemd5);
                $('#resMD5').val(rowData.resmd5);
                 $('#stateTemplate').val(rowData.statetemplate);
            } else {
            }
        } else {
        }
        $("#add_form").find('table').show();
    }
    function validate(formData, jqForm, options) {
        for (var i = 0; i < formData.length; i++) {
            if (!formData[i].value) {
                $().message("请输入完整相关信息");
                return false;
            }
        }
        $().message("正在提交...");
    }

    function isJSON_test(str) {
        if (str == "") {
            return true;
        }
        if (typeof str == 'string') {
            try {
                var obj = JSON.parse(str);
                if (typeof obj == 'object' && obj) {
                    return true;
                } else {
                    return false;
                }

            } catch (e) {
                console.log('error：' + str + '!!!' + e);
                return false;
            }
        }
        console.log('It is not a string!')
        return false;
    }

    function form2JsonString() {
        var jsonObj = {};
        jsonObj['gameid'] = parseInt($('#gameId').val());
        jsonObj['version'] = $('#version').val();
        jsonObj['engineversion'] = parseInt($('#engineVersion').val());

        if ($('#engineType').val() == "COCOS") {
            jsonObj['enginetype'] = 1
        } else if ($('#engineType').val() == "WEB") {
            jsonObj['enginetype'] = 2
        } else if ($('#engineType').val() == "COCOS2_3_1") {
            jsonObj['enginetype'] = 3
        } else {
            jsonObj['enginetype'] = 0
        }
        // jsonObj['enginetype'] = parseInt($('#engineType').val());
        jsonObj['packagepath'] = $('#packagePath').val();
        jsonObj['picurl'] = $('#picUrl').val();
        jsonObj['gameurl'] = $('#gameUrl').val();
        jsonObj['resurl'] = $('#resUrl').val();
        jsonObj['extraproperty'] = $('#extraProperty').val();
        var ret = isJSON_test(jsonObj['extraproperty'])
        if (!ret) {
            alert("extraProperty 格式错误： " + jsonObj['extraproperty'])
            return 
        }
        jsonObj['packagemd5'] = $('#packageMD5').val();
        jsonObj['resmd5'] = $('#resMD5').val();
         jsonObj['statetemplate'] = $('#stateTemplate').val();
        console.log("form2JsonString : ", jsonObj);
        return JSON.stringify(jsonObj);
    }

    function setMasterParams() {
        var engtype = ["COCOS", "WEB", "COCOS2_3_1"];
        var owner_html;
        for (var i = 0; i < engtype.length; i++) {
            owner_html += '<option>' + engtype[i] + '</option>'
        }
        $("#engineType").html(owner_html);

    }


</script>