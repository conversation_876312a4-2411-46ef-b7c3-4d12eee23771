// Code generated by quicksilver-cli. DO NOT EDIT.
package present_count

import (
	context "context"
	"golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/present-count-go"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	ClearUserPresentCount(ctx context.Context, uid, channelId uint32) (*pb.ClearUserPresentCountResp, protocol.ServerError)
	GetPresentCountById(ctx context.Context, opUid uint32, req *pb.GetPresentCountByIdReq) (*pb.GetPresentCountByIdResp, protocol.ServerError)
	GetPresentCountRank(ctx context.Context, opUid uint32, req *pb.GetPresentCountRankReq) (*pb.GetPresentCountRankResp, protocol.ServerError)
	GetPresentCountState(ctx context.Context, opUid uint32, req *pb.GetPresentCountStateReq) (*pb.GetPresentCountStateResp, protocol.ServerError)
	SwitchPresentCount(ctx context.Context, opUid uint32, req *pb.PresentCountReq) (*pb.PresentCountResp, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
