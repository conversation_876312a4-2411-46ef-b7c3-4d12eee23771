package cache

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"strconv"
	"strings"
	"time"

	redis "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

const (
	gameInfoInfoExpire       = 24 * time.Hour
	gameInfoPlayId           = "game_info_%d:play_id"
	gameInfoPhase            = "game_info_%d:phase"
	gameInfoEnrollQue        = "game_info_%d:enroll_que"
	gameInfoParticipateQue   = "game_info_%d:participate_que"
	gameInfoSelectingUserIdx = "game_info_%d:selecting_user_idx"
	gameInfoSelectEndTime    = "game_info_%d:select_end_time"
	gameInfoParticipateType  = "game_info_%d:participate_type"
	gameInfoBombNumber       = "game_info_%d:bomb_number"
	gameInfoSelectRange      = "game_info_%d:select_range"
	gameInfoLeaveChannelUid  = "game_info_%d:leave_channel_uid"

	selectEndTIme    = "select_end_time"
	noHostTimeoutEnd = "no_host_timeout_end"
)

var (
	noResultErr = fmt.Errorf("no result")
)

func genGameInfoKey(format string, channelId uint32) string {
	return fmt.Sprintf(format, channelId)
}

// SetDigitalBombPhase 设置游戏阶段
func (cache *Cache) SetDigitalBombPhase(ctx context.Context, channelId, phase uint32) error {
	return cache.cmder.Set(ctx, genGameInfoKey(gameInfoPhase, channelId), phase, gameInfoInfoExpire).Err()
}

// BatchGetDigitalBombPhase use MGet
func (cache *Cache) BatchGetDigitalBombPhase(ctx context.Context, channelId []uint32) (map[uint32]uint32, error) {
	keys := make([]string, 0, len(channelId))
	for _, cid := range channelId {
		keys = append(keys, genGameInfoKey(gameInfoPhase, cid))
	}
	rs, err := cache.cmder.MGet(ctx, keys...).Result()
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetDigitalBombPhase,channelId:%+v, err: %v", channelId, err)
		return nil, err
	}
	ret := make(map[uint32]uint32, len(rs))

	if len(rs) == 0 {
		return ret, nil
	}
	for i, item := range rs {
		if item == nil {
			continue
		}
		phase, _ := strconv.Atoi(item.(string))
		ret[channelId[i]] = uint32(phase)

	}
	return ret, nil
}

// GetDigitalBombPhase 获取游戏阶段
func (cache *Cache) GetDigitalBombPhase(ctx context.Context, channelId uint32) (uint32, error) {
	phase, err := cache.cmder.Get(ctx, genGameInfoKey(gameInfoPhase, channelId)).Int()
	if err == redis.Nil {
		return 0, nil
	}
	return uint32(phase), err
}

// UserEnroll 用户报名
func (cache *Cache) UserEnroll(ctx context.Context, channelId, uid uint32) error {
	_, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := genGameInfoKey(gameInfoEnrollQue, channelId)
		if err := pipe.ZAdd(ctx, key, &redis.Z{Score: float64(timeNowMilli()), Member: uid}).Err(); err != nil {
			return err
		}
		return pipe.Expire(ctx, key, gameInfoInfoExpire).Err()
	})
	return err
}

// CancelUserEnroll 取消用户报名
func (cache *Cache) CancelUserEnroll(ctx context.Context, channelId, uid uint32) (bool, error) {
	effect, err := cache.cmder.ZRem(ctx, genGameInfoKey(gameInfoEnrollQue, channelId), uid).Result()
	if err != nil {
		return false, err
	}
	return effect == 1, nil
}

// UserParticipate 选择用户参与
func (cache *Cache) UserParticipate(ctx context.Context, channelId, uid uint32) error {
	_, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := genGameInfoKey(gameInfoParticipateQue, channelId)
		if err := pipe.ZAdd(ctx, key, &redis.Z{Score: float64(timeNowMilli()), Member: uid}).Err(); err != nil {
			return err
		}
		return pipe.Expire(ctx, key, gameInfoInfoExpire).Err()
	})
	return err
}

// HostParticipate 主持参与
func (cache *Cache) HostParticipate(ctx context.Context, channelId, uid uint32) error {
	_, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := genGameInfoKey(gameInfoParticipateQue, channelId)
		if err := pipe.ZAdd(ctx, key, &redis.Z{Score: float64(1), Member: uid}).Err(); err != nil {
			return err
		}
		return pipe.Expire(ctx, key, gameInfoInfoExpire).Err()
	})
	return err
}

// ReplaceUserParticipate 替换参与用户, 打乱顺序使用
func (cache *Cache) ReplaceUserParticipate(ctx context.Context, channelId uint32, uid []uint32) error {
	_, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		if err := pipe.Del(ctx, genGameInfoKey(gameInfoParticipateQue, channelId)).Err(); err != nil {
			return err
		}

		member := make([]*redis.Z, len(uid))
		for i, uid := range uid {
			member[i] = &redis.Z{
				Score:  float64(i),
				Member: uid,
			}
		}
		return pipe.ZAdd(ctx, genGameInfoKey(gameInfoParticipateQue, channelId), member...).Err()
	})
	return err
}

// GetAllParticipateUser 获取参加游戏的用户
func (cache *Cache) GetAllParticipateUser(ctx context.Context, channelId uint32) ([]uint32, error) {
	uids := make([]uint32, 0, 8)
	rs, err := cache.cmder.ZRange(ctx, genGameInfoKey(gameInfoParticipateQue, channelId), 0, -1).Result()
	for _, item := range rs {
		uid, _ := strconv.Atoi(item)
		if uid == 0 {
			continue
		}
		uids = append(uids, uint32(uid))
	}
	return uids, err
}

// CancelUserParticipate 取消选择用户参与
func (cache *Cache) CancelUserParticipate(ctx context.Context, channelId, uid uint32) (bool, error) {
	effect, err := cache.cmder.ZRem(ctx, genGameInfoKey(gameInfoParticipateQue, channelId), uid).Result()
	if err != nil {
		return false, err
	}
	return effect == 1, nil
}

// IncrSelectingUserIdx 选人用户下标推进
func (cache *Cache) IncrSelectingUserIdx(ctx context.Context, channelId uint32, selectEndTime uint64) (uint32, error) {
	cmds, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := genGameInfoKey(gameInfoSelectingUserIdx, channelId)
		if err := pipe.Incr(ctx, key).Err(); err != nil {
			return err
		}
		pipe.Expire(ctx, key, gameInfoInfoExpire)

		// 更新自动选时间
		pipe.Set(ctx, genGameInfoKey(gameInfoSelectEndTime, channelId), selectEndTime, gameInfoInfoExpire)
		return pipe.ZAdd(ctx, selectEndTIme, &redis.Z{
			Score:  float64(selectEndTime),
			Member: channelId,
		}).Err()
	})
	if err != nil {
		return 0, err
	}
	if len(cmds) < 1 {
		return 0, noResultErr
	}

	ret, ok := cmds[0].(*redis.IntCmd)
	if !ok {
		return 0, noResultErr

	}
	idx, err := ret.Result()
	return uint32(idx), err
}

func (cache *Cache) ResetSelectingUserIdx(ctx context.Context, channelId, idx uint32) error {
	return cache.cmder.Set(ctx, genGameInfoKey(gameInfoSelectingUserIdx, channelId), idx, gameInfoInfoExpire).Err()
}

func (cache *Cache) SetSelectEndTime(ctx context.Context, channelId uint32, endTime uint64) error {
	return cache.cmder.Set(ctx, genGameInfoKey(gameInfoSelectEndTime, channelId), endTime, gameInfoInfoExpire).Err()
}

func (cache *Cache) GetSelectEndTime(ctx context.Context, channelId uint32) uint64 {
	rs, _ := cache.cmder.Get(ctx, genGameInfoKey(gameInfoSelectEndTime, channelId)).Result()
	selectEndTime, _ := strconv.Atoi(rs)
	return uint64(selectEndTime)
}

func (cache *Cache) GetSelectingUserIdx(ctx context.Context, channelId uint32) (uint32, error) {
	rs, err := cache.cmder.Get(ctx, genGameInfoKey(gameInfoSelectingUserIdx, channelId)).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	idx, _ := strconv.Atoi(rs)
	return uint32(idx), err
}

func (cache *Cache) GetSelectingUid(ctx context.Context, channelId uint32) (uint32, error) {
	selectIdx, err := cache.GetSelectingUserIdx(ctx, channelId)
	if err != nil {
		return 0, err
	}

	participateUid, err := cache.GetAllParticipateUser(ctx, channelId)
	if err != nil {
		return 0, err
	}

	if len(participateUid) == 0 {
		return 0, nil
	}

	return participateUid[int(selectIdx)%len(participateUid)], nil
}

// SetParticipateType 设置可报名用户类型
func (cache *Cache) SetParticipateType(ctx context.Context, channelId, participateType uint32) error {
	return cache.cmder.Set(ctx, genGameInfoKey(gameInfoParticipateType, channelId), participateType, gameInfoInfoExpire).Err()
}

// GetParticipateType 获取用户报名类型
func (cache *Cache) GetParticipateType(ctx context.Context, channelId uint32) (uint32, error) {
	rsStr, err := cache.cmder.Get(ctx, genGameInfoKey(gameInfoParticipateType, channelId)).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	pt, _ := strconv.Atoi(rsStr)
	return uint32(pt), nil
}

// GetAllEnrollUser 获取报名用户
func (cache *Cache) GetAllEnrollUser(ctx context.Context, channelId uint32) ([]uint32, error) {
	uids := make([]uint32, 0, 8)
	rs, err := cache.cmder.ZRange(ctx, genGameInfoKey(gameInfoEnrollQue, channelId), 0, -1).Result()
	for _, item := range rs {
		uid, _ := strconv.Atoi(item)
		if uid == 0 {
			continue
		}
		uids = append(uids, uint32(uid))
	}
	return uids, err
}

func (cache *Cache) ClearEnrollUser(ctx context.Context, channelId uint32) error {
	return cache.cmder.Del(ctx, genGameInfoKey(gameInfoEnrollQue, channelId)).Err()
}

func (cache *Cache) SetBombNumber(ctx context.Context, channelId, num uint32) error {
	return cache.cmder.Set(ctx, genGameInfoKey(gameInfoBombNumber, channelId), num, gameInfoInfoExpire).Err()
}

func (cache *Cache) GetBombNumber(ctx context.Context, channelId uint32) (uint32, error) {
	rs, err := cache.cmder.Get(ctx, genGameInfoKey(gameInfoBombNumber, channelId)).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}

	bombNum, err := strconv.Atoi(rs)
	return uint32(bombNum), err
}

func (cache *Cache) SetSelectRange(ctx context.Context, channelId, selectRangeStart, selectRangeEnd uint32) error {
	selectRange := fmt.Sprintf("%d-%d", selectRangeStart, selectRangeEnd)
	return cache.cmder.Set(ctx, genGameInfoKey(gameInfoSelectRange, channelId), selectRange, gameInfoInfoExpire).Err()
}

func (cache *Cache) GetSelectRange(ctx context.Context, channelId uint32) (uint32, uint32, error) {
	rs, err := cache.cmder.Get(ctx, genGameInfoKey(gameInfoSelectRange, channelId)).Result()
	if err == redis.Nil {
		return 0, 0, nil
	}
	if err != nil {
		return 0, 0, err
	}
	part := strings.Split(rs, "-")
	if len(part) < 2 {
		return 0, 0, fmt.Errorf("invalid selectRange")
	}
	selectRangeStart, _ := strconv.Atoi(part[0])
	selectRangeEnd, _ := strconv.Atoi(part[1])
	return uint32(selectRangeStart), uint32(selectRangeEnd), nil
}

func (cache *Cache) GenPlayId(ctx context.Context, channelId uint32) (uint32, error) {
	playId := time.Now().Unix()
	rs, err := cache.cmder.SetNX(ctx, genGameInfoKey(gameInfoPlayId, channelId), playId, gameInfoInfoExpire).Result()
	if err != nil {
		return 0, err
	}
	if !rs {
		return 0, fmt.Errorf("play_id is exist")
	}
	return uint32(playId), nil
}

func (cache *Cache) GetPlayId(ctx context.Context, channelId uint32) uint32 {
	rs, _ := cache.cmder.Get(ctx, genGameInfoKey(gameInfoPlayId, channelId)).Result()
	playId, _ := strconv.Atoi(rs)
	return uint32(playId)
}

func (cache *Cache) SetChannelSelectEndTime(ctx context.Context, channelId uint32, selectEndTime uint64) error {
	return cache.cmder.ZAdd(ctx, selectEndTIme, &redis.Z{
		Score:  float64(selectEndTime),
		Member: channelId,
	}).Err()
}

func (cache *Cache) GetChannelSelectEndTime(ctx context.Context) ([]uint32, error) {
	cmder, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		max := strconv.FormatUint(timeNowMilli(), 10)
		err := pipe.ZRangeByScore(ctx, selectEndTIme, &redis.ZRangeBy{
			Min: "0",
			Max: max,
		}).Err()
		if err != nil {
			return err
		}
		return pipe.ZRemRangeByScore(ctx, selectEndTIme, "0", max).Err()
	})
	if err != nil {
		return nil, err
	}

	if len(cmder) < 1 {
		return nil, noResultErr
	}

	if strCmd, ok := cmder[0].(*redis.StringSliceCmd); ok {
		rs, err := strCmd.Result()
		if err != nil {
			return nil, err
		}
		cids := make([]uint32, len(rs))
		for i, item := range rs {
			cid, _ := strconv.Atoi(item)
			if cid == 0 {
				continue
			}
			cids[i] = uint32(cid)
		}
		return cids, nil
	}

	return nil, nil
}

func (cache *Cache) ClearGameInfo(ctx context.Context, channelId uint32) error {
	delKeys := []string{
		genGameInfoKey(gameInfoPlayId, channelId),
		genGameInfoKey(gameInfoPhase, channelId),
		genGameInfoKey(gameInfoBombNumber, channelId),
		genGameInfoKey(gameInfoEnrollQue, channelId),
		genGameInfoKey(gameInfoParticipateQue, channelId),
		genGameInfoKey(gameInfoSelectRange, channelId),
		genGameInfoKey(gameInfoSelectEndTime, channelId),
		genGameInfoKey(gameInfoSelectingUserIdx, channelId),
	}
	_, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.Del(ctx, delKeys...)
		pipe.ZRem(ctx, selectEndTIme, channelId)
		return nil
	})

	return err
}

//var ukwFakeUidReplaceScript = redis.NewScript(`
//    local ret = {}
//	local participate_key = KEYS[1]
//	local enroll_key = KEYS[2]
//	local uid = tonumber(ARGV[1])
//	local fake_uid = tonumber(ARGV[2])
//
//	local participate_uid = redis.call('ZRANGE', participate_key, 0, -1)
//	redis.call('ZREMRANGEBYRANK', participate_key, 0, -1)
//    for idx = 1, #participate_uid do
//		local this_uid = tonumber(participate_uid[idx])
//		if (this_uid == fake_uid)
//		then
//			participate_uid[idx] = uid
//		end
//		redis.call('ZADD', participate_key, idx, participate_uid[idx])
//	end
//
//	local enroll_uid = redis.call('ZRANGE', enroll_key, 0, -1)
//	redis.call('ZREMRANGEBYRANK', enroll_key, 0, -1)
//    for idx = 1, #enroll_uid do
//		local this_uid = tonumber(enroll_uid[idx])
//		if (this_uid == fake_uid)
//		then
//			enroll_uid[idx] = uid
//		end
//		redis.call('ZADD', enroll_key, idx, enroll_uid[idx])
//	end
//
//	return ret
//`)
//
//func (cache *Cache) UKWFakeUidReplace(channelId, uid, fakeUid uint32) error {
//    return ukwFakeUidReplaceScript.Run(cache.cmder,
//        []string{genGameInfoKey(gameInfoParticipateQue, channelId), genGameInfoKey(gameInfoEnrollQue, channelId)},
//        uid, fakeUid).Err()
//}

func (cache *Cache) SetNoHostTimeoutEnd(ctx context.Context, channelId uint32, timeout uint64) error {
	return cache.cmder.ZAdd(ctx, noHostTimeoutEnd, &redis.Z{
		Score:  float64(timeout),
		Member: channelId,
	}).Err()
}

func (cache *Cache) DelNoHostTimeoutEnd(ctx context.Context, channelId uint32) error {
	return cache.cmder.ZRem(ctx, noHostTimeoutEnd, channelId).Err()
}

func (cache *Cache) GetNoHostTimeoutEnd(ctx context.Context) ([]uint32, error) {
	cmder, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		max := strconv.FormatUint(timeNowMilli(), 10)
		if err := pipe.ZRangeByScore(ctx, noHostTimeoutEnd, &redis.ZRangeBy{
			Min: "0",
			Max: max,
		}).Err(); err != nil {
			return err
		}
		return pipe.ZRemRangeByScore(ctx, noHostTimeoutEnd, "0", max).Err()
	})
	if err != nil {
		return nil, err
	}

	if len(cmder) < 1 {
		return nil, noResultErr
	}

	if strCmd, ok := cmder[0].(*redis.StringSliceCmd); ok {
		rs, err := strCmd.Result()
		if err != nil {
			return nil, err
		}
		cids := make([]uint32, len(rs))
		for i, item := range rs {
			cid, _ := strconv.Atoi(item)
			if cid == 0 {
				continue
			}
			cids[i] = uint32(cid)
		}
		return cids, nil
	}

	return nil, nil
}

func (cache *Cache) AddLeaveChannelUid(ctx context.Context, cid, uid uint32) error {
	_, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := genGameInfoKey(gameInfoLeaveChannelUid, cid)
		if err := pipe.SAdd(ctx, key, uid).Err(); err != nil {
			return err
		}
		return pipe.Expire(ctx, key, gameInfoInfoExpire).Err()
	})
	return err
}

func (cache *Cache) GetAllLeaveChannelUid(ctx context.Context, cid uint32) ([]uint32, error) {
	cmder, err := cache.cmder.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		key := genGameInfoKey(gameInfoLeaveChannelUid, cid)
		if err := pipe.SMembers(ctx, key).Err(); err != nil {
			return err
		}
		return pipe.Del(ctx, key).Err()
	})

	rs := make([]uint32, 0)
	if len(cmder) < 1 {
		return rs, fmt.Errorf("not found")
	}
	if strSliceCmd, ok := cmder[0].(*redis.StringSliceCmd); ok {
		strSlice, err := strSliceCmd.Result()
		if err != nil {
			return rs, err
		}

		for _, item := range strSlice {
			uid, _ := strconv.Atoi(item)
			if uid == 0 {
				continue
			}

			rs = append(rs, uint32(uid))
		}
	}

	return rs, err
}
