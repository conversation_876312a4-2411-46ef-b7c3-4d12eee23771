// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/fellow-accompany/internal/cache (interfaces: ICache)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	redis "github.com/go-redis/redis/v8"
	gomock "github.com/golang/mock/gomock"
	fellow_accompany "golang.52tt.com/protocol/services/fellow-accompany"
	cache "golang.52tt.com/services/tt-rev/fellow-accompany/internal/cache"
)

// MockICache is a mock of ICache interface.
type MockICache struct {
	ctrl     *gomock.Controller
	recorder *MockICacheMockRecorder
}

// MockICacheMockRecorder is the mock recorder for MockICache.
type MockICacheMockRecorder struct {
	mock *MockICache
}

// NewMockICache creates a new mock instance.
func NewMockICache(ctrl *gomock.Controller) *MockICache {
	mock := &MockICache{ctrl: ctrl}
	mock.recorder = &MockICacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICache) EXPECT() *MockICacheMockRecorder {
	return m.recorder
}

// BatchGetPairAccompanyValue mocks base method.
func (m *MockICache) BatchGetPairAccompanyValue(arg0 context.Context, arg1 []*fellow_accompany.AccompanyPair) ([]*cache.UserPairWithValue, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetPairAccompanyValue", arg0, arg1)
	ret0, _ := ret[0].([]*cache.UserPairWithValue)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetPairAccompanyValue indicates an expected call of BatchGetPairAccompanyValue.
func (mr *MockICacheMockRecorder) BatchGetPairAccompanyValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetPairAccompanyValue", reflect.TypeOf((*MockICache)(nil).BatchGetPairAccompanyValue), arg0, arg1)
}

// Close mocks base method.
func (m *MockICache) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockICacheMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockICache)(nil).Close))
}

// GetAllRankValue mocks base method.
func (m *MockICache) GetAllRankValue(arg0 context.Context, arg1 *cache.UserPair) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRankValue", arg0, arg1)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRankValue indicates an expected call of GetAllRankValue.
func (mr *MockICacheMockRecorder) GetAllRankValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRankValue", reflect.TypeOf((*MockICache)(nil).GetAllRankValue), arg0, arg1)
}

// GetDayRankExpandInfo mocks base method.
func (m *MockICache) GetDayRankExpandInfo(arg0 context.Context, arg1 []*cache.UserPair) ([]*cache.ExpandItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDayRankExpandInfo", arg0, arg1)
	ret0, _ := ret[0].([]*cache.ExpandItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDayRankExpandInfo indicates an expected call of GetDayRankExpandInfo.
func (mr *MockICacheMockRecorder) GetDayRankExpandInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDayRankExpandInfo", reflect.TypeOf((*MockICache)(nil).GetDayRankExpandInfo), arg0, arg1)
}

// GetMonthRankExpandInfo mocks base method.
func (m *MockICache) GetMonthRankExpandInfo(arg0 context.Context, arg1 []*cache.UserPair) ([]*cache.ExpandItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonthRankExpandInfo", arg0, arg1)
	ret0, _ := ret[0].([]*cache.ExpandItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonthRankExpandInfo indicates an expected call of GetMonthRankExpandInfo.
func (mr *MockICacheMockRecorder) GetMonthRankExpandInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonthRankExpandInfo", reflect.TypeOf((*MockICache)(nil).GetMonthRankExpandInfo), arg0, arg1)
}

// GetPairRankInfo mocks base method.
func (m *MockICache) GetPairRankInfo(arg0 context.Context, arg1, arg2 uint32) (*fellow_accompany.GetPairRankInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPairRankInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(*fellow_accompany.GetPairRankInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPairRankInfo indicates an expected call of GetPairRankInfo.
func (mr *MockICacheMockRecorder) GetPairRankInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPairRankInfo", reflect.TypeOf((*MockICache)(nil).GetPairRankInfo), arg0, arg1, arg2)
}

// GetRankListByType mocks base method.
func (m *MockICache) GetRankListByType(arg0 context.Context, arg1, arg2 int64, arg3 uint32, arg4 bool) ([]*cache.UserPairWithValue, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRankListByType", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*cache.UserPairWithValue)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRankListByType indicates an expected call of GetRankListByType.
func (mr *MockICacheMockRecorder) GetRankListByType(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRankListByType", reflect.TypeOf((*MockICache)(nil).GetRankListByType), arg0, arg1, arg2, arg3, arg4)
}

// GetRedisClient mocks base method.
func (m *MockICache) GetRedisClient() redis.Cmdable {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedisClient")
	ret0, _ := ret[0].(redis.Cmdable)
	return ret0
}

// GetRedisClient indicates an expected call of GetRedisClient.
func (mr *MockICacheMockRecorder) GetRedisClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedisClient", reflect.TypeOf((*MockICache)(nil).GetRedisClient))
}

// GetTopAccompanyPair mocks base method.
func (m *MockICache) GetTopAccompanyPair(arg0 context.Context, arg1 uint32) (*fellow_accompany.GetTopAccompanyPairResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTopAccompanyPair", arg0, arg1)
	ret0, _ := ret[0].(*fellow_accompany.GetTopAccompanyPairResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTopAccompanyPair indicates an expected call of GetTopAccompanyPair.
func (mr *MockICacheMockRecorder) GetTopAccompanyPair(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTopAccompanyPair", reflect.TypeOf((*MockICache)(nil).GetTopAccompanyPair), arg0, arg1)
}

// GetTotalAccompanyValue mocks base method.
func (m *MockICache) GetTotalAccompanyValue(arg0 context.Context, arg1, arg2 uint32) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalAccompanyValue", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalAccompanyValue indicates an expected call of GetTotalAccompanyValue.
func (mr *MockICacheMockRecorder) GetTotalAccompanyValue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalAccompanyValue", reflect.TypeOf((*MockICache)(nil).GetTotalAccompanyValue), arg0, arg1, arg2)
}

// GetUserTop10Info mocks base method.
func (m *MockICache) GetUserTop10Info(arg0 context.Context, arg1 uint32) (map[uint32]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserTop10Info", arg0, arg1)
	ret0, _ := ret[0].(map[uint32]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTop10Info indicates an expected call of GetUserTop10Info.
func (mr *MockICacheMockRecorder) GetUserTop10Info(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTop10Info", reflect.TypeOf((*MockICache)(nil).GetUserTop10Info), arg0, arg1)
}

// GetWeekRankExpandInfo mocks base method.
func (m *MockICache) GetWeekRankExpandInfo(arg0 context.Context, arg1 []*cache.UserPair) ([]*cache.ExpandItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWeekRankExpandInfo", arg0, arg1)
	ret0, _ := ret[0].([]*cache.ExpandItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWeekRankExpandInfo indicates an expected call of GetWeekRankExpandInfo.
func (mr *MockICacheMockRecorder) GetWeekRankExpandInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWeekRankExpandInfo", reflect.TypeOf((*MockICache)(nil).GetWeekRankExpandInfo), arg0, arg1)
}

// IncreaseAccompanyValue mocks base method.
func (m *MockICache) IncreaseAccompanyValue(arg0 context.Context, arg1, arg2, arg3 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseAccompanyValue", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncreaseAccompanyValue indicates an expected call of IncreaseAccompanyValue.
func (mr *MockICacheMockRecorder) IncreaseAccompanyValue(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseAccompanyValue", reflect.TypeOf((*MockICache)(nil).IncreaseAccompanyValue), arg0, arg1, arg2, arg3)
}

// RmAccompanyValue mocks base method.
func (m *MockICache) RmAccompanyValue(arg0 context.Context, arg1, arg2 uint32) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RmAccompanyValue", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RmAccompanyValue indicates an expected call of RmAccompanyValue.
func (mr *MockICacheMockRecorder) RmAccompanyValue(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RmAccompanyValue", reflect.TypeOf((*MockICache)(nil).RmAccompanyValue), arg0, arg1, arg2)
}
