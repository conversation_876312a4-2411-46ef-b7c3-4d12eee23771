package conf

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"time"

	"golang.52tt.com/pkg/log"
)

const BusinessConfFile = "/data/oss/conf-center/tt/pgc-channel-pk-logic.json"

var LastConfMd5Sum [md5.Size]byte

type Source struct {
	Url string `json:"url"`
	Md5 string `json:"md5"`
}

type MvpSourceConf struct {
	MinScore      uint32  `json:"min_score"`
	MaxScore      uint32  `json:"max_score"`
	SourceType    uint32  `json:"source_type"`
	SourceId      uint32  `json:"source_id"`
	Source        *Source `json:"source"`
	BackgroundPic string  `json:"background_pic"`
}

type ResultSourceConf struct {
	Rank             uint32  `json:"rank"` //  1-初级 2-中级 3-高级
	MinScore         uint32  `json:"min_score"`
	MaxScore         uint32  `json:"max_score"`
	SourceType       uint32  `json:"source_type"`
	SourceId         uint32  `json:"source_id"`
	SourceWithMvp    *Source `json:"source_with_mvp"`
	SourceWithoutMvp *Source `json:"source_without_mvp"`
	TitleSource      *Source `json:"title_source"`
}

type BusinessConf struct {
	ResultSourceConfList []*ResultSourceConf `json:"result_source_conf_list"`
	MvpSourceConfList    []*MvpSourceConf    `json:"mvp_source_conf_list"`
	MvpIconConfList      []*Source           `json:"mvp_icon_conf_list"`
}

func (c *BusinessConf) Parse(configFile string) (isChange bool, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("Failed to parse config: %v \n", e)
		}
	}()

	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		return false, err
	}

	md5Sum := md5.Sum(data)
	if md5Sum == LastConfMd5Sum {
		isChange = false
		return
	}

	err = json.Unmarshal(data, &c)
	if err != nil {
		return false, err
	}

	LastConfMd5Sum = md5Sum

	log.Infof("BusinessConf : %+v", c)
	return true, nil
}

type BusinessConfManager struct {
	Done chan interface{}
	//mutex sync.RWMutex
	conf *BusinessConf
}

func NewBusinessConfManager() (*BusinessConfManager, error) {
	businessConf := &BusinessConf{}

	_, err := businessConf.Parse(BusinessConfFile)
	if err != nil {
		log.Errorf("NewBusinessConfManager fail to Parse BusinessConf err:%v", err)
		return nil, err
	}

	confMgr := &BusinessConfManager{
		conf: businessConf,
		Done: make(chan interface{}),
	}

	go confMgr.Watch(BusinessConfFile)

	return confMgr, nil
}

func (bm *BusinessConfManager) Reload(file string) error {
	businessConf := &BusinessConf{}

	isChange, err := businessConf.Parse(file)
	if err != nil {
		log.Errorf("Reload fail to Parse BusinessConf err:%v", err)
		return err
	}

	if isChange {
		//bm.mutex.Lock()
		bm.conf = businessConf
		//bm.mutex.Unlock()

		log.Infof("Reload %+v", businessConf)
	}

	return nil
}

func (bm *BusinessConfManager) Watch(file string) {
	log.Infof("Watch start. file:%s", file)

	for {
		select {
		case _, ok := <-bm.Done:
			if !ok {
				log.Infof("Watch done")
				return
			}

		case <-time.After(30 * time.Second):
			log.Debugf("Watch check change")

			err := bm.Reload(file)
			if err != nil {
				log.Errorf("Watch Reload fail. file:%s, err:%v", file, err)
			}
		}
	}
}

func (bm *BusinessConfManager) GetResultSourceConfList() []*ResultSourceConf {
	return bm.conf.ResultSourceConfList
}

func (bm *BusinessConfManager) GetMvpSourceConfList() []*MvpSourceConf {
	return bm.conf.MvpSourceConfList
}

func (bm *BusinessConfManager) GetMvpIconList() []*Source {
	return bm.conf.MvpIconConfList
}

func (bm *BusinessConfManager) Close() {
	close(bm.Done)
}
