package coin_proxy

import (
	"context"
	"github.com/grpc-ecosystem/go-grpc-middleware"
	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/coin-proxy"
)

const (
	serviceName = "coin-proxy"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewCoinProxyClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.CoinProxyClient { return c.Stub().(pb.CoinProxyClient) }

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

type RiskAddReq = pb.RiskAddReq
type RiskAddResp = pb.RiskAddResp

func (c *Client) RiskAdd(ctx context.Context, req *RiskAddReq) (*RiskAddResp, protocol.ServerError) {
	resp, err := c.typedStub().RiskAdd(ctx, req)
	if err != nil {
		return resp, protocol.ToServerError(err)
	}

	return resp, nil
}

func (c *Client) GetLastChargeOrder(ctx context.Context, req *pb.GetLastChargeOrderReq) (*pb.GetLastChargeOrderResp, protocol.ServerError) {
	resp, err := c.typedStub().GetLastChargeOrder(ctx, req)
	if err != nil {
		return resp, protocol.ToServerError(err)
	}

	return resp, nil
}
